
import { useState, useCallback } from 'react';
import { useToast } from './use-toast';

interface Location {
  lng: number;
  lat: number;
  address?: string;
  coordinates?: [number, number];
}

interface UseAddressMinimapOptions {
  initialLocation?: Location;
  onLocationSaved?: (location: Location) => void;
}

export function useAddressMinimap(options: UseAddressMinimapOptions = {}) {
  const [location, setLocation] = useState<Location | undefined>(options.initialLocation);
  const [isEditingLocation, setIsEditingLocation] = useState(false);
  const { toast } = useToast();

  const handleSaveLocation = useCallback((newLocation: Location) => {
    setLocation(newLocation);
    setIsEditingLocation(false);
    
    // Call the provided callback if available
    if (options.onLocationSaved) {
      options.onLocationSaved(newLocation);
    }
    
    toast({
      title: "Location Updated",
      description: "The location has been saved successfully."
    });
  }, [options.onLocationSaved, toast]);

  const startEditing = useCallback(() => {
    setIsEditingLocation(true);
  }, []);

  const cancelEditing = useCallback(() => {
    setIsEditingLocation(false);
  }, []);

  return {
    location,
    setLocation,
    isEditingLocation,
    startEditing,
    cancelEditing,
    handleSaveLocation
  };
}
