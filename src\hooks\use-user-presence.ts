
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';

export interface UserPresence {
  isOnline: boolean;
  lastSeen?: Date;
}

export function useUserPresence(userId?: string) {
  const [presence, setPresence] = useState<UserPresence>({
    isOnline: false
  });

  useEffect(() => {
    if (!userId) return;

    const channel = supabase.channel(`user_presence_${userId}`);

    channel
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        const userState = state[userId];
        if (userState) {
          setPresence({
            isOnline: true,
            lastSeen: new Date()
          });
        }
      })
      .on('presence', { event: 'leave' }, () => {
        setPresence({
          isOnline: false,
          lastSeen: new Date()
        });
      })
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [userId]);

  return presence;
}
