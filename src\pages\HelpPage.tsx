
import React, { useState } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  Search,
  User,
  Map,
  Calendar,
  CreditCard,
  Shield,
  MessageCircle,
  HelpCircle,
  ThumbsUp,
  ThumbsDown,
  Mail,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

export default function HelpPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [feedbackSubmitted, setFeedbackSubmitted] = useState<'helpful' | 'not-helpful' | null>(null);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real implementation, this would search through help content
    console.log('Searching for:', searchQuery);
  };

  const handleFeedback = (type: 'helpful' | 'not-helpful') => {
    setFeedbackSubmitted(type);
    // In a real implementation, this would send feedback to the server
    console.log('Feedback submitted:', type);
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-8 px-4 max-w-6xl">
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold mb-4">Help & Support</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Find answers to common questions or get in touch with our support team
          </p>
        </div>

        {/* Status Banner */}
        <Alert className="mb-8 bg-green-50 border-green-200">
          <CheckCircle className="h-4 w-4 text-green-500" />
          <AlertTitle>All systems operational</AlertTitle>
          <AlertDescription>
            BuddySurf is running smoothly. If you're experiencing issues, please contact support.
          </AlertDescription>
        </Alert>

        {/* Search Bar */}
        <div className="mb-12">
          <form onSubmit={handleSearch} className="flex w-full max-w-3xl mx-auto">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                type="text"
                placeholder="How can we help you?"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 py-6 text-lg rounded-l-lg"
              />
            </div>
            <Button type="submit" className="rounded-l-none px-6">
              Search
            </Button>
          </form>
        </div>

        {/* Quick Links */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12">
          <QuickLinkCard
            icon={<User className="h-6 w-6" />}
            title="Account"
            description="Profile, settings, and account management"
          />
          <QuickLinkCard
            icon={<Map className="h-6 w-6" />}
            title="Map & Location"
            description="Using the map and location features"
          />
          <QuickLinkCard
            icon={<Calendar className="h-6 w-6" />}
            title="Activities"
            description="Creating and joining activities"
          />
          <QuickLinkCard
            icon={<CreditCard className="h-6 w-6" />}
            title="Payments"
            description="Billing, subscriptions, and refunds"
          />
        </div>

        {/* Main Content Tabs */}
        <Tabs defaultValue="faq" className="mb-12">
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-3 mb-8">
            <TabsTrigger value="faq">FAQ</TabsTrigger>
            <TabsTrigger value="guides">Guides</TabsTrigger>
            <TabsTrigger value="contact">Contact Us</TabsTrigger>
          </TabsList>

          <TabsContent value="faq" className="bg-white rounded-lg p-6 shadow-sm border">
            <h2 className="text-2xl font-bold mb-6">Frequently Asked Questions</h2>

            <Accordion type="single" collapsible className="w-full">
              <AccordionItem value="item-1">
                <AccordionTrigger>How do I create an account?</AccordionTrigger>
                <AccordionContent>
                  <p className="mb-4">
                    To create an account on BuddySurf, follow these steps:
                  </p>
                  <ol className="list-decimal pl-5 space-y-2 mb-4">
                    <li>Click on the "Sign Up" button in the top right corner of the homepage</li>
                    <li>Enter your email address and create a password</li>
                    <li>Verify your email address by clicking the link sent to your inbox</li>
                    <li>Complete your profile by adding your name, photo, and other details</li>
                  </ol>
                  <p>
                    Once your account is created, you'll have access to all features of BuddySurf.
                  </p>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-2">
                <AccordionTrigger>How does location sharing work?</AccordionTrigger>
                <AccordionContent>
                  <p className="mb-4">
                    BuddySurf uses your location to help you connect with people and activities nearby. Here's how it works:
                  </p>
                  <ul className="list-disc pl-5 space-y-2 mb-4">
                    <li>Your location is only shared when you grant permission</li>
                    <li>You can set a default location if you don't want to share your exact position</li>
                    <li>Your location updates periodically when you're using the app</li>
                    <li>You can disable location sharing at any time in your settings</li>
                  </ul>
                  <p>
                    We take your privacy seriously and only use your location to enhance your experience on the platform.
                  </p>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-3">
                <AccordionTrigger>How do I create an activity?</AccordionTrigger>
                <AccordionContent>
                  <p className="mb-4">
                    Creating an activity on BuddySurf is easy:
                  </p>
                  <ol className="list-decimal pl-5 space-y-2 mb-4">
                    <li>Navigate to the Activities page</li>
                    <li>Click the "Create Activity" button</li>
                    <li>Fill in the details: title, description, location, date, and time</li>
                    <li>Set any additional options like maximum participants or price</li>
                    <li>Click "Create" to publish your activity</li>
                  </ol>
                  <p>
                    Your activity will be visible to other users who can request to join. You'll receive notifications when someone wants to join your activity.
                  </p>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-4">
                <AccordionTrigger>Is BuddySurf free to use?</AccordionTrigger>
                <AccordionContent>
                  <p className="mb-4">
                    BuddySurf offers both free and premium options:
                  </p>
                  <ul className="list-disc pl-5 space-y-2 mb-4">
                    <li>Basic features are available to all users for free</li>
                    <li>Premium features require a subscription</li>
                    <li>Some activities may have fees set by their hosts</li>
                  </ul>
                  <p>
                    You can enjoy most of BuddySurf's core features without paying anything. Premium subscriptions offer additional benefits like advanced filters, unlimited messaging, and profile boosts.
                  </p>
                </AccordionContent>
              </AccordionItem>

              <AccordionItem value="item-5">
                <AccordionTrigger>How do I report inappropriate content or users?</AccordionTrigger>
                <AccordionContent>
                  <p className="mb-4">
                    If you encounter inappropriate content or behavior on BuddySurf:
                  </p>
                  <ol className="list-decimal pl-5 space-y-2 mb-4">
                    <li>Click the "Report" button on the user's profile or activity</li>
                    <li>Select the reason for your report</li>
                    <li>Provide any additional details about the issue</li>
                    <li>Submit your report</li>
                  </ol>
                  <p className="mb-4">
                    Our moderation team reviews all reports and takes appropriate action according to our community guidelines.
                  </p>
                  <p>
                    For urgent issues, you can also contact our support team directly.
                  </p>
                </AccordionContent>
              </AccordionItem>
            </Accordion>

            {/* Feedback Section */}
            <div className="mt-8 pt-6 border-t">
              <h3 className="text-lg font-medium mb-4">Was this helpful?</h3>
              <div className="flex gap-4">
                <Button
                  variant={feedbackSubmitted === 'helpful' ? 'default' : 'outline'}
                  onClick={() => handleFeedback('helpful')}
                  className="flex items-center gap-2"
                  disabled={feedbackSubmitted !== null}
                >
                  <ThumbsUp className="h-4 w-4" />
                  Yes, it helped
                </Button>
                <Button
                  variant={feedbackSubmitted === 'not-helpful' ? 'default' : 'outline'}
                  onClick={() => handleFeedback('not-helpful')}
                  className="flex items-center gap-2"
                  disabled={feedbackSubmitted !== null}
                >
                  <ThumbsDown className="h-4 w-4" />
                  No, I need more help
                </Button>
              </div>
              {feedbackSubmitted && (
                <p className="mt-4 text-sm text-muted-foreground">
                  Thank you for your feedback! {feedbackSubmitted === 'not-helpful' && 'Our support team will reach out to help you further.'}
                </p>
              )}
            </div>
          </TabsContent>

          <TabsContent value="guides" className="bg-white rounded-lg p-6 shadow-sm border">
            <h2 className="text-2xl font-bold mb-6">Guides & Tutorials</h2>

            <div className="grid md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Getting Started Guide</CardTitle>
                  <CardDescription>Learn the basics of using BuddySurf</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    This comprehensive guide covers everything you need to know to get started with BuddySurf, from creating your account to joining your first activity.
                  </p>
                  <Badge>Beginner</Badge>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full">Read Guide</Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Creating Activities</CardTitle>
                  <CardDescription>Learn how to host successful activities</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    This guide explains how to create engaging activities that attract participants, manage your guest list, and ensure everyone has a great time.
                  </p>
                  <Badge>Intermediate</Badge>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full">Read Guide</Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Using the Map</CardTitle>
                  <CardDescription>Master the interactive map features</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Learn how to use the interactive map to discover people and activities near you, customize your map view, and manage your location settings.
                  </p>
                  <Badge>Beginner</Badge>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full">Read Guide</Button>
                </CardFooter>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Safety Guidelines</CardTitle>
                  <CardDescription>Stay safe while using BuddySurf</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm text-muted-foreground mb-4">
                    Important safety tips and guidelines to follow when meeting new people, joining activities, or hosting your own events on BuddySurf.
                  </p>
                  <Badge className="bg-orange-500">Important</Badge>
                </CardContent>
                <CardFooter>
                  <Button variant="outline" className="w-full">Read Guide</Button>
                </CardFooter>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="contact" className="bg-white rounded-lg p-6 shadow-sm border">
            <h2 className="text-2xl font-bold mb-6">Contact Support</h2>

            <div className="grid md:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-medium mb-4">Email Support</h3>
                <p className="text-muted-foreground mb-4">
                  Send us an email and we'll get back to you within 24 hours.
                </p>
                <Button className="flex items-center gap-2 mb-6">
                  <Mail className="h-4 w-4" />
                  <EMAIL>
                </Button>

                <h3 className="text-lg font-medium mb-4">Live Chat</h3>
                <p className="text-muted-foreground mb-4">
                  Chat with our support team in real-time during business hours.
                </p>
                <Button className="flex items-center gap-2">
                  <MessageCircle className="h-4 w-4" />
                  Start Chat
                </Button>
              </div>

              <div>
                <h3 className="text-lg font-medium mb-4">Submit a Support Ticket</h3>
                <form className="space-y-4">
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium mb-1">Name</label>
                    <Input id="name" placeholder="Your name" />
                  </div>
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium mb-1">Email</label>
                    <Input id="email" type="email" placeholder="Your email address" />
                  </div>
                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium mb-1">Subject</label>
                    <Input id="subject" placeholder="What's your issue about?" />
                  </div>
                  <div>
                    <label htmlFor="message" className="block text-sm font-medium mb-1">Message</label>
                    <textarea
                      id="message"
                      rows={4}
                      className="w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                      placeholder="Describe your issue in detail"
                    ></textarea>
                  </div>
                  <Button type="submit" className="w-full">Submit Ticket</Button>
                </form>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}

interface QuickLinkCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
}

function QuickLinkCard({ icon, title, description }: QuickLinkCardProps) {
  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-6">
        <div className="flex flex-col items-center text-center">
          <div className="p-3 rounded-full bg-primary/10 text-primary mb-4">
            {icon}
          </div>
          <h3 className="font-medium mb-1">{title}</h3>
          <p className="text-sm text-muted-foreground">{description}</p>
        </div>
      </CardContent>
    </Card>
  );
}
