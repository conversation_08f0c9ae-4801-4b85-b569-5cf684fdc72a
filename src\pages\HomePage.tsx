import React, { useState, useEffect } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { LoginDialog } from "@/components/auth/login-dialog";
import { SignupDialog } from "@/components/auth/signup-dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useAuth } from '@/hooks/use-auth';
import { Link } from 'react-router-dom';
import {
  MapPin, Calendar, Briefcase, Map, UserPlus, AlertCircle,
  MessageCircle, Bell, Activity, Compass, Users, ChevronRight,
  Clock, TrendingUp, Star, Check, Zap, Gem, Crown, Shield, Waves, Sparkles
} from 'lucide-react';
import { OnboardingModal } from "@/components/onboarding/OnboardingModal";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { SubscriptionButton } from "@/components/subscription";
import { Card, CardContent, CardDescription, CardFooter, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useActivities } from '@/hooks/use-activities';
import { Progress } from "@/components/ui/progress";
import { motion } from "framer-motion";
import { UserSuggestions } from "@/components/user/UserSuggestions";

export default function HomePage() {
  const [isSignupOpen, setIsSignupOpen] = React.useState(false);
  const [isLoginOpen, setIsLoginOpen] = React.useState(false);
  const {
    user,
    isLoading,
    profile
  } = useAuth();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [dismissedAlert, setDismissedAlert] = useState(false);
  const { data: activities, isLoading: activitiesLoading } = useActivities();
  // Check if profile is incomplete by first checking the onboarding_completed flag
  // Require all essential fields from the onboarding process
  const isProfileIncomplete = user && (
    profile?.onboarding_completed === false ||
    (!profile?.onboarding_completed && (
      !profile?.display_name ||
      !profile?.avatar_url ||
      !profile?.birthday ||
      !profile?.gender ||
      !profile?.default_location ||
      !profile?.location_permission_granted ||
      !profile?.vibes ||
      !Array.isArray(profile?.vibes) ||
      profile?.vibes.length === 0
    ))
  );

  // Check if we've shown onboarding recently to avoid showing it on every page visit
  const onboardingKey = user ? `onboarding_shown_${user.id}` : null;

  // Removed automatic onboarding trigger - onboarding will only show once after signup

  const handleSignupClick = () => {
    setIsLoginOpen(false);
    setIsSignupOpen(true);
  };

  const handleCompleteProfileClick = () => {
    console.log("Complete profile button clicked on HomePage");
    // Always show onboarding when button is clicked, regardless of whether it's been shown recently
    setShowOnboarding(true);

    // Don't update localStorage here - we want the modal to appear when the button is clicked
    // even if it's been shown recently
  };

  // Get upcoming activities (next 3 days)
  const upcomingActivities = activities?.filter(activity => {
    if (!activity.start_time) return false;
    const activityDate = new Date(activity.start_time);
    const now = new Date();
    const threeDaysFromNow = new Date();
    threeDaysFromNow.setDate(now.getDate() + 3);
    return activityDate >= now && activityDate <= threeDaysFromNow;
  }).slice(0, 3) || [];

  // Calculate profile completion percentage
  const getProfileCompletionPercentage = () => {
    if (!user || !profile) return 0;

    // If onboarding is explicitly marked as completed, return 100%
    if (profile.onboarding_completed === true) {
      return 100;
    }

    // Required fields from the onboarding process
    const requiredFields = [
      !!profile.display_name,
      !!profile.avatar_url,
      !!profile.birthday,
      !!profile.gender,
      !!profile.default_location,
      !!profile.location_permission_granted,
      Array.isArray(profile.vibes) && profile.vibes.length > 0
    ];

    // Optional fields that contribute to profile completeness but aren't required
    const optionalFields = [
      !!profile.bio,
      !!profile.notifications_enabled,
      Array.isArray(profile.purposes) && profile.purposes.length > 0,
      Array.isArray(profile.interests) && profile.interests.length > 0,
      Array.isArray(profile.gallery) && profile.gallery.length > 0
    ];

    // Calculate percentage based on required fields (70% of total) and optional fields (30% of total)
    const completedRequiredFields = requiredFields.filter(Boolean).length;
    const completedOptionalFields = optionalFields.filter(Boolean).length;

    const requiredPercentage = Math.round((completedRequiredFields / requiredFields.length) * 70);
    const optionalPercentage = Math.round((completedOptionalFields / optionalFields.length) * 30);

    return requiredPercentage + optionalPercentage;
  };

  const profileCompletionPercentage = getProfileCompletionPercentage();

  return (
    <MainLayout title="Home">
      <div className="container mx-auto max-w-6xl py-4 px-4 md:px-0">
        {/* Profile completion alert */}
        {isProfileIncomplete && !dismissedAlert && (
          <Alert className="mb-6 bg-amber-50 border-amber-200">
            <AlertCircle className="h-4 w-4 text-amber-500" />
            <AlertTitle>Complete your profile</AlertTitle>
            <AlertDescription className="flex items-center justify-between">
              <span>Complete your profile to unlock all features and improve your experience.</span>
              <div className="flex gap-3">
                <Button onClick={handleCompleteProfileClick} size="sm" className="bg-primary-purple hover:bg-primary-deep-purple">
                  Complete Now
                </Button>
                <Button onClick={() => setDismissedAlert(true)} size="sm" variant="outline">
                  Dismiss
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Hero section */}
        <section className="relative flex flex-col md:flex-row items-center gap-8 pb-16 border-b border-border overflow-hidden min-h-[420px]">
          {/* Animated background gradient */}
          <div className="absolute inset-0 -z-10">
            <div className="absolute left-1/2 top-0 w-[120vw] h-[60vh] -translate-x-1/2 bg-gradient-to-tr from-primary/30 via-accent/20 to-secondary/30 blur-2xl opacity-70 animate-gradient-move" />
          </div>

          {/* Hero text */}
          <div className="flex-1 space-y-7 z-10">
            <span className="inline-flex items-center gap-2 px-3 py-1 rounded-full bg-primary/10 text-primary font-semibold text-base mb-2 animate-fade-in">
              <Sparkles className="w-5 h-5 text-accent" />
              <span>Meet, Surf, Connect 🌊</span>
            </span>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-extrabold tracking-tight bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent drop-shadow-lg animate-fade-in">
              Connect with people in your area
            </h1>
            <p className="text-xl text-muted-foreground animate-fade-in delay-100">
              BuddySurf helps you discover and meet up with people who share your interests, hobbies, and passions.
            </p>
            {/* CTA Buttons */}
            <div className="flex flex-wrap gap-4 pt-4 animate-fade-in delay-200">
              {!user && !isLoading ? (
                <>
                  <Button onClick={() => setIsSignupOpen(true)} size="lg" className="bg-primary hover:bg-primary/90 shadow-lg animate-pulse-slow">
                    Get Started
                  </Button>
                  <Button onClick={() => setIsLoginOpen(true)} variant="outline" size="lg">
                    Log In
                  </Button>
                </>
              ) : (
                <>
                  <Button size="lg" className="bg-primary hover:bg-primary/90 shadow-lg animate-pulse-slow" asChild>
                    <Link to="/meetmap">Explore Map</Link>
                  </Button>
                  {isProfileIncomplete && (
                    <Button onClick={handleCompleteProfileClick} variant="outline" size="lg" className="flex items-center gap-2">
                      <UserPlus className="w-4 h-4" />
                      Complete Your Profile
                    </Button>
                  )}
                </>
              )}
            </div>
            {/* Social proof avatars/stats */}
            <div className="flex items-center gap-3 pt-6 animate-fade-in delay-300">
              <Avatar className="w-9 h-9 border-2 border-white -ml-0">
                <AvatarImage src="/avatars/avatar1.png" alt="User 1" />
                <AvatarFallback>AL</AvatarFallback>
              </Avatar>
              <Avatar className="w-9 h-9 border-2 border-white -ml-3">
                <AvatarImage src="/avatars/avatar2.png" alt="User 2" />
                <AvatarFallback>JP</AvatarFallback>
              </Avatar>
              <Avatar className="w-9 h-9 border-2 border-white -ml-3">
                <AvatarImage src="/avatars/avatar3.png" alt="User 3" />
                <AvatarFallback>SK</AvatarFallback>
              </Avatar>
              <span className="ml-2 text-sm text-muted-foreground font-medium">Join <span className="text-primary font-bold">2,500+</span> BuddySurfers</span>
            </div>
          </div>

          {/* Hero image with overlapping effect */}
          <div className="flex-1 flex items-center justify-center relative z-10 animate-fade-in delay-200">
            <div className="relative w-full max-w-md">
              <img
                src="https://images.unsplash.com/photo-1531844251246-9a1bfaae09fc?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1080&q=80"
                alt="People meeting up"
                className="rounded-2xl shadow-2xl w-full h-auto object-cover border-4 border-white/80"
              />
              {/* Overlapping shape for depth */}
              <div className="absolute -bottom-6 -right-6 w-32 h-32 bg-accent/30 rounded-full blur-2xl z-[-1]" />
            </div>
          </div>

          {/* Scroll indicator */}
          <div className="absolute bottom-4 left-1/2 -translate-x-1/2 flex flex-col items-center animate-bounce z-20">
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
              <ChevronRight className="w-6 h-6 text-primary rotate-90" />
            </div>
            <span className="text-xs text-muted-foreground mt-1">Scroll down</span>
          </div>
        </section>

        {/* Quick Stats Section (for logged in users) */}
        {user && (
          <motion.div
            className="py-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <h2 className="text-2xl font-bold mb-6">Your Dashboard</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Profile Completion Card */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Profile Completion</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between text-sm">
                      <span>{profileCompletionPercentage}% Complete</span>
                      <span className="text-muted-foreground">{profileCompletionPercentage < 100 ? 'Incomplete' : 'Complete'}</span>
                    </div>
                    <Progress value={profileCompletionPercentage} className="h-2" />
                    {profileCompletionPercentage < 100 && (
                      <Button variant="ghost" size="sm" className="w-full mt-2 text-primary" onClick={handleCompleteProfileClick}>
                        Complete Profile
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Upcoming Activities Card */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Upcoming Activities</CardTitle>
                </CardHeader>
                <CardContent>
                  {upcomingActivities.length > 0 ? (
                    <div className="space-y-3">
                      {upcomingActivities.map((activity) => (
                        <div key={activity.id} className="flex items-center gap-3">
                          <div className="w-10 h-10 rounded-full bg-primary/10 flex items-center justify-center">
                            <Calendar className="h-5 w-5 text-primary" />
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{activity.title}</p>
                            <p className="text-xs text-muted-foreground">
                              {activity.start_time ? new Date(activity.start_time).toLocaleDateString(undefined, { weekday: 'short', month: 'short', day: 'numeric' }) : 'Date TBD'}
                            </p>
                          </div>
                          <Badge variant="outline" className="whitespace-nowrap">
                            {activity.is_paid ? `$${activity.price}` : 'Free'}
                          </Badge>
                        </div>
                      ))}
                      <Button variant="ghost" size="sm" className="w-full mt-2 text-primary" asChild>
                        <Link to="/meetup">
                          View All
                          <ChevronRight className="h-4 w-4 ml-1" />
                        </Link>
                      </Button>
                    </div>
                  ) : (
                    <div className="text-center py-3">
                      <p className="text-sm text-muted-foreground">No upcoming activities</p>
                      <Button variant="ghost" size="sm" className="mt-2 text-primary" asChild>
                        <Link to="/activity">Find Activities</Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Notifications Card */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-lg">Recent Notifications</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      <div className="w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center">
                        <Bell className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm font-medium">Welcome to BuddySurf!</p>
                        <p className="text-xs text-muted-foreground">Complete your profile to get started</p>
                      </div>
                    </div>
                    <Button variant="ghost" size="sm" className="w-full mt-2 text-primary" asChild>
                      <Link to="/notifications">
                        View All
                        <ChevronRight className="h-4 w-4 ml-1" />
                      </Link>
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* People Suggestions Section */}
            <div className="mt-8">
              <UserSuggestions limit={4} />
            </div>
          </motion.div>
        )}

        {/* Features section */}
        <div className="py-12">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-3">Explore BuddySurf</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Discover all the ways you can connect, explore, and engage with the community
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <FeatureCard
              icon={<Compass className="h-8 w-8" />}
              title="MeetMap"
              description="Discover people and activities near you on our interactive 3D map."
              link="/meetmap"
              color="bg-blue-500"
            />
            <FeatureCard
              icon={<Activity className="h-8 w-8" />}
              title="Activities"
              description="Find and join local events, meetups and activities."
              link="/activity"
              color="bg-green-500"
            />
            <FeatureCard
              icon={<Briefcase className="h-8 w-8" />}
              title="Hire"
              description="Find and hire local service providers for your needs."
              link="/hire"
              color="bg-purple-500"
            />
            <FeatureCard
              icon={<MessageCircle className="h-8 w-8" />}
              title="Chat"
              description="Connect and message with new people who share your interests."
              link="/chat"
              color="bg-orange-500"
            />
          </div>
        </div>

        {/* Pricing Section */}
        <div className="py-12 border-t border-border">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-3">Choose Your Plan</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              Select the subscription that fits your needs and unlock premium features
            </p>
          </div>

          {/* Current Plan Card - Only shown for logged in users */}
          {user && (
            <motion.div
              className="mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 }}
            >
              <Card className="mb-8 overflow-hidden border-none shadow-lg">
                <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-0.5">
                  <div className="bg-white p-6">
                    <div className="flex flex-col md:flex-row justify-between items-center gap-6">
                      <div className="flex items-start gap-4">
                        <div className="bg-gray-100 p-3 rounded-full">
                          <Waves className="h-8 w-8 text-gray-500" />
                        </div>
                        <div>
                          <h2 className="text-xl font-bold mb-1">Your Current Plan: Free Tier</h2>
                          <p className="text-muted-foreground mb-3">
                            You're currently on the free plan with limited features
                          </p>
                          <div className="flex flex-wrap gap-2 mb-2">
                            <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">Basic map access</span>
                            <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">1 activity/week</span>
                            <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">Text-only chat</span>
                          </div>
                        </div>
                      </div>
                      <SubscriptionButton
                        className="bg-primary-purple hover:bg-primary-deep-purple"
                        size="lg"
                        showBadge={true}
                        badgeText="Upgrade"
                      >
                        <Sparkles className="h-5 w-5 mr-2" />
                        Unlock Premium
                      </SubscriptionButton>
                    </div>
                  </div>
                </div>
              </Card>
            </motion.div>
          )}

          <div className="grid md:grid-cols-3 gap-6 mb-6">
            {/* Free Plan */}
            <div className="rounded-xl border border-gray-200 overflow-hidden bg-white hover:shadow-md transition-all duration-300">
              <div className="bg-gradient-to-br from-gray-50 to-gray-100 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-gray-700">Free Tier</h3>
                  <span className="bg-gray-200 text-gray-700 text-xs px-2 py-1 rounded-full font-medium">
                    Basic
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="mb-4">
                  <p className="text-3xl font-bold">$0<span className="text-sm font-normal text-muted-foreground">/forever</span></p>
                  <p className="text-sm text-muted-foreground mt-1">Basic features to get started</p>
                </div>

                <ul className="space-y-2 mb-6">
                  <li className="flex items-start">
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">Basic map access</span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">Limited activity creation (1 per week)</span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">Standard chat (text-only)</span>
                  </li>
                </ul>

                <Button variant="outline" className="w-full" asChild>
                  <Link to="/subscription">Current Plan</Link>
                </Button>
              </div>
            </div>

            {/* Monthly Plan */}
            <div className="rounded-xl border-2 border-primary-purple overflow-hidden bg-white shadow-lg relative -mt-4 z-10">
              <div className="absolute top-0 right-0 w-24 h-24">
                <div className="absolute transform rotate-45 bg-primary-purple text-white font-semibold text-xs py-1 right-[-35px] top-[20px] w-[140px] text-center">
                  POPULAR
                </div>
              </div>
              <div className="bg-gradient-to-br from-purple-50 to-purple-100 px-6 py-6">
                <h3 className="text-2xl font-bold text-purple-700">Monthly Pro</h3>
              </div>
              <div className="p-6">
                <div className="mb-4">
                  <p className="text-4xl font-bold">$15<span className="text-sm font-normal text-muted-foreground">/month</span></p>
                  <p className="text-sm text-muted-foreground mt-1">Perfect for active social users</p>
                </div>

                <ul className="space-y-2 mb-6">
                  <li className="flex items-start">
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">Unlimited activity creation</span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">Priority in activity queues</span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">Advanced filters</span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">Premium badge on profile</span>
                  </li>
                </ul>

                <SubscriptionButton
                  className="w-full bg-primary-purple hover:bg-primary-deep-purple"
                  size="lg"
                >
                  <Star className="h-4 w-4 mr-2" />
                  Get Monthly Pro
                </SubscriptionButton>
              </div>
            </div>

            {/* Lifetime Plan */}
            <div className="rounded-xl border border-amber-200 overflow-hidden bg-white hover:shadow-md transition-all duration-300">
              <div className="bg-gradient-to-br from-amber-50 to-amber-100 px-6 py-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-xl font-bold text-amber-700">Lifetime Surf</h3>
                  <span className="bg-green-100 text-green-600 text-xs px-2 py-1 rounded-full font-medium">
                    Save 80% vs monthly
                  </span>
                </div>
              </div>
              <div className="p-6">
                <div className="mb-4">
                  <p className="text-3xl font-bold">$35<span className="text-sm font-normal text-muted-foreground"> one-time</span></p>
                  <p className="text-sm text-muted-foreground mt-1">Best value for power users</p>
                </div>

                <ul className="space-y-2 mb-6">
                  <li className="flex items-start">
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">All Monthly Pro features</span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">Permanent ad-free experience</span>
                  </li>
                  <li className="flex items-start">
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">Verified badge</span>
                  </li>
                </ul>

                <SubscriptionButton
                  variant="outline"
                  className="w-full border-amber-300 text-amber-700 hover:bg-amber-50"
                >
                  Get Lifetime Access
                </SubscriptionButton>
              </div>
            </div>
          </div>

          <div className="text-center">
            <Button variant="outline" size="lg" asChild>
              <Link to="/subscription">
                View All Plan Details
              </Link>
            </Button>
          </div>
        </div>

        {/* Community Highlights */}
        <div className="py-12 border-t border-border">
          <h2 className="text-3xl font-bold text-center mb-12">Community Highlights</h2>

          <div className="grid md:grid-cols-3 gap-8">
            <CommunityCard
              icon={<Users className="h-6 w-6" />}
              title="Active Community"
              description="Join thousands of active users connecting daily in your area."
              stat="5,000+"
              statLabel="Active Users"
            />
            <CommunityCard
              icon={<Calendar className="h-6 w-6" />}
              title="Weekly Activities"
              description="Hundreds of activities created every week across different categories."
              stat="200+"
              statLabel="Weekly Activities"
            />
            <CommunityCard
              icon={<Star className="h-6 w-6" />}
              title="Verified Hosts"
              description="Activities hosted by verified community members you can trust."
              stat="500+"
              statLabel="Verified Hosts"
            />
          </div>
        </div>

        {/* People You Might Like - For non-logged in users */}
        {!user && !isLoading && (
          <div className="py-12 border-t border-border">
            <h2 className="text-3xl font-bold text-center mb-4">Meet Our Community</h2>
            <p className="text-center text-muted-foreground max-w-2xl mx-auto mb-8">
              Connect with amazing people who share your interests. Sign up to follow and message them!
            </p>
            <UserSuggestions limit={6} />
            <div className="mt-8 text-center">
              <Button
                onClick={() => setIsSignupOpen(true)}
                size="lg"
                className="bg-primary-purple hover:bg-primary-deep-purple"
              >
                <UserPlus className="h-5 w-5 mr-2" />
                Join Now to Connect
              </Button>
            </div>
          </div>
        )}

        {/* Animated text banner */}
        <div className="py-8 overflow-hidden border-y border-border my-12">
          <div className="flex animate-marquee whitespace-nowrap">
            <span className="text-4xl font-bold mx-4 text-primary-purple">EXPLORE</span>
            <span className="text-4xl mx-4">•</span>
            <span className="text-4xl font-bold mx-4 text-primary">CONNECT</span>
            <span className="text-4xl mx-4">•</span>
            <span className="text-4xl font-bold mx-4 text-secondary">EXPERIENCE</span>
            <span className="text-4xl mx-4">•</span>
            <span className="text-4xl font-bold mx-4 text-primary-purple">EXPLORE</span>
            <span className="text-4xl mx-4">•</span>
            <span className="text-4xl font-bold mx-4 text-primary">CONNECT</span>
            <span className="text-4xl mx-4">•</span>
            <span className="text-4xl font-bold mx-4 text-secondary">EXPERIENCE</span>
          </div>
        </div>

        {/* CTA section */}
        <div className="py-16 bg-gradient-to-r from-primary/10 to-secondary/10 rounded-2xl p-8 text-center mb-12">
          <h2 className="text-3xl font-bold mb-6">Ready to get started?</h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join thousands of people who are already using BuddySurf to connect, meet, and build meaningful relationships.
          </p>

          {!user && !isLoading ? (
            <Button onClick={() => setIsSignupOpen(true)} size="lg" className="bg-primary hover:bg-primary/90">
              Sign Up Now
            </Button>
          ) : (
            <div className="flex flex-wrap gap-4 justify-center">
              <Button size="lg" className="bg-primary hover:bg-primary/90" asChild>
                <Link to="/meetmap">Start Exploring</Link>
              </Button>
              <SubscriptionButton
                size="lg"
                className="bg-primary-purple hover:bg-primary-deep-purple"
              >
                View Subscription Plans
              </SubscriptionButton>
            </div>
          )}
        </div>
      </div>

      <SignupDialog open={isSignupOpen} setOpen={setIsSignupOpen} setLoginOpen={setIsLoginOpen} />
      <LoginDialog open={isLoginOpen} setOpen={setIsLoginOpen} />
      {user && <OnboardingModal open={showOnboarding} onOpenChange={setShowOnboarding} user={user} profile={profile} />}
    </MainLayout>
  );
}

interface FeatureCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  link: string;
  color?: string;
}

function FeatureCard({
  icon,
  title,
  description,
  link,
  color = "bg-primary"
}: FeatureCardProps) {
  return (
    <Link
      to={link}
      className="group flex flex-col items-center text-center p-6 rounded-xl bg-white shadow-sm border border-border hover:shadow-md hover:-translate-y-1 transition-all duration-300"
    >
      <div className={`mb-4 p-4 rounded-full ${color} bg-opacity-10 group-hover:bg-opacity-20 transition-colors`}>
        <div className={`text-${color.replace('bg-', '')}`}>{icon}</div>
      </div>
      <h3 className="text-xl font-semibold mb-2">{title}</h3>
      <p className="text-muted-foreground">{description}</p>
      <div className="mt-4 text-primary font-medium text-sm flex items-center opacity-0 group-hover:opacity-100 transition-opacity">
        Explore <ChevronRight className="h-4 w-4 ml-1" />
      </div>
    </Link>
  );
}

interface CommunityCardProps {
  icon: React.ReactNode;
  title: string;
  description: string;
  stat: string;
  statLabel: string;
}

function CommunityCard({
  icon,
  title,
  description,
  stat,
  statLabel
}: CommunityCardProps) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-2">
          <div className="p-2 rounded-full bg-primary/10">
            {icon}
          </div>
          <CardTitle>{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent>
        <p className="text-muted-foreground mb-4">{description}</p>
        <div className="flex flex-col items-center bg-muted/20 p-4 rounded-lg">
          <span className="text-3xl font-bold text-primary">{stat}</span>
          <span className="text-sm text-muted-foreground">{statLabel}</span>
        </div>
      </CardContent>
    </Card>
  );
}
