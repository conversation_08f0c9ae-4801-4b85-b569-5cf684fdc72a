
import React from 'react';
import { Activity } from '@/types/activity';
import { Badge } from '@/components/ui/badge';
import { Calendar, MapPin, DollarSign, Users } from 'lucide-react';
import { cn } from '@/lib/utils';
import { format, isPast, isToday } from 'date-fns';
import { UserAvatar } from '@/components/user/UserAvatar';

interface ActivityListItemProps {
  activity: Activity;
  onClick?: () => void;
  className?: string;
  compact?: boolean;
}

export function ActivityListItem({ 
  activity, 
  onClick, 
  className,
  compact = false 
}: ActivityListItemProps) {
  const isExpired = activity.end_time ? isPast(new Date(activity.end_time)) : false;
  const isTodayActivity = activity.start_time ? isToday(new Date(activity.start_time)) : false;
  
  // Get appropriate gradient based on activity type
  const getGradient = () => {
    const categoryName = activity.category?.name.toLowerCase();
    if (categoryName === 'sports' || categoryName === 'fitness') 
      return 'hover:border-blue-200/50 hover:bg-blue-50/10';
    if (categoryName === 'music') 
      return 'hover:border-purple-200/50 hover:bg-purple-50/10';
    if (categoryName === 'social' || categoryName === 'dating') 
      return 'hover:border-pink-200/50 hover:bg-pink-50/10';
    if (categoryName === 'outdoors') 
      return 'hover:border-green-200/50 hover:bg-green-50/10';
    if (categoryName === 'dining') 
      return 'hover:border-amber-200/50 hover:bg-amber-50/10';
    return 'hover:border-primary/20 hover:bg-primary/5'; // default
  };
  
  return (
    <div 
      className={cn(
        "bg-card border rounded-lg shadow-sm overflow-hidden transition-all hover:shadow-md cursor-pointer",
        getGradient(),
        isExpired && "opacity-60",
        className
      )}
      onClick={onClick}
    >
      <div className="p-3 flex items-center gap-3">
        <div className="h-10 w-10 rounded-md bg-primary/10 backdrop-blur-sm flex items-center justify-center">
          <Calendar className="h-5 w-5 text-primary" />
        </div>
        
        <div className="flex-1 min-w-0">
          <div className="flex items-center justify-between gap-2">
            <h4 className="font-medium text-sm truncate">
              {activity.title}
            </h4>
            
            {activity.host && (
              <UserAvatar user={activity.host} size="xs" showVerified={false} />
            )}
          </div>
          
          <div className="flex items-center mt-0.5 gap-3 text-muted-foreground text-xs">
            {activity.start_time && (
              <div className="flex items-center">
                <Calendar className="h-3 w-3 mr-1 text-primary" />
                <span className={isTodayActivity ? "text-primary font-medium" : ""}>
                  {isTodayActivity ? 'Today' : format(new Date(activity.start_time), 'MMM d')}
                </span>
              </div>
            )}
            
            {!compact && activity.address && (
              <div className="flex items-center truncate">
                <MapPin className="h-3 w-3 mr-1 flex-shrink-0 text-rose-500" />
                <span className="truncate">{activity.address}</span>
              </div>
            )}
          </div>
          
          {!compact && (
            <div className="flex mt-1 gap-1">
              <Badge 
                variant={activity.is_paid ? 'default' : 'outline'} 
                className={`text-[10px] px-1 ${activity.is_paid ? 'bg-amber-100/90 text-amber-700 border-amber-200/30' : 'bg-green-50/90 text-green-700 border-green-200/30'}`}
              >
                {activity.is_paid ? (
                  <><DollarSign className="h-2.5 w-2.5 mr-0.5" />{activity.price}</>
                ) : (
                  'Free'
                )}
              </Badge>
              
              {activity.max_participants && (
                <Badge variant="outline" className="text-[10px] px-1 flex items-center bg-background/50 backdrop-blur-sm">
                  <Users className="h-2.5 w-2.5 mr-0.5 text-primary" />
                  {activity.max_participants}
                </Badge>
              )}
              
              {activity.category && (
                <Badge variant="secondary" className="text-[10px] px-1 bg-background/50 backdrop-blur-sm">
                  {activity.category.name}
                </Badge>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
