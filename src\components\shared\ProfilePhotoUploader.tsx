import React, { useRef, useState } from 'react';
import { Camera, ImagePlus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';

interface ProfilePhotoUploaderProps {
  previewUrl: string | null;
  onPhotoChange: (file: File, dataUrl: string) => void;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
  required?: boolean;
  id?: string; // Add ID prop for accessibility
  name?: string; // Add name prop for form association
  label?: string; // Add label prop for accessibility
}

export function ProfilePhotoUploader({
  previewUrl,
  onPhotoChange,
  size = 'md',
  className = '',
  required = false,
  id,
  name,
  label
}: ProfilePhotoUploaderProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { toast } = useToast();
  const [isHovering, setIsHovering] = useState(false);

  // Generate unique IDs for accessibility
  const uniqueId = id || `profile-photo-${Math.random().toString(36).substring(2, 9)}`;
  const inputId = `${uniqueId}-input`;
  const buttonId = `${uniqueId}-button`;
  // Use the name prop or fallback to the ID
  const fieldName = name || uniqueId;

  // Determine size dimensions
  const sizeClasses = {
    sm: 'w-24 h-24',
    md: 'w-32 h-32',
    lg: 'w-40 h-40'
  };

  const handleThumbnailClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];

      // Validate file size (max 5MB)
      const maxSizeInBytes = 5 * 1024 * 1024; // 5MB
      if (file.size > maxSizeInBytes) {
        toast({
          title: "File too large",
          description: "Please select an image smaller than 5MB",
          variant: "destructive"
        });
        return;
      }

      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: "Invalid file type",
          description: "Please select an image file",
          variant: "destructive"
        });
        return;
      }

      console.log(`ProfilePhotoUploader - Processing file: ${file.name}, size: ${file.size} bytes, type: ${file.type}`);

      // Read the file as a data URL
      const reader = new FileReader();

      reader.onloadend = () => {
        try {
          const dataUrl = reader.result as string;
          console.log(`ProfilePhotoUploader - File read as data URL (length: ${dataUrl.length})`);

          // Validate data URL
          if (!dataUrl || !dataUrl.startsWith('data:image/')) {
            throw new Error('Invalid data URL format');
          }

          // Call the callback with both the file and the data URL
          onPhotoChange(file, dataUrl);

          toast({
            title: "Photo selected!",
            description: "Your profile picture looks great!"
          });
        } catch (error) {
          console.error('Error processing image file:', error);
          toast({
            title: "Error processing image",
            description: "Please try another image file",
            variant: "destructive"
          });
        }
      };

      reader.onerror = () => {
        console.error('Error reading file as data URL');
        toast({
          title: "Error reading file",
          description: "Please try another image file",
          variant: "destructive"
        });
      };

      reader.readAsDataURL(file);
    }
  };

  return (
    <div className={`flex flex-col items-center ${className}`} id={uniqueId}>
      {label && (
        <label htmlFor={inputId} className="sr-only">{label}</label>
      )}
      <div
        className={`relative ${sizeClasses[size]} rounded-full overflow-hidden border-2 border-dashed border-primary-purple/40 cursor-pointer transition-all duration-300 shadow-lg hover:shadow-xl hover:border-primary-purple/70 bg-white`}
        onClick={handleThumbnailClick}
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
        role="button"
        aria-label={previewUrl ? "Change profile photo" : "Select profile photo"}
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleThumbnailClick();
          }
        }}
      >
        {previewUrl ? (
          <div className="w-full h-full">
            <img
              src={previewUrl}
              alt="Profile"
              className="w-full h-full object-cover"
              onError={(e) => {
                console.error('Error loading profile image');
                // Set a fallback image or hide the broken image
                e.currentTarget.style.display = 'none';
                // Show error toast
                toast({
                  title: "Error loading image",
                  description: "Please try uploading your photo again",
                  variant: "destructive"
                });
                // Reset the file input to allow selecting a new file
                if (fileInputRef.current) {
                  fileInputRef.current.value = '';
                }
              }}
            />
            <div className={`absolute inset-0 bg-gradient-to-t from-black/40 to-transparent transition-opacity duration-300 flex items-center justify-center ${isHovering ? 'opacity-100' : 'opacity-0'}`}>
              <div className="bg-white/90 rounded-full p-3 shadow-md">
                <Camera className="h-5 w-5 text-primary-purple" />
              </div>
            </div>
          </div>
        ) : (
          <div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-purple-50 to-purple-100/50 p-4">
            <div className="bg-white rounded-full p-3 shadow-md mb-3">
              <ImagePlus className="h-6 w-6 text-primary-purple" />
            </div>
            <span className="text-xs text-primary-purple font-medium text-center">Add Photo</span>
            {required && <span className="text-xs text-red-500 mt-1">Required *</span>}
          </div>
        )}
      </div>
      <input
        ref={fileInputRef}
        id={inputId}
        name={fieldName}
        type="file"
        accept="image/*"
        onChange={handleFileChange}
        className="hidden"
        aria-required={required}
        aria-label={label || "Profile photo"}
      />
      <Button
        id={buttonId}
        type="button"
        variant={previewUrl ? "outline" : "default"}
        size="sm"
        onClick={handleThumbnailClick}
        className={`mt-3 ${previewUrl
          ? 'bg-white border-primary-purple/30 text-primary-purple hover:bg-primary-purple/5'
          : 'bg-gradient-to-r from-primary-purple to-primary-deep-purple hover:shadow-md text-white'}`}
        aria-controls={inputId}
        aria-haspopup="dialog"
      >
        <Camera className="h-4 w-4 mr-2" />
        {previewUrl ? "Change Photo" : "Select Photo"}
      </Button>
      {previewUrl && (
        <p className="text-xs text-green-600 mt-2 flex items-center">
          <span className="bg-green-100 rounded-full p-0.5 mr-1">✓</span> Photo selected
        </p>
      )}
    </div>
  );
}
