# Enable Leaked Password Protection in Supabase

To enable the leaked password protection feature in Supabase Auth:

## Option 1: Using the Supabase Dashboard

1. Log in to your Supabase dashboard
2. Navigate to Authentication > Settings
3. Scroll down to the "Security" section
4. Find the "Leaked Password Protection" setting
5. Toggle it to "Enabled"
6. Save your changes

## Option 2: Using the Supabase Management API

You can also enable this feature programmatically using the Supabase Management API:

```javascript
const SUPABASE_ACCESS_TOKEN = 'your-access-token';
const PROJECT_REF = 'your-project-ref';

fetch(`https://api.supabase.io/v1/projects/${PROJECT_REF}/auth/config`, {
  method: 'PATCH',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${SUPABASE_ACCESS_TOKEN}`
  },
  body: JSON.stringify({
    security: {
      enable_leaked_password_protection: true
    }
  })
})
.then(response => response.json())
.then(data => console.log(data))
.catch(error => console.error('Error:', error));
```

## Benefits of Enabling This Feature

- Prevents users from using passwords that have been exposed in data breaches
- Enhances overall security of your application
- Protects users from credential stuffing attacks
- Follows security best practices recommended by NIST
