
import React, { useState, useEffect, useRef, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Send, Paperclip, MapPin, Loader2, CheckCircle2, UserPlus, MoreVertical, Smile, Calendar, Share2 } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { useChatMessages } from "@/hooks/use-chat-messages";
import { useSendMessage } from "@/hooks/use-send-message";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from 'date-fns';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { useToast } from "@/hooks/use-toast";
import { ChatProposal } from './ChatProposal';
import { ChatMessages } from './ChatMessages';
import { useParticipants } from '@/hooks/use-participants';
import { UserAvatar } from '../user/UserAvatar';
import { EmojiPicker } from './EmojiPicker';
import { useSendActivityProposal, useSendActivityShare } from '@/hooks/use-activity-proposals';
import { supabase } from '@/integrations/supabase/client';

interface ChatContainerProps {
  conversationId: string;
  recipientId?: string;
  className?: string;
  onParticipantsUpdate?: (participants: any[]) => void;
  isGroupChat?: boolean;
  activityId?: string;
}

interface Message {
  id: string;
  content: string;
  sender_id: string;
  created_at: string;
  message_type: string;
  media_url?: string;
  location_data?: any;
  sender?: {
    id: string;
    display_name?: string;
    avatar_url?: string;
    username?: string;
  };
}

export function ChatContainer({
  conversationId,
  recipientId,
  className,
  onParticipantsUpdate,
  isGroupChat = false,
  activityId
}: ChatContainerProps) {
  const [input, setInput] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
  const { user } = useAuth();
  const { toast } = useToast();
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { data: messages, isLoading } = useChatMessages(conversationId);
  const sendMessage = useSendMessage();
  const { data: participants, isLoading: isLoadingParticipants } = useParticipants(conversationId);

  useEffect(() => {
    if (onParticipantsUpdate && participants) {
      onParticipantsUpdate(participants);
    }
  }, [participants, onParticipantsUpdate]);

  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages, scrollToBottom]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  const handleSendMessage = async (content: string, messageType: string = 'text', mediaUrl?: string, locationData?: any) => {
    if (!content.trim() && !mediaUrl && !locationData) return;

    const messageData = {
      conversation_id: conversationId,
      content: content || '',
      message_type: messageType,
      media_url: mediaUrl,
      location_data: locationData,
      recipient_id: recipientId
    };

    try {
      await sendMessage.mutateAsync(messageData);
      setTimeout(() => {
        if (messagesEndRef.current) {
          messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
        }
      }, 100);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    handleSendMessage(input);
    setInput('');
  };

  const formatTimeAgo = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid date";
    }
  };

  const handleFileSelect = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files && e.target.files[0];
    if (!file) return;

    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select a file smaller than 5MB.",
        variant: "destructive"
      });
      return;
    }

    setSelectedFile(file);

    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('conversation_id', conversationId);

      const { data, error } = await supabase
        .storage
        .from('chat-media')
        .upload(`${conversationId}/${Date.now()}-${file.name}`, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (error) {
        console.error('Error uploading file:', error);
        toast({
          title: "Upload failed",
          description: "There was an error uploading your file.",
          variant: "destructive"
        });
        return;
      }

      const { data: { publicUrl } } = supabase.storage.from('chat-media').getPublicUrl(data.path);
      await handleSendMessage('', 'media', publicUrl);
      setSelectedFile(null);
    } catch (err) {
      console.error('Error uploading file:', err);
      toast({
        title: "Upload failed",
        description: "There was an error uploading your file.",
        variant: "destructive"
      });
    }
  };

  const handleLocationShare = async () => {
    try {
      const position = await getCurrentLocation();
      const locationData = {
        latitude: position.coords.latitude,
        longitude: position.coords.longitude,
        timestamp: new Date().toISOString(),
        accuracy: position.coords.accuracy
      };

      await handleSendMessage('📍 Location shared', 'location', undefined, locationData);
    } catch (error) {
      console.error('Error sharing location:', error);
      toast({
        title: "Location Error",
        description: "Could not access your location. Please enable location services.",
        variant: "destructive"
      });
    }
  };

  const getCurrentLocation = (): Promise<GeolocationPosition> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        reject(new Error("Geolocation is not supported by this browser."));
      }

      navigator.geolocation.getCurrentPosition(
        (position) => {
          resolve(position);
        },
        (error) => {
          reject(error);
        }
      );
    });
  };

  const toggleEmojiPicker = () => {
    setIsEmojiPickerOpen(!isEmojiPickerOpen);
  };

  const addEmoji = (emoji: string) => {
    setInput(prevInput => prevInput + emoji);
  };

  return (
    <div className={cn("flex flex-col h-full", className)}>
      {/* Chat Header */}
      <div className="border-b py-2 px-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold">
          {isGroupChat ? 'Group Chat' : 'Direct Message'}
        </h3>
        <div className="flex items-center space-x-2">
          {isGroupChat && (
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="h-8 w-8 p-0">
                  <span className="sr-only">Open menu</span>
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuItem>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Participants
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          )}
        </div>
      </div>

      {/* Chat Messages */}
      <ChatMessages
        messages={messages || []}
        isLoading={false}
        proposals={[]}
        conversationId={conversationId}
      />

      {/* Chat Input */}
      <form onSubmit={handleSubmit} className="border-t py-2 px-4">
        <div className="relative flex items-center">
          <Button
            type="button"
            variant="ghost"
            className="h-9 w-9 p-0 mr-2"
            onClick={toggleEmojiPicker}
          >
            <Smile className="h-4 w-4" />
          </Button>
          {isEmojiPickerOpen && (
            <div className="absolute bottom-12 left-0 shadow-md z-10">
              <EmojiPicker onEmojiClick={addEmoji} onClose={() => setIsEmojiPickerOpen(false)} />
            </div>
          )}
          <Input
            type="text"
            placeholder="Type your message..."
            value={input}
            onChange={handleInputChange}
            className="rounded-full py-2 pr-12"
          />
          <div className="absolute right-1 top-1/2 transform -translate-y-1/2 space-x-1.5">
            <Button
              type="button"
              variant="ghost"
              className="h-9 w-9 p-0"
              onClick={handleFileSelect}
            >
              <Paperclip className="h-4 w-4" />
            </Button>
            <Button
              type="submit"
              variant="ghost"
              className="h-9 w-9 p-0"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          <input
            type="file"
            accept="image/*, video/*"
            className="hidden"
            ref={fileInputRef}
            onChange={handleFileChange}
          />
        </div>
        <div className="flex items-center justify-between mt-2">
          <div className="flex gap-2">
            <Button
              type="button"
              variant="secondary"
              size="sm"
              onClick={handleLocationShare}
            >
              <MapPin className="h-4 w-4 mr-2" />
              Share Location
            </Button>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  type="button"
                  variant="secondary"
                  size="sm"
                >
                  <Calendar className="h-4 w-4 mr-2" />
                  Activity
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="start">
                <DropdownMenuItem onClick={() => {/* TODO: Open activity proposal modal */}}>
                  <Calendar className="h-4 w-4 mr-2" />
                  Propose Activity
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => {/* TODO: Open activity share modal */}}>
                  <Share2 className="h-4 w-4 mr-2" />
                  Share Activity
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <span className="text-xs text-gray-500">
            {selectedFile ? `Selected: ${selectedFile.name}` : 'No file selected'}
          </span>
        </div>
      </form>
    </div>
  );
}
