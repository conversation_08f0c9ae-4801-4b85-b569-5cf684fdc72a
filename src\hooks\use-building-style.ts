import { useCallback } from 'react';
import mapboxgl from 'mapbox-gl';

export function useBuildingStyle() {
  const applyBuildingStyle = useCallback((
    map: mapboxgl.Map, 
    options: {
      minZoom?: number;
      maxOpacity?: number;
      baseColor?: string;
      highlightColor?: string;
      edgeColor?: string;
      ambientIntensity?: number;
      transitionDuration?: number;
      roundedCorners?: boolean;
      shadowOpacity?: number;
    } = {}
  ) => {
    if (!map || !map.isStyleLoaded()) return;

    const {
      minZoom = 12,
      maxOpacity = 0.95,
      baseColor = '#F5F5F0', // Off-white/ivory base
      highlightColor = '#E0E0E0', // Soft gray for shadows
      edgeColor = '#DEDEDE', // Light gray stroke
      ambientIntensity = 0.35,
      roundedCorners = true,
      shadowOpacity = 0.2
    } = options;

    // Remove existing layers if present
    ['3d-buildings', 'building-shadows', 'building-patterns'].forEach(layerId => {
      if (map.getLayer(layerId)) {
        map.removeLayer(layerId);
      }
    });

    // Find symbol layer for placement
    const layers = map.getStyle().layers;
    let firstSymbolId;
    for (const layer of layers!) {
      if (layer.type === 'symbol') {
        firstSymbolId = layer.id;
        break;
      }
    }

    // Enhanced 3D buildings with improved LOD transitions
    map.addLayer({
      'id': '3d-buildings',
      'source': 'composite',
      'source-layer': 'building',
      'filter': ['==', 'extrude', 'true'],
      'type': 'fill-extrusion',
      'minzoom': minZoom - 0.5,
      'paint': {
        'fill-extrusion-color': [
          'interpolate',
          ['linear'],
          ['get', 'height'],
          0, baseColor,
          25, baseColor,
          50, ['mix', baseColor, highlightColor, 0.3],
          100, ['mix', baseColor, highlightColor, 0.6],
          200, highlightColor
        ],
        'fill-extrusion-height': [
          'interpolate', 
          ['exponential', 1.5],
          ['zoom'],
          minZoom, 0,
          minZoom + 0.5, ['*', ['get', 'height'], 0.1],
          minZoom + 1, ['*', ['get', 'height'], 0.3],
          minZoom + 1.5, ['*', ['get', 'height'], 0.5],
          minZoom + 2, ['*', ['get', 'height'], 0.7],
          minZoom + 2.5, ['*', ['get', 'height'], 0.8],
          minZoom + 3, ['*', ['get', 'height'], 0.9],
          minZoom + 3.5, ['*', ['get', 'height'], 0.95],
          minZoom + 4, ['get', 'height']
        ],
        'fill-extrusion-base': [
          'interpolate', 
          ['exponential', 1.5],
          ['zoom'],
          minZoom, 0,
          minZoom + 0.5, ['*', ['get', 'min_height'], 0.1],
          minZoom + 1, ['*', ['get', 'min_height'], 0.3],
          minZoom + 1.5, ['*', ['get', 'min_height'], 0.5],
          minZoom + 2, ['*', ['get', 'min_height'], 0.7],
          minZoom + 2.5, ['*', ['get', 'min_height'], 0.8],
          minZoom + 3, ['*', ['get', 'min_height'], 0.9],
          minZoom + 3.5, ['*', ['get', 'min_height'], 0.95],
          minZoom + 4, ['get', 'min_height']
        ],
        'fill-extrusion-opacity': [
          'interpolate', 
          ['exponential', 1.2],
          ['zoom'],
          minZoom, 0,
          minZoom + 0.5, 0.1,
          minZoom + 1, 0.2,
          minZoom + 1.5, 0.3,
          minZoom + 2, 0.4,
          minZoom + 2.5, 0.5,
          minZoom + 3, 0.6,
          minZoom + 3.5, 0.7,
          minZoom + 4, maxOpacity
        ],
        'fill-extrusion-vertical-gradient': true,
        'fill-extrusion-ambient-occlusion-intensity': [
          'interpolate',
          ['linear'],
          ['zoom'],
          minZoom, 0.1,
          minZoom + 2, ambientIntensity,
          minZoom + 4, ambientIntensity * 1.2
        ],
        'fill-extrusion-ambient-occlusion-radius': [
          'interpolate',
          ['linear'],
          ['zoom'],
          14, 2,
          15.5, 3,
          17, 4
        ]
      }
    }, firstSymbolId);

    // Enhanced shadow layer with better performance
    if (shadowOpacity > 0) {
      map.addLayer({
        'id': 'building-shadows',
        'type': 'fill-extrusion',
        'source': 'composite',
        'source-layer': 'building',
        'filter': ['==', 'extrude', 'true'],
        'minzoom': minZoom + 2,
        'paint': {
          'fill-extrusion-color': '#000000',
          'fill-extrusion-height': ['get', 'height'],
          'fill-extrusion-base': ['get', 'min_height'],
          'fill-extrusion-opacity': shadowOpacity,
          'fill-extrusion-translate': [3, 3],
          'fill-extrusion-translate-anchor': 'viewport',
          'fill-extrusion-vertical-gradient': true
        }
      }, '3d-buildings');
    }

    // Add enhanced vegetation layer
    map.addLayer({
      'id': 'enhanced-vegetation',
      'type': 'fill',
      'source': 'composite',
      'source-layer': 'landuse',
      'filter': ['in', 'class', 'park', 'wood', 'grass', 'forest', 'garden'],
      'paint': {
        'fill-color': '#E8F5E9', // Light mint green
        'fill-opacity': 0.85
      }
    }, 'building-shadows');

    // Add vegetation accents
    map.addLayer({
      'id': 'vegetation-accents',
      'type': 'fill',
      'source': 'composite',
      'source-layer': 'landuse',
      'filter': ['==', 'class', 'park'],
      'paint': {
        'fill-color': '#C8E6C9', // Darker green accents
        'fill-opacity': 0.85
      }
    }, 'enhanced-vegetation');

    return () => {
      if (!map.loaded()) return;
      ['3d-buildings', 'building-shadows', 'enhanced-vegetation', 'vegetation-accents'].forEach(layerId => {
        if (map.getLayer(layerId)) {
          map.removeLayer(layerId);
        }
      });
    };
  }, []);

  const applyBuildingPatterns = useCallback((
    map: mapboxgl.Map,
    options: {
      pattern?: string;
      minZoom?: number;
      opacity?: number;
      baseColor?: string;
      accentColor?: string;
    } = {}
  ) => {
    if (!map || !map.isStyleLoaded()) return;

    const {
      pattern = 'concrete',
      minZoom = 15,
      opacity = 0.15,
      baseColor = '#E8F5E9', // Light mint green base
      accentColor = '#C8E6C9' // Darker green accents
    } = options;

    // Remove existing pattern layer if it exists
    if (map.getLayer('building-patterns')) {
      map.removeLayer('building-patterns');
    }
    
    // Add ground vegetation layer first
    try {
      if (!map.getLayer('ground-vegetation')) {
        map.addLayer({
          'id': 'ground-vegetation',
          'type': 'fill',
          'source': 'composite',
          'source-layer': 'landuse',
          'filter': [
            'in', 
            'class', 
            'park', 'wood', 'grass', 'forest', 'garden'
          ],
          'paint': {
            'fill-color': baseColor,
            'fill-opacity': 0.85 // 85% opacity
          }
        }, 'building-patterns');
      }
    } catch (error) {
      console.log('Could not add ground vegetation layer', error);
    }

    // Add subtle pattern to buildings
    try {
      map.addLayer({
        'id': 'building-patterns',
        'source': 'composite',
        'source-layer': 'building',
        'type': 'fill-extrusion',
        'minzoom': minZoom,
        'filter': ['==', 'extrude', 'true'],
        'paint': {
          'fill-extrusion-pattern': pattern,
          'fill-extrusion-height': ['get', 'height'],
          'fill-extrusion-base': ['get', 'min_height'],
          'fill-extrusion-opacity': opacity,
          'fill-extrusion-vertical-gradient': true
        }
      }, '3d-buildings');
    } catch (error) {
      console.log('Could not add building patterns layer', error);
    }
    
    // Add vegetation accents
    try {
      if (!map.getLayer('vegetation-accents')) {
        map.addLayer({
          'id': 'vegetation-accents',
          'type': 'fill',
          'source': 'composite',
          'source-layer': 'landuse',
          'filter': ['==', 'class', 'park'],
          'paint': {
            'fill-color': accentColor,
            'fill-opacity': 0.85 // 85% opacity for consistency
          }
        });
      }
    } catch (error) {
      console.log('Could not add vegetation accents layer', error);
    }
    
    return () => {
      if (map.getLayer('building-patterns')) {
        map.removeLayer('building-patterns');
      }
      if (map.getLayer('ground-vegetation')) {
        map.removeLayer('ground-vegetation');
      }
      if (map.getLayer('vegetation-accents')) {
        map.removeLayer('vegetation-accents');
      }
    };
  }, []);

  return {
    applyBuildingStyle,
    applyBuildingPatterns
  };
}
