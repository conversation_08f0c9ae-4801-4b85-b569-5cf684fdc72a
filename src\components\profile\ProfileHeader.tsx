import React, { useState } from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { FollowButton } from '@/components/profile/FollowButton';
import { ProfileCoverUploader } from '@/components/profile/ProfileCoverUploader';
import { ProfilePhotoUploader } from '@/components/shared/ProfilePhotoUploader';
import { ProfileStats } from '@/components/profile/ProfileStats';
import { EnhancedUserProfile } from '@/types/enhanced-profile';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { useFollowUser } from '@/hooks/use-follow-user';
import { uploadAvatarFromDataUrl } from '@/utils/file-upload';
import { supabase } from '@/integrations/supabase/client';
import { Check, Edit, MessageSquare, Share, User, VerifiedIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TrustScoreBadge } from './TrustScoreBadge';

interface ProfileHeaderProps {
  profile: EnhancedUserProfile;
  isLoading?: boolean;
  onEditClick?: () => void;
  onMessageClick?: () => void;
  onProfileUpdated?: () => void;
}

export function ProfileHeader({
  profile,
  isLoading = false,
  onEditClick,
  onMessageClick,
  onProfileUpdated
}: ProfileHeaderProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isEditingAvatar, setIsEditingAvatar] = useState(false);
  const [isEditingCover, setIsEditingCover] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Check if this is the current user's profile
  const isOwnProfile = user?.id === profile?.id;

  // Follow functionality
  const { isFollowing, toggleFollow, isLoading: isFollowLoading } = useFollowUser(profile?.id);

  // Handle avatar change
  const handleAvatarChange = async (file: File, dataUrl: string) => {
    if (!isOwnProfile) return;

    try {
      setIsUploading(true);

      // Upload the avatar
      const { url, error } = await uploadAvatarFromDataUrl(profile.id, dataUrl);

      if (error) {
        toast({
          title: "Error uploading avatar",
          description: error.message,
          variant: "destructive"
        });
        return;
      }

      if (url) {
        // Update the profile
        const { error: updateError } = await supabase
          .from('profiles')
          .update({ avatar_url: url })
          .eq('id', profile.id);

        if (updateError) throw updateError;

        toast({
          title: "Avatar updated",
          description: "Your profile picture has been updated successfully."
        });

        // Refresh the profile
        if (onProfileUpdated) onProfileUpdated();
      }
    } catch (error) {
      console.error('Error updating avatar:', error);
      toast({
        title: "Error updating avatar",
        description: "There was an error updating your profile picture.",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
      setIsEditingAvatar(false);
    }
  };

  // Handle cover photo change
  const handleCoverChange = async (file: File, dataUrl: string) => {
    if (!isOwnProfile) return;

    try {
      setIsUploading(true);

      // Upload the cover photo
      const fileName = `cover_${profile.id}_${Date.now()}`;
      const { data, error } = await supabase.storage
        .from('avatars')
        .upload(`public/${fileName}`, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) throw error;

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(`public/${fileName}`);

      // Update the profile
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ cover_url: publicUrl })
        .eq('id', profile.id);

      if (updateError) throw updateError;

      toast({
        title: "Cover photo updated",
        description: "Your cover photo has been updated successfully."
      });

      // Refresh the profile
      if (onProfileUpdated) onProfileUpdated();
    } catch (error) {
      console.error('Error updating cover photo:', error);
      toast({
        title: "Error updating cover photo",
        description: "There was an error updating your cover photo.",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
      setIsEditingCover(false);
    }
  };

  // Handle share profile
  const handleShareProfile = async () => {
    try {
      const shareUrl = `${window.location.origin}/${profile.username || profile.id}`;

      if (navigator.share) {
        await navigator.share({
          title: `${profile.display_name || profile.username}'s Profile`,
          text: `Check out ${profile.display_name || profile.username}'s profile on BuddySurf!`,
          url: shareUrl
        });
      } else {
        await navigator.clipboard.writeText(shareUrl);
        toast({
          title: "Link copied",
          description: "Profile link copied to clipboard."
        });
      }
    } catch (error) {
      console.error('Error sharing profile:', error);
    }
  };

  if (isLoading) {
    return <ProfileHeaderSkeleton />;
  }

  return (
    <div className="relative w-full">
      {/* Cover Photo with Gradient Overlay */}
      <div className="relative h-48 md:h-64 w-full rounded-t-lg overflow-hidden">
        {profile.cover_url ? (
          <>
            <img
              src={profile.cover_url}
              alt={`${profile.display_name || profile.username}'s cover`}
              className="w-full h-full object-cover"
            />
            {/* Gradient overlay for better contrast */}
            <div className="absolute inset-0 bg-gradient-to-b from-black/30 via-black/10 to-transparent pointer-events-none" />
          </>
        ) : (
          <div className="w-full h-full bg-gradient-to-r from-primary-blue/10 to-primary-purple/10" />
        )}
        {/* Subtle shadow at the bottom */}
        <div className="absolute bottom-0 left-0 w-full h-8 bg-gradient-to-t from-black/20 to-transparent pointer-events-none" />
        {/* Cover Photo Edit Button */}
        {isOwnProfile && (
          <div className="absolute top-4 right-4 z-10">
            <Button
              variant="outline"
              size="sm"
              className="bg-white/80 hover:bg-white shadow-md"
              onClick={() => setIsEditingCover(true)}
              disabled={isUploading}
            >
              <Edit className="h-4 w-4 mr-1" />
              {profile.cover_url ? 'Change Cover' : 'Add Cover'}
            </Button>
          </div>
        )}
      </div>

      {/* Profile Info Section */}
      <Card className="relative px-4 pt-20 pb-4 -mt-16 border-t-0 rounded-t-none shadow-lg">
        {/* Avatar with border and shadow */}
        <div className="absolute -top-16 left-1/2 -translate-x-1/2 md:left-8 md:translate-x-0 z-20">
          {isOwnProfile && isEditingAvatar ? (
            <div className="h-28 w-28 md:h-32 md:w-32">
              <ProfilePhotoUploader
                previewUrl={profile.avatar_url || null}
                onPhotoChange={handleAvatarChange}
                size="lg"
              />
            </div>
          ) : (
            <div className="relative group">
              <Avatar className="h-28 w-28 md:h-32 md:w-32 border-4 border-white shadow-xl ring-4 ring-primary/10 bg-white">
                <AvatarImage src={profile.avatar_url || ''} alt={profile.display_name || 'User'} />
                <AvatarFallback className="bg-primary-purple/10 text-primary-purple text-2xl">
                  {profile.display_name?.substring(0, 2) || profile.username?.substring(0, 2) || <User />}
                </AvatarFallback>
              </Avatar>
              {/* (Optional) Online indicator */}
              {/* <span className="absolute bottom-2 right-2 h-4 w-4 rounded-full bg-green-500 border-2 border-white" /> */}
              {isOwnProfile && (
                <Button
                  variant="outline"
                  size="icon"
                  className="absolute bottom-0 right-0 rounded-full bg-white shadow-sm opacity-0 group-hover:opacity-100 transition-opacity border border-primary"
                  onClick={() => setIsEditingAvatar(true)}
                  disabled={isUploading}
                  aria-label="Edit Avatar"
                >
                  <Edit className="h-4 w-4" />
                </Button>
              )}
            </div>
          )}
        </div>

        <div className="flex flex-col items-center md:flex-row md:items-center md:justify-between mt-16 md:mt-0">
          {/* Name and Username */}
          <div className="flex flex-col items-center md:items-start">
            <div className="flex items-center gap-2">
              <h1 className="text-3xl md:text-4xl font-extrabold text-primary drop-shadow-sm">
                {profile.display_name || 'User'}
              </h1>
            </div>
            <div className="flex items-center gap-2 mt-1">
              {profile.username && (
                <span className="text-muted-foreground text-base font-medium">@{profile.username}</span>
              )}
              {profile.is_verified && (
                <VerifiedIcon className="h-5 w-5 text-primary-blue" />
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-2 mt-4 md:mt-0">
            {isOwnProfile ? (
              <Button
                variant="default"
                size="lg"
                onClick={onEditClick}
                className="gap-2 font-semibold shadow-md"
              >
                <Edit className="h-5 w-5" />
                Edit Profile
              </Button>
            ) : (
              <>
                <FollowButton
                  userId={profile.id}
                  isFollowing={isFollowing}
                  onToggle={toggleFollow}
                  disabled={isFollowLoading}
                />
                {/* Message Button with Tooltip */}
                <div className="relative group">
                  <Button
                    variant="outline"
                    size="icon"
                    onClick={onMessageClick}
                    className="rounded-full"
                    aria-label="Message"
                  >
                    <MessageSquare className="h-5 w-5" />
                  </Button>
                  <span className="absolute -bottom-7 left-1/2 -translate-x-1/2 px-2 py-1 text-xs bg-black text-white rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-30 whitespace-nowrap">Message</span>
                </div>
              </>
            )}
            {/* Share Button with Tooltip */}
            <div className="relative group">
              <Button
                variant="ghost"
                size="icon"
                onClick={handleShareProfile}
                className="rounded-full"
                aria-label="Share Profile"
              >
                <Share className="h-5 w-5" />
              </Button>
              <span className="absolute -bottom-7 left-1/2 -translate-x-1/2 px-2 py-1 text-xs bg-black text-white rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none z-30 whitespace-nowrap">Share</span>
            </div>
          </div>
        </div>

        {/* Profile Stats */}
        <div className="mt-8">
          {profile.trust_score !== undefined && (
            <TrustScoreBadge score={profile.trust_score} size="md" />
          )}
          <ProfileStats profile={profile} />
        </div>
      </Card>

      {/* Cover Photo Uploader Modal */}
      {isEditingCover && (
        <ProfileCoverUploader
          previewUrl={profile.cover_url || null}
          onPhotoChange={handleCoverChange}
          onClose={() => setIsEditingCover(false)}
          isUploading={isUploading}
        />
      )}
    </div>
  );
}

function ProfileHeaderSkeleton() {
  return (
    <div className="relative w-full">
      {/* Cover Photo Skeleton */}
      <Skeleton className="h-48 md:h-64 w-full rounded-t-lg" />

      {/* Profile Info Section Skeleton */}
      <Card className="relative px-4 pt-16 pb-4 -mt-12 border-t-0 rounded-t-none">
        {/* Avatar Skeleton */}
        <div className="absolute -top-16 left-6 md:left-8">
          <Skeleton className="h-24 w-24 md:h-32 md:w-32 rounded-full" />
        </div>

        <div className="ml-32 md:ml-40 flex flex-col md:flex-row md:items-center md:justify-between">
          {/* Name and Username Skeleton */}
          <div>
            <Skeleton className="h-8 w-40 mb-2" />
            <Skeleton className="h-4 w-24" />
          </div>

          {/* Action Buttons Skeleton */}
          <div className="flex gap-2 mt-3 md:mt-0">
            <Skeleton className="h-10 w-28" />
            <Skeleton className="h-10 w-28" />
            <Skeleton className="h-10 w-10 rounded-full" />
          </div>
        </div>

        {/* Profile Stats Skeleton */}
        <div className="mt-6 flex gap-4">
          <Skeleton className="h-16 flex-1" />
          <Skeleton className="h-16 flex-1" />
          <Skeleton className="h-16 flex-1" />
        </div>
      </Card>
    </div>
  );
}
