
import { useState } from 'react';

export type MapStyleId = 'streets-v11' | 'satellite-streets-v12' | 'light-v11' | 'dark-v11' | 'outdoors-v12';

interface MapStyleOption {
  id: string;
  name: string;
  url: string;
  preview: string;
}

interface FogConfig {
  color: string;
  'high-color': string;
  'horizon-blend': number;
}

interface LightConfig {
  anchor: 'map' | 'viewport';
  color: string;
  intensity: number;
  position: [number, number, number];
}

export interface BuildingConfig {
  fillColor: string;
  opacity: number;
}

export interface VegetationConfig {
  fillColor: string;
  opacity: number;
}

export interface StyleConfig {
  fog?: FogConfig;
  light?: LightConfig;
  buildingConfig?: BuildingConfig;
  vegetationConfig?: VegetationConfig;
}

export function useMapStyle() {
  const [mapStyle, setMapStyle] = useState<MapStyleId>('streets-v11');
  const [styleId, setStyleId] = useState<MapStyleId>('streets-v11');
  const [isLoading, setIsLoading] = useState(false);
  const [isCustomStyle, setIsCustomStyle] = useState(false);

  const styles: Record<MapStyleId, MapStyleOption> = {
    'streets-v11': {
      id: 'streets-v11',
      name: 'Streets',
      url: 'mapbox://styles/mapbox/streets-v11',
      preview: '/assets/map-styles/streets.png'
    },
    'satellite-streets-v12': {
      id: 'satellite-streets-v12',
      name: 'Satellite',
      url: 'mapbox://styles/mapbox/satellite-streets-v12',
      preview: '/assets/map-styles/satellite.png'
    },
    'light-v11': {
      id: 'light-v11',
      name: 'Light',
      url: 'mapbox://styles/mapbox/light-v11',
      preview: '/assets/map-styles/light.png'
    },
    'dark-v11': {
      id: 'dark-v11',
      name: 'Dark',
      url: 'mapbox://styles/mapbox/dark-v11',
      preview: '/assets/map-styles/dark.png'
    },
    'outdoors-v12': {
      id: 'outdoors-v12',
      name: 'Outdoors',
      url: 'mapbox://styles/mapbox/outdoors-v12',
      preview: '/assets/map-styles/outdoors.png'
    }
  };

  const styleConfig: StyleConfig = {
    fog: {
      'color': 'rgb(255, 255, 255)',
      'high-color': 'rgb(200, 200, 225)',
      'horizon-blend': 0.2
    },
    light: {
      anchor: 'viewport',
      color: '#ffffff',
      intensity: 0.4,
      position: [1.15, 210, 30]
    },
    buildingConfig: {
      fillColor: '#d8e2ebaa',
      opacity: 0.7
    },
    vegetationConfig: {
      fillColor: '#c6e2c6',
      opacity: 0.5
    }
  };

  const changeStyle = (styleId: MapStyleId) => {
    setIsLoading(true);
    setMapStyle(styleId);
    setStyleId(styleId);
    setIsCustomStyle(false);
    // Simulate loading
    setTimeout(() => {
      setIsLoading(false);
    }, 300);
  };

  return {
    mapStyle,
    styleId,
    style: styles[styleId],
    styles,
    changeStyle,
    isLoading,
    styleConfig,
    isCustomStyle
  };
}
