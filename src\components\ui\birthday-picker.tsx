"use client"

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { format, subYears, isValid, parse } from "date-fns";

interface BirthdayPickerProps {
  value?: Date;
  onChange: (date: Date | undefined) => void;
  className?: string;
  placeholder?: string;
  minAge?: number;
  maxAge?: number;
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
}

export function BirthdayPicker({
  value,
  onChange,
  className,
  placeholder = "Select your birthday",
  minAge = 18,
  maxAge = 100,
  label,
  description,
  error,
  required = false
}: BirthdayPickerProps) {
  const [date, setDate] = useState<Date | undefined>(value);
  const [view, setView] = useState<'date' | 'month' | 'year'>('date');
  const [yearDecade, setYearDecade] = useState<number>(new Date().getFullYear() - 20);
  const [selectedMonth, setSelectedMonth] = useState<number | null>(date?.getMonth() ?? null);
  const [selectedYear, setSelectedYear] = useState<number | null>(date?.getFullYear() ?? null);
  const [isOpen, setIsOpen] = useState(false);
  
  // Calculate the maximum and minimum dates based on minAge and maxAge
  const maxDate = subYears(new Date(), minAge);
  const minDate = subYears(new Date(), maxAge);
  
  // Update the internal date when the value prop changes
  useEffect(() => {
    setDate(value);
    if (value) {
      setSelectedMonth(value.getMonth());
      setSelectedYear(value.getFullYear());
    }
  }, [value]);
  
  // Months array for selection
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June', 
    'July', 'August', 'September', 'October', 'November', 'December'
  ];
  
  // Generate years for the year picker (10 years per view)
  const years = Array.from({ length: 10 }, (_, i) => yearDecade + i);
  
  // Navigate between decades
  const prevDecade = () => setYearDecade(yearDecade - 10);
  const nextDecade = () => setYearDecade(yearDecade + 10);
  
  // Handle month selection
  const handleMonthSelect = (monthIndex: number) => {
    setSelectedMonth(monthIndex);
    
    if (selectedYear !== null) {
      // If we already have a year, we can create a date
      const newDate = new Date(selectedYear, monthIndex, 1);
      setDate(newDate);
      onChange(newDate);
      setView('date'); // Move to date selection
    } else {
      // If we don't have a year yet, move to year selection
      setView('year');
    }
  };
  
  // Handle year selection
  const handleYearSelect = (year: number) => {
    setSelectedYear(year);
    
    if (selectedMonth !== null) {
      // If we already have a month, we can create a date
      const newDate = new Date(year, selectedMonth, 1);
      setDate(newDate);
      onChange(newDate);
      setView('date'); // Move to date selection
    } else {
      // If we don't have a month yet, move to month selection
      setView('month');
    }
  };
  
  // Handle day selection
  const handleDaySelect = (day: number) => {
    if (selectedMonth !== null && selectedYear !== null) {
      const newDate = new Date(selectedYear, selectedMonth, day);
      setDate(newDate);
      onChange(newDate);
      setIsOpen(false); // Close the popover after selection
    }
  };
  
  // Generate days for the current month/year
  const getDaysInMonth = () => {
    if (selectedMonth === null || selectedYear === null) return [];
    
    const daysInMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate();
    return Array.from({ length: daysInMonth }, (_, i) => i + 1);
  };
  
  // Get day of week for the first day of the month (0 = Sunday, 6 = Saturday)
  const getFirstDayOfMonth = () => {
    if (selectedMonth === null || selectedYear === null) return 0;
    return new Date(selectedYear, selectedMonth, 1).getDay();
  };
  
  // Render the date view (calendar)
  const renderDateView = () => {
    const days = getDaysInMonth();
    const firstDay = getFirstDayOfMonth();
    const dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];
    
    // Create empty cells for days before the first day of the month
    const emptyCells = Array.from({ length: firstDay }, (_, i) => (
      <div key={`empty-${i}`} className="h-8 w-8"></div>
    ));
    
    return (
      <div className="p-3">
        <div className="flex justify-between items-center mb-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setView('month')}
            className="text-sm font-medium"
          >
            {selectedMonth !== null ? months[selectedMonth] : 'Month'}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setView('year')}
            className="text-sm font-medium"
          >
            {selectedYear !== null ? selectedYear : 'Year'}
          </Button>
        </div>
        
        <div className="grid grid-cols-7 gap-1 mb-2">
          {dayNames.map(day => (
            <div key={day} className="h-8 w-8 flex items-center justify-center text-xs font-medium text-muted-foreground">
              {day}
            </div>
          ))}
        </div>
        
        <div className="grid grid-cols-7 gap-1">
          {emptyCells}
          {days.map(day => {
            const currentDate = new Date(selectedYear!, selectedMonth!, day);
            const isSelected = date && date.getDate() === day && 
                              date.getMonth() === selectedMonth && 
                              date.getFullYear() === selectedYear;
            const isDisabled = currentDate > maxDate || currentDate < minDate;
            
            return (
              <Button
                key={day}
                variant={isSelected ? "default" : "ghost"}
                className={cn(
                  "h-8 w-8 p-0 font-normal",
                  isDisabled && "opacity-50 cursor-not-allowed",
                  isSelected && "text-primary-foreground"
                )}
                disabled={isDisabled}
                onClick={() => !isDisabled && handleDaySelect(day)}
              >
                {day}
              </Button>
            );
          })}
        </div>
      </div>
    );
  };
  
  // Render the month view
  const renderMonthView = () => (
    <div className="p-3">
      <div className="flex justify-between items-center mb-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setView('date')}
          className="text-sm font-medium"
        >
          Back
        </Button>
        <div className="text-sm font-medium">
          Select Month
        </div>
        <div className="w-16"></div> {/* Spacer for alignment */}
      </div>
      
      <div className="grid grid-cols-3 gap-2">
        {months.map((month, index) => {
          const isSelected = selectedMonth === index;
          
          return (
            <Button
              key={month}
              variant={isSelected ? "default" : "outline"}
              className="h-10"
              onClick={() => handleMonthSelect(index)}
            >
              {month.substring(0, 3)}
            </Button>
          );
        })}
      </div>
    </div>
  );
  
  // Render the year view
  const renderYearView = () => (
    <div className="p-3">
      <div className="flex justify-between items-center mb-4">
        <Button
          variant="ghost"
          size="icon"
          onClick={prevDecade}
          className="h-7 w-7"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        <div className="text-sm font-medium">
          {yearDecade} - {yearDecade + 9}
        </div>
        <Button
          variant="ghost"
          size="icon"
          onClick={nextDecade}
          className="h-7 w-7"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>
      
      <div className="grid grid-cols-3 gap-2">
        {years.map(year => {
          const isSelected = selectedYear === year;
          const isDisabled = year > maxDate.getFullYear() || year < minDate.getFullYear();
          
          return (
            <Button
              key={year}
              variant={isSelected ? "default" : "outline"}
              className={cn(
                "h-10",
                isDisabled && "opacity-50 cursor-not-allowed"
              )}
              disabled={isDisabled}
              onClick={() => !isDisabled && handleYearSelect(year)}
            >
              {year}
            </Button>
          );
        })}
      </div>
    </div>
  );
  
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <div className="flex items-center">
          <div className="text-sm font-medium">{label}</div>
          {required && <span className="text-red-500 ml-1">*</span>}
        </div>
      )}
      
      {description && (
        <p className="text-xs text-gray-500">{description}</p>
      )}
      
      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !date && "text-muted-foreground",
              error && "border-red-500"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, "MMMM d, yyyy") : placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          {view === 'date' && renderDateView()}
          {view === 'month' && renderMonthView()}
          {view === 'year' && renderYearView()}
        </PopoverContent>
      </Popover>
      
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  );
}
