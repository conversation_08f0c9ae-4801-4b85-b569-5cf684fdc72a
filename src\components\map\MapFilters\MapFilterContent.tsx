
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Slider } from '@/components/ui/slider';
import { Search, Calendar } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { format } from 'date-fns';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';

interface MapFilterContentProps {
  onFilterChange?: (filters: any) => void;
  className?: string;
}

const MapFilterContent = ({ onFilterChange, className }: MapFilterContentProps) => {
  const [activeTab, setActiveTab] = useState('nearby');
  const [meetingPoint, setMeetingPoint] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [distance, setDistance] = useState(10); // in km
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [date, setDate] = useState<Date | undefined>(undefined);
  const [categories, setCategories] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState('distance'); // Default sort option
  
  // List of available categories
  const availableCategories = [
    { id: 'sports', name: 'Sports', emoji: '🏀' },
    { id: 'dining', name: 'Dining', emoji: '🍽️' },
    { id: 'music', name: 'Music', emoji: '🎵' },
    { id: 'outdoors', name: 'Outdoors', emoji: '🌳' },
    { id: 'art', name: 'Art', emoji: '🎨' },
    { id: 'dating', name: 'Dating', emoji: '💖' },
    { id: 'social', name: 'Social', emoji: '🎉' },
    { id: 'education', name: 'Education', emoji: '📚' },
    { id: 'technology', name: 'Technology', emoji: '💻' },
  ];

  // Mocked data for UI demonstration
  const nearbyCounts = {
    nearby: 24,
    activity: 8,
    dating: 12,
    hire: 15
  };

  const handleTabChange = (value: string) => {
    setActiveTab(value);
    if (onFilterChange) {
      onFilterChange({ 
        type: value, 
        meetingPoint, 
        distance,
        priceRange,
        date,
        categories,
        sortBy
      });
    }
  };

  const handleMeetingPointChange = (checked: boolean) => {
    setMeetingPoint(checked);
    if (onFilterChange) {
      onFilterChange({ 
        type: activeTab, 
        meetingPoint: checked, 
        distance,
        priceRange,
        date,
        categories,
        sortBy
      });
    }
  };

  const handleDistanceChange = (value: number[]) => {
    setDistance(value[0]);
    if (onFilterChange) {
      onFilterChange({ 
        type: activeTab, 
        meetingPoint, 
        distance: value[0],
        priceRange,
        date,
        categories,
        sortBy
      });
    }
  };

  const handleCategoryToggle = (categoryId: string) => {
    const newCategories = categories.includes(categoryId)
      ? categories.filter(id => id !== categoryId)
      : [...categories, categoryId];
    
    setCategories(newCategories);
    
    if (onFilterChange) {
      onFilterChange({ 
        type: activeTab, 
        meetingPoint, 
        distance,
        priceRange,
        date,
        categories: newCategories,
        sortBy
      });
    }
  };

  const handleSearch = () => {
    if (onFilterChange) {
      onFilterChange({ 
        type: activeTab, 
        meetingPoint, 
        distance,
        searchQuery,
        priceRange,
        date,
        categories,
        sortBy
      });
    }
  };

  const handleDateSelect = (selectedDate: Date | undefined) => {
    setDate(selectedDate);
    if (onFilterChange) {
      onFilterChange({ 
        type: activeTab, 
        meetingPoint, 
        distance,
        searchQuery,
        priceRange,
        date: selectedDate,
        categories,
        sortBy
      });
    }
  };

  return (
    <div className={cn("flex flex-col gap-4", className)}>
      <Tabs 
        defaultValue="nearby" 
        className="w-full" 
        value={activeTab} 
        onValueChange={handleTabChange}
      >
        <TabsList className="grid grid-cols-4 w-full">
          <TabsTrigger value="nearby" className="relative">
            Nearby
            <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center bg-primary text-[10px]">
              {nearbyCounts.nearby}
            </Badge>
          </TabsTrigger>
          <TabsTrigger value="activity">
            Activity
            {nearbyCounts.activity > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center bg-primary text-[10px]">
                {nearbyCounts.activity}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="dating">
            Dating
            {nearbyCounts.dating > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center bg-accent-orange text-[10px]">
                {nearbyCounts.dating}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="hire">
            Hire
            {nearbyCounts.hire > 0 && (
              <Badge className="absolute -top-2 -right-2 h-5 w-5 p-0 flex items-center justify-center bg-secondary text-[10px]">
                {nearbyCounts.hire}
              </Badge>
            )}
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="nearby" className="space-y-4 mt-4">
          <p className="text-sm text-muted-foreground">
            {nearbyCounts.nearby} users are within {distance}km of your location
          </p>
        </TabsContent>
        <TabsContent value="activity" className="space-y-4 mt-4">
          <p className="text-sm text-muted-foreground">
            {nearbyCounts.activity} activities are available near you
          </p>
        </TabsContent>
        <TabsContent value="dating" className="space-y-4 mt-4">
          <p className="text-sm text-muted-foreground">
            {nearbyCounts.dating} dating opportunities are available nearby
          </p>
        </TabsContent>
        <TabsContent value="hire" className="space-y-4 mt-4">
          <p className="text-sm text-muted-foreground">
            {nearbyCounts.hire} people are offering services in your area
          </p>
        </TabsContent>
      </Tabs>

      <div className="space-y-2">
        <Label htmlFor="sort-by">Sort Results By</Label>
        <Select value={sortBy} onValueChange={setSortBy}>
          <SelectTrigger id="sort-by">
            <SelectValue placeholder="Sort by..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="distance">Distance</SelectItem>
            <SelectItem value="latest">Most Recent</SelectItem>
            <SelectItem value="popularity">Popularity</SelectItem>
            <SelectItem value="rating">Rating</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="flex items-center space-x-2">
        <Switch 
          id="meeting-point" 
          checked={meetingPoint} 
          onCheckedChange={(checked) => {
            setMeetingPoint(checked);
            if (onFilterChange) {
              onFilterChange({ 
                type: activeTab, 
                meetingPoint: checked, 
                distance,
                priceRange,
                date,
                categories,
                sortBy
              });
            }
          }}
        />
        <Label htmlFor="meeting-point">Show meeting points</Label>
      </div>

      <div className="relative">
        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
        <Input 
          placeholder="Search users, activities, or services..." 
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-10"
          onKeyDown={(e) => e.key === 'Enter' && onFilterChange && onFilterChange({
            type: activeTab,
            meetingPoint,
            distance,
            searchQuery: e.currentTarget.value,
            priceRange,
            date,
            categories,
            sortBy
          })}
        />
      </div>

      <div className="space-y-2">
        <Label>Categories</Label>
        <div className="flex flex-wrap gap-2 mt-1">
          {availableCategories.map(category => (
            <Badge 
              key={category.id}
              variant={categories.includes(category.id) ? "default" : "outline"}
              className="cursor-pointer"
              onClick={() => {
                const newCategories = categories.includes(category.id)
                  ? categories.filter(id => id !== category.id)
                  : [...categories, category.id];
                
                setCategories(newCategories);
                
                if (onFilterChange) {
                  onFilterChange({ 
                    type: activeTab, 
                    meetingPoint, 
                    distance,
                    priceRange,
                    date,
                    categories: newCategories,
                    sortBy
                  });
                }
              }}
            >
              <span className="mr-1">{category.emoji}</span> {category.name}
            </Badge>
          ))}
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex justify-between">
          <Label htmlFor="distance">Distance</Label>
          <span className="text-sm font-medium">{distance} km</span>
        </div>
        <Slider 
          id="distance"
          min={1} 
          max={50} 
          step={1}
          value={[distance]}
          onValueChange={(value) => {
            setDistance(value[0]);
            if (onFilterChange) {
              onFilterChange({ 
                type: activeTab, 
                meetingPoint, 
                distance: value[0],
                priceRange,
                date,
                categories,
                sortBy
              });
            }
          }}
          className="w-full"
        />
      </div>

      <div className="space-y-2">
        <Label>Date</Label>
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-start text-left font-normal"
            >
              <Calendar className="mr-2 h-4 w-4" />
              {date ? format(date, 'PPP') : <span>Pick a date</span>}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <CalendarComponent
              mode="single"
              selected={date}
              onSelect={(selectedDate) => {
                setDate(selectedDate);
                if (onFilterChange) {
                  onFilterChange({ 
                    type: activeTab, 
                    meetingPoint, 
                    distance,
                    searchQuery,
                    priceRange,
                    date: selectedDate,
                    categories,
                    sortBy
                  });
                }
              }}
              initialFocus
              disabled={(date) => {
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                return date < today;
              }}
            />
          </PopoverContent>
        </Popover>
      </div>

      <div className="flex justify-end pt-2">
        <Button onClick={() => onFilterChange && onFilterChange({
          type: activeTab,
          meetingPoint,
          distance,
          searchQuery,
          priceRange,
          date,
          categories,
          sortBy
        })}>
          Apply Filters
        </Button>
      </div>
    </div>
  );
};

export default MapFilterContent;
