
import { useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';

export interface GroupChatMessage {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  created_at: string;
  is_read: boolean;
  message_type?: string;
  is_system_message?: boolean;
  metadata?: any;
  sender?: {
    id: string;
    display_name?: string;
    avatar_url?: string;
    username?: string;
  } | null;
}

interface ActivityGroupChatOptions {
  activityId: string;
}

export function useActivityGroupChat(options: ActivityGroupChatOptions) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const activityId = options.activityId;

  const { data: groupChat, isLoading: isChatLoading } = useQuery({
    queryKey: ['activity-group-chat', activityId],
    queryFn: async () => {
      if (!activityId || !user) return null;

      try {
        const { data: existingChat } = await supabase
          .from('chat_conversations')
          .select('*')
          .eq('is_group', true)
          .maybeSingle();

        if (existingChat) {
          return existingChat;
        }

        const { data: newChat, error } = await supabase
          .from('chat_conversations')
          .insert({
            is_group: true
          })
          .select()
          .single();

        if (error) throw error;
        return newChat;
      } catch (error) {
        console.error('Error fetching/creating group chat:', error);
        return null;
      }
    },
    enabled: !!activityId && !!user
  });

  const { data: messages, isLoading: isMessagesLoading } = useQuery({
    queryKey: ['activity-group-messages', groupChat?.id],
    queryFn: async (): Promise<GroupChatMessage[]> => {
      if (!groupChat?.id) return [];

      try {
        const { data, error } = await supabase
          .from('messages')
          .select('*')
          .eq('conversation_id', groupChat.id)
          .order('created_at', { ascending: true });

        if (error) throw error;

        return (data || []).map(msg => ({
          ...msg,
          sender: null,
          is_system_message: msg.is_system_message || false,
          metadata: msg.metadata || null
        })) as GroupChatMessage[];
      } catch (error) {
        console.error('Error fetching messages:', error);
        return [];
      }
    },
    enabled: !!groupChat?.id
  });

  const sendMessage = useMutation({
    mutationFn: async (content: string) => {
      if (!user || !groupChat?.id) {
        throw new Error("Missing required information to send message");
      }

      const { data, error } = await supabase
        .from('messages')
        .insert({
          sender_id: user.id,
          conversation_id: groupChat.id,
          content: content.trim(),
          message_type: 'text'
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      if (groupChat?.id) {
        queryClient.invalidateQueries({
          queryKey: ['activity-group-messages', groupChat.id]
        });
      }
    }
  });

  useEffect(() => {
    if (!groupChat?.id) return;

    const channel = supabase
      .channel(`activity-chat:${groupChat.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'messages',
          filter: `conversation_id=eq.${groupChat.id}`
        },
        () => {
          queryClient.invalidateQueries({
            queryKey: ['activity-group-messages', groupChat.id]
          });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [groupChat?.id, queryClient]);

  return {
    conversation: groupChat ? { id: groupChat.id } : null,
    isLoadingConversation: isChatLoading,
    messages: messages || [],
    isLoadingMessages: isMessagesLoading,
    participants: [],
    isLoadingParticipants: false,
    sendMessage: {
      mutate: sendMessage.mutate,
      isPending: sendMessage.isPending
    }
  };
}
