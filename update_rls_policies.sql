-- Script to update RLS policies to use (select auth.<function>()) instead of auth.<function>()
-- This improves query performance by preventing re-evaluation for each row

-- Function to generate ALTER POLICY statements
CREATE OR REPLACE FUNCTION update_auth_function_calls()
R<PERSON><PERSON><PERSON> void AS $$
DECLARE
    policy_record RECORD;
    new_qual TEXT;
    new_with_check TEXT;
    new_using TEXT;
BEGIN
    -- Loop through all policies in the public schema
    FOR policy_record IN 
        SELECT 
            schemaname, 
            tablename, 
            policyname, 
            cmd,
            qual,
            with_check,
            using_expr
        FROM 
            pg_policies 
        WHERE 
            schemaname = 'public'
    LOOP
        -- Skip if all expressions are NULL or don't contain auth.
        IF (policy_record.qual IS NULL OR policy_record.qual NOT LIKE '%auth.%') AND 
           (policy_record.with_check IS NULL OR policy_record.with_check NOT LIKE '%auth.%') AND
           (policy_record.using_expr IS NULL OR policy_record.using_expr NOT LIKE '%auth.%') THEN
            CONTINUE;
        END IF;
        
        -- Replace auth.uid() with (SELECT auth.uid())
        IF policy_record.qual IS NOT NULL AND policy_record.qual LIKE '%auth.uid()%' THEN
            new_qual := regexp_replace(policy_record.qual, 'auth\.uid\(\)', '(SELECT auth.uid())', 'g');
            
            -- Execute the ALTER POLICY statement for qual
            EXECUTE format(
                'ALTER POLICY %I ON %I.%I USING (%s);',
                policy_record.policyname,
                policy_record.schemaname,
                policy_record.tablename,
                new_qual
            );
            
            RAISE NOTICE 'Updated USING expression for policy % on table %.%', 
                policy_record.policyname, 
                policy_record.schemaname, 
                policy_record.tablename;
        END IF;
        
        -- Replace auth.uid() with (SELECT auth.uid()) in WITH CHECK expression
        IF policy_record.with_check IS NOT NULL AND policy_record.with_check LIKE '%auth.uid()%' THEN
            new_with_check := regexp_replace(policy_record.with_check, 'auth\.uid\(\)', '(SELECT auth.uid())', 'g');
            
            -- Execute the ALTER POLICY statement for with_check
            EXECUTE format(
                'ALTER POLICY %I ON %I.%I WITH CHECK (%s);',
                policy_record.policyname,
                policy_record.schemaname,
                policy_record.tablename,
                new_with_check
            );
            
            RAISE NOTICE 'Updated WITH CHECK expression for policy % on table %.%', 
                policy_record.policyname, 
                policy_record.schemaname, 
                policy_record.tablename;
        END IF;
        
        -- Replace other auth functions like auth.role(), auth.email(), etc.
        IF policy_record.qual IS NOT NULL AND policy_record.qual ~ 'auth\.[a-zA-Z_]+\(\)' THEN
            new_qual := regexp_replace(policy_record.qual, 'auth\.([a-zA-Z_]+)\(\)', '(SELECT auth.\1())', 'g');
            
            -- Execute the ALTER POLICY statement for qual with other auth functions
            EXECUTE format(
                'ALTER POLICY %I ON %I.%I USING (%s);',
                policy_record.policyname,
                policy_record.schemaname,
                policy_record.tablename,
                new_qual
            );
            
            RAISE NOTICE 'Updated USING expression with other auth functions for policy % on table %.%', 
                policy_record.policyname, 
                policy_record.schemaname, 
                policy_record.tablename;
        END IF;
        
        -- Replace other auth functions in WITH CHECK expression
        IF policy_record.with_check IS NOT NULL AND policy_record.with_check ~ 'auth\.[a-zA-Z_]+\(\)' THEN
            new_with_check := regexp_replace(policy_record.with_check, 'auth\.([a-zA-Z_]+)\(\)', '(SELECT auth.\1())', 'g');
            
            -- Execute the ALTER POLICY statement for with_check with other auth functions
            EXECUTE format(
                'ALTER POLICY %I ON %I.%I WITH CHECK (%s);',
                policy_record.policyname,
                policy_record.schemaname,
                policy_record.tablename,
                new_with_check
            );
            
            RAISE NOTICE 'Updated WITH CHECK expression with other auth functions for policy % on table %.%', 
                policy_record.policyname, 
                policy_record.schemaname, 
                policy_record.tablename;
        END IF;
    END LOOP;
    
    RAISE NOTICE 'All RLS policies have been updated.';
END;
$$ LANGUAGE plpgsql;

-- Execute the function to update all policies
SELECT update_auth_function_calls();

-- Clean up by dropping the function when done
DROP FUNCTION update_auth_function_calls();
