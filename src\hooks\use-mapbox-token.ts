
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface MapboxTokenResponse {
  token: string;
  error?: string;
  details?: string;
}

export function useMapboxToken() {
  const [token, setToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    async function fetchMapboxToken() {
      try {
        setIsLoading(true);
        
        // Try to get from env first
        const envToken = import.meta.env.VITE_MAPBOX_TOKEN;
        if (envToken) {
          setToken(envToken);
          setIsLoading(false);
          return;
        }
        
        // Fall back to Supabase Edge Function
        const { data, error } = await supabase.functions.invoke('get-mapbox-token');
        
        if (error) {
          console.error('Error fetching Mapbox token:', error);
          setError(error.message);
          toast({
            title: "Error fetching map token",
            description: "Could not retrieve Mapbox token. Some map features may not work.",
            variant: "destructive"
          });
          return;
        }
        
        if (data?.token) {
          setToken(data.token);
        } else if (data?.error) {
          setError(data.error);
          toast({
            title: "Map configuration error",
            description: data.details || "Missing Mapbox token. Please check your configuration.",
            variant: "destructive"
          });
        }
      } catch (error: any) {
        console.error("Failed to fetch Mapbox token:", error);
        setError(error.message || "Unknown error fetching Mapbox token");
      } finally {
        setIsLoading(false);
      }
    }

    fetchMapboxToken();
  }, [toast]);

  return { token, isLoading, error };
}
