
export interface User {
  id: string;
  display_name?: string;
  username?: string;
  avatar_url?: string;
  bio?: string;
  is_verified?: boolean;
  email?: string;
  location?: string;
  birthday?: string;
  gender?: string;
  is_banned?: boolean;
  ban_reason?: string;
  banned_until?: string;
  trust_score?: number;
  created_at?: string;
  updated_at?: string;
  last_sign_in_at?: string;
  onboarding_completed?: boolean;
  location_permission_granted?: boolean;
  is_online?: boolean;
  last_seen_at?: string;
  interests?: string[];
  moderation_status?: string;
  current_participants?: number;
}
