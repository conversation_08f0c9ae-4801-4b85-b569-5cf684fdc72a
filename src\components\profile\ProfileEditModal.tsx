import React, { useState } from 'react';
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription
} from "@/components/ui/form";
import { supabase } from "@/integrations/supabase/client";
import { useImageUpload } from "@/hooks/use-image-upload";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Camera, RefreshCw, Plus, Trash2, Link as LinkIcon } from "lucide-react";
import { ProfilePhotoUploader } from '@/components/shared/ProfilePhotoUploader';
import { OnboardingModal } from "@/components/onboarding/OnboardingModal";
import { EnhancedUserProfile, SocialLink, ProfileVisibilitySettings } from '@/types/enhanced-profile';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { uploadAvatarFromDataUrl } from "@/utils/file-upload";
import { SocialLinksEditor } from './SocialLinksEditor';
import { PrivacySettingsEditor } from './PrivacySettingsEditor';
import { CoverPhotoEditor } from './CoverPhotoEditor';
import { ProfileCoverUploader } from '@/components/profile/ProfileCoverUploader';

interface ProfileEditModalProps {
  open: boolean;
  onClose: () => void;
  profile: EnhancedUserProfile;
  onProfileUpdated: () => void;
}

// Define the social link schema
const socialLinkSchema = z.object({
  platform: z.string().min(1, "Platform name is required"),
  url: z.string().url("Please enter a valid URL"),
  username: z.string().optional(),
  icon: z.string().optional()
});

// Define the profile visibility schema
const profileVisibilitySchema = z.object({
  followers: z.boolean().default(true),
  following: z.boolean().default(true),
  activities: z.boolean().default(true),
  gallery: z.boolean().default(true),
  about: z.boolean().default(true)
});

// Define the enhanced profile form schema
const profileFormSchema = z.object({
  // Basic info
  display_name: z.string().min(2, "Name must be at least 2 characters").max(50),
  username: z.string().min(3, "Username must be at least 3 characters").max(30),
  bio: z.string().max(500, "Bio must be 500 characters or less").optional().nullable(),

  // Profile theme
  profile_theme: z.string().optional().nullable(),

  // Social links
  social_links: z.array(socialLinkSchema).optional().default([]),

  // Privacy settings
  is_profile_public: z.boolean().default(true),
  profile_visibility: profileVisibilitySchema.optional().default({
    followers: true,
    following: true,
    activities: true,
    gallery: true,
    about: true
  })
});

type ProfileFormValues = z.infer<typeof profileFormSchema>;

export function ProfileEditModal({ open, onClose, profile, onProfileUpdated }: ProfileEditModalProps) {
  const { previewUrl, fileInputRef, handleThumbnailClick, handleFileChange, handleRemove } = useImageUpload();
  const [showFullOnboarding, setShowFullOnboarding] = useState(false);
  const [onboardingStep, setOnboardingStep] = useState(1);
  const [activeTab, setActiveTab] = useState('basic');
  const [isEditingCover, setIsEditingCover] = useState(false);
  const [isUploading, setIsUploading] = useState(false);

  // Social links state
  const [socialLinks, setSocialLinks] = useState<SocialLink[]>(profile?.social_links || []);

  // Track profile completion status
  const getProfileCompletionPercentage = () => {
    if (!profile) return 0;

    // If onboarding is explicitly marked as completed, return 100%
    if (profile.onboarding_completed === true) {
      return 100;
    }

    const fields = [
      !!profile.display_name,
      !!profile.username,
      !!profile.bio,
      !!profile.avatar_url,
      !!profile.cover_url,
      !!profile.birthday,
      !!profile.gender,
      !!profile.default_location,
      !!profile.location_permission_granted,
      !!profile.notifications_enabled,
      Array.isArray(profile.purposes) && profile.purposes.length > 0,
      Array.isArray(profile.vibes) && profile.vibes.length > 0,
      Array.isArray(profile.gallery) && profile.gallery.length > 0,
      Array.isArray(profile.social_links) && profile.social_links.length > 0
    ];

    const completedFields = fields.filter(Boolean).length;
    return Math.round((completedFields / fields.length) * 100);
  };

  const completionPercentage = getProfileCompletionPercentage();

  // Initialize form with default values
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      display_name: profile?.display_name || "",
      username: profile?.username || "",
      bio: profile?.bio || "",
      profile_theme: profile?.profile_theme || "",
      social_links: profile?.social_links || [],
      is_profile_public: profile?.is_profile_public !== false, // Default to true if not explicitly false
      profile_visibility: profile?.profile_visibility || {
        followers: true,
        following: true,
        activities: true,
        gallery: true,
        about: true
      }
    },
  });

  // Update form when profile changes
  React.useEffect(() => {
    if (profile && open) {
      form.reset({
        display_name: profile.display_name || "",
        username: profile.username || "",
        bio: profile.bio || "",
        profile_theme: profile.profile_theme || "",
        social_links: profile.social_links || [],
        is_profile_public: profile.is_profile_public !== false,
        profile_visibility: profile.profile_visibility || {
          followers: true,
          following: true,
          activities: true,
          gallery: true,
          about: true
        }
      });

      // Update social links state
      setSocialLinks(profile.social_links || []);
    }
  }, [profile, form, open]);

  // Handle cover photo change
  const handleCoverChange = async (file: File, dataUrl: string) => {
    try {
      setIsUploading(true);

      // Upload the cover photo
      const fileName = `cover_${profile.id}_${Date.now()}`;
      const { data, error } = await supabase.storage
        .from('avatars')
        .upload(`public/${fileName}`, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) throw error;

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(`public/${fileName}`);

      // Update the profile
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ cover_url: publicUrl })
        .eq('id', profile.id);

      if (updateError) throw updateError;

      // Refresh the profile
      onProfileUpdated();
    } catch (error) {
      console.error('Error updating cover photo:', error);
    } finally {
      setIsUploading(false);
      setIsEditingCover(false);
    }
  };

  // Handle form submission
  const onSubmit = async (values: ProfileFormValues) => {
    try {
      const { data } = await supabase.auth.getSession();
      const session = data?.session;
      if (!session?.user) return;

      const updates: any = {
        id: session.user.id,
        display_name: values.display_name,
        username: values.username,
        bio: values.bio,
        profile_theme: values.profile_theme,
        social_links: values.social_links,
        is_profile_public: values.is_profile_public,
        profile_visibility: values.profile_visibility,
        updated_at: new Date().toISOString(),
      };

      // Upload avatar if changed
      if (previewUrl) {
        try {
          // Check if it's a data URL
          if (previewUrl.startsWith('data:')) {
            console.log("ProfileEditModal - uploading avatar from data URL...");

            // Use our utility function to upload the avatar
            const { url, error: uploadError } = await uploadAvatarFromDataUrl(session.user.id, previewUrl);

            if (uploadError) {
              console.error("Error uploading avatar:", uploadError);
            } else if (url) {
              updates.avatar_url = url;
              console.log("Avatar uploaded successfully:", url);
            }
          }
          // If it's a file from the input
          else if (fileInputRef.current?.files?.length) {
            const file = fileInputRef.current.files[0];
            console.log("ProfileEditModal - uploading avatar from file input...");

            // Read the file as a data URL
            const reader = new FileReader();
            const dataUrl = await new Promise<string>((resolve) => {
              reader.onloadend = () => resolve(reader.result as string);
              reader.readAsDataURL(file);
            });

            // Upload using our utility function
            const { url, error: uploadError } = await uploadAvatarFromDataUrl(session.user.id, dataUrl);

            if (uploadError) {
              console.error("Error uploading avatar:", uploadError);
            } else if (url) {
              updates.avatar_url = url;
              console.log("Avatar uploaded successfully:", url);
            }
          }
        } catch (error) {
          console.error("Error processing avatar:", error);
        }
      }

      // Update profile
      const { error } = await supabase
        .from('profiles')
        .upsert(updates);

      if (error) throw error;

      onProfileUpdated();
      onClose();
    } catch (error) {
      console.error('Error updating profile:', error);
    }
  };

  // Handle adding a new social link
  const handleAddSocialLink = () => {
    const newSocialLinks = [...socialLinks, {
      platform: '',
      url: '',
      username: ''
    }];

    setSocialLinks(newSocialLinks);
    form.setValue('social_links', newSocialLinks);
  };

  // Handle removing a social link
  const handleRemoveSocialLink = (index: number) => {
    const newSocialLinks = [...socialLinks];
    newSocialLinks.splice(index, 1);

    setSocialLinks(newSocialLinks);
    form.setValue('social_links', newSocialLinks);
  };

  // Handle updating a social link
  const handleUpdateSocialLink = (index: number, field: keyof SocialLink, value: string) => {
    const newSocialLinks = [...socialLinks];
    newSocialLinks[index] = {
      ...newSocialLinks[index],
      [field]: value
    };

    setSocialLinks(newSocialLinks);
    form.setValue('social_links', newSocialLinks);
  };

  const handleFullOnboarding = (initialStep: number = 1) => {
    setOnboardingStep(initialStep);
    onClose();
    setShowFullOnboarding(true);
  };

  // Determine which steps need completion
  const getNextIncompleteStep = () => {
    if (!profile) return 1;

    if (!profile.display_name || !profile.username) return 1;
    if (!profile.birthday || !profile.gender) return 2;
    if (!profile.purposes || profile.purposes.length === 0) return 3;
    if (!profile.vibes || profile.vibes.length === 0) return 5;

    return 1; // Default to beginning if all essential parts are complete
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onClose}>
        <DialogContent className="sm:max-w-[700px] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit profile</DialogTitle>
            <DialogDescription>
              Update your profile information visible to others
            </DialogDescription>
          </DialogHeader>

          <div className="py-3">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">Profile completion</span>
              <span className="text-sm text-muted-foreground">
                {completionPercentage}% Complete
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="h-2 rounded-full bg-primary-purple"
                style={{ width: `${completionPercentage}%` }}
              />
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
                <TabsList className="grid grid-cols-4 mb-4">
                  <TabsTrigger value="basic">Basic Info</TabsTrigger>
                  <TabsTrigger value="appearance">Appearance</TabsTrigger>
                  <TabsTrigger value="social">Social Links</TabsTrigger>
                  <TabsTrigger value="privacy">Privacy</TabsTrigger>
                </TabsList>

                {/* Basic Info Tab */}
                <TabsContent value="basic" className="space-y-4">
                  {/* Avatar Upload */}
                  <div className="flex justify-center">
                    <ProfilePhotoUploader
                      previewUrl={previewUrl || profile?.avatar_url || null}
                      onPhotoChange={(file, dataUrl) => {
                        // Set the preview URL
                        if (handleFileChange) {
                          const syntheticEvent = {
                            target: {
                              files: [file]
                            }
                          } as unknown as React.ChangeEvent<HTMLInputElement>;

                          handleFileChange(syntheticEvent);
                        }
                      }}
                      size="md"
                    />
                  </div>

                  {/* Basic Form Fields */}
                  <FormField
                    control={form.control}
                    name="display_name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Display Name</FormLabel>
                        <FormControl>
                          <Input placeholder="Your name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="username"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Username</FormLabel>
                        <FormControl>
                          <Input placeholder="username" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="bio"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bio</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Tell others about yourself"
                            className="resize-none"
                            rows={4}
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                {/* Appearance Tab */}
                <TabsContent value="appearance" className="space-y-4">
                  {/* Cover Photo */}
                  <div className="space-y-2">
                    <FormLabel>Cover Photo</FormLabel>
                    <div className="relative h-48 w-full rounded-lg overflow-hidden bg-muted">
                      {profile.cover_url ? (
                        <img
                          src={profile.cover_url}
                          alt="Cover photo"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full bg-gradient-to-r from-primary-blue/10 to-primary-purple/10 flex items-center justify-center">
                          <p className="text-muted-foreground">No cover photo</p>
                        </div>
                      )}

                      <Button
                        variant="outline"
                        size="sm"
                        className="absolute bottom-2 right-2 bg-white/80 hover:bg-white"
                        onClick={() => setIsEditingCover(true)}
                      >
                        {profile.cover_url ? 'Change Cover' : 'Add Cover'}
                      </Button>
                    </div>
                  </div>

                  {/* Profile Theme */}
                  <FormField
                    control={form.control}
                    name="profile_theme"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Profile Theme</FormLabel>
                        <FormControl>
                          <select
                            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                            {...field}
                            value={field.value || ''}
                          >
                            <option value="">Default</option>
                            <option value="ocean">Ocean Blue</option>
                            <option value="sunset">Sunset</option>
                            <option value="forest">Forest Green</option>
                            <option value="lavender">Lavender</option>
                            <option value="midnight">Midnight</option>
                          </select>
                        </FormControl>
                        <FormDescription>
                          Choose a theme for your profile page
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </TabsContent>

                {/* Social Links Tab */}
                <TabsContent value="social" className="space-y-4">
                  <div className="flex items-center justify-between">
                    <FormLabel className="text-base">Social Links</FormLabel>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={handleAddSocialLink}
                      className="gap-1"
                    >
                      <Plus className="h-4 w-4" />
                      Add Link
                    </Button>
                  </div>

                  {socialLinks.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      No social links added yet. Click "Add Link" to add your social media profiles.
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {socialLinks.map((link, index) => (
                        <div key={index} className="flex items-start gap-2">
                          <div className="flex-1 grid grid-cols-2 gap-2">
                            <Input
                              placeholder="Platform (e.g. Instagram)"
                              value={link.platform}
                              onChange={(e) => handleUpdateSocialLink(index, 'platform', e.target.value)}
                            />
                            <Input
                              placeholder="Username (optional)"
                              value={link.username || ''}
                              onChange={(e) => handleUpdateSocialLink(index, 'username', e.target.value)}
                            />
                            <Input
                              className="col-span-2"
                              placeholder="URL (e.g. https://instagram.com/username)"
                              value={link.url}
                              onChange={(e) => handleUpdateSocialLink(index, 'url', e.target.value)}
                            />
                          </div>
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            onClick={() => handleRemoveSocialLink(index)}
                            className="h-10 w-10 text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </TabsContent>

                {/* Privacy Tab */}
                <TabsContent value="privacy" className="space-y-4">
                  <FormField
                    control={form.control}
                    name="is_profile_public"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Public Profile</FormLabel>
                          <FormDescription>
                            Allow anyone to view your profile without logging in
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <div className="space-y-2">
                    <FormLabel className="text-base">Profile Section Visibility</FormLabel>
                    <FormDescription>
                      Control which sections of your profile are visible to others
                    </FormDescription>

                    <div className="space-y-2 mt-2">
                      <FormField
                        control={form.control}
                        name="profile_visibility.followers"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                            <FormLabel className="font-normal">Followers</FormLabel>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="profile_visibility.following"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                            <FormLabel className="font-normal">Following</FormLabel>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="profile_visibility.activities"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                            <FormLabel className="font-normal">Activities</FormLabel>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="profile_visibility.gallery"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                            <FormLabel className="font-normal">Gallery</FormLabel>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="profile_visibility.about"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                            <FormLabel className="font-normal">About</FormLabel>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </div>
                  </div>
                </TabsContent>
              </Tabs>

              <DialogFooter className="flex flex-wrap items-center justify-between gap-2">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => handleFullOnboarding()}
                        className="flex items-center gap-1"
                      >
                        <RefreshCw className="h-4 w-4" />
                        Full Onboarding
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Start the complete profile setup process</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>

                <div className="flex gap-2 ml-auto">
                  <Button type="button" variant="outline" onClick={onClose}>Cancel</Button>
                  <Button type="submit">Save changes</Button>
                </div>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Cover Photo Uploader Modal */}
      {isEditingCover && (
        <ProfileCoverUploader
          previewUrl={profile.cover_url || null}
          onPhotoChange={handleCoverChange}
          onClose={() => setIsEditingCover(false)}
          isUploading={isUploading}
        />
      )}

      {profile && showFullOnboarding && (
        <OnboardingModal
          open={showFullOnboarding}
          onOpenChange={setShowFullOnboarding}
          user={{ id: profile.id }}
          profile={profile}
          initialStep={onboardingStep}
        />
      )}
    </>
  );
}
