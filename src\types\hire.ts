export interface FriendProfile {
  id: string;
  displayName: string;
  username: string;
  avatarUrl: string;
  bio: string;
  vibes: string[];
  location: string;
  distance: number;
  hourlyRate: number;
  rating: number;
  reviewCount: number;
  isAvailable: boolean;
  hirePurposes: string[];
}

export interface Review {
  id: string;
  reviewerName: string;
  rating: number;
  text: string;
  date: string;
}

export interface BookingData {
  date: Date;
  duration: number;
  purpose: string;
  message: string;
  location: string;
} 