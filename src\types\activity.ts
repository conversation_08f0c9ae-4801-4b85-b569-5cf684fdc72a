import { User } from './user';
import { Category } from './category';

export interface Location {
  x: number;
  y: number;
  lng?: number;
  lat?: number;
  latitude?: number;
  longitude?: number;
  coordinates?: [number, number];
  address?: string;
}

export interface CoordinateLocation {
  x: number;
  y: number;
  latitude?: number;
  longitude?: number;
  lat?: number;
  lng?: number;
}

export interface Activity {
  id: string;
  title: string;
  description: string;
  start_time: string;
  end_time: string;
  location: Location;
  address?: string;
  host_id: string;
  host?: User;
  is_paid: boolean;
  price?: number;
  max_participants?: number;
  current_participants?: number;
  media_urls: string[];
  created_at: string;
  updated_at: string;
  status: 'active' | 'cancelled' | 'completed';
  visibility: 'public' | 'private' | 'unlisted';
  queue_type: 'fcfs' | 'priority' | 'fifo';
  allow_waitlist: boolean;
  group_chat_id?: string;
  moderation_status?: string;
  is_verified?: boolean;
  category?: Category;
}

export interface ActivityCreationData {
  title: string;
  description: string;
  category_id?: string;
  location: Location;
  address?: string;
  start_time: string;
  end_time?: string;
  is_paid: boolean;
  price?: number;
  max_participants?: number;
  media_urls?: string[];
  queue_type?: 'fcfs' | 'priority' | 'fifo';
  visibility?: 'public' | 'private' | 'unlisted';
  allow_waitlist?: boolean;
}

export interface ActivityQueueEntry {
  id: string;
  activity_id: string;
  user_id: string;
  user?: User;
  status: 'confirmed' | 'pending' | 'waitlisted' | 'cancelled';
  position: number;
  created_at: string;
  updated_at: string;
}

export interface ActivityCategory {
  id: string;
  name: string;
  icon?: string;
  type?: string;
}

export interface MediaFile {
  id: string;
  url: string;
  type: 'image' | 'video';
  name: string;
  size: number;
  file?: File;
}

export interface ActivityMapFilters {
  category?: string;
  categoryIds?: string[];
  status?: string;
  radius?: number;
  minPrice?: number;
  maxPrice?: number;
  dateRange?: {
    start?: string;
    end?: string;
  };
  priceRange?: {
    min?: number;
    max?: number;
  };
  distance?: number;
}

export interface QueueManagementStats {
  totalParticipants: number;
  confirmedParticipants: number;
  pendingParticipants: number;
  waitlistedParticipants: number;
  averageWaitTime: number;
  confirmed: number;
  pending: number;
  waitlisted: number;
  total: number;
  maxParticipants: number;
  estimatedWaitTime: number;
  capacityRemaining?: number;
  entries: ActivityQueueEntry[];
}

export interface QueueAnalytics {
  joinRate: number;
  dropoutRate: number;
  conversionRate: number;
  peakHours: string[];
  averageProcessingTime: number;
  avgWaitTime: number;
  acceptanceRate: number;
  averageWaitTime: number;
  popularityScore: number;
  average_wait_time: number;
  conversion_rate: number;
  filled_percentage: number;
  peak_times: {
    hour: number;
    count: number;
  }[];
}

export type { Category };
