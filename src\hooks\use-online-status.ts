
import { useState, useEffect } from 'react';

type OnlineStatus = 'online' | 'offline' | 'unknown';

export function useOnlineStatus() {
  const [status, setStatus] = useState<OnlineStatus>(() => {
    return typeof navigator !== 'undefined' && typeof navigator.onLine === 'boolean'
      ? navigator.onLine
        ? 'online'
        : 'offline'
      : 'unknown';
  });

  useEffect(() => {
    const setOnline = () => setStatus('online');
    const setOffline = () => setStatus('offline');

    window.addEventListener('online', setOnline);
    window.addEventListener('offline', setOffline);

    return () => {
      window.removeEventListener('online', setOnline);
      window.removeEventListener('offline', setOffline);
    };
  }, []);

  return status;
}
