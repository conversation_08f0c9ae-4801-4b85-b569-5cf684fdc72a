
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface Participant {
  user_id: string;
  display_name?: string;
  avatar_url?: string;
  username?: string;
}

export function useParticipants(conversationId?: string) {
  return useQuery({
    queryKey: ['participants', conversationId],
    queryFn: async (): Promise<Participant[]> => {
      if (!conversationId) return [];

      const { data, error } = await supabase
        .from('chat_participants')
        .select('user_id')
        .eq('conversation_id', conversationId);

      if (error) throw error;

      // Fetch user profiles separately to avoid join issues
      const userIds = (data || []).map(p => p.user_id).filter(Boolean);
      if (userIds.length === 0) return [];
      
      const { data: profiles } = await supabase
        .from('profiles')
        .select('id, display_name, avatar_url, username')
        .in('id', userIds);

      const profileMap = new Map(profiles?.map(p => [p.id, p]) || []);

      return (data || []).map(participant => ({
        user_id: participant.user_id,
        display_name: profileMap.get(participant.user_id)?.display_name || '',
        avatar_url: profileMap.get(participant.user_id)?.avatar_url || '',
        username: profileMap.get(participant.user_id)?.username || ''
      })) as Participant[];
    },
    enabled: !!conversationId
  });
}
