
import React, { useState, useEffect } from 'react';
import { OnboardingModal } from './OnboardingModal';
import { OnboardingFormValues } from '@/types/onboarding';
import { UserProfile } from '@/types/user-profile';
import { supabase } from '@/integrations/supabase/client';

interface OnboardingModalWrapperProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: { id: string; email?: string };
  profile: UserProfile | null;
  initialStep?: number;
  forceComplete?: boolean;
  redirectPath?: string;
}

export function OnboardingModalWrapper({
  open,
  onOpenChange,
  user,
  profile,
  initialStep,
  forceComplete = false,
  redirectPath
}: OnboardingModalWrapperProps) {
  const [step, setStep] = useState(initialStep || 1);
  const [formData, setFormData] = useState<Partial<OnboardingFormValues>>({});

  // Pre-populate form data with existing profile info
  useEffect(() => {
    if (profile && open) {
      setFormData({
        display_name: profile.display_name || '',
        username: profile.username || '',
        bio: profile.bio || '',
        avatar_url: profile.avatar_url,
        birthday: profile.birthday ? new Date(profile.birthday) : undefined,
        gender: profile.gender as any,
        purposes: profile.purposes || [],
        vibes: profile.vibes || [],
        gallery: profile.gallery || [],
        favorite_locations: profile.favorite_locations || [],
        defaultLocation: profile.default_location,
        // Use optional chaining to safely access location_display
        location: profile.location_display || '',
        locationPermissionGranted: profile.location_permission_granted,
        notificationsEnabled: profile.notifications_enabled,
        onboarding_completed: profile.onboarding_completed,
      });
    }
  }, [profile, open]);

  return (
    <OnboardingModal
      open={open}
      onOpenChange={async (newOpen) => {
        // If trying to close the modal and we're in force complete mode, verify completion
        if (!newOpen && user && forceComplete) {
          // Check if onboarding is complete
          const { data } = await supabase
            .from('profiles')
            .select('onboarding_completed')
            .eq('id', user.id)
            .single();

          if (data?.onboarding_completed !== true) {
            console.log("Preventing onboarding modal from closing - onboarding not complete");
            return;
          }
        }

        // If not in force complete mode or onboarding is complete, allow the change
        onOpenChange(newOpen);
      }}
      user={user}
      profile={profile}
      initialFormValues={formData}
      initialStep={step}
      onStepChange={setStep}
      allowSkip={false} // Remove the skip button allowSkip to false
      forceComplete={forceComplete}
      redirectPath={redirectPath}
    />
  );
}
