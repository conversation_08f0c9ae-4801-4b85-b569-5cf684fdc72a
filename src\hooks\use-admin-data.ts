import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';

// Define types for admin data
interface AdminMessage {
  id: string;
  content: string;
  sender_id: string;
  is_admin_message: boolean;
  created_at: string;
}

interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  totalActivities: number;
  pendingReports: number;
  activeActivities?: number;
  totalTransactions?: number;
  revenue?: number;
  userGrowth?: number;
  activityGrowth?: number;
}

// Define the user type for admin panel
interface AdminUser {
  id: string;
  display_name: string;
  avatar_url: string | null;
  created_at: string;
  is_admin: boolean;
  is_banned: boolean;
  email?: string;
}

// Define the activity type for admin panel
interface AdminActivity {
  id: string;
  title: string;
  host_id: string;
  host_name: string;
  start_time: string | null;
  is_paid: boolean;
  price: number | null;
  status: 'active' | 'cancelled' | 'completed';
  flag_count: number;
  moderation_status: 'pending' | 'approved' | 'rejected';
}

// Hook to check if the current user is an admin
export function useIsAdmin() {
  const { user } = useAuth();
  
  return useQuery({
    queryKey: ['is-admin', user?.id],
    queryFn: async () => {
      if (!user) return false;
      
      // First try to use the RPC function
      try {
        const { data: rpcData, error: rpcError } = await supabase.rpc('is_admin');
        
        if (!rpcError) {
          return rpcData;
        }
      } catch (e) {
        console.error('Error calling is_admin RPC:', e);
      }
      
      // Fallback to direct query if RPC fails
      const { data, error } = await supabase
        .from('profiles')
        .select('is_admin')
        .eq('id', user.id)
        .single();
      
      if (error) {
        console.error('Error checking admin status:', error);
        return false;
      }
      
      return data?.is_admin || false;
    },
    enabled: !!user
  });
}

// Hook for fetching admin messages
export function useAdminMessages() {
  return useQuery({
    queryKey: ['admin-messages'],
    queryFn: async (): Promise<AdminMessage[]> => {
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('is_admin_message', true)
        .order('created_at', { ascending: false });
        
      if (error) throw error;
      return data || [];
    }
  });
}

// Hook for fetching admin dashboard statistics
export function useAdminStats() {
  return useQuery({
    queryKey: ['admin-stats'],
    queryFn: async (): Promise<AdminStats> => {
      // Try to use direct counts for basic stats
      try {
        // Try to get pending reports count
        let pendingReports = 0;
        try {
          // Manual count for pending reports using messages table
          const { count, error } = await supabase
            .from('messages')
            .select('*', { count: 'exact', head: true })
            .eq('is_admin_message', true);
          
          if (error) {
            console.error('Error fetching pending reports count:', error);
          } else {
            pendingReports = count || 0;
          }
        } catch (e) {
          console.error('Error fetching pending reports count:', e);
        }
        
        // Fetch total users
        const { count: totalUsers, error: usersError } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true });
        
        if (usersError) throw usersError;
        
        // Fetch active users (active in last 7 days)
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7);
        
        const { count: activeUsers, error: activeError } = await supabase
          .from('profiles')
          .select('*', { count: 'exact', head: true })
          .gt('last_seen', sevenDaysAgo.toISOString());
        
        if (activeError) throw activeError;
        
        // Fetch total activities
        const { count: totalActivities, error: activitiesError } = await supabase
          .from('activities')
          .select('*', { count: 'exact', head: true });
        
        if (activitiesError) throw activitiesError;
        
        // Fetch active activities (not cancelled or completed)
        const { count: activeActivities, error: activeActivitiesError } = await supabase
          .from('activities')
          .select('*', { count: 'exact', head: true })
          .not('status', 'eq', 'cancelled')
          .not('status', 'eq', 'completed');
        
        if (activeActivitiesError) throw activeActivitiesError;
        
        return {
          totalUsers: totalUsers || 0,
          activeUsers: activeUsers || 0,
          totalActivities: totalActivities || 0,
          activeActivities: activeActivities || 0,
          pendingReports: pendingReports,
          userGrowth: 5, // Mock data - would be calculated from historical data
          activityGrowth: 8, // Mock data - would be calculated from historical data
          revenue: 15820, // Mock data - would be calculated from transactions
          totalTransactions: 425 // Mock data - would be calculated from transactions
        };
      } catch (error) {
        console.error('Error fetching admin stats:', error);
        
        // Return default values on error
        return {
          totalUsers: 0,
          activeUsers: 0,
          totalActivities: 0,
          pendingReports: 0,
          userGrowth: 0,
          activityGrowth: 0
        };
      }
    }
  });
}

// Hook for fetching users for admin panel
export function useAdminUsers(searchQuery: string = '') {
  return useQuery({
    queryKey: ['admin-users', searchQuery],
    queryFn: async (): Promise<AdminUser[]> => {
      try {
        let query = supabase
          .from('profiles')
          .select('id, display_name, avatar_url, created_at, is_admin, banned_until')
          .order('created_at', { ascending: false });
        
        if (searchQuery) {
          query = query.or(`display_name.ilike.%${searchQuery}%`);
        }
        
        const { data, error } = await query;
        
        if (error) throw error;
        
        return (data || []).map(user => ({
          id: user.id,
          display_name: user.display_name || 'Unnamed User',
          avatar_url: user.avatar_url,
          created_at: user.created_at,
          is_admin: user.is_admin || false,
          is_banned: user.banned_until ? new Date(user.banned_until) > new Date() : false
        }));
      } catch (error) {
        console.error('Error fetching admin users:', error);
        return [];
      }
    }
  });
}

// Hook for fetching activities for admin panel
export function useAdminActivities(searchQuery: string = '') {
  return useQuery({
    queryKey: ['admin-activities', searchQuery],
    queryFn: async (): Promise<AdminActivity[]> => {
      try {
        // First query activities table
        let query = supabase
          .from('activities')
          .select(`
            id,
            title,
            host_id,
            start_time,
            is_paid,
            price,
            status
          `)
          .order('created_at', { ascending: false });
        
        if (searchQuery) {
          query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
        }
        
        const { data, error } = await query;
        
        if (error) {
          console.error('Error fetching admin activities:', error);
          return [];
        }
        
        if (!data) {
          return [];
        }
        
        // Now get host display names for each activity
        const enhancedData = await Promise.all(data.map(async (activity) => {
          // Get host info
          let hostName = 'Unknown Host';
          try {
            const { data: hostData } = await supabase
              .from('profiles')
              .select('display_name')
              .eq('id', activity.host_id)
              .single();
              
            if (hostData) {
              hostName = hostData.display_name || hostName;
            }
          } catch (err) {
            console.error(`Error fetching host for activity ${activity.id}:`, err);
          }
          
          // Ensure status is one of the valid types
          let typedStatus: 'active' | 'cancelled' | 'completed' = 'active';
          if (activity.status === 'cancelled' || activity.status === 'completed') {
            typedStatus = activity.status;
          }
          
          // Add default moderation status since the column might not exist
          const typedModerationStatus: 'pending' | 'approved' | 'rejected' = 'pending';
          
          return {
            id: activity.id,
            title: activity.title,
            host_id: activity.host_id,
            host_name: hostName,
            start_time: activity.start_time,
            is_paid: activity.is_paid,
            price: activity.price,
            status: typedStatus,
            flag_count: 0, // Default since the column might not exist
            moderation_status: typedModerationStatus
          };
        }));
        
        return enhancedData;
      } catch (error) {
        console.error('Error fetching admin activities:', error);
        return [];
      }
    }
  });
}

// Hook for sending admin announcements
export function useAdminAnnouncement() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: async ({ 
      content, 
      senderId 
    }: { 
      content: string; 
      senderId: string;
    }) => {
      // Create a system-wide announcement
      const { data, error } = await supabase
        .from('messages')
        .insert({
          content,
          sender_id: senderId,
          is_admin_message: true,
          is_system_announcement: true,
          created_at: new Date().toISOString()
        })
        .select()
        .single();
        
      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-messages'] });
      toast({
        title: "Announcement sent",
        description: "Your announcement has been sent to all users"
      });
    },
    onError: (error) => {
      toast({
        title: "Error sending announcement",
        description: error.message,
        variant: "destructive"
      });
    }
  });
}

// Hook for fetching admin reports
export function useAdminReports() {
  return useQuery({
    queryKey: ['admin-reports'],
    queryFn: async () => {
      try {
        // Try using messages table as a fallback for content reports
        const { data, error } = await supabase
          .from('messages')
          .select(`
            *,
            sender:sender_id(display_name, avatar_url)
          `)
          .eq('is_admin_message', true)
          .order('created_at', { ascending: false });
            
        if (error) {
          console.error('Error fetching reports:', error);
          return [];
        }
        
        return data || [];
      } catch (error) {
        console.error('Error in useAdminReports:', error);
        return [];
      }
    }
  });
}

// Hook for handling report resolution
export function useReportResolution() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: async ({ 
      reportId, 
      resolution, 
      adminId 
    }: { 
      reportId: string; 
      resolution: string;
      adminId: string;
    }) => {
      // Update a message to mark it as resolved
      try {
        const { data, error } = await supabase
          .from('messages')
          .update({ 
            // Use fields that are allowed in the messages table
            content: `RESOLVED: ${resolution}`,
            updated_at: new Date().toISOString()
          })
          .eq('id', reportId)
          .select()
          .single();
          
        if (error) throw error;
        return data;
      } catch (error) {
        console.error('Error resolving report:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-reports'] });
      toast({
        title: "Report resolved",
        description: "The report has been marked as resolved"
      });
    },
    onError: (error) => {
      toast({
        title: "Error resolving report",
        description: error.message,
        variant: "destructive"
      });
    }
  });
}

// Function to fetch admin messages
const fetchAdminMessages = async () => {
  try {
    const { data, error } = await supabase
      .from('messages')
      .select('*')
      .eq('is_admin_message', true)
      .order('created_at', { ascending: false });
      
    if (error) throw error;
    return data || [];
  } catch (error) {
    console.error('Error fetching admin messages:', error);
    return [];
  }
};

// Function to process message data safely
const processMessageData = (message: any) => {
  // Safely handle potentially non-object values
  const baseMessage = typeof message === 'object' && message !== null ? message : {};
  
  return {
    ...baseMessage,
    processed: true,
    processed_at: new Date().toISOString(),
  };
};

export {
  fetchAdminMessages,
  processMessageData
};
