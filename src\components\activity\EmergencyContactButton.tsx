
import React from 'react';
import { Button } from '@/components/ui/button';
import { AlertCircle } from 'lucide-react';
import { useEmergencyContact } from '@/hooks/use-emergency-contact';
import { useToast } from '@/hooks/use-toast';

export function EmergencyContactButton() {
  const { toast } = useToast();
  const { emergencyContact, hasEmergencyContact } = useEmergencyContact();
  
  const handleEmergencyClick = () => {
    // In a real implementation, this would trigger an emergency notification
    // For now, we'll just show a toast
    toast({
      title: "Emergency Contact Shared",
      description: "Your location and activity details would be shared with emergency contacts.",
      variant: "destructive"
    });
  };
  
  if (!hasEmergencyContact) {
    return null;
  }
  
  return (
    <Button 
      variant="outline" 
      className="w-full bg-red-50 border-red-200 text-red-600 hover:bg-red-100 hover:text-red-700"
      onClick={handleEmergencyClick}
    >
      <AlertCircle className="h-4 w-4 mr-2" />
      Emergency Help
    </Button>
  );
}
