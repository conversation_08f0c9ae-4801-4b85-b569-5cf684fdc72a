
import { useMemo } from 'react';
import { Activity } from './use-activities';
import { isPast, isToday, isWithinInterval, addDays, isAfter } from 'date-fns';

export interface ActivityFilters {
  selectedCategories: string[];
  showPaidActivities: boolean;
  showFreeActivities: boolean;
  showTodayOnly: boolean;
  showUpcomingOnly: boolean;
  showPopular?: boolean;
  showNearby?: boolean;
  radiusInKm?: number;
  userLocation?: { x: number; y: number } | null;
  dateRange?: [Date | null, Date | null];
  participantCount?: number;
  searchText?: string;
}

export function useFilteredActivities(activities: Activity[] | undefined, filters: ActivityFilters) {
  return useMemo(() => {
    if (!activities) return [];

    return activities.filter(activity => {
      // Category filter
      const matchesCategory = filters.selectedCategories.length === 0 || 
        filters.selectedCategories.includes(activity.category?.id || '');
      
      // Pricing filter
      const matchesPricing = 
        (filters.showPaidActivities && activity.is_paid) ||
        (filters.showFreeActivities && !activity.is_paid);
      
      // Date filters
      const activityDate = new Date(activity.start_time);
      const matchesToday = !filters.showTodayOnly || isToday(activityDate);
      
      // Upcoming filter - activities within next 30 days
      const matchesUpcoming = !filters.showUpcomingOnly || 
        (!isPast(activityDate) && 
         isWithinInterval(activityDate, { 
           start: new Date(), 
           end: addDays(new Date(), 30)
         }));
      
      // Date range filter if provided
      let matchesDateRange = true;
      if (filters.dateRange && (filters.dateRange[0] || filters.dateRange[1])) {
        const [startDate, endDate] = filters.dateRange;
        if (startDate && endDate) {
          matchesDateRange = isWithinInterval(activityDate, { start: startDate, end: endDate });
        } else if (startDate) {
          matchesDateRange = isAfter(activityDate, startDate) || isToday(startDate);
        } else if (endDate) {
          matchesDateRange = isWithinInterval(activityDate, { start: new Date(), end: endDate });
        }
      }

      // Location filter if provided
      let matchesLocation = true;
      if (filters.showNearby && filters.userLocation && filters.radiusInKm) {
        // Earth radius in km
        const earthRadius = 6371;
        
        // Convert coordinates to radians
        const lat1 = filters.userLocation.y * Math.PI / 180;
        const lon1 = filters.userLocation.x * Math.PI / 180;
        const lat2 = activity.location.y * Math.PI / 180;
        const lon2 = activity.location.x * Math.PI / 180;
        
        // Haversine formula
        const dLat = lat2 - lat1;
        const dLon = lon2 - lon1;
        const a = 
          Math.sin(dLat/2) * Math.sin(dLat/2) +
          Math.cos(lat1) * Math.cos(lat2) * 
          Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        const distance = earthRadius * c;
        
        matchesLocation = distance <= filters.radiusInKm;
      }

      // Search text filter
      const matchesSearch = !filters.searchText || 
        activity.title.toLowerCase().includes(filters.searchText.toLowerCase()) ||
        (activity.description?.toLowerCase() || '').includes(filters.searchText.toLowerCase()) ||
        (activity.address?.toLowerCase() || '').includes(filters.searchText.toLowerCase());

      // Combine all filters
      return matchesCategory && 
             matchesPricing && 
             matchesToday && 
             matchesUpcoming && 
             matchesDateRange && 
             matchesLocation && 
             matchesSearch;
    });
  }, [activities, filters]);
}
