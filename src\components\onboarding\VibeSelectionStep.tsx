
import React, { useState, useEffect } from 'react';
import { FormField, FormItem, FormControl } from "@/components/ui/form";
import { VibeSelectionStepProps } from "@/types/onboarding-props";
import { allVibes, vibeCategories } from "@/types/onboarding";
import { But<PERSON> } from "@/components/ui/button";
import { toast } from "sonner";
import { Check, X } from "lucide-react";

export function VibeSelectionStep({ form, onSubmit, isLoading, initialValues }: VibeSelectionStepProps) {
  const selectedVibes = form.watch('vibes') || [];
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [showMaxWarning, setShowMaxWarning] = useState(false);

  // Maximum number of vibes that can be selected
  const MAX_VIBES = 5;

  // Get unique categories from all vibes
  const categories = Object.keys(vibeCategories);

  // Set initial active category
  useEffect(() => {
    if (!activeCategory && categories.length > 0) {
      setActiveCategory(categories[0]);
    }
    
    // Set initial vibes from initialValues if they exist
    if (initialValues?.vibes?.length && (!selectedVibes || selectedVibes.length === 0)) {
      form.setValue('vibes', initialValues.vibes);
    }
  }, []);

  const toggleVibe = (vibeId: string) => {
    const currentVibes = [...(selectedVibes || [])];
    const index = currentVibes.indexOf(vibeId);

    if (index === -1) {
      // If not already selected, try to add it
      if (currentVibes.length < MAX_VIBES) {
        currentVibes.push(vibeId);
        setShowMaxWarning(false);
      } else {
        // Show warning if trying to select more than the maximum
        setShowMaxWarning(true);
        toast.warning(`You can select up to ${MAX_VIBES} vibes only`);
        return;
      }
    } else {
      // If already selected, remove it
      currentVibes.splice(index, 1);
      setShowMaxWarning(false);
    }

    form.setValue('vibes', currentVibes);
  };

  // Filter vibes by active category
  const filteredVibes = activeCategory && vibeCategories[activeCategory as keyof typeof vibeCategories]
    ? vibeCategories[activeCategory as keyof typeof vibeCategories]
    : allVibes;

  const handleSubmit = async () => {
    try {
      const values = form.getValues();
      console.log("VibeSelectionStep submitting:", values);
      await onSubmit(values);
    } catch (error) {
      console.error("Error in VibeSelectionStep:", error);
      toast.error("Failed to save your vibes. Please try again.");
    }
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-4">
        <h2 className="text-xl font-semibold">Your Vibe</h2>
        <p className="text-sm text-muted-foreground mt-1">
          Select up to {MAX_VIBES} vibes that represent you
        </p>
      </div>

      {/* Category tabs */}
      <div className="flex overflow-x-auto pb-2 scrollbar-hide -mx-1 px-1">
        <div className="flex space-x-2">
          {categories.map((category) => (
            <Button
              key={category}
              variant={activeCategory === category ? "default" : "outline"}
              size="sm"
              onClick={() => setActiveCategory(category)}
              className={`whitespace-nowrap ${
                activeCategory === category
                  ? 'bg-primary-purple text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {category.charAt(0).toUpperCase() + category.slice(1)}
            </Button>
          ))}
        </div>
      </div>

      {/* Selection counter */}
      <div className={`flex items-center justify-between p-2 rounded-lg transition-colors ${
        showMaxWarning ? 'bg-amber-50 border border-amber-200' : 'bg-primary-purple/5'
      }`}>
        <span className="text-sm font-medium">
          {showMaxWarning ? 'Maximum vibes reached' : 'Selected vibes'}
        </span>
        <span className={`font-medium px-3 py-1 rounded-md border ${
          selectedVibes.length === MAX_VIBES
            ? 'bg-primary-purple text-white border-primary-purple'
            : 'bg-white text-primary-purple border-primary-purple/20'
        }`}>
          {selectedVibes.length}/{MAX_VIBES}
        </span>
      </div>

      <FormField
        control={form.control}
        name="vibes"
        render={() => (
          <FormItem>
            <FormControl>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 gap-2 max-h-[320px] overflow-y-auto pr-1">
                {filteredVibes.map((vibe) => {
                  const isSelected = selectedVibes.includes(vibe.id);
                  return (
                    <div
                      key={vibe.id}
                      className={`
                        flex items-center p-3 rounded-md border cursor-pointer
                        transition-all duration-200 relative
                        ${isSelected
                          ? 'border-primary-purple bg-primary-purple/10 shadow-sm'
                          : 'border-gray-200 hover:border-gray-300'}
                      `}
                      onClick={() => toggleVibe(vibe.id)}
                    >
                      <div className="mr-3 text-xl">{vibe.icon}</div>
                      <div className="flex-1 min-w-0">
                        <div className="font-medium truncate pr-6">{vibe.label}</div>
                        <div className="text-xs text-muted-foreground">
                          {vibe.id.split('_')[0]}
                        </div>
                      </div>
                      {isSelected && (
                        <div className="absolute right-2 top-2 bg-primary-purple text-white rounded-full p-0.5">
                          <Check size={14} />
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </FormControl>
          </FormItem>
        )}
      />

      {/* Selected vibes list */}
      {selectedVibes.length > 0 && (
        <div className="mt-4">
          <h3 className="text-sm font-medium mb-2">Your selected vibes:</h3>
          <div className="flex flex-wrap gap-2">
            {selectedVibes.map((vibeId) => {
              const vibe = allVibes.find(v => v.id === vibeId);
              if (!vibe) return null;

              return (
                <div
                  key={vibeId}
                  className="flex items-center bg-primary-purple/10 text-primary-purple rounded-full pl-2 pr-1 py-1"
                >
                  <span className="mr-1">{vibe.icon}</span>
                  <span className="text-xs font-medium">{vibe.label}</span>
                  <button
                    className="ml-1 p-0.5 hover:bg-primary-purple/20 rounded-full"
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleVibe(vibeId);
                    }}
                  >
                    <X size={12} />
                  </button>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
