// <PERSON>ript to create the avatar bucket in Supabase storage
import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config({ path: path.resolve(__dirname, '../.env.local') });

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Supabase URL or key not found in environment variables');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function createAvatarBucket() {
  try {
    console.log('Checking if avatar bucket exists...');

    // List all buckets
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();

    if (listError) {
      console.error('Error listing buckets:', listError);
      process.exit(1);
    }

    // Check if avatar bucket exists
    const avatarBucket = buckets.find(bucket => bucket.name === 'avatars');

    if (avatarBucket) {
      console.log('Avatar bucket already exists');
    } else {
      console.log('Creating avatar bucket...');

      // Create avatar bucket
      const { data, error } = await supabase.storage.createBucket('avatars', {
        public: true,
        fileSizeLimit: 5242880, // 5MB
        allowedMimeTypes: ['image/png', 'image/jpeg', 'image/gif', 'image/webp']
      });

      if (error) {
        console.error('Error creating avatar bucket:', error);
        process.exit(1);
      }

      console.log('Avatar bucket created successfully');
    }

    // Update bucket policies to make it public readable
    console.log('Updating bucket read policies...');

    const { error: readPolicyError } = await supabase.storage.from('avatars').createPolicy(
      'public-read',
      {
        name: 'Public Read Access',
        definition: {
          statements: [
            {
              effect: 'allow',
              action: 'object:get',
              principal: '*'
            }
          ]
        }
      }
    );

    if (readPolicyError) {
      console.error('Error updating bucket read policies:', readPolicyError);
      process.exit(1);
    }

    console.log('Bucket read policies updated successfully');

    // Add write policy for authenticated users
    console.log('Adding write policy for authenticated users...');

    const { error: writePolicyError } = await supabase.storage.from('avatars').createPolicy(
      'authenticated-write',
      {
        name: 'Authenticated Write Access',
        definition: {
          statements: [
            {
              effect: 'allow',
              action: 'object:create',
              principal: 'authenticated'
            },
            {
              effect: 'allow',
              action: 'object:update',
              principal: 'authenticated'
            },
            {
              effect: 'allow',
              action: 'object:delete',
              principal: 'authenticated'
            }
          ]
        }
      }
    );

    if (writePolicyError) {
      console.error('Error updating bucket write policies:', writePolicyError);
      process.exit(1);
    }

    console.log('Bucket write policies updated successfully');
    console.log('Avatar bucket setup complete!');
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

createAvatarBucket();
