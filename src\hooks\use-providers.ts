
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

// Define ServiceProvider interface for typing
export interface ServiceProvider {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  avatar_url?: string;
  display_name?: string;
  username?: string;
  rating?: number;
  total_reviews?: number;
  services?: any[];
  trust_score?: number;
  verification_status?: string;
  verification_level_id?: string;
  hourly_rate?: number;
  created_at: string;
  updated_at: string;
  profiles?: {
    avatar_url?: string;
    display_name?: string;
    username?: string;
    bio?: string; // Added the bio property
  };
}

export function useProviders() {
  // Mock implementation since service_providers table might not exist
  const { data: providers, isLoading } = useQuery({
    queryKey: ['service-providers'],
    queryFn: async (): Promise<ServiceProvider[]> => {
      try {
        // This is mock code to simulate fetching from a database
        // In a real implementation, we would query the service_providers table
        
        // Mock data
        return [
          {
            id: '1',
            user_id: '1',
            name: '<PERSON>',
            description: 'Professional services for all your needs',
            avatar_url: 'https://example.com/avatar.jpg',
            display_name: '<PERSON>e',
            username: 'johndoe',
            rating: 4.8,
            total_reviews: 24,
            services: [],
            trust_score: 85,
            verification_status: 'verified',
            verification_level_id: '1',
            hourly_rate: 50,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            profiles: {
              avatar_url: 'https://example.com/avatar.jpg',
              display_name: 'John Doe',
              username: 'johndoe'
            }
          }
        ];
      } catch (error) {
        console.error('Error fetching providers:', error);
        return [];
      }
    }
  });

  return {
    providers: providers || [],
    isLoading
  };
}
