
import React, { useState } from 'react';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2, Plus } from "lucide-react";
import { useAddFunds } from "@/hooks/use-add-funds";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface AddFundsDialogProps {
  trigger?: React.ReactNode;
}

export function AddFundsDialog({ trigger }: AddFundsDialogProps) {
  const { mutate: addFunds, isPending } = useAddFunds();
  const [open, setOpen] = useState(false);

  const handleAddFunds = () => {
    addFunds();
    // The dialog will be closed by the redirect to Stripe
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Add Funds
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Add Funds to Wallet</DialogTitle>
          <DialogDescription>
            Add money to your BuddySurf wallet to join paid activities and access premium features.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="amount" className="text-right">
              Amount
            </Label>
            <div className="col-span-3 relative">
              <span className="absolute left-3 top-1/2 -translate-y-1/2">$</span>
              <Input
                id="amount"
                defaultValue="25.00"
                className="pl-7"
                disabled // We're using a fixed amount for now
              />
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button onClick={handleAddFunds} disabled={isPending}>
            {isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              'Continue to Payment'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
