
import React, { useState } from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { UseFormReturn } from "react-hook-form";
import { OnboardingFormValues } from "@/types/onboarding";
import { MapPin } from "lucide-react";
import { EnhancedMapLocationPicker } from '@/components/map/EnhancedMapLocationPicker';
import { useMapboxToken } from '@/hooks/use-mapbox-token';
import { MapLocation } from '@/types/location';

interface BioLocationStepProps {
  form: UseFormReturn<OnboardingFormValues>;
}

export function BioLocationStep({ form }: BioLocationStepProps) {
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null);
  const { token: mapboxToken, isLoading } = useMapboxToken();

  const handleLocationSelect = (location: MapLocation) => {
    setSelectedLocation(location);

    // Use the address for display purposes, not for database storage
    form.setValue('location', location.address || '');

    // We'll update actual coordinates elsewhere in the app
    // This approach prevents PostgreSQL point errors
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold">About you</h2>
        <p className="text-sm text-muted-foreground mt-1">
          Tell others more about you
        </p>
      </div>

      <FormField
        control={form.control}
        name="bio"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Bio <span className="text-muted-foreground text-xs font-normal">(Optional)</span></FormLabel>
            <FormDescription>
              Share a little about yourself, your interests, or what brings you here. You can skip this if you prefer.
            </FormDescription>
            <FormControl>
              <Textarea
                placeholder="Tell others about yourself"
                className="resize-none"
                rows={4}
                {...field}
                value={field.value || ''}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="location"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Location</FormLabel>
            <FormDescription>
              Your city or general area (this helps connect you with nearby events)
            </FormDescription>
            <FormControl>
              <div className="space-y-4">
                <div className="relative">
                  <MapPin className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Your city or location"
                    className="pl-10"
                    value={selectedLocation?.address || field.value || ''}
                    onChange={(e) => {
                      field.onChange(e.target.value);
                      if (!e.target.value) {
                        setSelectedLocation(null);
                      }
                    }}
                  />
                </div>

                {mapboxToken && (
                  <EnhancedMapLocationPicker
                    onLocationSelect={handleLocationSelect}
                    className="mt-4"
                    title="Find your location"
                    description="Search for your location or adjust the marker on the map"
                    mapboxToken={mapboxToken}
                  />
                )}

                {isLoading && (
                  <div className="text-sm text-muted-foreground text-center py-4">
                    Loading map...
                  </div>
                )}
              </div>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
