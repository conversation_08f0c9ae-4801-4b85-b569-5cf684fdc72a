
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useQueueWrapper } from '@/hooks/use-queue-wrapper';
import { useAuth } from '@/hooks/use-auth';
import { Activity } from '@/types/activity';
import { Badge } from '@/components/ui/badge';
import { Loader2, Check, Users, ListOrdered, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useJoinQueue, useLeaveQueue } from '@/hooks/use-activity-queue';

interface ActivityQueueSystemProps {
  activity: Activity;
}

export function ActivityQueueSystem({ activity }: ActivityQueueSystemProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const joinQueueMutation = useJoinQueue();
  const leaveQueueMutation = useLeaveQueue();

  // Use the queue wrapper to manage queue operations
  const queueManager = useQueueWrapper(activity.id);
  
  const { 
    isProcessing,
    userEntry,
    stats,
    queueStats
  } = queueManager;

  const handleJoinQueue = async () => {
    setIsLoading(true);
    try {
      if (!user) throw new Error("You must be logged in");
      await joinQueueMutation.mutateAsync({
        activityId: activity.id,
        userId: user.id
      });
      toast({
        title: "Success",
        description: "You've joined the activity queue"
      });
    } catch (error) {
      console.error("Error joining queue:", error);
      toast({
        title: "Error",
        description: "Failed to join the queue. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleLeaveQueue = async () => {
    setIsLoading(true);
    try {
      if (!userEntry) throw new Error("No queue entry found");
      await leaveQueueMutation.mutateAsync(userEntry.id);
      toast({
        title: "Success",
        description: "You've left the activity queue"
      });
    } catch (error) {
      console.error("Error leaving queue:", error);
      toast({
        title: "Error",
        description: "Failed to leave the queue. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Don't show anything if user is the host
  if (user?.id === activity.host_id) {
    return null;
  }

  // If the activity doesn't have a queue system
  // Check only allow_waitlist as allow_queue might not be defined
  if (activity.allow_waitlist === false) {
    return null;
  }

  const renderQueueStatusBadge = () => {
    if (!userEntry) return null;
    
    switch (userEntry.status) {
      case 'confirmed':
        return (
          <Badge className="bg-green-500">
            <Check className="mr-1 h-3 w-3" />
            Confirmed
          </Badge>
        );
      case 'pending':
        return (
          <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
            <ListOrdered className="mr-1 h-3 w-3" />
            Pending Approval
          </Badge>
        );
      case 'waitlisted':
        return (
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Users className="mr-1 h-3 w-3" />
            Waitlisted #{userEntry.position}
          </Badge>
        );
      default:
        return null;
    }
  };

  // Use queueStats if stats is not available
  const displayStats = stats || queueStats;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Join Activity</span>
          {renderQueueStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="text-sm space-y-2">
          {displayStats && (
            <>
              <div className="flex justify-between">
                <span className="text-muted-foreground">Confirmed participants:</span>
                <span className="font-medium">{displayStats.confirmed}/{activity.max_participants || "∞"}</span>
              </div>
              
              {displayStats.pending > 0 && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Pending approval:</span>
                  <span className="font-medium">{displayStats.pending}</span>
                </div>
              )}
              
              {displayStats.waitlisted > 0 && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Waitlisted:</span>
                  <span className="font-medium">{displayStats.waitlisted}</span>
                </div>
              )}
            </>
          )}
        </div>
      </CardContent>
      <CardFooter>
        {userEntry ? (
          <Button 
            variant="outline" 
            className="w-full" 
            onClick={handleLeaveQueue} 
            disabled={isLoading || isProcessing}
          >
            {isLoading || isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <X className="mr-2 h-4 w-4" />
                Leave Queue
              </>
            )}
          </Button>
        ) : (
          <Button 
            className="w-full" 
            onClick={handleJoinQueue} 
            disabled={isLoading || isProcessing}
          >
            {isLoading || isProcessing ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Processing...
              </>
            ) : (
              <>
                <Users className="mr-2 h-4 w-4" />
                Join Activity Queue
              </>
            )}
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
