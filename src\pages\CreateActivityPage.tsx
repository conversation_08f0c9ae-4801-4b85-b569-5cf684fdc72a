import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { StepActivityCreationModal } from '@/components/activity/StepActivityCreationModal';
import { useAuth } from '@/hooks/use-auth';
import { withActivityRouter } from '@/components/activity/ActivityRouter';
import { Plus, ArrowLeft } from 'lucide-react';

function CreateActivityPage() {
  const [showModal, setShowModal] = useState(false);
  const navigate = useNavigate();
  const { user, isLoading } = useAuth();

  // Automatically show the modal when the page loads
  useEffect(() => {
    if (!isLoading) {
      setShowModal(true);
    }
  }, [isLoading]);

  // Redirect to login if not authenticated
  useEffect(() => {
    if (!isLoading && !user) {
      navigate('/login?redirect=/activity/create');
    }
  }, [user, isLoading, navigate]);

  const handleSuccess = (activityId: string) => {
    // Navigate to the activity details page
    navigate(`/activity/${activityId}`);
  };

  const handleClose = () => {
    // Close the modal and navigate back
    setShowModal(false);
    navigate(-1);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <Button
        variant="ghost"
        className="mb-4"
        onClick={() => navigate(-1)}
      >
        <ArrowLeft className="h-4 w-4 mr-2" />
        Back
      </Button>

      <Card>
        <CardHeader>
          <CardTitle>Create Activity</CardTitle>
          <CardDescription>
            Create a new activity for others to join
          </CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center">
          <Button onClick={() => setShowModal(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create New Activity
          </Button>
        </CardContent>
      </Card>

      <StepActivityCreationModal
        isOpen={showModal}
        onClose={handleClose}
        onSuccess={handleSuccess}
      />
    </div>
  );
}

// Wrap with ActivityRouter to enable activity routing functionality
export default withActivityRouter(CreateActivityPage);
