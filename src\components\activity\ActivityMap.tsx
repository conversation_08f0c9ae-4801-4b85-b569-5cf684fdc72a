
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Activity, Location } from '@/types/activity';
import { MapPin } from 'lucide-react';

interface ActivityMapProps {
  activity: Activity;
}

export function ActivityMap({ activity }: ActivityMapProps) {
  // If there's no location data, don't render the map
  if (!activity.location && !activity.address) {
    return null;
  }
  
  // Format location coordinates using the correct type
  const location = activity.location as Location;
  const latitude = location?.latitude;
  const longitude = location?.longitude;
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Location
        </CardTitle>
      </CardHeader>
      <CardContent>
        {activity.address && (
          <div className="mb-4">
            <h4 className="text-sm font-medium">Address</h4>
            <p className="text-muted-foreground">{activity.address}</p>
          </div>
        )}
        
        {/* Placeholder for map - in a real app, you'd render a proper map component here */}
        <div className="bg-muted rounded-md h-48 flex items-center justify-center">
          <div className="text-center text-muted-foreground">
            <MapPin className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>Map would be displayed here</p>
            {latitude !== undefined && longitude !== undefined && (
              <p className="text-xs mt-2">
                {latitude.toFixed(6)}, {longitude.toFixed(6)}
              </p>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
