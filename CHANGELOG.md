

# BuddySurf Changelog

_Track release notes, features, and bugfixes here._

## [Unreleased]

### Added
- Complete Activity Creation Modal with all required fields
- Full Activity Queue System for joining activities
- Real-time queue position updates and notifications
- Support for paid activities with payment processing
- Queue management tools for activity hosts
- Activity join button component for easy integration
- Background recalculation of queue positions

### Improved
- Enhanced 3D building LOD transitions with smoother zoom levels
- Better color and height interpolation for buildings
- More detailed building ambient occlusion based on zoom level

### Fixed
- Fixed issue with queue position calculation
- Improved error handling in activity creation

## [0.1.0] - 2023-04-20

### Added
- Initial application structure
- Basic map functionality
- User profiles
- Activity creation

