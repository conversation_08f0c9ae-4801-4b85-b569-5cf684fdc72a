
import React, { useRef, useEffect } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { cn } from '@/lib/utils';
import { Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Expand } from 'lucide-react';

interface MiniMapProps {
  location: { x: number; y: number };
  mapboxToken?: string;
  className?: string;
  title?: string;
  showExpandButton?: boolean;
  markerLabel?: string;
  markerColor?: string;
  zoom?: number;
  activityMarkers?: Array<{ x: number; y: number; label?: string; color?: string }>;
}

const MiniMap = ({ 
  location, 
  mapboxToken, 
  className, 
  title,
  showExpandButton = true,
  markerLabel,
  markerColor = '#0EA5E9',
  zoom = 13,
  activityMarkers = []
}: MiniMapProps) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const marker = useRef<mapboxgl.Marker | null>(null);
  const activityMarkerRefs = useRef<mapboxgl.Marker[]>([]);

  useEffect(() => {
    if (!mapboxToken || !mapContainer.current || map.current) return;
    
    try {
      mapboxgl.accessToken = mapboxToken;
      
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [location.x, location.y],
        zoom: zoom,
        interactive: true, // Allow basic interaction
        pitch: 30, // Add a slight 3D effect
        attributionControl: false, // Hide attribution for cleaner look
      });

      // Add minimal navigation controls
      const navControl = new mapboxgl.NavigationControl({
        showCompass: false,
        showZoom: true,
        visualizePitch: false,
      });
      
      map.current.addControl(navControl, 'top-right');
      
      // Allow zooming but limit other interactions for better UX
      map.current.scrollZoom.enable();
      map.current.dragPan.enable();
      map.current.dragRotate.disable(); // Disable rotation for simplicity
      
      // Add a marker for the main location
      const markerElement = document.createElement('div');
      markerElement.className = 'flex items-center justify-center';
      markerElement.style.width = '30px';
      markerElement.style.height = '30px';
      
      if (markerLabel) {
        markerElement.style.backgroundColor = markerColor;
        markerElement.style.color = 'white';
        markerElement.style.borderRadius = '50%';
        markerElement.style.display = 'flex';
        markerElement.style.alignItems = 'center';
        markerElement.style.justifyContent = 'center';
        markerElement.style.fontWeight = 'bold';
        markerElement.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';
        markerElement.textContent = markerLabel;
      } else {
        const pin = document.createElement('div');
        pin.style.width = '20px';
        pin.style.height = '20px';
        pin.style.borderRadius = '50%';
        pin.style.backgroundColor = markerColor;
        pin.style.border = '3px solid white';
        pin.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';
        markerElement.appendChild(pin);
      }
      
      marker.current = new mapboxgl.Marker(markerElement)
        .setLngLat([location.x, location.y])
        .addTo(map.current);
        
      // Add activity markers if provided
      if (activityMarkers.length > 0) {
        activityMarkers.forEach((activityLocation) => {
          const activityMarkerEl = document.createElement('div');
          activityMarkerEl.style.width = '16px';
          activityMarkerEl.style.height = '16px';
          activityMarkerEl.style.borderRadius = '50%';
          activityMarkerEl.style.backgroundColor = activityLocation.color || '#F97316'; // Orange default
          activityMarkerEl.style.border = '2px solid white';
          activityMarkerEl.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';
          
          // Add label text if provided
          if (activityLocation.label) {
            const tooltip = document.createElement('div');
            tooltip.className = 'absolute -mt-8 -ml-2 bg-white px-2 py-1 rounded text-xs shadow-md whitespace-nowrap';
            tooltip.style.opacity = '0';
            tooltip.style.transition = 'opacity 0.2s';
            tooltip.textContent = activityLocation.label;
            
            activityMarkerEl.appendChild(tooltip);
            activityMarkerEl.addEventListener('mouseenter', () => {
              tooltip.style.opacity = '1';
            });
            activityMarkerEl.addEventListener('mouseleave', () => {
              tooltip.style.opacity = '0';
            });
          }
          
          const activityMarker = new mapboxgl.Marker(activityMarkerEl)
            .setLngLat([activityLocation.x, activityLocation.y])
            .addTo(map.current!);
            
          activityMarkerRefs.current.push(activityMarker);
        });
      }
      
      // Add fog effect for better depth perception
      map.current.on('style.load', () => {
        map.current?.setFog({
          'color': 'rgb(255, 255, 255)',
          'high-color': 'rgb(200, 200, 225)',
          'horizon-blend': 0.2,
        });
      });
      
    } catch (error) {
      console.error('Error initializing mini map:', error);
    }
    
    return () => {
      if (map.current) {
        activityMarkerRefs.current.forEach(marker => marker.remove());
        activityMarkerRefs.current = [];
        if (marker.current) marker.current.remove();
        map.current.remove();
        map.current = null;
      }
    };
  }, [mapboxToken, location, markerLabel, markerColor, zoom, activityMarkers]);

  return (
    <div className={cn("relative w-full h-40 rounded-md overflow-hidden shadow-md", className)}>
      <div ref={mapContainer} className="absolute inset-0" />
      
      {/* Semi-transparent gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent to-background/10 pointer-events-none" />
      
      {title && (
        <div className="absolute top-2 left-2 bg-background/80 backdrop-blur-sm px-2 py-1 rounded text-xs font-medium">
          {title}
        </div>
      )}
      
      {showExpandButton && (
        <div className="absolute bottom-2 right-2">
          <Link to={`/meetmap?lng=${location.x}&lat=${location.y}`}>
            <Button size="sm" variant="secondary" className="bg-background/80 backdrop-blur-sm">
              <Expand className="h-4 w-4 mr-1" />
              Full Map
            </Button>
          </Link>
        </div>
      )}
    </div>
  );
};

export default MiniMap;
