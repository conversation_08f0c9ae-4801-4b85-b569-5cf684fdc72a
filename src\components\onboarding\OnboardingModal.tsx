import React, { useState, useEffect, useContext } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { StepProgress } from './StepProgress';
import { BasicInfoStep } from './BasicInfoStep';
import { BirthdayGenderStep } from './BirthdayGenderStep';
import { DefaultLocationStep } from './DefaultLocationStep';
import { PurposeSelectionStep } from './PurposeSelectionStep';
import { VibeSelectionStep } from './VibeSelectionStep';
import { PermissionsStep } from './PermissionsStep';
import { InterestsStep } from './InterestsStep';
import { CompletionStep } from './CompletionStep';
import { toast } from 'sonner';
import { useNavigate, UNSAFE_NavigationContext, useLocation } from 'react-router-dom';
import { OnboardingFormValues } from '@/types/onboarding';
import { UserProfile } from '@/types/user-profile';
import { ArrowRight, ArrowLeft } from 'lucide-react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useForm, FormProvider } from 'react-hook-form';

interface OnboardingModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  user: { id: string; email?: string };
  profile: UserProfile | null;
  initialStep?: number;
  initialFormValues?: Partial<OnboardingFormValues>;
  onStepChange?: (step: number) => void;
  allowSkip?: boolean;
  forceComplete?: boolean;
  redirectPath?: string;
}

export function OnboardingModal({
  open,
  onOpenChange,
  user,
  profile: _profile, // Renamed to _profile to indicate it's not used directly
  initialStep = 1,
  initialFormValues = {},
  onStepChange,
  allowSkip = false,
  forceComplete = false,
  redirectPath
}: OnboardingModalProps) {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [formValues, setFormValues] = useState<Partial<OnboardingFormValues>>(initialFormValues);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const queryClient = useQueryClient();

  // Check if we're inside a Router context before using useNavigate
  const hasRouter = useContext(UNSAFE_NavigationContext) !== undefined;
  const navigate = hasRouter ? useNavigate() : null;
  const location = hasRouter ? useLocation() : null;

  // Create a form instance
  const form = useForm<OnboardingFormValues>({
    defaultValues: initialFormValues as OnboardingFormValues,
  });

  // Total number of steps in the onboarding flow
  const totalSteps = 7;

  useEffect(() => {
    if (initialStep && initialStep !== currentStep) {
      setCurrentStep(initialStep);
    }
  }, [initialStep]);

  useEffect(() => {
    if (onStepChange) {
      onStepChange(currentStep);
    }
  }, [currentStep, onStepChange]);

  // Save data for the current step and proceed to next
  const handleNext = async (stepData: Partial<OnboardingFormValues> = {}) => {
    try {
      console.log(`OnboardingModal - handleNext called for step ${currentStep} with data:`, stepData);

      const updatedValues = { ...formValues, ...stepData };
      setFormValues(updatedValues);

      console.log("OnboardingModal - Combined form values:", updatedValues);

      // Save the current step data to the database
      const saveSuccess = await saveStepData(updatedValues);

      if (!saveSuccess) {
        throw new Error("Failed to save step data");
      }

      // If we're on the last step, mark onboarding as completed
      if (currentStep === totalSteps) {
        console.log("OnboardingModal - On last step, completing onboarding");
        await completeOnboarding(updatedValues);
      } else {
        // Move to next step
        console.log(`OnboardingModal - Moving to step ${currentStep + 1}`);
        setCurrentStep(prev => prev + 1);
      }
    } catch (error) {
      console.error("Error saving step data:", error);
      toast.error("Error saving your information. Please try again.");

      // Show more detailed error in development
      if (process.env.NODE_ENV === 'development') {
        console.error("Detailed error:", error);
        toast.error(`Developer info: ${(error as Error).message || 'Unknown error'}`);
      }
    }
  };

  // Save the current step data to the database
  const saveStepData = async (data: Partial<OnboardingFormValues>) => {
    try {
      console.log("OnboardingModal - saveStepData called with data:", data);
      setIsSubmitting(true);

      // Make sure we have valid data
      const cleanedData = { ...data };

      // Ensure defaultLocation has valid x and y values if it exists
      if (cleanedData.defaultLocation) {
        // Convert camelCase to snake_case for database compatibility
        (cleanedData as any).default_location = {
          x: Number(cleanedData.defaultLocation.x),
          y: Number(cleanedData.defaultLocation.y)
        };
        // Remove the camelCase version to avoid conflicts
        delete cleanedData.defaultLocation;
        console.log("OnboardingModal - Converted defaultLocation to default_location:", (cleanedData as any).default_location);
      }

      // Handle vibes array - ensure it's properly formatted as a JSON array
      if (cleanedData.vibes && Array.isArray(cleanedData.vibes)) {
        // No need to do anything, it's already an array
        console.log("OnboardingModal - Vibes is already an array:", cleanedData.vibes);

        // Map vibes to interests for database compatibility if needed
        // Many components expect the interests field in the database
        (cleanedData as any).interests = cleanedData.vibes;
      }

      // Handle avatar_url - if it's a data URL, upload it to storage first
      if (cleanedData.avatar_url && typeof cleanedData.avatar_url === 'string' && cleanedData.avatar_url.startsWith('data:')) {
        console.log("OnboardingModal - Avatar is a data URL, uploading to storage first");

        try {
          // Import the upload function
          const { uploadAvatarFromDataUrl } = await import('@/utils/file-upload');

          // Upload the avatar
          const { url, error: uploadError } = await uploadAvatarFromDataUrl(user.id, cleanedData.avatar_url);

          if (uploadError) {
            console.error("Error uploading avatar:", uploadError);
            toast.error("Error uploading profile photo. Please try again.");
          } else if (url) {
            console.log("Avatar uploaded successfully:", url);
            // Replace the data URL with the actual URL
            cleanedData.avatar_url = url;
          }
        } catch (uploadError) {
          console.error("Error in avatar upload:", uploadError);
          // Continue with saving other data even if avatar upload fails
        }
      }

      // Skip the saveProfileData function and use direct update for reliability
      console.log("OnboardingModal - Using direct update for reliability");

      // Directly update the profile using Supabase client
      // Convert any Date objects to ISO strings for database storage
      const dbData: Record<string, any> = {};

      // Only copy the fields we need to update and handle type conversions
      Object.keys(cleanedData).forEach(key => {
        if (key === 'birthday' && cleanedData.birthday instanceof Date) {
          dbData.birthday = cleanedData.birthday.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        } else if (key === 'default_location') {
          // Already converted, just copy it
          dbData.default_location = (cleanedData as any).default_location;
        } else if (key === 'locationPermissionGranted') {
          // Convert camelCase to snake_case for database compatibility
          dbData.location_permission_granted = cleanedData.locationPermissionGranted;
          // Skip adding the camelCase version
        } else if (key === 'notificationsEnabled') {
          // Convert camelCase to snake_case for database compatibility
          dbData.notifications_enabled = cleanedData.notificationsEnabled;
          // Skip adding the camelCase version
        } else if (key === 'vibes' && Array.isArray(cleanedData.vibes)) {
          // Store vibes array directly
          dbData.vibes = cleanedData.vibes;
          // Also map vibes to interests for backward compatibility
          dbData.interests = cleanedData.vibes;
        } else {
          dbData[key] = cleanedData[key as keyof typeof cleanedData];
        }
      });

      console.log("Prepared database data:", dbData);

      const { error: directError } = await supabase
        .from('profiles')
        .update(dbData)
        .eq('id', user.id);

      if (directError) {
        console.error("Direct update failed:", directError);

        // If the error is related to function ambiguity, try a different approach
        if (directError.message?.includes('function') && directError.message?.includes('not unique')) {
          console.log("Detected function ambiguity error, trying update without trigger");

          // Try to update with a different approach that avoids the trigger
          // Use a direct SQL query as a last resort
          const { error: fallbackError } = await supabase
            .from('profiles')
            .update({
              ...dbData,
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

          if (fallbackError) {
            console.error("Fallback update also failed:", fallbackError);
            throw fallbackError;
          } else {
            console.log("Fallback update succeeded");
            // Invalidate profile query to refresh profile data
            queryClient.invalidateQueries({ queryKey: ['profile', user.id] });
            return true;
          }
        }

        throw directError;
      } else {
        console.log("Direct update succeeded");
        // Invalidate profile query to refresh profile data
        queryClient.invalidateQueries({ queryKey: ['profile', user.id] });
        return true;
      }
    } catch (error) {
      console.error("Error saving profile data:", error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  };

  // Mark onboarding as completed and save all data
  const completeOnboarding = async (data: Partial<OnboardingFormValues>) => {
    try {
      console.log("OnboardingModal - completeOnboarding called with data:", data);
      setIsSubmitting(true);

      // Ensure onboarding_completed flag is set
      const completeData = {
        ...data,
        onboarding_completed: true
        // Remove onboardingCompleted as it doesn't exist in the database
      };

      console.log("OnboardingModal - Prepared complete data:", completeData);

      // Handle avatar_url - if it's a data URL, upload it to storage first
      if (completeData.avatar_url && typeof completeData.avatar_url === 'string' && completeData.avatar_url.startsWith('data:')) {
        console.log("OnboardingModal - Avatar is a data URL, uploading to storage first");

        try {
          // Import the upload function
          const { uploadAvatarFromDataUrl } = await import('@/utils/file-upload');

          // Upload the avatar
          const { url, error: uploadError } = await uploadAvatarFromDataUrl(user.id, completeData.avatar_url);

          if (uploadError) {
            console.error("Error uploading avatar:", uploadError);
            toast.error("Error uploading profile photo. Please try again.");
          } else if (url) {
            console.log("Avatar uploaded successfully:", url);
            // Replace the data URL with the actual URL
            completeData.avatar_url = url;
          }
        } catch (uploadError) {
          console.error("Error in avatar upload:", uploadError);
          // Continue with saving other data even if avatar upload fails
        }
      }

      // Skip the saveOnboardingData function and use direct update for reliability
      console.log("OnboardingModal - Using direct update for completion");

      // Directly update the profile using Supabase client
      const dbData: Record<string, any> = {
        onboarding_completed: true, // Use only onboarding_completed as it exists in the database
        updated_at: new Date().toISOString()
      };

      // Only copy the fields we need to update and handle type conversions
      Object.keys(completeData).forEach(key => {
        if (key === 'birthday' && completeData.birthday instanceof Date) {
          dbData.birthday = completeData.birthday.toISOString().split('T')[0]; // Format as YYYY-MM-DD
        } else if (key === 'defaultLocation') {
          // Convert camelCase to snake_case for database compatibility
          dbData.default_location = {
            x: Number(completeData.defaultLocation.x),
            y: Number(completeData.defaultLocation.y)
          };
          // Skip adding the camelCase version
        } else if (key === 'locationPermissionGranted') {
          // Convert camelCase to snake_case for database compatibility
          dbData.location_permission_granted = completeData.locationPermissionGranted;
          // Skip adding the camelCase version
        } else if (key === 'notificationsEnabled') {
          // Convert camelCase to snake_case for database compatibility
          dbData.notifications_enabled = completeData.notificationsEnabled;
          // Skip adding the camelCase version
        } else if (key === 'vibes' && Array.isArray(completeData.vibes)) {
          // Store vibes array directly
          dbData.vibes = completeData.vibes;
          // Also map vibes to interests for backward compatibility
          dbData.interests = completeData.vibes;
        } else {
          dbData[key] = completeData[key as keyof typeof completeData];
        }
      });

      console.log("Prepared database data for completion:", dbData);

      const { error: directError } = await supabase
        .from('profiles')
        .update(dbData)
        .eq('id', user.id);

      if (directError) {
        console.error("Direct update failed:", directError);

        // If the error is related to function ambiguity, try a different approach
        if (directError.message?.includes('function') && directError.message?.includes('not unique')) {
          console.log("Detected function ambiguity error, trying update without trigger");

          // Try to update with a different approach that avoids the trigger
          const { error: fallbackError } = await supabase
            .from('profiles')
            .update({
              ...dbData,
              updated_at: new Date().toISOString()
            })
            .eq('id', user.id);

          if (fallbackError) {
            console.error("Fallback update also failed:", fallbackError);
            throw fallbackError;
          } else {
            console.log("Fallback update for completion succeeded");
          }
        } else {
          throw directError;
        }
      } else {
        console.log("Direct update for completion succeeded");
      }

      // Fallback: Also set the flag directly using direct database update
      try {
        console.log("OnboardingModal - Setting onboarding_completed flag directly");

        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            onboarding_completed: true, // Use only onboarding_completed as it exists in the database
            updated_at: new Date().toISOString()
          })
          .eq('id', user.id);

        console.log("OnboardingModal - Direct update result:", { error: updateError });

        if (updateError) {
          console.warn("Direct update failed, but continuing since we already saved data:", updateError);
        }
      } catch (updateError) {
        console.warn("Error in direct update, but continuing since we already saved data:", updateError);
      }

      // Invalidate profile query to refresh profile data
      queryClient.invalidateQueries({ queryKey: ['profile', user.id] });

      // Show success message with details about unlocked features
      toast.success("Profile completed! Access to all features unlocked.", {
        description: "You can now access chats, activities, and all other features.",
        duration: 5000
      });

      console.log("OnboardingModal - Onboarding completed successfully, closing modal and redirecting");

      // Close the dialog
      onOpenChange(false);

      // Changed navigation logic - Don't redirect to home page
      if (hasRouter && navigate) {
        // Determine where to navigate after completion
        if (redirectPath) {
          // If a specific redirect path was provided, use that
          console.log(`Redirecting to specified path: ${redirectPath}`);
          navigate(redirectPath);
        } else if (location?.pathname === '/') {
          // If we're on the home page, navigate to profile
          console.log("On home page, navigating to profile");
          // Use the username for the profile URL if available
          if (completeData.username) {
            navigate(`/${completeData.username}`);
          } else {
            navigate('/profile');
          }
        } else {
          // Stay on current page after completing onboarding
          console.log("Onboarding complete, staying on current page");
          // Use reload to ensure all components re-evaluate their routes
          window.location.reload();
        }
      }
    } catch (error) {
      console.error("Error completing onboarding:", error);
      toast.error("Error saving your profile. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 1) {
      setCurrentStep(prev => prev - 1);
    }
  };

  // Render the current step
  const renderStep = () => {
    // Wrap the form methods in the FormProvider
    return (
      <FormProvider {...form}>
        {(() => {
          switch (currentStep) {
            case 1:
              return (
                <BasicInfoStep
                  form={form}
                  initialValues={formValues}
                  isLoading={isSubmitting}
                  user={user}
                  onSubmit={handleNext}
                  previewUrl={form.watch("avatar_url") || null}
                  handleThumbnailClick={() => {}}
                  fileInputRef={React.createRef()}
                  handleFileChange={() => {}}
                />
              );
            case 2:
              return (
                <BirthdayGenderStep
                  form={form}
                  initialValues={formValues}
                  isLoading={isSubmitting}
                  onSubmit={handleNext}
                />
              );
            case 3:
              return (
                <DefaultLocationStep
                  onSubmit={handleNext}
                  isLoading={isSubmitting}
                  initialValues={formValues}
                  userId={user.id}
                  user={user}
                />
              );
            case 4:
              return (
                <PurposeSelectionStep
                  form={form}
                  onSubmit={handleNext}
                  isLoading={isSubmitting}
                  initialValues={formValues}
                />
              );
            case 5:
              return (
                <InterestsStep
                  form={form}
                  onSubmit={handleNext}
                  isLoading={isSubmitting}
                  initialValues={formValues}
                />
              );
            case 6:
              return (
                <VibeSelectionStep
                  form={form}
                  onSubmit={handleNext}
                  isLoading={isSubmitting}
                  initialValues={formValues}
                />
              );
            case 7:
              return (
                <PermissionsStep
                  onSubmit={handleNext}
                  isLoading={isSubmitting}
                  initialValues={formValues}
                />
              );
            case 8:
              return (
                <CompletionStep
                  onSubmit={handleNext}
                  isLoading={isSubmitting}
                  initialValues={formValues}
                />
              );
            default:
              return (
                <BasicInfoStep
                  form={form}
                  initialValues={formValues}
                  isLoading={isSubmitting}
                  onSubmit={handleNext}
                  user={user}
                  previewUrl={form.watch("avatar_url") || null}
                  handleThumbnailClick={() => {}}
                  fileInputRef={React.createRef()}
                  handleFileChange={() => {}}
                />
              );
          }
        })()}
      </FormProvider>
    );
  };

  // Add a class to hide the close button when forceComplete is true
  useEffect(() => {
    if (forceComplete) {
      // Add a style tag to hide the close button
      const styleTag = document.createElement('style');
      styleTag.id = 'hide-dialog-close';
      styleTag.innerHTML = `
        [data-radix-dialog-close] {
          display: none !important;
        }
      `;
      document.head.appendChild(styleTag);

      return () => {
        // Remove the style tag when the component unmounts
        const existingStyle = document.getElementById('hide-dialog-close');
        if (existingStyle) {
          existingStyle.remove();
        }
      };
    }
  }, [forceComplete]);

  return (
    <Dialog
      open={open}
      onOpenChange={(newOpen) => {
        // If trying to close the dialog and not on the last step, prevent closing
        if (!newOpen && currentStep < totalSteps) {
          console.log("Preventing onboarding modal from closing - onboarding not complete");
          return;
        }
        // Otherwise, allow the parent component to handle the change
        onOpenChange(newOpen);
      }}
      modal={true} // Always make it modal to prevent closing by clicking outside
    >
      <DialogContent className="sm:max-w-[600px] min-h-[550px] flex flex-col bg-white border-0 shadow-2xl rounded-2xl overflow-hidden p-0">
        {/* Gradient header background */}
        <div className="w-full bg-gradient-to-r from-primary-purple to-primary-deep-purple h-2" />

        <DialogHeader className="pt-6 pb-4 px-6">
          <DialogTitle className="text-2xl font-bold text-center text-gray-800">
            Complete Your Profile
          </DialogTitle>
          <DialogDescription className="text-center px-4 text-gray-600">
            Let's set up your profile to help you get the most out of BuddySurf
          </DialogDescription>
        </DialogHeader>

        <div className="px-6">
          <StepProgress currentStep={currentStep} totalSteps={totalSteps} />
        </div>

        <div className="flex-grow px-6 py-6 overflow-y-auto">
          {renderStep()}
        </div>

        <div className="flex justify-between items-center px-6 py-4 bg-gray-50 border-t border-gray-100">
          <Button
            variant="outline"
            onClick={handlePrevious}
            disabled={currentStep === 1}
            className="border-gray-200 text-gray-600 hover:bg-gray-100 h-10"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>

          <div className="flex gap-3">
            {/* Only show skip button if allowSkip is true and we're not on the last step */}
            {allowSkip && currentStep < totalSteps && (
              <Button
                variant="ghost"
                onClick={() => setCurrentStep(prev => prev + 1)}
                className="text-gray-500 hover:text-gray-700 hover:bg-gray-100 h-10"
              >
                Skip
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            )}

            {/* Continue button - always visible */}
            <Button
              variant="default"
              onClick={() => {
                // Get form data for the current step
                const currentFormData = form.getValues();
                console.log("Continue button clicked, submitting form data:", currentFormData);

                // For BasicInfoStep, we need display_name and avatar_url
                if (currentStep === 1) {
                  const { display_name, avatar_url } = currentFormData;
                  handleNext({ display_name, avatar_url });
                }
                // For other steps, we'll handle them based on the current step
                else if (currentStep === totalSteps) {
                  // If we're on the last step, complete onboarding
                  handleNext(currentFormData);
                } else {
                  // For other steps, just move to the next step
                  handleNext(currentFormData);
                }
              }}
              className="bg-gradient-to-r from-primary-purple to-primary-deep-purple text-white px-8 py-2 h-10 rounded-md shadow-md hover:shadow-lg transition-all duration-200"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <div className="flex items-center">
                  <div className="animate-spin mr-2 h-4 w-4 border-t-2 border-b-2 border-white rounded-full"></div>
                  Processing...
                </div>
              ) : (
                <>
                  {currentStep === totalSteps ? "Complete" : "Continue"}
                  <ArrowRight className="ml-2 h-4 w-4" />
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
