-- SQL script to create activity-related tables and set up RLS policies
-- Run this script in the Supabase SQL Editor

-- Function to check if a user is an admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    SELECT email = '<EMAIL>'
    FROM auth.users
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create categories table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.categories (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  icon TEXT,
  type TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create activities table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.activities (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  category_id UUID REFERENCES categories(id),
  location POINT NOT NULL,
  address TEXT,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  is_paid BOOLEAN DEFAULT FALSE,
  price NUMERIC,
  max_participants INTEGER,
  host_id UUID NOT NULL REFERENCES auth.users(id),
  status TEXT DEFAULT 'active',
  visibility TEXT DEFAULT 'public',
  queue_type TEXT DEFAULT 'fifo',
  allow_waitlist BOOLEAN DEFAULT TRUE,
  group_chat_id UUID,
  media_urls TEXT[],
  early_bird_price NUMERIC,
  group_discount_threshold INTEGER,
  group_discount_percentage NUMERIC,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create activity_participants table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.activity_participants (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  activity_id UUID NOT NULL REFERENCES activities(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'pending',
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  payment_status TEXT,
  payment_id TEXT,
  is_host BOOLEAN DEFAULT FALSE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(activity_id, user_id)
);

-- Create activity_queue table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.activity_queue (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  activity_id UUID NOT NULL REFERENCES activities(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  status TEXT NOT NULL DEFAULT 'pending',
  position INTEGER NOT NULL,
  payment_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(activity_id, user_id)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_activities_host_id ON public.activities(host_id);
CREATE INDEX IF NOT EXISTS idx_activities_category_id ON public.activities(category_id);
CREATE INDEX IF NOT EXISTS idx_activities_group_chat_id ON public.activities(group_chat_id);
CREATE INDEX IF NOT EXISTS idx_activity_participants_user_id ON public.activity_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_participants_activity_id ON public.activity_participants(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_queue_user_id ON public.activity_queue(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_queue_activity_id ON public.activity_queue(activity_id);

-- Enable Row-Level Security on all tables
ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_participants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.activity_queue ENABLE ROW LEVEL SECURITY;

-- Activities Table Policies
DROP POLICY IF EXISTS "Users can view all activities" ON public.activities;
DROP POLICY IF EXISTS "Users can create activities" ON public.activities;
DROP POLICY IF EXISTS "Users can update their own activities" ON public.activities;
DROP POLICY IF EXISTS "Users can delete their own activities" ON public.activities;
DROP POLICY IF EXISTS "Admins can manage all activities" ON public.activities;

CREATE POLICY "Users can view all activities"
ON public.activities
FOR SELECT
USING (true);

CREATE POLICY "Users can create activities"
ON public.activities
FOR INSERT
WITH CHECK ((host_id = auth.uid()));

CREATE POLICY "Users can update their own activities"
ON public.activities
FOR UPDATE
USING ((host_id = auth.uid()))
WITH CHECK ((host_id = auth.uid()));

CREATE POLICY "Users can delete their own activities"
ON public.activities
FOR DELETE
USING ((host_id = auth.uid()));

CREATE POLICY "Admins can manage all activities"
ON public.activities
FOR ALL
USING (is_admin());

-- Activity Participants Table Policies
DROP POLICY IF EXISTS "Users can view all activity participants" ON public.activity_participants;
DROP POLICY IF EXISTS "Users can join activities" ON public.activity_participants;
DROP POLICY IF EXISTS "Users can update their own participation" ON public.activity_participants;
DROP POLICY IF EXISTS "Hosts can update participants for their activities" ON public.activity_participants;
DROP POLICY IF EXISTS "Users can delete their own participation" ON public.activity_participants;
DROP POLICY IF EXISTS "Admins can manage all participants" ON public.activity_participants;

CREATE POLICY "Users can view all activity participants"
ON public.activity_participants
FOR SELECT
USING (true);

CREATE POLICY "Users can join activities"
ON public.activity_participants
FOR INSERT
WITH CHECK ((user_id = auth.uid()));

CREATE POLICY "Users can update their own participation"
ON public.activity_participants
FOR UPDATE
USING ((user_id = auth.uid()))
WITH CHECK ((user_id = auth.uid()));

CREATE POLICY "Hosts can update participants for their activities"
ON public.activity_participants
FOR UPDATE
USING (EXISTS (
  SELECT 1 FROM activities a
  WHERE a.id = activity_id
  AND a.host_id = auth.uid()
));

CREATE POLICY "Users can delete their own participation"
ON public.activity_participants
FOR DELETE
USING ((user_id = auth.uid()));

CREATE POLICY "Admins can manage all participants"
ON public.activity_participants
FOR ALL
USING (is_admin());

-- Activity Queue Table Policies
DROP POLICY IF EXISTS "Users can view all activity queues" ON public.activity_queue;
DROP POLICY IF EXISTS "Users can join activity queues" ON public.activity_queue;
DROP POLICY IF EXISTS "Users can update their own queue entries" ON public.activity_queue;
DROP POLICY IF EXISTS "Hosts can update queue entries for their activities" ON public.activity_queue;
DROP POLICY IF EXISTS "Users can delete their own queue entries" ON public.activity_queue;
DROP POLICY IF EXISTS "Admins can manage all queue entries" ON public.activity_queue;

CREATE POLICY "Users can view all activity queues"
ON public.activity_queue
FOR SELECT
USING (true);

CREATE POLICY "Users can join activity queues"
ON public.activity_queue
FOR INSERT
WITH CHECK ((user_id = auth.uid()));

CREATE POLICY "Users can update their own queue entries"
ON public.activity_queue
FOR UPDATE
USING ((user_id = auth.uid()))
WITH CHECK ((user_id = auth.uid()));

CREATE POLICY "Hosts can update queue entries for their activities"
ON public.activity_queue
FOR UPDATE
USING (EXISTS (
  SELECT 1 FROM activities a
  WHERE a.id = activity_id
  AND a.host_id = auth.uid()
));

CREATE POLICY "Users can delete their own queue entries"
ON public.activity_queue
FOR DELETE
USING ((user_id = auth.uid()));

CREATE POLICY "Admins can manage all queue entries"
ON public.activity_queue
FOR ALL
USING (is_admin());

-- Insert some default categories if they don't exist
INSERT INTO public.categories (name, icon, type)
SELECT 'Social', '🎭', 'activity'
WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Social' AND type = 'activity');

INSERT INTO public.categories (name, icon, type)
SELECT 'Sports', '⚽', 'activity'
WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Sports' AND type = 'activity');

INSERT INTO public.categories (name, icon, type)
SELECT 'Food & Drink', '🍔', 'activity'
WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Food & Drink' AND type = 'activity');

INSERT INTO public.categories (name, icon, type)
SELECT 'Arts & Culture', '🎨', 'activity'
WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Arts & Culture' AND type = 'activity');

INSERT INTO public.categories (name, icon, type)
SELECT 'Outdoors', '🏞️', 'activity'
WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Outdoors' AND type = 'activity');

INSERT INTO public.categories (name, icon, type)
SELECT 'Education', '📚', 'activity'
WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Education' AND type = 'activity');

INSERT INTO public.categories (name, icon, type)
SELECT 'Networking', '🤝', 'activity'
WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Networking' AND type = 'activity');

INSERT INTO public.categories (name, icon, type)
SELECT 'Entertainment', '🎬', 'activity'
WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Entertainment' AND type = 'activity');

INSERT INTO public.categories (name, icon, type)
SELECT 'Travel', '✈️', 'activity'
WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Travel' AND type = 'activity');

INSERT INTO public.categories (name, icon, type)
SELECT 'Other', '🔍', 'activity'
WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Other' AND type = 'activity');
