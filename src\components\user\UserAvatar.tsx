import React from 'react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { <PERSON>geCheck } from 'lucide-react';
import { cn } from '@/lib/utils';

interface UserAvatarProps {
  user: {
    avatar_url?: string | null;
    display_name?: string | null;
    username?: string | null;
    is_verified?: boolean;
  } | null;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl';
  showVerified?: boolean;
  className?: string;
  pulseEffect?: boolean;
  borderColor?: string;
  showStatus?: boolean;
  isOnline?: boolean;
}

export function UserAvatar({ 
  user, 
  size = 'md', 
  showVerified = true,
  className,
  pulseEffect = false,
  borderColor = 'white',
  showStatus = false,
  isOnline = false
}: UserAvatarProps) {
  const sizes = {
    xs: 'h-6 w-6',
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16',
    '2xl': 'h-20 w-20',
    '3xl': 'h-24 w-24'
  };

  const getInitials = () => {
    if (user?.display_name) {
      return user.display_name.substring(0, 2).toUpperCase();
    }
    if (user?.username) {
      return user.username.substring(0, 2).toUpperCase();
    }
    return 'U';
  };

  const badgeSizes = {
    xs: 'h-2.5 w-2.5 -bottom-0.5 -right-0.5',
    sm: 'h-3 w-3 -bottom-0.5 -right-0.5',
    md: 'h-4 w-4 -bottom-0.5 -right-0.5',
    lg: 'h-5 w-5 -bottom-1 -right-1',
    xl: 'h-6 w-6 -bottom-1 -right-1',
    '2xl': 'h-7 w-7 -bottom-1.5 -right-1.5',
    '3xl': 'h-8 w-8 -bottom-2 -right-2'
  };
  
  const statusSizes = {
    xs: 'h-2 w-2 -bottom-0 -right-0',
    sm: 'h-2.5 w-2.5 -bottom-0 -right-0',
    md: 'h-3 w-3 -bottom-0 -right-0',
    lg: 'h-3.5 w-3.5 -bottom-0.5 -right-0.5',
    xl: 'h-4 w-4 -bottom-0.5 -right-0.5',
    '2xl': 'h-5 w-5 -bottom-1 -right-1',
    '3xl': 'h-6 w-6 -bottom-1 -right-1'
  };
  
  const pulseClass = pulseEffect ? 'animate-pulse' : '';
  const borderWidth = size === 'xs' || size === 'sm' ? 'border' : 'border-2';
  
  return (
    <div className="relative inline-block">
      <Avatar className={cn(
        sizes[size], 
        `${borderWidth} border-${borderColor} shadow-lg transition-all duration-300`, 
        pulseClass,
        className
      )}>
        <AvatarImage 
          src={user?.avatar_url || undefined} 
          alt={user?.display_name || 'User'} 
          className="object-cover"
          onError={(e) => {
            e.currentTarget.onerror = null;
            e.currentTarget.src = '';
          }}
        />
        <AvatarFallback className="text-primary bg-primary-foreground font-medium">
          {getInitials()}
        </AvatarFallback>
      </Avatar>
      
      {showVerified && user?.is_verified && (
        <div className={cn(
          "absolute bg-blue-500 rounded-full p-0.5 shadow-md border-2 border-white",
          badgeSizes[size]
        )}>
          <BadgeCheck className="h-full w-full text-white" />
        </div>
      )}
      
      {showStatus && (
        <div className={cn(
          "absolute rounded-full border-2 border-white shadow-md transition-colors duration-300", 
          statusSizes[size],
          isOnline ? "bg-green-500 animate-pulse" : "bg-gray-400"
        )} />
      )}
    </div>
  );
}
