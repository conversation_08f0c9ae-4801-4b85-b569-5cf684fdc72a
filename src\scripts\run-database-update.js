/**
 * This script runs the database update to ensure the profiles table has all the necessary columns
 * for storing onboarding data.
 * 
 * Usage:
 * 1. Make sure you have the Supabase service key in your .env file
 * 2. Run this script with Node.js: node src/scripts/run-database-update.js
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY; // Use service key for admin operations

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function runDatabaseUpdate() {
  try {
    console.log('Running database update...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../../sql/update_profiles_table.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.sql(sqlContent);
    
    if (error) {
      console.error('Error updating database:', error);
      process.exit(1);
    }
    
    console.log('Database update completed successfully!');
    
    // Verify the profiles table structure
    const { data, error: tableError } = await supabase
      .from('profiles')
      .select('*')
      .limit(1);
    
    if (tableError) {
      console.error('Error verifying profiles table:', tableError);
    } else {
      console.log('Profiles table structure verified.');
      if (data && data.length > 0) {
        console.log('Sample profile data:', data[0]);
      } else {
        console.log('No profiles found in the table.');
      }
    }
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

runDatabaseUpdate();
