
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, <PERSON>Footer, CardHeader, CardTitle } from '@/components/ui/card';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { SavedLocation, useSavedLocations } from '@/hooks/use-saved-locations';
import { Star, Plus, MapPin } from 'lucide-react';
import { ScrollArea } from '@/components/ui/scroll-area';
import { useToast } from '@/hooks/use-toast';

interface SavedLocationManagerProps {
  currentLocation?: { x: number; y: number };
  onSelectLocation?: (location: SavedLocation) => void;
  className?: string;
}

export function SavedLocationManager({ currentLocation, onSelectLocation, className }: SavedLocationManagerProps) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [locationName, setLocationName] = useState('');
  const [locationDescription, setLocationDescription] = useState('');
  const { savedLocations, isLoading, saveLocation } = useSavedLocations();
  const { toast } = useToast();

  const handleSaveCurrentLocation = async () => {
    if (!currentLocation || !locationName.trim()) {
      toast({
        title: 'Missing information',
        description: 'Please provide a name for this location',
        variant: 'destructive',
      });
      return;
    }

    try {
      await saveLocation({
        name: locationName,
        description: locationDescription,
        location: currentLocation,
      });
      setLocationName('');
      setLocationDescription('');
      setIsDialogOpen(false);
    } catch (error) {
      console.error('Error saving location:', error);
    }
  };

  const handleLocationSelect = (location: SavedLocation) => {
    if (onSelectLocation) {
      onSelectLocation(location);
    }
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader className="py-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Saved Locations</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => setIsDialogOpen(true)}
            >
              <Plus className="h-4 w-4" />
              <span className="sr-only">Add location</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="py-2">
          {isLoading ? (
            <div className="flex justify-center py-4">
              <p className="text-sm text-muted-foreground">Loading saved locations...</p>
            </div>
          ) : savedLocations.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-6 text-center">
              <MapPin className="h-8 w-8 text-muted-foreground opacity-50" />
              <p className="mt-2 text-sm font-medium">No saved locations</p>
              <p className="text-xs text-muted-foreground">
                Save your favorite places for quick access
              </p>
            </div>
          ) : (
            <ScrollArea className="h-[180px] pr-3">
              {savedLocations.map((location) => (
                <div
                  key={location.id}
                  className="group mb-2 flex cursor-pointer items-center justify-between rounded-md border border-border p-2 hover:border-primary"
                  onClick={() => handleLocationSelect(location)}
                >
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-muted-foreground group-hover:text-primary" />
                    <div>
                      <p className="text-sm font-medium">{location.name}</p>
                      {location.description && (
                        <p className="text-xs text-muted-foreground line-clamp-1">
                          {location.description}
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Save Current Location</DialogTitle>
            <DialogDescription>
              Save this location to easily find it later.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Name
              </Label>
              <Input
                id="name"
                value={locationName}
                onChange={(e) => setLocationName(e.target.value)}
                className="col-span-3"
                placeholder="Home, Work, Favorite Restaurant..."
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="description" className="text-right">
                Description
              </Label>
              <Textarea
                id="description"
                value={locationDescription}
                onChange={(e) => setLocationDescription(e.target.value)}
                className="col-span-3"
                placeholder="Add some notes about this place..."
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleSaveCurrentLocation} disabled={!locationName.trim()}>
              Save Location
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
