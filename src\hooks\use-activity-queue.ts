
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { ActivityQueueEntry } from '@/types/activity';

export function useActivityQueue(activityId?: string) {
  return useQuery({
    queryKey: ['activity-queue', activityId],
    queryFn: async () => {
      if (!activityId) return [];

      const { data, error } = await supabase
        .from('activity_queue')
        .select(`
          *,
          user:user_id (
            id,
            display_name,
            username,
            avatar_url
          )
        `)
        .eq('activity_id', activityId)
        .order('position', { ascending: true });

      if (error) throw error;

      return data.map((entry: any) => {
        // Ensure status is one of the allowed values
        let validStatus: 'confirmed' | 'pending' | 'waitlisted' | 'cancelled' = 'pending';
        if (['confirmed', 'pending', 'waitlisted', 'cancelled'].includes(entry.status)) {
          validStatus = entry.status as 'confirmed' | 'pending' | 'waitlisted' | 'cancelled';
        }
        
        return {
          ...entry,
          status: validStatus,
          user: entry.user ? {
            id: entry.user.id,
            display_name: entry.user.display_name,
            username: entry.user.username,
            avatar_url: entry.user.avatar_url
          } : undefined
        } as ActivityQueueEntry;
      });
    },
    enabled: !!activityId
  });
}

export function useJoinQueue() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      activityId, 
      userId 
    }: { 
      activityId: string; 
      userId: string; 
    }) => {
      const { data, error } = await supabase
        .from('activity_queue')
        .insert({
          activity_id: activityId,
          user_id: userId,
          status: 'pending',
          position: 0, // Position will be calculated server-side
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select();

      if (error) throw error;
      return data[0];
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['activity-queue', data.activity_id] });
      toast({
        title: "Request sent!",
        description: "Your request to join has been sent to the host.",
      });
    },
    onError: (error) => {
      console.error('Error joining queue:', error);
      toast({
        title: "Error joining",
        description: "Failed to join activity. Please try again.",
        variant: "destructive"
      });
    }
  });
}

export function useUpdateQueueStatus() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      entryId, 
      status 
    }: { 
      entryId: string; 
      status: 'confirmed' | 'cancelled' | 'pending' | 'waitlisted'; 
    }) => {
      const { data: entry, error: getError } = await supabase
        .from('activity_queue')
        .select('activity_id')
        .eq('id', entryId)
        .single();

      if (getError) throw getError;

      const { error: updateError } = await supabase
        .from('activity_queue')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', entryId);

      if (updateError) throw updateError;

      return { entryId, activityId: entry.activity_id, status };
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['activity-queue', result.activityId] });
      toast({
        title: "Status updated",
        description: "Participant status has been successfully updated.",
      });
    },
    onError: (error) => {
      console.error('Error updating queue status:', error);
      toast({
        title: "Update failed",
        description: "Failed to update participant status.",
        variant: "destructive"
      });
    }
  });
}

export function useLeaveQueue() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (queueEntryId: string) => {
      const { data: entry, error: getError } = await supabase
        .from('activity_queue')
        .select('activity_id')
        .eq('id', queueEntryId)
        .single();

      if (getError) throw getError;

      const { error: updateError } = await supabase
        .from('activity_queue')
        .update({
          status: 'cancelled',
          updated_at: new Date().toISOString()
        })
        .eq('id', queueEntryId);

      if (updateError) throw updateError;

      return { entryId: queueEntryId, activityId: entry.activity_id };
    },
    onSuccess: (result) => {
      queryClient.invalidateQueries({ queryKey: ['activity-queue', result.activityId] });
      toast({
        title: "Queue left",
        description: "You have left the activity queue.",
      });
    },
    onError: (error) => {
      console.error('Error leaving queue:', error);
      toast({
        title: "Error leaving queue",
        description: "Failed to leave the queue. Please try again.",
        variant: "destructive"
      });
    }
  });
}

// Helper function to get queue entry for a user in an activity
export function useUserQueueEntry(activityId?: string, userId?: string) {
  return useQuery({
    queryKey: ['user-queue-entry', activityId, userId],
    queryFn: async () => {
      if (!activityId || !userId) return null;
      
      const { data, error } = await supabase
        .from('activity_queue')
        .select('*')
        .eq('activity_id', activityId)
        .eq('user_id', userId)
        .not('status', 'eq', 'cancelled')
        .order('created_at', { ascending: false })
        .limit(1);
        
      if (error) throw error;
      return data.length > 0 ? data[0] : null;
    },
    enabled: !!activityId && !!userId
  });
}

// Updated calculateQueueStats to match the QueueManagementStats interface
export function calculateQueueStats(entries: ActivityQueueEntry[], maxParticipants?: number | null, userId?: string) {
  const confirmedCount = entries.filter(e => e.status === 'confirmed').length;
  const pendingCount = entries.filter(e => e.status === 'pending').length;
  const waitlistedCount = entries.filter(e => e.status === 'waitlisted').length;
  const cancelledCount = entries.filter(e => e.status === 'cancelled').length;
  const totalCapacity = maxParticipants || null;
  const capacityRemaining = totalCapacity !== null ? Math.max(0, totalCapacity - confirmedCount) : null;
  const queueProgress = totalCapacity ? (confirmedCount / totalCapacity) * 100 : 0;
  
  // User's status in the queue
  const userEntry = userId ? entries.find(e => e.user_id === userId && e.status !== 'cancelled') : undefined;
  const userStatus = userEntry?.status;
  const userPosition = userEntry?.position || 0;
  const hasJoined = !!userEntry;
  
  // Calculate estimated wait time (simplified)
  const estimatedWaitTime = pendingCount > 0 && capacityRemaining !== null ? 
    (pendingCount / capacityRemaining) * 30 : // estimate 30 mins per spot
    null;
  
  return {
    // Original properties
    total: entries.length,
    confirmed: confirmedCount,
    pending: pendingCount,
    waitlisted: waitlistedCount,
    cancelled: cancelledCount,
    capacity: totalCapacity,
    remaining: capacityRemaining,
    
    // Additional properties needed by components
    confirmedCount,
    pendingCount,
    totalCapacity,
    capacityRemaining,
    queueProgress,
    userStatus,
    userPosition,
    hasJoined,
    estimatedWaitTime
  };
}
