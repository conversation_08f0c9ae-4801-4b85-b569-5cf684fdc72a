
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { Conversation, ConversationParticipant } from '@/types/conversation';

// Constants for the support display
const SUPPORT_DISPLAY_NAME = 'Buddy Support';
const SUPPORT_AVATAR_URL = 'https://api.dicebear.com/7.x/bottts/svg?seed=support&backgroundColor=b6e3f4';

export interface SupportMessage {
  id: string;
  content: string;
  sender_id: string;
  created_at: string;
  is_read: boolean;
  media_url?: string | null;
  attachments?: string[];
  conversation_id: string;
  sender_avatar_url?: string | null;
  sender_name?: string | null;
  is_admin_message?: boolean;
}

type SendSupportMessageInput = {
  content: string;
  mediaUrl?: string;
  conversationId: string;
}

/**
 * Hook for users to access their support conversation
 */
export function useSupportConversation() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Get or create a support conversation for the current user
  const supportConversationQuery = useQuery({
    queryKey: ['supportConversation', user?.id],
    staleTime: 5 * 60 * 1000, // 5 minutes
    queryFn: async () => {
      if (!user) return null;

      // Mock implementation instead of actual database queries
      const mockConversation: Conversation = {
        id: 'support_' + user.id,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        last_message: 'How can we help you today?',
        last_message_at: new Date().toISOString(),
        is_group: false,
        is_support: true,
        participants: [
          {
            user_id: user.id,
            conversation_id: 'support_' + user.id,
            display_name: user.user_metadata?.full_name || 'User',
            avatar_url: user.user_metadata?.avatar_url
          },
          {
            user_id: 'support',
            conversation_id: 'support_' + user.id,
            display_name: SUPPORT_DISPLAY_NAME,
            avatar_url: SUPPORT_AVATAR_URL
          }
        ]
      };

      return mockConversation;
    },
    enabled: !!user
  });

  // Send a message in the support conversation
  const sendSupportMessage = useMutation({
    mutationFn: async ({ content, mediaUrl, conversationId }: SendSupportMessageInput) => {
      if (!user) throw new Error('You must be signed in to send a message');

      console.log('Sending support message to conversation:', conversationId);

      try {
        // Mock message creation instead of database insert
        const mockMessage: SupportMessage = {
          id: `msg_${Date.now()}`,
          content,
          sender_id: user.id,
          conversation_id: conversationId,
          media_url: mediaUrl || null,
          is_read: false,
          created_at: new Date().toISOString()
        };

        // In a real implementation, we'd insert to the database here
        console.log('Created mock support message:', mockMessage);

        // Return the mock message
        return mockMessage;
      } catch (error) {
        console.error('Failed to send support message:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['messages', supportConversationQuery.data?.id]
      });
      queryClient.invalidateQueries({
        queryKey: ['conversations']
      });
      queryClient.invalidateQueries({
        queryKey: ['chat-conversations']
      });
    },
    onError: (error) => {
      toast({
        title: 'Error sending message',
        description: error.message,
        variant: 'destructive'
      });
    }
  });

  return {
    supportConversation: supportConversationQuery.data,
    isLoading: supportConversationQuery.isLoading,
    sendSupportMessage,
    refetch: supportConversationQuery.refetch
  };
}

/**
 * Hook for admins to access all support conversations
 */
export function useAdminSupportConversations() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  // Get all support conversations for admin
  const supportConversationsQuery = useQuery({
    queryKey: ['adminSupportConversations'],
    queryFn: async () => {
      if (!user) return [];

      // Mock admin check
      const isAdmin = true; // In a real app, this would check the database

      if (!isAdmin) {
        throw new Error('Only admins can access support conversations');
      }

      // Mock implementation returning fake conversations
      const mockConversations: Conversation[] = [
        {
          id: 'support_1',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          last_message: 'How can I reset my password?',
          last_message_at: new Date().toISOString(),
          is_group: false,
          is_support: true,
          participants: [
            {
              user_id: 'user1',
              conversation_id: 'support_1',
              display_name: 'John Doe',
              avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=john'
            }
          ]
        },
        {
          id: 'support_2',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          updated_at: new Date(Date.now() - 3600000).toISOString(),
          last_message: 'I have an issue with a payment',
          last_message_at: new Date(Date.now() - 3600000).toISOString(),
          is_group: false,
          is_support: true,
          participants: [
            {
              user_id: 'user2',
              conversation_id: 'support_2',
              display_name: 'Jane Smith',
              avatar_url: 'https://api.dicebear.com/7.x/avataaars/svg?seed=jane'
            }
          ]
        }
      ];

      return mockConversations;
    },
    enabled: !!user
  });

  // Send a message as admin in a support conversation
  const sendAdminSupportMessage = useMutation({
    mutationFn: async ({ content, conversationId }: { content: string; conversationId: string }) => {
      if (!user) throw new Error('You must be signed in to send a message');

      // Mock admin check
      const isAdmin = true; // In a real app, this would check the database

      if (!isAdmin) {
        throw new Error('Only admins can send support messages');
      }

      console.log('Admin sending support message to conversation:', conversationId);

      try {
        // Create mock message
        const mockMessage: SupportMessage = {
          id: `msg_admin_${Date.now()}`,
          content,
          sender_id: user.id,
          conversation_id: conversationId,
          is_read: false,
          is_admin_message: true,
          created_at: new Date().toISOString(),
          sender_name: 'Admin Support',
          sender_avatar_url: SUPPORT_AVATAR_URL,
        };

        console.log('Created mock admin support message:', mockMessage);

        return mockMessage;
      } catch (error) {
        console.error('Failed to send admin support message:', error);
        throw error;
      }
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['messages', variables.conversationId]
      });
      queryClient.invalidateQueries({
        queryKey: ['conversations']
      });
      queryClient.invalidateQueries({
        queryKey: ['chat-conversations']
      });
      queryClient.invalidateQueries({
        queryKey: ['adminSupportConversations']
      });

      toast({
        title: 'Support message sent',
        description: 'Your response has been sent to the user.',
      });
    },
    onError: (error) => {
      toast({
        title: 'Error sending message',
        description: error.message,
        variant: 'destructive'
      });
    }
  });

  return {
    supportConversations: supportConversationsQuery.data || [],
    isLoading: supportConversationsQuery.isLoading,
    sendAdminSupportMessage
  };
}
