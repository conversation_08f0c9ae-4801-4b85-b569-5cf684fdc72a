import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Loader2, MapPin, RefreshCw, Wifi, WifiOff } from 'lucide-react';
import { useRealtimeLocationSystem } from '@/hooks/use-realtime-location-system';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

interface MapLocationStatusProps {
  className?: string;
}

export function MapLocationStatus({ className }: MapLocationStatusProps) {
  const { 
    isConnected, 
    lastLocationUpdate, 
    isUpdating, 
    updateMyLocation 
  } = useRealtimeLocationSystem();
  
  const [autoUpdate, setAutoUpdate] = React.useState(true);

  const handleManualUpdate = () => {
    updateMyLocation();
  };

  const toggleAutoUpdate = (checked: boolean) => {
    setAutoUpdate(checked);
    // The actual auto-update logic is handled in the useRealtimeLocationSystem hook
    // This is just for UI state
  };

  return (
    <Card className={cn("w-full shadow-md", className)}>
      <CardContent className="p-4">
        <div className="flex flex-col space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              {isConnected ? (
                <Wifi className="h-4 w-4 text-green-500" />
              ) : (
                <WifiOff className="h-4 w-4 text-red-500" />
              )}
              <span className="text-sm font-medium">
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
            <Badge variant={isConnected ? "outline" : "destructive"} className="text-xs">
              {isConnected ? 'LIVE' : 'OFFLINE'}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4 text-blue-500" />
              <span className="text-sm">Last update:</span>
            </div>
            <span className="text-xs text-muted-foreground">
              {lastLocationUpdate 
                ? formatDistanceToNow(lastLocationUpdate, { addSuffix: true })
                : 'Never updated'}
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button 
              variant="outline" 
              size="sm" 
              className="flex-1"
              onClick={handleManualUpdate}
              disabled={isUpdating}
            >
              {isUpdating ? (
                <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              ) : (
                <RefreshCw className="h-4 w-4 mr-2" />
              )}
              Update Now
            </Button>
          </div>
          
          <div className="flex items-center space-x-2">
            <Switch 
              id="auto-update" 
              checked={autoUpdate}
              onCheckedChange={toggleAutoUpdate}
            />
            <Label htmlFor="auto-update" className="text-sm">Auto-update (every 5 min)</Label>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
