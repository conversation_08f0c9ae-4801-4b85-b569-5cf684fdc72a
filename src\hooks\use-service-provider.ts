
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

interface ServiceProvider {
  id: string;
  name: string;
  bio: string;
  hourlyRate: number;
  rating: number;
  totalReviews: number;
  tags: string[];
  location: string;
  avatarUrl: string;
  isVerified: boolean;
  verificationStatus: string;
}

export function useServiceProvider(providerId: string) {
  return useQuery({
    queryKey: ['service-provider', providerId],
    queryFn: async (): Promise<ServiceProvider | null> => {
      if (!providerId) return null;

      const { data, error } = await supabase
        .from('service_providers')
        .select(`
          *,
          user:profiles!service_providers_user_id_fkey(
            id,
            display_name,
            avatar_url,
            username
          )
        `)
        .eq('id', providerId)
        .single();

      if (error) {
        console.error('Error fetching service provider:', error);
        return null;
      }

      return {
        id: data.id,
        name: data.name || data.user?.display_name || 'Unknown Provider',
        bio: data.bio || '',
        hourlyRate: Number(data.hourly_rate) || 0,
        rating: Number(data.rating) || 0,
        totalReviews: data.total_reviews || 0,
        tags: data.tags || [],
        location: data.location || '',
        avatarUrl: data.avatar_url || data.user?.avatar_url || '',
        isVerified: data.is_verified || false,
        verificationStatus: data.verification_status || 'pending'
      };
    },
    enabled: !!providerId
  });
}

export function useServiceCategories() {
  return useQuery({
    queryKey: ['service-categories'],
    queryFn: async () => {
      // Return mock data for now
      return [
        { id: '1', name: 'Technology', icon: '💻', type: 'service' },
        { id: '2', name: 'Design', icon: '🎨', type: 'service' },
        { id: '3', name: 'Business', icon: '💼', type: 'service' }
      ];
    }
  });
}

export function useUserServiceProvider() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user-service-provider', user?.id],
    queryFn: async (): Promise<ServiceProvider | null> => {
      if (!user) return null;

      const { data, error } = await supabase
        .from('service_providers')
        .select(`
          *,
          user:profiles!service_providers_user_id_fkey(
            id,
            display_name,
            avatar_url,
            username
          )
        `)
        .eq('user_id', user.id)
        .single();

      if (error) {
        // User doesn't have a service provider profile yet
        if (error.code === 'PGRST116') return null;
        console.error('Error fetching user service provider:', error);
        return null;
      }

      return {
        id: data.id,
        name: data.name || data.user?.display_name || 'Unknown Provider',
        bio: data.bio || '',
        hourlyRate: Number(data.hourly_rate) || 0,
        rating: Number(data.rating) || 0,
        totalReviews: data.total_reviews || 0,
        tags: data.tags || [],
        location: data.location || '',
        avatarUrl: data.avatar_url || data.user?.avatar_url || '',
        isVerified: data.is_verified || false,
        verificationStatus: data.verification_status || 'pending'
      };
    },
    enabled: !!user
  });
}

export function useCreateServiceProvider() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (data: {
      business_name: string;
      description: string;
      hourly_rate: number;
      location: string;
      tags: string[];
      service_area_radius?: number;
      availability_schedule?: any;
      portfolio_images?: string[];
      certifications?: string[];
      insurance_info?: any;
      business_documents?: any;
    }) => {
      if (!user) throw new Error('User not authenticated');

      const { data: serviceProvider, error } = await supabase
        .from('service_providers')
        .insert({
          user_id: user.id,
          name: data.business_name,
          bio: data.description,
          hourly_rate: data.hourly_rate,
          location: data.location,
          tags: data.tags,
          is_verified: false,
          verification_status: 'pending',
          rating: 0,
          total_reviews: 0,
          trust_score: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) throw error;
      return serviceProvider;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-service-provider'] });
      queryClient.invalidateQueries({ queryKey: ['service-providers'] });
      toast({
        title: 'Success!',
        description: 'Service provider profile created successfully',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create service provider profile',
        variant: 'destructive'
      });
    }
  });
}
