
import React from 'react';
import { Link } from 'react-router-dom';
import { useAuth } from '@/hooks/use-auth';
import { Button } from "@/components/ui/button";
import { NavBar } from "@/components/ui/tubelight-navbar";
import { Home, Map, Activity, Store, LogIn, UserPlus } from "lucide-react";
import { UserMenu } from "@/components/navigation/UserMenu";

const navItems = [
  { name: 'Home', url: '/', icon: Home },
  { name: 'MeetMap', url: '/meetmap', icon: Map },
  { name: 'Activity', url: '/activity', icon: Activity },
  { name: 'Gigs', url: '/gigs', icon: Store }
];

export function AppNav() {
  const { user, profile, signOut } = useAuth();
  
  return (
    <nav className="sticky top-0 z-50 border-b border-border/40 bg-gradient-to-b from-background/80 to-background/40 backdrop-blur-sm shadow-custom">
      <div className="container mx-auto px-4">
        <div className="h-16 flex items-center justify-between">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-2">
            <span className="font-bold text-xl text-primary">BuddySurf</span>
          </Link>
          
          {/* Main Navigation */}
          <div className="flex-1 flex justify-center">
            <NavBar items={navItems} />
          </div>
          
          {/* Auth Buttons / User Menu */}
          <div className="flex items-center space-x-2">
            {user ? (
              <UserMenu user={user} profile={profile} signOut={signOut} />
            ) : (
              <>
                <Button asChild variant="ghost" size="sm">
                  <Link to="/login" className="flex items-center gap-1">
                    <LogIn className="h-4 w-4 mr-1" />
                    Log In
                  </Link>
                </Button>
                <Button asChild>
                  <Link to="/signup" className="flex items-center gap-1">
                    <UserPlus className="h-4 w-4 mr-1" />
                    Sign Up
                  </Link>
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
