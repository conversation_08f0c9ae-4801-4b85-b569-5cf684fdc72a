
@import '@/styles/map-markers.css';
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 94%; /* #F0F0F0 */
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 56%; /* #1E90FF */
    --primary-foreground: 210 40% 98%;

    --secondary: 212 100% 20%; /* #003366 */
    --secondary-foreground: 210 40% 98%;

    --muted: 0 0% 83%; /* #D3D3D3 */
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 100% 56%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 212 100% 20%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 94%;
    --sidebar-foreground: 212 100% 20%;
    --sidebar-primary: 210 100% 56%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 83%;
    --sidebar-accent-foreground: 212 100% 20%;
    --sidebar-border: 0 0% 83%;
    --sidebar-ring: 210 100% 56%;

    /* Added variables for vibration animation */
    --primary-purple: 258 90% 66%;
    --primary-deep-purple: 273 90% 50%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 100% 56%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 212 100% 20%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 210 100% 56%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;

    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 210 100% 56%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 210 100% 56%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gradient-to-b from-[#F0F0F0] to-[#D3D3D3] text-foreground min-h-screen;
  }
}

@layer utilities {
  /* Hide scrollbar for Chrome, Safari and Opera */
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  /* Hide scrollbar for IE, Edge and Firefox */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
  }

  /* Animation utilities */
  .animate-fadeIn {
    animation: fadeIn 0.5s ease-in-out;
  }

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }

  /* Debug utilities */
  .debug-outline {
    outline: 2px solid red;
  }

  .debug-router {
    position: fixed;
    bottom: 10px;
    right: 10px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 9999;
  }

  .router-context-available {
    border: 2px solid green;
  }

  .router-context-missing {
    border: 2px solid red;
  }
}
