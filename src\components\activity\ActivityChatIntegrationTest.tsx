import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, MessageSquare, Users, Calendar } from 'lucide-react';

interface ActivityChatIntegrationTestProps {
  activityId: string;
  activityTitle: string;
}

export function ActivityChatIntegrationTest({
  activityId,
  activityTitle
}: ActivityChatIntegrationTestProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [isCreatingChat, setIsCreatingChat] = useState(false);
  const [isJoiningActivity, setIsJoiningActivity] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, result]);
  };

  const testCreateGroupChat = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "Please sign in to test chat integration",
        variant: "destructive"
      });
      return;
    }

    setIsCreatingChat(true);
    addTestResult("🧪 Testing group chat creation...");

    try {
      // Test the create_activity_group_chat function
      const { data: chatId, error } = await supabase.rpc('create_activity_group_chat', {
        p_activity_id: activityId
      });

      if (error) {
        addTestResult(`❌ Failed to create group chat: ${error.message}`);
        return;
      }

      addTestResult(`✅ Group chat created successfully! Chat ID: ${chatId}`);

      // Verify the chat was created
      const { data: conversation, error: fetchError } = await supabase
        .from('chat_conversations')
        .select('*')
        .eq('id', chatId)
        .single();

      if (fetchError) {
        addTestResult(`❌ Failed to fetch created chat: ${fetchError.message}`);
        return;
      }

      addTestResult(`✅ Chat verified: ${conversation.id}`);
      addTestResult(`📋 Chat is group chat: ${conversation.is_group}`);

    } catch (error) {
      addTestResult(`❌ Error: ${error}`);
    } finally {
      setIsCreatingChat(false);
    }
  };

  const testJoinActivity = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "Please sign in to test activity joining",
        variant: "destructive"
      });
      return;
    }

    setIsJoiningActivity(true);
    addTestResult("🧪 Testing activity join and auto-chat addition...");

    try {
      // First, add user to activity queue
      const { data: queueData, error: queueError } = await supabase
        .from('activity_queue')
        .insert({
          activity_id: activityId,
          user_id: user.id,
          status: 'confirmed',
          position: 1
        })
        .select()
        .single();

      if (queueError) {
        addTestResult(`❌ Failed to join activity: ${queueError.message}`);
        return;
      }

      addTestResult(`✅ Joined activity queue successfully!`);

      // Wait a moment for the trigger to fire
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Check if user was added to chat
      const { data: activity, error: activityError } = await supabase
        .from('activities')
        .select('group_chat_id')
        .eq('id', activityId)
        .single();

      if (activityError || !activity.group_chat_id) {
        addTestResult(`❌ Activity doesn't have a group chat yet`);
        return;
      }

      const { data: participant, error: participantError } = await supabase
        .from('chat_participants')
        .select('*')
        .eq('conversation_id', activity.group_chat_id)
        .eq('user_id', user.id)
        .single();

      if (participantError) {
        addTestResult(`❌ User not found in chat participants: ${participantError.message}`);
        return;
      }

      addTestResult(`✅ User automatically added to group chat!`);
      addTestResult(`📋 Role: ${participant.role}`);

      // Check for system message
      const { data: messages, error: messagesError } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', activity.group_chat_id)
        .eq('is_system_message', true)
        .order('created_at', { ascending: false })
        .limit(1);

      if (messagesError) {
        addTestResult(`❌ Failed to fetch system messages: ${messagesError.message}`);
        return;
      }

      if (messages && messages.length > 0) {
        addTestResult(`✅ System message created: "${messages[0].content}"`);
      }

    } catch (error) {
      addTestResult(`❌ Error: ${error}`);
    } finally {
      setIsJoiningActivity(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5" />
          Activity-Chat Integration Test
        </CardTitle>
        <div className="flex items-center gap-2">
          <Badge variant="outline">{activityTitle}</Badge>
          <Badge variant="secondary">ID: {activityId.slice(0, 8)}...</Badge>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="flex gap-2 flex-wrap">
          <Button
            onClick={testCreateGroupChat}
            disabled={isCreatingChat || !user}
            className="flex items-center gap-2"
          >
            {isCreatingChat ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Users className="h-4 w-4" />
            )}
            Test Group Chat Creation
          </Button>

          <Button
            onClick={testJoinActivity}
            disabled={isJoiningActivity || !user}
            variant="outline"
            className="flex items-center gap-2"
          >
            {isJoiningActivity ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Calendar className="h-4 w-4" />
            )}
            Test Activity Join
          </Button>

          <Button
            onClick={clearResults}
            variant="ghost"
            size="sm"
          >
            Clear Results
          </Button>
        </div>

        {testResults.length > 0 && (
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium mb-2">Test Results:</h4>
            <div className="space-y-1 text-sm font-mono">
              {testResults.map((result, index) => (
                <div key={index} className="text-gray-700">
                  {result}
                </div>
              ))}
            </div>
          </div>
        )}

        {!user && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <p className="text-yellow-800 text-sm">
              Please sign in to test the activity-chat integration features.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
