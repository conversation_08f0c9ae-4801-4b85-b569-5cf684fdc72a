
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Transaction {
  id: string;
  user_id: string;
  amount: number;
  description: string;
  type: 'deposit' | 'withdrawal' | 'payment' | 'refund' | 'pending';
  status: 'completed' | 'pending' | 'failed';
  payment_id: string | null;
  activity_id: string | null;
  created_at: string;
}

export function useTransactions(userId: string | undefined) {
  const { toast } = useToast();

  return useQuery({
    queryKey: ['transactions', userId],
    queryFn: async (): Promise<Transaction[]> => {
      if (!userId) return [];

      try {
        // Mock data instead of querying non-existent table
        const mockTransactions: Transaction[] = [
          {
            id: '1',
            user_id: userId,
            amount: 25.00,
            description: 'Added funds to wallet',
            type: 'deposit',
            status: 'completed',
            payment_id: 'pay_123456',
            activity_id: null,
            created_at: new Date().toISOString()
          },
          {
            id: '2',
            user_id: userId,
            amount: 15.00,
            description: 'Payment for activity booking',
            type: 'payment',
            status: 'completed',
            payment_id: 'pay_789012',
            activity_id: 'act_123',
            created_at: new Date(Date.now() - 86400000).toISOString() // 1 day ago
          },
          {
            id: '3',
            user_id: userId,
            amount: 5.00,
            description: 'Service fee refund',
            type: 'refund',
            status: 'pending',
            payment_id: 'ref_345678',
            activity_id: null,
            created_at: new Date(Date.now() - 172800000).toISOString() // 2 days ago
          }
        ];

        return mockTransactions;
      } catch (error) {
        toast({
          title: 'Error fetching transactions',
          description: error instanceof Error ? error.message : 'Unknown error',
          variant: 'destructive'
        });
        throw error;
      }
    },
    enabled: !!userId
  });
}
