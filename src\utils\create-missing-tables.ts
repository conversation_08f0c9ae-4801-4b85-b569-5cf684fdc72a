import { supabase } from '@/integrations/supabase/client';

/**
 * Creates missing tables in the database if they don't exist
 * This is useful for features that rely on tables that might not exist yet
 */
export async function createMissingTables(): Promise<void> {
  // Check if the message_reactions table exists
  const { data: tableExistsData, error: tableError } = await supabase.rpc('check_table_exists', {
    table_name: 'message_reactions'
  });
  
  const messageReactionsExists = tableExistsData === true;
  
  if (!messageReactionsExists) {
    console.log('Creating missing message_reactions table');
    
    // Create the message_reactions table if it doesn't exist
    const { error: createError } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS public.message_reactions (
          id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
          message_id TEXT NOT NULL,
          conversation_id UUID NOT NULL,
          user_id UUID NOT NULL,
          emoji TEXT NOT NULL,
          count INTEGER DEFAULT 1,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc', now()),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc', now()),
          UNIQUE(message_id, user_id, emoji)
        );

        -- Add RLS policies
        ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;

        -- Insert policy: Any authenticated user can insert their own reactions
        CREATE POLICY "Users can add their own reactions" ON public.message_reactions
          FOR INSERT
          TO authenticated
          WITH CHECK (auth.uid() = user_id);

        -- Select policy: Users can read reactions for conversations they participate in
        CREATE POLICY "Users can read reactions for their conversations" ON public.message_reactions
          FOR SELECT
          TO authenticated
          USING (
            EXISTS (
              SELECT 1 FROM public.chat_participants cp
              WHERE cp.conversation_id = message_reactions.conversation_id
              AND cp.user_id = auth.uid()
            )
          );

        -- Update policy: Users can only update their own reactions
        CREATE POLICY "Users can update their own reactions" ON public.message_reactions
          FOR UPDATE
          TO authenticated
          USING (auth.uid() = user_id)
          WITH CHECK (auth.uid() = user_id);

        -- Delete policy: Users can only delete their own reactions
        CREATE POLICY "Users can delete their own reactions" ON public.message_reactions
          FOR DELETE
          TO authenticated
          USING (auth.uid() = user_id);
      `
    });

    if (createError) {
      console.error('Error creating message_reactions table:', createError);
      throw new Error('Failed to create message_reactions table');
    }
  }
  
  // Here we could check for other missing tables and create them as needed
}

/**
 * Initializes the database by creating missing tables
 * This function should be called when the application starts
 */
// Create activity tables if they don't exist
async function createActivityTables() {
  try {
    // Check if activities table exists
    const { data, error } = await supabase
      .from('activities')
      .select('id')
      .limit(1);

    // If the table exists, we don't need to create it
    if (!error) {
      console.log('Activities table already exists');
      return;
    }

    console.log('Creating activity tables...');

    // Execute the create_activity_tables function
    try {
      await supabase.rpc('exec_sql', {
        sql: `
          CREATE OR REPLACE FUNCTION create_activity_tables()
          RETURNS boolean
          LANGUAGE plpgsql
          SECURITY DEFINER
          AS $$
          BEGIN
            -- Function to check if a user is an admin
            CREATE OR REPLACE FUNCTION public.is_admin()
            RETURNS BOOLEAN AS $func$
            BEGIN
              RETURN (
                SELECT email = '<EMAIL>'
                FROM auth.users
                WHERE id = auth.uid()
              );
            END;
            $func$ LANGUAGE plpgsql SECURITY DEFINER;

            -- Create categories table if it doesn't exist
            CREATE TABLE IF NOT EXISTS public.categories (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              name TEXT NOT NULL,
              icon TEXT,
              type TEXT NOT NULL,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );

            -- Create activities table if it doesn't exist
            CREATE TABLE IF NOT EXISTS public.activities (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              title TEXT NOT NULL,
              description TEXT,
              category_id UUID REFERENCES categories(id),
              location POINT NOT NULL,
              address TEXT,
              start_time TIMESTAMP WITH TIME ZONE NOT NULL,
              end_time TIMESTAMP WITH TIME Z,
              is_paid BOOLEAN DEFAULT FALSE,
              price NUMERIC,
              max_participants INTEGER,
              host_id UUID NOT NULL REFERENCES auth.users(id),
              status TEXT DEFAULT 'active',
              visibility TEXT DEFAULT 'public',
              queue_type TEXT DEFAULT 'fifo',
              allow_waitlist BOOLEAN DEFAULT TRUE,
              group_chat_id UUID,
              media_urls TEXT[],
              early_bird_price NUMERIC,
              group_discount_threshold INTEGER,
              group_discount_percentage NUMERIC,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
            );

            -- Create activity_participants table if it doesn't exist
            CREATE TABLE IF NOT EXISTS public.activity_participants (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              activity_id UUID NOT NULL REFERENCES activities(id) ON DELETE CASCADE,
              user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
              status TEXT NOT NULL DEFAULT 'pending',
              joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              payment_status TEXT,
              payment_id TEXT,
              is_host BOOLEAN DEFAULT FALSE,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              UNIQUE(activity_id, user_id)
            );

            -- Create activity_queue table if it doesn't exist
            CREATE TABLE IF NOT EXISTS public.activity_queue (
              id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
              activity_id UUID NOT NULL REFERENCES activities(id) ON DELETE CASCADE,
              user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
              status TEXT NOT NULL DEFAULT 'pending',
              position INTEGER NOT NULL,
              payment_id TEXT,
              created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
              UNIQUE(activity_id, user_id)
            );

            -- Enable Row-Level Security on all tables
            ALTER TABLE public.activities ENABLE ROW LEVEL SECURITY;
            ALTER TABLE public.activity_participants ENABLE ROW LEVEL SECURITY;
            ALTER TABLE public.activity_queue ENABLE ROW LEVEL SECURITY;

            -- Activities Table Policies
            DROP POLICY IF EXISTS "Users can view all activities" ON public.activities;
            CREATE POLICY "Users can view all activities"
            ON public.activities
            FOR SELECT
            USING (true);

            DROP POLICY IF EXISTS "Users can create activities" ON public.activities;
            CREATE POLICY "Users can create activities"
            ON public.activities
            FOR INSERT
            WITH CHECK ((host_id = auth.uid()));

            DROP POLICY IF EXISTS "Users can update their own activities" ON public.activities;
            CREATE POLICY "Users can update their own activities"
            ON public.activities
            FOR UPDATE
            USING ((host_id = auth.uid()))
            WITH CHECK ((host_id = auth.uid()));

            DROP POLICY IF EXISTS "Users can delete their own activities" ON public.activities;
            CREATE POLICY "Users can delete their own activities"
            ON public.activities
            FOR DELETE
            USING ((host_id = auth.uid()));

            DROP POLICY IF EXISTS "Admins can manage all activities" ON public.activities;
            CREATE POLICY "Admins can manage all activities"
            ON public.activities
            FOR ALL
            USING (is_admin());

            -- Insert default categories
            INSERT INTO public.categories (name, icon, type)
            SELECT 'Social', '🎭', 'activity'
            WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Social' AND type = 'activity');

            INSERT INTO public.categories (name, icon, type)
            SELECT 'Sports', '⚽', 'activity'
            WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Sports' AND type = 'activity');

            INSERT INTO public.categories (name, icon, type)
            SELECT 'Food & Drink', '🍔', 'activity'
            WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Food & Drink' AND type = 'activity');

            INSERT INTO public.categories (name, icon, type)
            SELECT 'Arts & Culture', '🎨', 'activity'
            WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Arts & Culture' AND type = 'activity');

            INSERT INTO public.categories (name, icon, type)
            SELECT 'Outdoors', '🏞️', 'activity'
            WHERE NOT EXISTS (SELECT 1 FROM public.categories WHERE name = 'Outdoors' AND type = 'activity');

            RETURN TRUE;
          END;
          $$;

          SELECT create_activity_tables();
        `
      });
      console.log('Activity tables created or already exist');
    } catch (error) {
      console.error('Error creating activity tables:', error);
    }
  } catch (error) {
    console.error('Error checking for activities table:', error);
  }
}

export async function initializeDatabase() {
  try {
    await createMissingTables();
    await createActivityTables();
    console.log('Database initialization complete');
  } catch (error) {
    console.error('Error initializing database:', error);
  }
}
