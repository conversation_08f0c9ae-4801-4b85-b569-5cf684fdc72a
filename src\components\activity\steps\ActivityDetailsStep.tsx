import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ActivityCreationValues } from '@/schemas/activity-creation-schema';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Switch } from '@/components/ui/switch';
import { ActivityDatePicker } from '@/components/ui/activity-date-picker';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar, Clock, Users, DollarSign } from 'lucide-react';

interface ActivityDetailsStepProps {
  form: UseFormReturn<ActivityCreationValues>;
  selectedStartDate: Date | undefined;
  startTime: string;
  onStartDateChange: (date: Date | undefined) => void;
  onStartTimeChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function ActivityDetailsStep({
  form,
  selectedStartDate,
  startTime,
  onStartDateChange,
  onStartTimeChange
}: ActivityDetailsStepProps) {
  const isPaid = form.watch('is_paid');

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-xl font-medium text-center text-primary-purple">Activity Details</h3>
        <p className="text-sm text-muted-foreground text-center">
          <span className="text-red-500">*</span> Required information
        </p>
      </div>

      {/* Date selection - more prominent */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-4 py-3 border-b border-gray-200">
          <h4 className="font-medium text-gray-800">When is your activity?</h4>
        </div>

        <div className="p-4 space-y-6">
          {/* Date picker - full width and prominent */}
          <div className="space-y-4">
            <FormLabel>Activity Date <span className="text-red-500">*</span></FormLabel>
            <div className="flex items-center">
              <Calendar className="h-4 w-4 mr-2 text-primary" />
              <div className="w-full">
                <ActivityDatePicker
                  value={selectedStartDate}
                  onChange={onStartDateChange}
                  placeholder="Select a future date"
                />
              </div>
            </div>
            <p className="text-xs text-muted-foreground">
              Your activity will be scheduled for a future date
            </p>
            <FormMessage>{form.formState.errors.start_time?.message}</FormMessage>
          </div>

          {/* Time picker - only shown after date is selected */}
          {selectedStartDate && (
            <div className="space-y-4 animate-fadeIn">
              <FormLabel>Start Time <span className="text-red-500">*</span></FormLabel>
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-primary" />
                <Input
                  type="time"
                  value={startTime}
                  onChange={onStartTimeChange}
                  className="border-gray-300 focus:border-primary focus:ring-primary"
                />
              </div>
              <p className="text-xs text-muted-foreground">
                Select a time for your activity on {selectedStartDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric' })}
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Interactive card for participation details */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gradient-to-r from-green-50 to-teal-50 px-4 py-3 border-b border-gray-200">
          <h4 className="font-medium text-gray-800">Who can participate?</h4>
        </div>

        <div className="p-4">
          <FormField
            control={form.control}
            name="max_participants"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Maximum Participants</FormLabel>
                <div className="flex items-center">
                  <Users className="h-4 w-4 mr-2 text-primary" />
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Leave blank for unlimited"
                      {...field}
                      value={field.value === null ? '' : field.value}
                      onChange={e => field.onChange(e.target.value === '' ? null : parseInt(e.target.value))}
                      className="border-gray-300 focus:border-primary focus:ring-primary"
                    />
                  </FormControl>
                </div>
                <FormDescription>
                  Set a limit on how many people can join your activity
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>

      {/* Interactive card for payment details */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gradient-to-r from-amber-50 to-yellow-50 px-4 py-3 border-b border-gray-200">
          <h4 className="font-medium text-gray-800">Payment Details</h4>
        </div>

        <div className="p-4 space-y-6">
          <FormField
            control={form.control}
            name="is_paid"
            render={({ field }) => (
              <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 bg-gray-50">
                <div className="space-y-0.5">
                  <FormLabel className="text-base">Paid Activity</FormLabel>
                  <FormDescription>
                    Will participants need to pay to join this activity?
                  </FormDescription>
                </div>
                <FormControl>
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                    className="data-[state=checked]:bg-primary"
                  />
                </FormControl>
              </FormItem>
            )}
          />

          {isPaid && (
            <FormField
              control={form.control}
              name="price"
              render={({ field }) => (
                <FormItem className="animate-fadeIn">
                  <FormLabel>Price <span className="text-red-500">*</span></FormLabel>
                  <div className="flex items-center">
                    <DollarSign className="h-4 w-4 mr-2 text-primary" />
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter price"
                        {...field}
                        value={field.value === null ? '' : field.value}
                        onChange={e => field.onChange(e.target.value === '' ? null : parseFloat(e.target.value))}
                        className="border-gray-300 focus:border-primary focus:ring-primary"
                      />
                    </FormControl>
                  </div>
                  <FormDescription>
                    How much will participants pay to join?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          )}
        </div>
      </div>

      {/* Interactive card for visibility settings */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        <div className="bg-gradient-to-r from-purple-50 to-indigo-50 px-4 py-3 border-b border-gray-200">
          <h4 className="font-medium text-gray-800">Visibility Settings</h4>
        </div>

        <div className="p-4">
          <FormField
            control={form.control}
            name="visibility"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Who can see your activity?</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="border-gray-300 focus:border-primary focus:ring-primary">
                      <SelectValue placeholder="Select visibility" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="public">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                        Public (visible to everyone)
                      </div>
                    </SelectItem>
                    <SelectItem value="unlisted">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                        Unlisted (only visible with link)
                      </div>
                    </SelectItem>
                    <SelectItem value="private">
                      <div className="flex items-center">
                        <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                        Private (invite only)
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  Control who can see and join your activity
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>
      </div>
    </div>
  );
}
