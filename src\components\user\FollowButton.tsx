
import { Button } from "@/components/ui/button";
import { useFollows } from "@/hooks/use-follows";
import { useAuth } from "@/hooks/use-auth";
import { UserP<PERSON>, User<PERSON>he<PERSON>, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { useCallback } from "react";

interface FollowButtonProps {
  userId: string;
  className?: string;
  size?: "default" | "sm" | "lg" | "icon";
  variant?: "default" | "outline" | "secondary" | "ghost";
  showIcon?: boolean;
  onFollowChange?: (isFollowing: boolean) => void;
  disabled?: boolean;
}

export function FollowButton({ 
  userId, 
  className, 
  size = "sm", 
  variant,
  showIcon = true,
  onFollowChange,
  disabled = false
}: FollowButtonProps) {
  const { user } = useAuth();
  const { isFollowing, follow, unfollow, isFollowLoading } = useFollows(userId);
  
  const handleFollowAction = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();  // Prevent click propagation
    
    if (isFollowing) {
      unfollow(userId);
      onFollowChange?.(false);
    } else {
      follow(userId);
      onFollowChange?.(true);
    }
  }, [isFollowing, follow, unfollow, userId, onFollowChange]);
  
  if (!user || user.id === userId) return null;

  // Determine the appropriate variant based on following status
  const buttonVariant = variant || (isFollowing ? "outline" : "default");
  
  return (
    <Button
      variant={buttonVariant}
      size={size}
      className={cn("transition-all", className)}
      onClick={handleFollowAction}
      disabled={disabled || isFollowLoading}
    >
      {isFollowLoading ? (
        <>
          <Loader2 className={cn("animate-spin", showIcon ? "mr-2 h-4 w-4" : "h-4 w-4")} />
          <span className="whitespace-nowrap">
            {showIcon && (isFollowing ? "Unfollowing..." : "Following...")}
          </span>
        </>
      ) : (
        <>
          {showIcon && isFollowing ? (
            <UserCheck className="mr-2 h-4 w-4" />
          ) : showIcon ? (
            <UserPlus className="mr-2 h-4 w-4" />
          ) : null}
          <span className="whitespace-nowrap">
            {isFollowing ? "Following" : "Follow"}
          </span>
        </>
      )}
    </Button>
  );
}
