
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getInitials } from '@/utils/get-initials';
import './typing-indicator.css';

interface TypingUser {
  id: string;
  display_name?: string;
  username?: string;
  avatar_url?: string;
}

export interface TypingIndicatorProps {
  users: TypingUser[];
  className?: string; // Added className prop
}

export function TypingIndicator({ users, className }: TypingIndicatorProps) {
  if (!users || users.length === 0) return null;

  return (
    <div className={`flex items-center gap-2 py-2 ${className || ''}`}>
      <Avatar className="h-8 w-8">
        <AvatarImage src={users[0].avatar_url} />
        <AvatarFallback>
          {getInitials(users[0].display_name || users[0].username)}
        </AvatarFallback>
      </Avatar>
      <div className="bg-muted/50 rounded-lg px-4 py-2 flex items-center">
        <span className="text-sm text-muted-foreground mr-2">
          {users.length === 1
            ? `${users[0].display_name || users[0].username || 'Someone'} is typing`
            : `${users.length} people are typing`}
        </span>
        <div className="typing-indicator">
          <span></span>
          <span></span>
          <span></span>
        </div>
      </div>
    </div>
  );
}
