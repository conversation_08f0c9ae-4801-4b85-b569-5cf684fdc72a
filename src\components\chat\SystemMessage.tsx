import React from 'react';
import { Badge } from "@/components/ui/badge";
import { 
  UserPlus, 
  UserMinus, 
  Clock, 
  MapPin, 
  AlertCircle, 
  CheckCircle, 
  XCircle,
  Calendar,
  Users,
  MessageSquare
} from 'lucide-react';
import { format } from 'date-fns';

interface SystemMessageProps {
  content: string;
  createdAt: string;
  metadata?: {
    update_type?: string;
    activity_id?: string;
    proposal_response?: string;
    [key: string]: any;
  };
}

export function SystemMessage({ content, createdAt, metadata }: SystemMessageProps) {
  const getSystemIcon = () => {
    if (!metadata?.update_type) {
      // Default system message (user joined/left)
      if (content.includes('joined')) {
        return <UserPlus className="w-4 h-4 text-green-600" />;
      }
      if (content.includes('left')) {
        return <UserMinus className="w-4 h-4 text-orange-600" />;
      }
      if (content.includes('accepted')) {
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      }
      if (content.includes('declined')) {
        return <XCircle className="w-4 h-4 text-red-600" />;
      }
      return <MessageSquare className="w-4 h-4 text-blue-600" />;
    }

    // Activity update messages
    switch (metadata.update_type) {
      case 'time_change':
        return <Clock className="w-4 h-4 text-blue-600" />;
      case 'location_change':
        return <MapPin className="w-4 h-4 text-purple-600" />;
      case 'status_change':
        if (content.includes('cancelled')) {
          return <XCircle className="w-4 h-4 text-red-600" />;
        }
        if (content.includes('completed')) {
          return <CheckCircle className="w-4 h-4 text-green-600" />;
        }
        return <AlertCircle className="w-4 h-4 text-orange-600" />;
      default:
        return <MessageSquare className="w-4 h-4 text-gray-600" />;
    }
  };

  const getSystemBadge = () => {
    if (!metadata?.update_type) {
      if (content.includes('joined')) {
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">User Joined</Badge>;
      }
      if (content.includes('left')) {
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">User Left</Badge>;
      }
      if (content.includes('accepted')) {
        return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Proposal Accepted</Badge>;
      }
      if (content.includes('declined')) {
        return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Proposal Declined</Badge>;
      }
      return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">System</Badge>;
    }

    switch (metadata.update_type) {
      case 'time_change':
        return <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">Time Updated</Badge>;
      case 'location_change':
        return <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">Location Updated</Badge>;
      case 'status_change':
        if (content.includes('cancelled')) {
          return <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">Activity Cancelled</Badge>;
        }
        if (content.includes('completed')) {
          return <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">Activity Completed</Badge>;
        }
        return <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">Status Updated</Badge>;
      default:
        return <Badge variant="outline" className="bg-gray-50 text-gray-700 border-gray-200">System</Badge>;
    }
  };

  const getBackgroundColor = () => {
    if (!metadata?.update_type) {
      if (content.includes('joined')) return 'bg-green-50/50';
      if (content.includes('left')) return 'bg-orange-50/50';
      if (content.includes('accepted')) return 'bg-green-50/50';
      if (content.includes('declined')) return 'bg-red-50/50';
      return 'bg-blue-50/50';
    }

    switch (metadata.update_type) {
      case 'time_change':
        return 'bg-blue-50/50';
      case 'location_change':
        return 'bg-purple-50/50';
      case 'status_change':
        if (content.includes('cancelled')) return 'bg-red-50/50';
        if (content.includes('completed')) return 'bg-green-50/50';
        return 'bg-orange-50/50';
      default:
        return 'bg-gray-50/50';
    }
  };

  return (
    <div className="flex justify-center my-4">
      <div className={`max-w-md mx-auto rounded-lg border p-3 ${getBackgroundColor()}`}>
        <div className="flex items-center gap-3">
          <div className="flex-shrink-0">
            {getSystemIcon()}
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              {getSystemBadge()}
              <span className="text-xs text-muted-foreground">
                {format(new Date(createdAt), 'h:mm a')}
              </span>
            </div>
            
            <p className="text-sm text-gray-700 leading-relaxed">
              {content}
            </p>
            
            {/* Additional context for activity updates */}
            {metadata?.activity_id && metadata.update_type && (
              <div className="mt-2 pt-2 border-t border-gray-200">
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Calendar className="w-3 h-3" />
                  <span>Activity Update</span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
