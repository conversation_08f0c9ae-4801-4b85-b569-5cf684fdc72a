
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { UseFormReturn } from 'react-hook-form';

interface ActivityTimePickerProps {
  form: UseFormReturn<any>;
}

export function ActivityTimePicker({ form }: ActivityTimePickerProps) {
  return (
    <div className="space-y-4">
      <FormField
        control={form.control}
        name="startTime"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Start Time</FormLabel>
            <FormControl>
              <Input 
                type="datetime-local"
                value={field.value ? new Date(field.value).toISOString().slice(0, 16) : ''}
                onChange={e => field.onChange(new Date(e.target.value))}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="endTime"
        render={({ field }) => (
          <FormItem>
            <FormLabel>End Time</FormLabel>
            <FormControl>
              <Input 
                type="datetime-local"
                value={field.value ? new Date(field.value).toISOString().slice(0, 16) : ''}
                onChange={e => field.onChange(e.target.value ? new Date(e.target.value) : null)}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
