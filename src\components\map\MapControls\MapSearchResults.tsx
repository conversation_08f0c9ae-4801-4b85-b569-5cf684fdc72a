
import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { MapPin, UserX, Search } from "lucide-react";
import { UnifiedMapUser } from '@/types/map';
import { cn } from "@/lib/utils";
import { Skeleton } from "@/components/ui/skeleton";

interface MapSearchResultsProps {
  results: UnifiedMapUser[];  // Updated type
  searchQuery: string;
  onSelectResult: (result: UnifiedMapUser) => void;  // Updated type
  onClose: () => void;
  className?: string;
  isLoading?: boolean;
}

const MapSearchResults = ({ 
  results, 
  searchQuery, 
  onSelectResult, 
  onClose,
  className,
  isLoading = false
}: MapSearchResultsProps) => {
  const renderResultItem = (user: UnifiedMapUser) => {
    // Safety check for required user properties
    if (!user || !user.user_id) {
      return null;
    }
    
    return (
      <div 
        key={user.user_id}
        className="flex items-center gap-2 p-2 rounded-md hover:bg-accent cursor-pointer"
        onClick={() => onSelectResult(user)}
      >
        <div className="w-10 h-10 rounded-full overflow-hidden border border-border">
          <img 
            src={user.avatar_url || `https://i.pravatar.cc/150?u=${user.user_id}`} 
            alt={user.display_name || user.username || `User ${user.user_id}`} 
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement;
              target.src = "https://images.unsplash.com/photo-1535713875002-d1d0cf377fde?auto=format&fit=crop&w=80&h=80";
            }}
          />
        </div>
        <div className="flex-1">
          <p className="text-sm font-medium truncate">
            {user.display_name || user.username || `User ${user.user_id.substring(0, 5)}`}
          </p>
          <p className="text-xs text-muted-foreground flex items-center gap-1">
            <MapPin size={10} /> 
            {user.location ? 
              `${user.location.x.toFixed(2)}, ${user.location.y.toFixed(2)}` : 
              'Location unknown'}
          </p>
        </div>
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="absolute top-16 left-4 z-10 w-72 bg-background rounded-lg shadow-lg border border-border">
        <div className="flex justify-between items-center p-3 border-b">
          <h3 className="text-sm font-medium">Searching...</h3>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-7 w-7 p-0">×</Button>
        </div>
        <div className="p-3">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex items-center gap-2 p-2 mb-2">
              <Skeleton className="w-10 h-10 rounded-full" />
              <div className="flex-1">
                <Skeleton className="h-4 w-3/4 mb-1" />
                <Skeleton className="h-3 w-1/2" />
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (!results || results.length === 0) {
    return (
      <div className="absolute top-16 left-4 z-10 w-72 bg-background rounded-lg shadow-lg border border-border">
        <div className="flex justify-between items-center p-3 border-b">
          <h3 className="text-sm font-medium">No Results</h3>
          <Button variant="ghost" size="sm" onClick={onClose} className="h-7 w-7 p-0">×</Button>
        </div>
        <div className="p-4 text-center">
          <UserX className="h-12 w-12 text-muted-foreground mx-auto mb-2" />
          <p className="text-sm text-muted-foreground">
            No users found matching "{searchQuery || 'your search'}"
          </p>
          <Button 
            variant="outline" 
            size="sm" 
            className="mt-3 w-full" 
            onClick={onClose}
          >
            Clear Search
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className={cn("absolute top-16 left-4 z-10 w-72 bg-background rounded-lg shadow-lg border border-border", className)}>
      <div className="flex justify-between items-center p-3 border-b">
        <h3 className="text-sm font-medium">Search Results</h3>
        <Button variant="ghost" size="sm" onClick={onClose} className="h-7 w-7 p-0">×</Button>
      </div>
      <ScrollArea className="h-[280px]">
        <div className="p-1">
          {results.map(user => renderResultItem(user))}
        </div>
      </ScrollArea>
    </div>
  );
};

export default MapSearchResults;
