
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from '@/hooks/use-auth';
import { useWallet } from '@/hooks/use-wallet';
import { Skeleton } from '@/components/ui/skeleton';

export function WalletBalance() {
  const { user } = useAuth();
  const { data: wallet, isLoading } = useWallet(user?.id);

  return (
    <Card>
      <CardHeader>
        <CardTitle>Wallet Balance</CardTitle>
        <CardDescription>Your current available funds</CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <Skeleton className="h-20 w-full" />
        ) : (
          <div className="flex flex-col">
            <div className="text-3xl font-bold">${wallet?.balance.toFixed(2) || '0.00'}</div>
            <p className="text-sm text-muted-foreground mt-1">
              Last updated: {wallet?.updated_at ? 
                new Date(wallet.updated_at).toLocaleString() : 
                'Never'}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
