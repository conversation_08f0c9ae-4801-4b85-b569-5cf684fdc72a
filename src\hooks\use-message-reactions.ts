
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './use-auth';
import { useState, useEffect } from 'react';
import { useSupabaseRealtime } from './use-supabase-realtime';
import { MessageReaction as MessageReactionType } from '@/types/message-reaction';

interface GroupedReaction {
  id: string;
  emoji: string;
  count: number;
  user_ids: string[];
}

interface UseMessageReactionsResult {
  reactions: GroupedReaction[];
  isLoading: boolean;
  addReaction: ReturnType<typeof useMutation>;
  removeReaction: ReturnType<typeof useMutation>;
  hasUserReacted: (emoji: string) => boolean;
}

export function useMessageReactions(messageId: string, conversationId?: string): UseMessageReactionsResult {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [reactions, setReactions] = useState<GroupedReaction[]>([]);
  const realtime = useSupabaseRealtime();

  // Get message conversation ID if not provided
  const { data: message } = useQuery({
    queryKey: ['message-conversation', messageId],
    queryFn: async () => {
      if (conversationId) return { conversation_id: conversationId };

      const { data, error } = await supabase
        .from('messages')
        .select('conversation_id')
        .eq('id', messageId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !conversationId,
  });

  // Get reactions for the message
  const { data: rawReactions, isLoading } = useQuery({
    queryKey: ['message-reactions', messageId],
    queryFn: async (): Promise<MessageReactionType[]> => {
      const { data, error } = await supabase
        .from('message_reactions')
        .select('*')
        .eq('message_id', messageId);

      if (error) throw error;

      // Add computed count property to each reaction to satisfy type requirements
      return (data || []).map(reaction => ({
        ...reaction,
        count: 1 // Default count for raw reactions
      }));
    },
    enabled: !!messageId,
  });

  // Group reactions by emoji
  useEffect(() => {
    if (!rawReactions) {
      setReactions([]);
      return;
    }

    try {
      // Group reactions by emoji
      const groupedReactions: Record<string, { count: number, user_ids: string[] }> = {};

      rawReactions.forEach((reaction) => {
        if (!reaction.emoji) return;

        if (!groupedReactions[reaction.emoji]) {
          groupedReactions[reaction.emoji] = { count: 0, user_ids: [] };
        }

        if (!groupedReactions[reaction.emoji].user_ids.includes(reaction.user_id)) {
          groupedReactions[reaction.emoji].count++;
          groupedReactions[reaction.emoji].user_ids.push(reaction.user_id);
        }
      });

      const formattedReactions = Object.entries(groupedReactions).map(([emoji, data]) => ({
        id: `${messageId}-${emoji}`,
        emoji,
        count: data.count,
        user_ids: data.user_ids
      }));

      setReactions(formattedReactions);
    } catch (e) {
      console.error("Error processing reactions:", e);
      setReactions([]);
    }
  }, [rawReactions, messageId]);

  // Subscribe to real-time updates for reactions
  useEffect(() => {
    if (!messageId || !message?.conversation_id) return;

    const channelName = `reactions:${messageId}`;
    const unsubscribe = realtime.subscribe(
      channelName,
      {
        table: 'message_reactions',
        filter: `message_id=eq.${messageId}`,
      },
      () => {
        // Invalidate the reactions query to refresh the data
        queryClient.invalidateQueries({ queryKey: ['message-reactions', messageId] });
      }
    );

    return unsubscribe;
  }, [messageId, message?.conversation_id, realtime, queryClient]);

  // Add reaction mutation
  const addReaction = useMutation({
    mutationFn: async (emoji: string) => {
      if (!user?.id) throw new Error('User not authenticated');
      if (!message?.conversation_id) throw new Error('Conversation ID not found');

      const { data, error } = await supabase
        .from('message_reactions')
        .insert({
          message_id: messageId,
          conversation_id: message.conversation_id,
          user_id: user.id,
          emoji
        })
        .select()
        .single();

      if (error) {
        // If the error is a unique violation, it means the user already reacted with this emoji
        if (error.code === '23505') {
          return null; // Already exists, not an error
        }
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['message-reactions', messageId] });
    },
  });

  // Remove reaction mutation
  const removeReaction = useMutation({
    mutationFn: async (emoji: string) => {
      if (!user?.id) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('message_reactions')
        .delete()
        .match({
          message_id: messageId,
          user_id: user.id,
          emoji
        });

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['message-reactions', messageId] });
    },
  });

  // Check if user has reacted with a specific emoji
  const hasUserReacted = (emoji: string): boolean => {
    if (!user?.id) return false;

    const reaction = reactions.find(r => r.emoji === emoji);
    return reaction ? reaction.user_ids.includes(user.id) : false;
  };

  return {
    reactions,
    isLoading,
    addReaction,
    removeReaction,
    hasUserReacted,
  };
}
