
import { cn, getActivityGradient, getCategory<PERSON>moji } from '@/lib/utils';
import { Activity } from '@/types/activity';
import { Calendar, Clock, MapPin, Users, DollarSign, Share2, MessageSquare } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow, format } from 'date-fns';
import { useActivityParticipantCount } from '@/hooks/use-activity-participants';
import { useToast } from '@/hooks/use-toast';
import { useJoinActivity } from '@/hooks/use-activity-participants';

interface MapActivityStatusProps {
  activity: Activity | null;
  className?: string;
  onClick?: () => void;
}

export function MapActivityStatus({ activity, className, onClick }: MapActivityStatusProps) {
  if (!activity) {
    return null;
  }

  const { toast } = useToast();
  const { data: participantCount } = useActivityParticipantCount(activity.id);
  const joinActivity = useJoinActivity();
  const isFreePricing = !activity.price || activity.price <= 0;
  const startTimeFormatted = activity.start_time
    ? format(new Date(activity.start_time), 'h:mm a, MMM d')
    : 'TBD';

  const timeUntil = activity.start_time
    ? formatDistanceToNow(new Date(activity.start_time), { addSuffix: true })
    : 'Date TBD';

  const participantsText = participantCount
    ? `${participantCount.confirmed}/${activity.max_participants || '∞'}`
    : '0/∞';

  const categoryEmoji = activity.category?.name
    ? getCategoryEmoji(activity.category.name)
    : '📌';

  const handleJoinClick = () => {
    joinActivity.mutate({ activityId: activity.id });
    toast({
      title: "Activity joined",
      description: `You've joined ${activity.title}`,
    });
  };

  const handleChatClick = () => {
    toast({
      title: "Chat opened",
      description: `Opening chat for ${activity.title}`,
    });
  };

  const handleShareClick = () => {
    toast({
      title: "Share activity",
      description: "Activity sharing will be available soon",
    });
  };

  const gradientClasses = getActivityGradient(activity.category?.name, true);

  return (
    <div
      className={cn(
        "bg-background/95 backdrop-blur-md rounded-lg shadow-lg border",
        `bg-gradient-to-br ${gradientClasses}`,
        "transform transition-all duration-300 hover:shadow-xl hover:scale-[1.01]",
        className
      )}
    >
      {/* Header */}
      <div
        className="p-3 border-b border-border/20 flex items-center justify-between cursor-pointer"
        onClick={onClick}
      >
        <div className="flex items-center gap-2">
          <div className="w-10 h-10 rounded-full bg-white/20 backdrop-blur-md flex items-center justify-center text-xl shadow-sm border border-white/10">
            {categoryEmoji}
          </div>
          <div>
            <h3 className="font-medium text-sm truncate max-w-[200px]">
              {activity.title}
            </h3>
            {activity.category && (
              <Badge variant="outline" className="text-xs bg-background/50 backdrop-blur-sm border-white/10">
                {activity.category.name}
              </Badge>
            )}
          </div>
        </div>
        <Badge
          variant={isFreePricing ? "outline" : "default"}
          className={`text-xs ${isFreePricing ? 'bg-green-50/50 text-green-700 border-green-200/30' : 'bg-amber-50/50 text-amber-700 border-amber-200/30'} backdrop-blur-sm`}
        >
          {isFreePricing ? (
            'Free'
          ) : (
            <div className="flex items-center">
              <DollarSign className="h-3 w-3 mr-0.5" />
              {typeof activity.price === 'number' ? activity.price.toFixed(2) : activity.price}
            </div>
          )}
        </Badge>
      </div>

      {/* Details */}
      <div className="p-3">
        <div className="grid grid-cols-2 gap-2">
          <div className="flex items-center text-xs text-muted-foreground px-2 py-1 rounded-full bg-background/50 backdrop-blur-sm border border-white/10">
            <Clock className="h-3 w-3 mr-1 text-primary" />
            <span className="truncate">{timeUntil}</span>
          </div>
          <div className="flex items-center text-xs text-muted-foreground px-2 py-1 rounded-full bg-background/50 backdrop-blur-sm border border-white/10">
            <Calendar className="h-3 w-3 mr-1 text-primary" />
            <span className="truncate">{startTimeFormatted}</span>
          </div>
          <div className="flex items-center text-xs text-muted-foreground px-2 py-1 rounded-full bg-background/50 backdrop-blur-sm border border-white/10">
            <Users className="h-3 w-3 mr-1 text-primary" />
            <span className="truncate">{participantsText}</span>
          </div>
          <div className="flex items-center text-xs text-muted-foreground px-2 py-1 rounded-full bg-background/50 backdrop-blur-sm border border-white/10">
            <MapPin className="h-3 w-3 mr-1 flex-shrink-0 text-rose-500" />
            <span className="truncate">{activity.address || 'Pin on map'}</span>
          </div>
        </div>

        {activity.description && (
          <p className="text-xs mt-2 text-muted-foreground line-clamp-2 px-1">
            {activity.description}
          </p>
        )}

        {/* Actions */}
        <div className="flex gap-2 mt-3">
          <Button
            size="sm"
            className="flex-1 bg-gradient-to-r from-primary to-primary-purple shadow-sm hover:opacity-90 transition-opacity"
            onClick={handleJoinClick}
            disabled={joinActivity.isPending}
          >
            Join
          </Button>
          <Button size="sm" variant="outline" onClick={handleChatClick} className="backdrop-blur-sm border-white/10 bg-background/30 hover:bg-background/50">
            <MessageSquare className="h-3 w-3" />
          </Button>
          <Button size="sm" variant="outline" onClick={handleShareClick} className="backdrop-blur-sm border-white/10 bg-background/30 hover:bg-background/50">
            <Share2 className="h-3 w-3" />
          </Button>
        </div>
      </div>
    </div>
  );
}
