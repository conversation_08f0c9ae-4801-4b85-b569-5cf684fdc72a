
# BuddySurf API Documentation

## API Endpoints

### Authentication

- **POST** `/auth/signup`: Create a new user account
  - Body: `{ email, password, username }`
  - Returns: `{ user, session }`

- **POST** `/auth/login`: Log in to an existing account
  - Body: `{ email, password }`
  - Returns: `{ user, session }`

- **POST** `/auth/logout`: Log out current user
  - Returns: `{ success }`

### Users

- **GET** `/users`: Get list of users
  - Query params: `?limit=10&offset=0&search=term`
  - Returns: `{ users: [], count }`

- **GET** `/users/:id`: Get user details
  - Returns: `{ id, username, avatar_url, ... }`

- **PATCH** `/users/:id`: Update user profile
  - Body: `{ username, bio, avatar_url, ... }`
  - Returns: `{ id, username, ... }`

### Activities

- **GET** `/activities`: Get list of activities
  - Query params: `?category=sports&isPaid=false&limit=10&offset=0`
  - Returns: `{ activities: [], count }`

- **GET** `/activities/:id`: Get activity details
  - Returns: `{ id, title, description, ... }`

- **POST** `/activities`: Create a new activity
  - Body: `{ title, description, location, start_time, ... }`
  - Returns: `{ id, title, ... }`

- **PATCH** `/activities/:id`: Update an activity
  - Body: `{ title, description, ... }`
  - Returns: `{ id, title, ... }`

- **DELETE** `/activities/:id`: Delete an activity
  - Returns: `{ success }`

### Services

- **GET** `/services`: Get list of services
  - Query params: `?category=coaching&limit=10&offset=0`
  - Returns: `{ services: [], count }`

- **GET** `/services/:id`: Get service details
  - Returns: `{ id, title, price, ... }`

- **POST** `/services`: Create a new service
  - Body: `{ title, description, price, ... }`
  - Returns: `{ id, title, ... }`

### Messages

- **GET** `/messages`: Get user conversations
  - Returns: `{ conversations: [] }`

- **GET** `/messages/:conversationId`: Get messages in a conversation
  - Query params: `?limit=50&before=timestamp`
  - Returns: `{ messages: [] }`

- **POST** `/messages`: Send a new message
  - Body: `{ recipient_id, content, media_url }`
  - Returns: `{ id, content, ... }`

### Proposals

- **POST** `/proposals`: Create a new proposal
  - Body: `{ recipient_id, type, details, price, ... }`
  - Returns: `{ id, status, ... }`

- **PATCH** `/proposals/:id`: Update proposal status
  - Body: `{ status: "accepted"|"rejected" }`
  - Returns: `{ id, status, ... }`

### Wallet

- **GET** `/wallet`: Get wallet balance and transactions
  - Returns: `{ balance, transactions: [] }`

- **POST** `/wallet/add-funds`: Add funds to wallet
  - Body: `{ amount, payment_method }`
  - Returns: `{ transaction_id, status, ... }`

### Subscriptions

- **GET** `/subscriptions`: Get user's subscription status
  - Returns: `{ active, plan, expires_at, ... }`

- **POST** `/subscriptions/create-checkout`: Create checkout session
  - Body: `{ plan: "weekly"|"monthly"|"lifetime" }`
  - Returns: `{ checkout_url }`

## Authentication & Authorization

All endpoints except authentication endpoints require a valid session token in the `Authorization` header:

```
Authorization: Bearer [token]
```

## Error Handling

All endpoints return appropriate HTTP status codes:

- `200 OK`: Request succeeded
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Invalid or missing authentication
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `500 Server Error`: Internal server error

Error responses follow this format:

```json
{
  "error": "Error message",
  "details": {}
}
```

## Rate Limiting

API requests are limited to 100 requests per minute per user. Exceeding this limit will result in a `429 Too Many Requests` response.

## Realtime Subscriptions

BuddySurf offers real-time updates via Supabase Realtime. The following channels are available:

- `user_locations`: For real-time user location updates
- `messages:{conversation_id}`: For real-time message updates
- `activities`: For real-time activity updates

## API Versioning

The current API version is v1. All endpoints should be prefixed with `/v1` to ensure compatibility with future API versions.
