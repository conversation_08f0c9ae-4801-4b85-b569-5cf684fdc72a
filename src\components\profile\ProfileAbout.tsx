
import React from 'react';
import { EnhancedUserProfile } from '@/types/enhanced-profile';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { FollowButton } from './FollowButton';
import { useAuth } from '@/hooks/use-auth';
import { useFollowUser } from '@/hooks/use-follow-user';
import { Button } from '@/components/ui/button';
import { 
  Calendar, 
  MapPin, 
  UserCheck, 
  Heart, 
  Star, 
  Globe, 
  Link as LinkIcon,
  Mail
} from 'lucide-react';

interface ProfileAboutProps {
  profile: EnhancedUserProfile;
  isLoading?: boolean;
  className?: string;
}

export function ProfileAbout({ profile, isLoading = false, className }: ProfileAboutProps) {
  const { user } = useAuth();
  const isOwnProfile = user?.id === profile.id;
  const { isFollowing, toggleFollow, isLoading: followLoading } = useFollowUser(profile.id);
  
  // Format the member since date
  const memberSince = profile?.created_at
    ? format(new Date(profile.created_at), 'MMMM yyyy')
    : 'Unknown';
    
  if (isLoading) {
    return <ProfileAboutSkeleton className={className} />;
  }
  
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          <div>
            <CardTitle>About {profile.display_name || profile.username}</CardTitle>
            <CardDescription>Bio and personal information</CardDescription>
          </div>
          
          {!isOwnProfile && (
            <FollowButton 
              userId={profile.id}
              isFollowing={!!isFollowing}
              onToggle={toggleFollow}
              disabled={followLoading}
              size="sm"
            />
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Bio section */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">Bio</h3>
          {profile.bio ? (
            <p className="text-muted-foreground whitespace-pre-wrap">{profile.bio}</p>
          ) : (
            <p className="text-muted-foreground italic">No bio provided</p>
          )}
        </div>
        
        {/* Personal details */}
        <div className="space-y-2">
          <h3 className="text-lg font-semibold">Details</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {/* Member since */}
            <div className="flex items-center gap-2 text-sm">
              <Calendar className="h-4 w-4 text-muted-foreground" />
              <span>Member since {memberSince}</span>
            </div>
            
            {/* Location if available */}
            {profile.location_display && (
              <div className="flex items-center gap-2 text-sm">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{profile.location_display}</span>
              </div>
            )}
            
            {/* Follower count */}
            <div className="flex items-center gap-2 text-sm">
              <UserCheck className="h-4 w-4 text-muted-foreground" />
              <span>{profile.follower_count || 0} followers</span>
            </div>
            
            {/* Trust score if available */}
            {profile.trust_score !== undefined && (
              <div className="flex items-center gap-2 text-sm">
                <Star className="h-4 w-4 text-muted-foreground" />
                <span>Trust score: {profile.trust_score}/100</span>
              </div>
            )}
          </div>
        </div>
        
        {/* Interests */}
        {profile.interests && profile.interests.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Interests</h3>
            <div className="flex flex-wrap gap-2">
              {profile.interests.map((interest, index) => (
                <Badge key={index} variant="secondary">{interest}</Badge>
              ))}
            </div>
          </div>
        )}
        
        {/* Vibes */}
        {profile.vibes && profile.vibes.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Vibes</h3>
            <div className="flex flex-wrap gap-2">
              {profile.vibes.map((vibe, index) => (
                <Badge key={index} variant="outline" className="bg-primary/10">{vibe}</Badge>
              ))}
            </div>
          </div>
        )}
        
        {/* Social links */}
        {profile.social_links && profile.social_links.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Connect</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {profile.social_links.map((link, index) => (
                <a 
                  key={index} 
                  href={link.url} 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="flex items-center gap-2 text-sm text-primary hover:underline"
                >
                  <LinkIcon className="h-4 w-4" />
                  <span>{link.platform}: {link.username || link.url}</span>
                </a>
              ))}
            </div>
          </div>
        )}
        
        {/* Contact section for own profile */}
        {isOwnProfile && (
          <div className="mt-6 pt-6 border-t">
            <Button variant="outline" className="w-full" asChild>
              <a href="mailto:<EMAIL>">
                <Mail className="h-4 w-4 mr-2" />
                Contact Support
              </a>
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function ProfileAboutSkeleton({ className }: { className?: string }) {
  return (
    <Card className={className}>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
          <div>
            <Skeleton className="h-6 w-32 mb-2" />
            <Skeleton className="h-4 w-64" />
          </div>
          <Skeleton className="h-9 w-24" />
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-2">
          <Skeleton className="h-5 w-16 mb-2" />
          <Skeleton className="h-4 w-full mb-1" />
          <Skeleton className="h-4 w-full mb-1" />
          <Skeleton className="h-4 w-2/3" />
        </div>
        
        <div className="space-y-2">
          <Skeleton className="h-5 w-16 mb-2" />
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            <Skeleton className="h-4 w-40" />
            <Skeleton className="h-4 w-40" />
            <Skeleton className="h-4 w-40" />
            <Skeleton className="h-4 w-40" />
          </div>
        </div>
        
        <div className="space-y-2">
          <Skeleton className="h-5 w-16 mb-2" />
          <div className="flex flex-wrap gap-2">
            <Skeleton className="h-6 w-16 rounded-full" />
            <Skeleton className="h-6 w-24 rounded-full" />
            <Skeleton className="h-6 w-20 rounded-full" />
            <Skeleton className="h-6 w-16 rounded-full" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
