
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { useAdminSupportConversations } from '@/hooks/use-support-conversations';
import { useChatMessages } from '@/hooks/use-chat-messages';
import { useAuth } from '@/hooks/use-auth';
import { useProfile } from '@/hooks/use-profile';
import { format } from 'date-fns';
import { HelpCircle, Send, User, Loader2 } from 'lucide-react';
import { supabase } from '@/integrations/supabase/client';

interface SupportChatPanelProps {
  conversationId?: string;
}

export function SupportChatPanel({ conversationId }: SupportChatPanelProps) {
  const { supportConversations, isLoading, sendAdminSupportMessage } = useAdminSupportConversations();
  const [selectedConversationId, setSelectedConversationId] = useState<string | null>(null);
  const [message, setMessage] = useState('');
  const [userProfiles, setUserProfiles] = useState<Record<string, any>>({});
  const { user } = useAuth();
  const { data: adminProfile } = useProfile(user?.id);
  const messagesQuery = useChatMessages(conversationId);
  const messages = messagesQuery.data || [];
  const [isSending, setIsSending] = useState(false);

  // Fetch profiles for all participants
  useEffect(() => {
    if (!supportConversations || supportConversations.length === 0) return;

    const fetchProfiles = async () => {
      // Get all unique user IDs from conversations
      const userIds = new Set<string>();

      supportConversations.forEach(conversation => {
        conversation.participants?.forEach(participant => {
          if (participant.user_id !== user?.id) {
            userIds.add(participant.user_id);
          }
        });
      });

      if (userIds.size === 0) return;

      const { data, error } = await supabase
        .from('profiles')
        .select('id, display_name, username, avatar_url')
        .in('id', Array.from(userIds));

      if (error) {
        console.error('Error fetching profiles:', error);
        return;
      }

      const profilesMap: Record<string, any> = {};
      data?.forEach(profile => {
        profilesMap[profile.id] = profile;
      });

      setUserProfiles(profilesMap);
    };

    fetchProfiles();
  }, [supportConversations, user?.id]);

  // Get the user name for a conversation
  const getUserName = (conversation: any) => {
    if (!conversation.participants) return 'Unknown User';

    const userParticipant = conversation.participants.find(
      (p: any) => p.user_id !== user?.id
    );

    if (!userParticipant) return 'Unknown User';

    const userProfile = userProfiles[userParticipant.user_id];
    return userProfile?.display_name || userProfile?.username || 'Unknown User';
  };

  // Get the user avatar for a conversation
  const getUserAvatar = (conversation: any) => {
    if (!conversation.participants) return null;

    const userParticipant = conversation.participants.find(
      (p: any) => p.user_id !== user?.id
    );

    if (!userParticipant) return null;

    const userProfile = userProfiles[userParticipant.user_id];
    return userProfile?.avatar_url;
  };

  // Handle sending a message
  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim() || !selectedConversationId) return;

    setIsSending(true);
    try {
      await sendAdminSupportMessage.mutateAsync({
        content: message,
        conversationId: selectedConversationId
      });
      setMessage('');
    } catch (error) {
      console.error('Error sending message:', error);
    } finally {
      setIsSending(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Support Conversations</CardTitle>
          <CardDescription>Loading support conversations...</CardDescription>
        </CardHeader>
        <CardContent className="flex justify-center p-6">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }

  if (!supportConversations || supportConversations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Support Conversations</CardTitle>
          <CardDescription>No support conversations found</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground py-8">
            No users have contacted support yet. When they do, their conversations will appear here.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-[600px] flex flex-col">
      <CardHeader>
        <CardTitle>Support Conversations</CardTitle>
        <CardDescription>Respond to user support requests</CardDescription>
      </CardHeader>
      <CardContent className="flex-1 overflow-hidden p-0">
        <div className="grid grid-cols-3 h-full">
          {/* Conversations List */}
          <div className="col-span-1 border-r overflow-y-auto">
            {supportConversations.map(conversation => (
              <div
                key={conversation.id}
                className={`p-3 border-b hover:bg-gray-50 cursor-pointer flex items-center gap-3 ${
                  selectedConversationId === conversation.id ? 'bg-gray-100' : ''
                }`}
                onClick={() => setSelectedConversationId(conversation.id)}
              >
                <Avatar className="h-10 w-10">
                  <AvatarImage src={getUserAvatar(conversation) || ''} />
                  <AvatarFallback>
                    <User className="h-5 w-5 text-gray-400" />
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-baseline">
                    <h3 className="font-medium truncate text-gray-900">
                      {getUserName(conversation)}
                    </h3>
                    <span className="text-xs text-gray-500">
                      {conversation.last_message_at
                        ? format(new Date(conversation.last_message_at), 'MMM d')
                        : ''}
                    </span>
                  </div>
                  <p className="text-sm truncate text-gray-500">
                    {conversation.last_message || 'No messages yet'}
                  </p>
                </div>
              </div>
            ))}
          </div>

          {/* Chat Area */}
          <div className="col-span-2 flex flex-col h-full">
            {selectedConversationId ? (
              <>
                {/* Chat Header */}
                <div className="p-3 border-b flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8">
                      <AvatarImage 
                        src={getUserAvatar(
                          supportConversations.find(c => c.id === selectedConversationId)
                        ) || ''} 
                      />
                      <AvatarFallback>
                        <User className="h-4 w-4 text-gray-400" />
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <h3 className="font-medium text-sm">
                        {getUserName(
                          supportConversations.find(c => c.id === selectedConversationId)
                        )}
                      </h3>
                      <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                        Support
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Messages */}
                <div className="flex-1 overflow-y-auto p-4 space-y-4">
                  {messagesQuery.isLoading ? (
                    <div className="flex justify-center p-4">
                      <Loader2 className="h-6 w-6 animate-spin text-primary" />
                    </div>
                  ) : messages && messages.length > 0 ? (
                    messages.map(message => (
                      <div
                        key={message.id}
                        className={`flex items-end gap-2 ${message.sender_id === user?.id ? 'justify-end' : ''}`}
                      >
                        {message.sender_id !== user?.id && (
                          <div className="flex-shrink-0">
                            <Avatar className="h-8 w-8">
                              <AvatarImage 
                                src={userProfiles[message.sender_id]?.avatar_url || ''} 
                              />
                              <AvatarFallback>
                                <User className="h-4 w-4 text-gray-400" />
                              </AvatarFallback>
                            </Avatar>
                          </div>
                        )}

                        <div
                          className={`max-w-[70%] p-3 rounded-t-lg ${
                            message.is_admin_message
                              ? 'bg-green-100 text-green-800 rounded-lg border border-green-200'
                              : message.sender_id === user?.id
                                ? 'bg-primary text-primary-foreground rounded-bl-lg rounded-br-none'
                                : 'bg-muted text-muted-foreground rounded-br-lg rounded-bl-none'
                          }`}
                        >
                          <p className="whitespace-pre-wrap">{message.content}</p>
                          <span className={`text-xs block mt-1 ${
                            message.is_admin_message
                              ? 'text-green-600/80'
                              : message.sender_id === user?.id
                                ? 'text-primary-foreground/70'
                                : 'text-muted-foreground/70'
                          }`}>
                            {format(new Date(message.created_at), 'p')}
                          </span>
                        </div>

                        {message.sender_id === user?.id && (
                          <div className="flex-shrink-0">
                            <Avatar className="h-8 w-8">
                              <AvatarImage src={adminProfile?.avatar_url || ''} />
                              <AvatarFallback>
                                <User className="h-4 w-4 text-gray-400" />
                              </AvatarFallback>
                            </Avatar>
                          </div>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="flex flex-col items-center justify-center h-full text-center p-4">
                      <div className="mb-4 p-4 rounded-full bg-green-50">
                        <HelpCircle className="h-8 w-8 text-green-500" />
                      </div>
                      <h3 className="text-lg font-medium text-green-700">Support Conversation</h3>
                      <p className="text-sm text-gray-600 max-w-xs mt-2">
                        Send a message to start helping this user.
                      </p>
                    </div>
                  )}
                </div>

                {/* Message Input */}
                <form onSubmit={handleSendMessage} className="p-3 border-t flex gap-2">
                  <Textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Type your message..."
                    className="min-h-[40px] resize-none"
                  />
                  <Button type="submit" size="icon" disabled={isSending || !message.trim()}>
                    {isSending ? <Loader2 className="h-4 w-4 animate-spin" /> : <Send className="h-4 w-4" />}
                  </Button>
                </form>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center p-4">
                <div className="mb-4 p-4 rounded-full bg-gray-100">
                  <HelpCircle className="h-8 w-8 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium text-gray-700">Select a Conversation</h3>
                <p className="text-sm text-gray-500 max-w-xs mt-2">
                  Choose a support conversation from the list to view and respond to messages.
                </p>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
