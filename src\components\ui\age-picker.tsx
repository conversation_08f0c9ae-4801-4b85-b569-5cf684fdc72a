"use client"

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { format, subYears, isValid, parse } from "date-fns";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

interface AgeDatePickerProps {
  value?: Date;
  onChange: (date: Date | undefined) => void;
  className?: string;
  placeholder?: string;
  minAge?: number;
  maxAge?: number;
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
}

export function AgeDatePicker({
  value,
  onChange,
  className,
  placeholder = "Select your birthday",
  minAge = 18,
  maxAge = 100,
  label,
  description,
  error,
  required = false
}: AgeDatePickerProps) {
  const [date, setDate] = useState<Date | undefined>(value);
  const [age, setAge] = useState<number | null>(null);
  const [yearView, setYearView] = useState(false);
  const [decade, setDecade] = useState<number>(new Date().getFullYear() - 20);
  const [isOpen, setIsOpen] = useState(false);
  
  // Calculate the maximum and minimum dates based on minAge and maxAge
  const maxDate = subYears(new Date(), minAge);
  const minDate = subYears(new Date(), maxAge);
  
  // Update the age whenever the date changes
  useEffect(() => {
    if (date && isValid(date)) {
      const today = new Date();
      let age = today.getFullYear() - date.getFullYear();
      const monthDiff = today.getMonth() - date.getMonth();
      
      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
        age--;
      }
      
      setAge(age);
    } else {
      setAge(null);
    }
  }, [date]);
  
  // Update the internal date when the value prop changes
  useEffect(() => {
    setDate(value);
  }, [value]);
  
  // Handle date selection from the calendar
  const handleSelect = (selectedDate: Date | undefined) => {
    if (selectedDate) {
      setDate(selectedDate);
      onChange(selectedDate);
      setIsOpen(false);
    }
  };
  
  // Handle direct age input
  const handleAgeInput = (e: React.ChangeEvent<HTMLInputElement>) => {
    const inputAge = parseInt(e.target.value);
    
    if (!isNaN(inputAge) && inputAge >= minAge && inputAge <= maxAge) {
      const today = new Date();
      const birthYear = today.getFullYear() - inputAge;
      const birthDate = new Date(birthYear, today.getMonth(), today.getDate());
      
      setDate(birthDate);
      onChange(birthDate);
      setAge(inputAge);
    } else if (e.target.value === '') {
      setDate(undefined);
      onChange(undefined);
      setAge(null);
    }
  };
  
  // Generate years for the year picker
  const years = Array.from({ length: 10 }, (_, i) => decade + i);
  
  // Handle year selection
  const handleYearSelect = (year: number) => {
    if (date) {
      const newDate = new Date(date);
      newDate.setFullYear(year);
      setDate(newDate);
      onChange(newDate);
    } else {
      const newDate = new Date(year, 0, 1);
      setDate(newDate);
      onChange(newDate);
    }
    setYearView(false);
  };
  
  // Navigate between decades
  const prevDecade = () => setDecade(decade - 10);
  const nextDecade = () => setDecade(decade + 10);
  
  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <div className="flex items-center">
          <Label className="text-sm font-medium">{label}</Label>
          {required && <span className="text-red-500 ml-1">*</span>}
        </div>
      )}
      
      {description && (
        <p className="text-xs text-gray-500">{description}</p>
      )}
      
      <div className="flex gap-3">
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full justify-start text-left font-normal",
                !date && "text-muted-foreground",
                error && "border-red-500"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {date ? format(date, "MMMM d, yyyy") : placeholder}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            {yearView ? (
              <div className="p-3">
                <div className="flex justify-between items-center mb-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={prevDecade}
                    className="h-7 w-7"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <div className="text-sm font-medium">
                    {decade} - {decade + 9}
                  </div>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={nextDecade}
                    className="h-7 w-7"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
                <div className="grid grid-cols-3 gap-2">
                  {years.map((year) => {
                    const isDisabled = 
                      year > maxDate.getFullYear() || 
                      year < minDate.getFullYear();
                    
                    return (
                      <Button
                        key={year}
                        variant={date?.getFullYear() === year ? "default" : "outline"}
                        className={cn(
                          "h-9",
                          isDisabled && "opacity-50 cursor-not-allowed"
                        )}
                        onClick={() => !isDisabled && handleYearSelect(year)}
                        disabled={isDisabled}
                      >
                        {year}
                      </Button>
                    );
                  })}
                </div>
                <Button
                  variant="ghost"
                  className="w-full mt-2"
                  onClick={() => setYearView(false)}
                >
                  Back to Calendar
                </Button>
              </div>
            ) : (
              <>
                <div className="flex justify-between items-center p-3 border-b">
                  <Button
                    variant="ghost"
                    className="text-sm h-auto p-1"
                    onClick={() => setYearView(true)}
                  >
                    Select Year
                  </Button>
                </div>
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={handleSelect}
                  disabled={(date) => {
                    return date > maxDate || date < minDate;
                  }}
                  initialFocus
                />
              </>
            )}
          </PopoverContent>
        </Popover>
        
        <div className="relative">
          <Input
            type="number"
            value={age !== null ? age.toString() : ''}
            onChange={handleAgeInput}
            className={cn("w-20", error && "border-red-500")}
            placeholder="Age"
            min={minAge}
            max={maxAge}
          />
          <div className="absolute right-3 top-1/2 -translate-y-1/2 text-xs text-muted-foreground pointer-events-none">
            yrs
          </div>
        </div>
      </div>
      
      {error && (
        <p className="text-sm text-red-500">{error}</p>
      )}
    </div>
  );
}
