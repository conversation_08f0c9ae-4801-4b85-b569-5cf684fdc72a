
import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Activity } from "@/types/activity";
import { useJoinActivity, useActivityParticipantCount } from "@/hooks/use-activity-participants";
import { format, isPast } from 'date-fns';
import { Link } from 'react-router-dom';
import { cn } from '@/lib/utils';
import { MapPin, Users, CalendarDays, MessageSquare, Share2, Image as ImageIcon } from 'lucide-react';
import { useActivityView } from '@/hooks/use-activity-view';
import { ActivityShareModal } from './ActivityShareModal';
import { ActivityChatButton } from './ActivityChatButton';
import { Badge } from "@/components/ui/badge";
import { useActivityRouter } from './ActivityRouter';

interface ActivityCardProps {
  activity: Activity;
  className?: string;
  mapboxToken?: string;
  onJoin?: () => void;
  onViewOnMap?: () => void;
  onViewDetails?: () => void;
}

export function ActivityCard({
  activity,
  className,
  mapboxToken,
  onJoin,
  onViewOnMap,
  onViewDetails
}: ActivityCardProps) {
  const { toast } = useToast();
  const joinActivity = useJoinActivity();
  const { data: participantCount } = useActivityParticipantCount(activity.id);
  const { setSelectedActivity } = useActivityView();
  const [showShareModal, setShowShareModal] = useState(false);
  const { openModal } = useActivityRouter();

  const isExpired = activity.end_time ? isPast(new Date(activity.end_time)) : false;

  const handleJoinClick = () => {
    if (isExpired) {
      toast({
        title: "Activity expired",
        description: "This activity has already ended.",
        variant: "destructive"
      });
      return;
    }

    joinActivity.mutate({
      activityId: activity.id,
      // Payment handling would go here for paid activities
    });

    if (onJoin) onJoin();
  };

  const handleShareClick = () => {
    setShowShareModal(true);
  };

  const handleViewOnMap = (e: React.MouseEvent) => {
    e.stopPropagation();
    setSelectedActivity(activity);
    if (onViewOnMap) {
      onViewOnMap();
    } else {
      // Navigate to the map view with the activity ID as a query parameter
      window.location.href = `/meetmap?activity=${activity.id}`;
    }
  };

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return format(date, "MMM d, h:mm a");
    } catch (error) {
      return dateString;
    }
  };

  const categoryEmojis: Record<string, string> = {
    sports: '🏀',
    dining: '🍽️',
    music: '🎵',
    outdoors: '🌳',
    art: '🎨',
    technology: '💻',
    gaming: '🎮',
    education: '📚',
    fitness: '💪',
    social: '🎉',
    dating: '❤️',
    default: '📌'
  };

  const emoji = activity.category
    ? categoryEmojis[activity.category.name.toLowerCase()] || categoryEmojis.default
    : categoryEmojis.default;

  const getGradient = () => {
    const categoryName = activity.category?.name.toLowerCase();
    if (categoryName === 'sports' || categoryName === 'fitness')
      return 'from-blue-500/90 to-cyan-500/90 text-white';
    if (categoryName === 'music')
      return 'from-indigo-500/90 to-purple-500/90 text-white';
    if (categoryName === 'social' || categoryName === 'dating')
      return 'from-pink-500/90 to-rose-500/90 text-white';
    if (categoryName === 'outdoors')
      return 'from-green-500/90 to-emerald-500/90 text-white';
    if (categoryName === 'dining')
      return 'from-amber-500/90 to-orange-500/90 text-white';
    return 'from-primary-blue to-primary-deep-purple text-white'; // default
  };

  return (
    <>
      <Card
        className={cn(
          "overflow-hidden hover:shadow-md transition-all duration-300 group cursor-pointer",
          className
        )}
        variant="default"
        onClick={() => openModal(activity)}
      >
        <CardContent className="p-0">
          <div className={`bg-gradient-to-r ${getGradient()} p-4 relative overflow-hidden`}>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-3xl bg-white/20 h-12 w-12 rounded-full flex items-center justify-center shadow-sm backdrop-blur-sm">{emoji}</span>
                <div>
                  <h3 className="font-bold text-xl">{activity.title}</h3>
                  {activity.category && (
                    <span className="text-sm bg-white/20 rounded-full px-2 py-0.5 inline-flex items-center backdrop-blur-sm">
                      {activity.category.name}
                    </span>
                  )}
                </div>
              </div>
              {isExpired && (
                <Badge variant="destructive" className="absolute top-3 right-3 shadow-lg backdrop-blur-sm">
                  Expired
                </Badge>
              )}
            </div>

            <div className="absolute -right-8 -bottom-8 w-32 h-32 bg-white/10 rounded-full blur-2xl"></div>
            <div className="absolute -left-4 -top-4 w-24 h-24 bg-white/5 rounded-full blur-xl"></div>
          </div>

          <div className="w-full h-36 bg-[#8E9196] flex items-center justify-center relative group overflow-hidden">
            {activity.media_urls && activity.media_urls[0] ? (
              <img
                src={activity.media_urls[0]}
                alt={activity.title}
                className="w-full h-full object-cover"
              />
            ) : (
              <div className="absolute inset-0 bg-gradient-to-br from-gray-700/50 to-gray-900/50 flex items-center justify-center">
                <ImageIcon className="h-10 w-10 text-white/70" />
              </div>
            )}
            <div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  handleViewOnMap(e);
                }}
                className="absolute bottom-3 right-3 bg-background/80 backdrop-blur-sm text-sm font-medium px-3 py-1.5 rounded-full shadow-lg flex items-center gap-1.5 transition-all duration-300 opacity-0 group-hover:opacity-100 hover:bg-background/90"
              >
                <MapPin className="h-3.5 w-3.5" />
                View on Map
              </button>
            </div>
          </div>

          <div className="p-5 space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center text-sm text-muted-foreground">
                <CalendarDays className="h-4 w-4 mr-2 text-primary" />
                {formatDate(activity.start_time)}
                {activity.end_time && ` - ${formatDate(activity.end_time)}`}
              </div>

              <div className="text-sm">
                {activity.is_paid ? (
                  <span className="font-medium text-amber-600 bg-amber-50 px-2 py-0.5 rounded-full">
                    ${activity.price?.toFixed(2)}
                  </span>
                ) : (
                  <span className="text-green-600 font-medium bg-green-50 px-2 py-0.5 rounded-full">
                    Free
                  </span>
                )}
              </div>
            </div>

            {activity.address && (
              <div className="flex items-center text-sm text-muted-foreground">
                <MapPin className="h-4 w-4 mr-2 text-primary" />
                <span className="truncate">{activity.address}</span>
              </div>
            )}

            <div className="flex justify-between">
              <div className="flex items-center text-sm text-muted-foreground">
                <Users className="h-4 w-4 mr-2 text-primary" />
                {participantCount ? (
                  <span>
                    {participantCount.confirmed} joined
                    {activity.max_participants && ` (${participantCount.confirmed}/${activity.max_participants})`}
                  </span>
                ) : (
                  <span>
                    0 joined
                    {activity.max_participants && ` (0/${activity.max_participants})`}
                  </span>
                )}
              </div>
            </div>

            {activity.description && (
              <p className="text-sm mt-2 line-clamp-2 text-muted-foreground">
                {activity.description}
              </p>
            )}

            <div className="flex space-x-2 pt-2 mt-2 border-t border-border/50">
              <Button
                className="flex-1 shadow-sm bg-gradient-to-r from-primary to-primary-purple hover:opacity-90 transition-opacity"
                onClick={(e) => {
                  e.stopPropagation();
                  handleJoinClick();
                }}
                disabled={isExpired || joinActivity.isPending}
              >
                {joinActivity.isPending ? "JOINING..." : "JOIN"}
              </Button>

              <ActivityChatButton
                activity={activity}
                className="flex-1"
                onClick={(e) => e.stopPropagation()}
              />

              <Button
                variant="outline"
                size="icon"
                onClick={(e) => {
                  e.stopPropagation();
                  handleShareClick();
                }}
                className="hover:bg-primary/5"
              >
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {showShareModal && (
        <ActivityShareModal
          activity={activity}
          isOpen={showShareModal}
          onClose={() => setShowShareModal(false)}
        />
      )}
    </>
  );
}
