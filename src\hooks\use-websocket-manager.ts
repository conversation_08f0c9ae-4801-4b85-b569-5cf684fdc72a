
import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { UnifiedMapUser } from '@/types/map';
import { useAuth } from '@/hooks/use-auth';
import { useLocationFormatter } from './use-location-formatter';
import { useSupabaseRealtime } from './use-supabase-realtime';

export function useWebSocketManager() {
  const [users, setUsers] = useState<UnifiedMapUser[]>([]);
  const [activities, setActivities] = useState<any[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<string | null>(null);
  const { user } = useAuth();
  const { normalizeUserLocation } = useLocationFormatter();
  const realtime = useSupabaseRealtime();
  const initialLoadRef = useRef(false);

  const fetchUserProfiles = useCallback(async () => {
    const { data, error } = await supabase
      .from('profiles')
      .select('id, username, display_name, avatar_url, bio');

    if (error) {
      console.error('Error fetching profiles:', error);
      return null;
    }

    // Create a map of profiles by user_id for easier lookup
    const profilesMap = new Map();
    if (data) {
      data.forEach(profile => {
        profilesMap.set(profile.id, profile);
      });
    }

    return profilesMap;
  }, []);

  const fetchUserLocations = useCallback(async () => {
    const { data, error } = await supabase
      .from('user_locations')
      .select('*');

    if (error) {
      console.error('Error fetching user locations:', error);
      return null;
    }

    return data;
  }, []);

  const fetchActivities = useCallback(async () => {
    const { data, error } = await supabase
      .from('activities')
      .select('*');

    if (error) {
      console.error('Error fetching activities:', error);
      return null;
    }

    return data;
  }, []);

  const refreshData = useCallback(async () => {
    try {
      console.log("Refreshing data...");

      // Fetch user locations and profiles
      const [locationsData, profilesMap, activitiesData] = await Promise.all([
        fetchUserLocations(),
        fetchUserProfiles(),
        fetchActivities()
      ]);

      if (!locationsData || !profilesMap) return;

      console.log('Raw location data:', locationsData);

      // Join the data manually
      const joinedData = locationsData.map(location => {
        const profile = profilesMap.get(location.user_id) || null;
        return {
          ...location,
          profiles: profile
        };
      });

      const normalizedUsers = joinedData.map(item => {
        const profile = item.profiles || {
          username: null,
          display_name: null,
          avatar_url: null,
          bio: null
        };

        // Use normalizeUserLocation to create a proper UnifiedMapUser
        const normalizedUser = normalizeUserLocation({
          user_id: item.user_id,
          id: item.user_id,
          username: profile.username,
          display_name: profile.display_name,
          avatar_url: profile.avatar_url,
          bio: profile.bio || item.bio,
          location: item.location,
          is_online: true,
          created_at: item.created_at,
          updated_at: item.updated_at
        });

        return normalizedUser;
      }).filter(user => user.location && (user.location.x || user.location.lng) && (user.location.y || user.location.lat));

      console.log('Normalized users:', normalizedUsers);
      setUsers(normalizedUsers);

      if (activitiesData) {
        console.log('Activity data:', activitiesData);
        setActivities(activitiesData);
      }

      setLastUpdate(new Date().toISOString());
      setIsConnected(true);
    } catch (error) {
      console.error('Error in refreshData:', error);
    }
  }, [fetchUserLocations, fetchUserProfiles, fetchActivities, normalizeUserLocation]);

  // Initial fetch and set up WebSocket subscriptions
  useEffect(() => {
    // Initial data load
    refreshData().then(() => {
      initialLoadRef.current = true;
    });

    // Set up WebSocket subscription for user locations
    const userLocationsChannel = `user-locations`;
    const unsubscribeUserLocations = realtime.subscribe(
      userLocationsChannel,
      {
        table: 'user_locations',
        event: '*'
      },
      async (payload) => {
        if (!initialLoadRef.current) return; // Skip until initial load is complete

        console.log('User location update:', payload);

        // Fetch the updated user profile
        const userId = payload.new?.user_id || payload.old?.user_id;
        if (!userId) return;

        const { data: profileData } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .single();

        // Handle different event types
        if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
          setUsers(prev => {
            // Check if user already exists in the list
            const existingIndex = prev.findIndex(u => u.user_id === userId);
            const updatedUser = normalizeUserLocation({
              ...payload.new,
              profiles: profileData
            });

            if (existingIndex >= 0) {
              // Update existing user
              const newUsers = [...prev];
              newUsers[existingIndex] = updatedUser;
              return newUsers;
            } else {
              // Add new user
              return [...prev, updatedUser];
            }
          });
        } else if (payload.eventType === 'DELETE') {
          // Remove user
          setUsers(prev => prev.filter(u => u.user_id !== userId));
        }

        setLastUpdate(new Date().toISOString());
      }
    );

    // Set up WebSocket subscription for activities
    const activitiesChannel = `activities`;
    const unsubscribeActivities = realtime.subscribe(
      activitiesChannel,
      {
        table: 'activities',
        event: '*'
      },
      async (payload) => {
        if (!initialLoadRef.current) return; // Skip until initial load is complete

        console.log('Activity update:', payload);

        // Refresh activities data on any change
        const { data } = await supabase
          .from('activities')
          .select('*')
          .order('created_at', { ascending: false });

        if (data) {
          setActivities(data);
          setLastUpdate(new Date().toISOString());
        }
      }
    );

    setIsConnected(true);

    return () => {
      unsubscribeUserLocations();
      unsubscribeActivities();
      setIsConnected(false);
    };
  }, [refreshData, realtime, normalizeUserLocation]);

  return {
    users,
    activities,
    isConnected,
    lastUpdate,
    refreshData
  };
}
