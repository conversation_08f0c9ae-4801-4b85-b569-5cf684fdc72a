
import React from 'react';
import { LocationSearch } from './LocationSearch';
import { Card } from '@/components/ui/card';

interface MapSearchBarProps {
  mapboxToken: string;
  onLocationSelect?: (location: {
    name: string;
    coordinates: [number, number];
    placeType: string;
  }) => void;
  className?: string;
}

export function MapSearchBar({ mapboxToken, onLocationSelect, className }: MapSearchBarProps) {
  if (!mapboxToken) {
    return null;
  }

  return (
    <Card className={`shadow-md ${className}`}>
      <div className="p-2">
        <LocationSearch
          mapboxToken={mapboxToken}
          onSelectLocation={onLocationSelect}
          placeholder="Search locations or users..."
        />
      </div>
    </Card>
  );
}
