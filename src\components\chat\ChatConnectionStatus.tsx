import React, { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { Badge } from '@/components/ui/badge';
import { Wifi, WifiOff, Loader2 } from 'lucide-react';

interface ChatConnectionStatusProps {
  className?: string;
}

export function ChatConnectionStatus({ className }: ChatConnectionStatusProps) {
  const { user } = useAuth();
  const [connectionStatus, setConnectionStatus] = useState<'connecting' | 'connected' | 'disconnected'>('connecting');
  const [lastConnected, setLastConnected] = useState<Date | null>(null);

  useEffect(() => {
    if (!user) return;

    // Monitor Supabase connection status
    const channel = supabase.channel('connection-status')
      .subscribe((status) => {
        switch (status) {
          case 'SUBSCRIBED':
            setConnectionStatus('connected');
            setLastConnected(new Date());
            break;
          case 'CLOSED':
          case 'CHANNEL_ERROR':
            setConnectionStatus('disconnected');
            break;
          default:
            setConnectionStatus('connecting');
        }
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user]);

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'bg-green-500';
      case 'disconnected':
        return 'bg-red-500';
      case 'connecting':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusIcon = () => {
    switch (connectionStatus) {
      case 'connected':
        return <Wifi className="h-3 w-3" />;
      case 'disconnected':
        return <WifiOff className="h-3 w-3" />;
      case 'connecting':
        return <Loader2 className="h-3 w-3 animate-spin" />;
      default:
        return <WifiOff className="h-3 w-3" />;
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected':
        return 'Connected';
      case 'disconnected':
        return 'Disconnected';
      case 'connecting':
        return 'Connecting...';
      default:
        return 'Unknown';
    }
  };

  return (
    <Badge 
      variant="outline" 
      className={`flex items-center gap-1 text-xs ${className}`}
    >
      <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
      {getStatusIcon()}
      <span>{getStatusText()}</span>
      {lastConnected && connectionStatus === 'connected' && (
        <span className="text-muted-foreground">
          • {lastConnected.toLocaleTimeString()}
        </span>
      )}
    </Badge>
  );
}
