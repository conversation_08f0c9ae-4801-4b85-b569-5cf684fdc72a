-- Fix the check_onboarding_completed function
-- The original function has a type mismatch with jsonb_array_length(text[])

-- First, drop the existing function if it exists
DROP FUNCTION IF EXISTS public.check_onboarding_completed(UUID);

-- Create a new version of the function that handles text[] arrays correctly
CREATE OR REPLACE FUNCTION public.check_onboarding_completed(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  profile_record RECORD;
  has_vibes BOOLEAN;
BEGIN
  -- Get the profile record
  SELECT * INTO profile_record
  FROM public.profiles
  WHERE id = user_id;
  
  -- If no profile found, return false
  IF profile_record IS NULL THEN
    RETURN FALSE;
  END IF;
  
  -- If onboarding_completed is already true, return true
  IF profile_record.onboarding_completed = TRUE THEN
    RETURN TRUE;
  END IF;
  
  -- Check if vibes array has elements
  has_vibes := profile_record.vibes IS NOT NULL AND 
               array_length(profile_record.vibes, 1) > 0;
  
  -- Check all required fields
  RETURN (
    profile_record.display_name IS NOT NULL AND
    profile_record.avatar_url IS NOT NULL AND
    profile_record.birthday IS NOT NULL AND
    profile_record.gender IS NOT NULL AND
    profile_record.default_location IS NOT NULL AND
    profile_record.location_permission_granted = TRUE AND
    has_vibes
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.check_onboarding_completed(UUID) TO authenticated;

-- Comment explaining the function
COMMENT ON FUNCTION public.check_onboarding_completed IS 'Checks if a user has completed onboarding by verifying all required fields are present.';
