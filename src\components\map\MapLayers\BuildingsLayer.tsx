
import { useEffect } from 'react';
import mapboxgl from 'mapbox-gl';
import { useMapPreferences } from '@/hooks/use-map-preferences';
import { useBuildingStyle } from '@/hooks/use-building-style';

interface BuildingsLayerProps {
  map: mapboxgl.Map | null;
  enabled: boolean;
}

export function BuildingsLayer({ map, enabled }: BuildingsLayerProps) {
  const { preferences } = useMapPreferences();
  const { applyBuildingStyle, applyBuildingPatterns } = useBuildingStyle();

  useEffect(() => {
    if (!map || !map.isStyleLoaded()) return;
    
    if (enabled) {
      // Apply enhanced building styles with soft styling, off-white/ivory base
      const cleanupBuildings = applyBuildingStyle(map, {
        minZoom: 12,
        maxOpacity: 0.85,
        baseColor: '#F5F5F0', // Off-white/ivory base color
        highlightColor: '#E0E0E0', // Soft gray for highlights
        edgeColor: '#DEDEDE', // Light gray stroke for building edges
        ambientIntensity: 0.35, // Slightly reduced ambient occlusion
        transitionDuration: 400, // Smoother transitions
        roundedCorners: true, // Simulate rounded corners effect
        shadowOpacity: 0.2 // 20% opacity for shadows
      });
      
      // Apply subtle mint-green patterns to buildings for vegetation effect
      const cleanupPatterns = applyBuildingPatterns(map, {
        pattern: 'vegetation',
        minZoom: 15,
        opacity: 0.15,
        baseColor: '#E8F5E9', // Light mint green
        accentColor: '#C8E6C9' // Darker green accents
      });
      
      // Cleanup function
      return () => {
        cleanupBuildings?.();
        cleanupPatterns?.();
      };
    } else {
      // Hide the layers if they exist
      if (map.getLayer('3d-buildings')) {
        map.setLayoutProperty('3d-buildings', 'visibility', 'none');
      }
      if (map.getLayer('building-patterns')) {
        map.setLayoutProperty('building-patterns', 'visibility', 'none');
      }
    }
  }, [map, enabled, applyBuildingStyle, applyBuildingPatterns]);
  
  return null;
}
