/**
 * Utility functions for saving profile data to Supabase
 */

import { supabase } from "@/integrations/supabase/client";
import { OnboardingFormValues } from "@/types/onboarding";
import { formatPoint } from "@/utils/format-point";

/**
 * Save profile data to Supabase
 * @param userId - The user ID
 * @param data - The profile data to save
 * @returns A promise that resolves to a success boolean and any error
 */
export async function saveProfileData(
  userId: string,
  data: Partial<OnboardingFormValues>
): Promise<{ success: boolean; error: Error | null }> {
  try {
    console.log(`Saving profile data for user ${userId}...`);

    // Create the updates object
    const updates: any = {
      id: userId,
      updated_at: new Date().toISOString(),
    };

    // Add basic profile fields
    if (data.display_name) updates.display_name = data.display_name;
    if (data.username) updates.username = data.username;
    if (data.bio !== undefined) updates.bio = data.bio || null;

    // Handle avatar URL with special validation
    if (data.avatar_url) {
      // If it's a data URL, it should be handled by the uploadAvatarFromDataUrl function
      // and not saved directly to the profile
      if (data.avatar_url.startsWith('data:')) {
        console.log('Avatar URL is a data URL, it should be uploaded first');
      } else if (data.avatar_url.startsWith('http')) {
        console.log(`Setting avatar_url to: ${data.avatar_url}`);
        updates.avatar_url = data.avatar_url;
      } else {
        console.log(`Invalid avatar_url format: ${data.avatar_url.substring(0, 20)}...`);
      }
    }

    // Add birthday and gender
    if (data.birthday) updates.birthday = data.birthday.toISOString();
    if (data.gender) updates.gender = data.gender;

    // Add arrays
    if (data.purposes && data.purposes.length > 0) updates.purposes = data.purposes;
    if (data.vibes && data.vibes.length > 0) updates.vibes = data.vibes;
    if (data.gallery && data.gallery.length > 0) updates.gallery = data.gallery;

    // Add location data
    if (data.defaultLocation) {
      try {
        const pointString = formatPoint(data.defaultLocation.x, data.defaultLocation.y);
        console.log(`Setting default_location to: ${pointString} from:`, data.defaultLocation);
        updates.default_location = pointString;
      } catch (err) {
        console.error("Error formatting location data:", err);
      }
    }

    if (data.location) {
      updates.location_display = data.location;
    }

    // Add permission flags
    if (data.locationPermissionGranted !== undefined) {
      updates.location_permission_granted = data.locationPermissionGranted;
    }

    if (data.notificationsEnabled !== undefined) {
      updates.notifications_enabled = data.notificationsEnabled;
    }

    // Add notification preferences
    if (data.notifyMessages !== undefined) updates.notifymessages = data.notifyMessages;
    if (data.notifyActivities !== undefined) updates.notifyactivities = data.notifyActivities;
    if (data.notifyFollows !== undefined) updates.notifyfollows = data.notifyFollows;

    // Add favorite locations if available
    if (data.favorite_locations && data.favorite_locations.length > 0) {
      updates.favorite_locations = data.favorite_locations;
    }

    // CRITICAL: Handle onboarding completion flag
    // Always explicitly set it as a boolean true if onboarding_completed is true
    const isOnboardingCompleted = data.onboarding_completed === true;

    if (isOnboardingCompleted || 'onboarding_completed' in data) {
      console.log(`Setting onboarding_completed to true explicitly`);
      updates.onboarding_completed = true; // Always set to true boolean, not string
    }

    console.log('Final updates to save:', JSON.stringify(updates, null, 2));

    // Check if the profile exists
    const { data: existingProfile, error: checkError } = await supabase
      .from('profiles')
      .select('id')
      .eq('id', userId)
      .single();

    // Use upsert operation instead of separate insert/update logic
    // This will insert if the record doesn't exist or update if it does
    const { error } = await supabase
      .from('profiles')
      .upsert({
        ...updates,
        // Add created_at for new profiles
        created_at: existingProfile ? undefined : new Date().toISOString()
      });

    if (error) {
      console.error('Error saving profile:', error);
      return { success: false, error: error };
    }

    console.log('Profile data saved successfully!');
    return { success: true, error: null };
  } catch (error) {
    console.error('Error saving profile data:', error);
    return { success: false, error: error as Error };
  }
}

/**
 * Mark onboarding as completed
 * @param userId - The user ID
 * @returns A promise that resolves to a success boolean and any error
 */
export async function markOnboardingCompleted(
  userId: string
): Promise<{ success: boolean; error: Error | null }> {
  try {
    console.log(`Marking onboarding as completed for user ${userId}...`);

    // Import the verification function
    const { forceMarkOnboardingCompleted } = await import('./verify-onboarding-status');

    // Use the function to mark onboarding as completed
    const success = await forceMarkOnboardingCompleted(userId);

    if (!success) {
      return { success: false, error: new Error('Failed to mark onboarding as completed') };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error marking onboarding as completed:', error);
    return { success: false, error: error as Error };
  }
}

/**
 * Save all onboarding data and mark as completed
 * @param userId - The user ID
 * @param data - The complete onboarding data
 * @returns A promise that resolves to a success boolean and any error
 */
export async function saveOnboardingData(
  userId: string,
  data: OnboardingFormValues
): Promise<{ success: boolean; error: Error | null }> {
  try {
    console.log(`Saving complete onboarding data for user ${userId}...`);

    // Handle avatar upload if it's a data URL
    if (data.avatar_url && data.avatar_url.startsWith('data:')) {
      console.log('Avatar is a data URL, uploading it first...');

      // Import the upload function dynamically to avoid circular dependencies
      const { uploadAvatarFromDataUrl } = await import('./file-upload');

      const { url, error: uploadError } = await uploadAvatarFromDataUrl(userId, data.avatar_url);

      if (uploadError) {
        console.error('Error uploading avatar during onboarding completion:', uploadError);
        // Continue with saving other data, but log the error
      } else if (url) {
        console.log(`Avatar uploaded successfully: ${url}`);
        // Update the avatar_url in the data object with the actual URL
        data.avatar_url = url;
      }
    }

    // IMPORTANT: Always explicitly set onboarding_completed to true
    const profileData = {
      ...data,
      // Ensure onboarding_completed is set
      onboarding_completed: true
    };

    console.log('Saving profile data with onboarding_completed flag...');

    // Save all profile data with the onboarding_completed flag
    const { success, error } = await saveProfileData(userId, profileData);

    if (!success) {
      console.error('Failed to save profile data during onboarding completion:', error);
      return { success, error };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error saving onboarding data:', error);
    return { success: false, error: error as Error };
  }
}

/**
 * Verify if onboarding is completed for a user
 * @param userId - The user ID
 * @returns A promise that resolves to a boolean indicating if onboarding is completed
 */
export async function verifyOnboardingStatus(
  userId: string
): Promise<boolean> {
  try {
    const { data, error } = await supabase
      .from('profiles')
      .select('onboarding_completed')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error checking onboarding status:', error);
      return false;
    }

    return data?.onboarding_completed === true;
  } catch (error) {
    console.error('Error in verifyOnboardingStatus:', error);
    return false;
  }
}
