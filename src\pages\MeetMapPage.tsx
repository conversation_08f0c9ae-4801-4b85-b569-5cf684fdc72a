import React, { useState, useEffect, useRef } from 'react';
import { MapComponent } from "@/components/map";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { Loader2, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { MeetMapSearchBar } from "@/components/map/MeetMapSearchBar";
import { useRealtimeLocationSystem } from "@/hooks/use-realtime-location-system";
import { EnhancedUserProfileCard } from "@/components/map/MapMarkers/EnhancedUserProfileCard";
import { UnifiedMapUser, lngLatToXY, xyToLngLat, mapPresenceToUnifiedUser } from '@/types/map';
import { MapStyleSelector, MapZoomControls } from '@/components/map/MapControls';
import { MapLocationStatus } from '@/components/map/MapLocationStatus';
import { PeriodicLocationUpdater } from '@/components/map/PeriodicLocationUpdater';
import { useMapPreferences } from '@/hooks/use-map-preferences';
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger, SheetFooter } from '@/components/ui/sheet';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { useActivities, useActivity } from '@/hooks/use-activities';
import MapRightSidebar, { MapRightSidebarTabValue } from '@/components/map/MapRightSidebar/MapRightSidebar';
import { useLocation, useNavigate } from 'react-router-dom';
import { useActivityRouter } from '@/components/activity/ActivityRouter';

export default function MeetMapPage() {
  const [mapboxToken, setMapboxToken] = useState<string | undefined>(undefined);
  const [isLoading, setIsLoading] = useState(true);
  const [isLocationUpdating, setIsLocationUpdating] = useState(false);
  const [initialLocationLoaded, setInitialLocationLoaded] = useState(false);
  const { toast } = useToast();
  const { preferences, savePreferences } = useMapPreferences();
  const {
    users: liveUsers,
    isConnected: isRealtimeConnected,
    myCoords,
    updateMyLocation,
    lastUpdate
  } = useRealtimeLocationSystem();
  const {
    activities: activitiesData,
    isLoading: activitiesLoading
  } = useActivities();
  
  const [selectedUser, setSelectedUser] = useState<UnifiedMapUser | null>(null);
  const [activeFilter, setActiveFilter] = useState<'all' | 'people' | 'activities'>('all');
  const [activeSidebarTab, setActiveSidebarTab] = useState<MapRightSidebarTabValue>('nearby');
  const [mapCenter, setMapCenter] = useState<{
    lng: number;
    lat: number;
  } | null>(null);
  const [myLocation, setMyLocation] = useState<{
    lng: number;
    lat: number;
  } | null>(null);
  const [mapStyle, setMapStyle] = useState<string>(preferences.map_style || preferences.mapStyle);
  const userLocations: UnifiedMapUser[] = liveUsers ?? [];

  // Router related hooks
  const location = useLocation();
  const navigate = useNavigate();
  const { openModal } = useActivityRouter();

  // Check for activity ID in URL parameters
  useEffect(() => {
    if (!activitiesData) return;

    // Check if we have an activity query parameter
    const searchParams = new URLSearchParams(location.search);
    const activityId = searchParams.get('activity');

    if (activityId) {
      // Find the activity in our data
      const activity = activitiesData.find(a => a.id === activityId);

      if (activity) {
        // Center the map on the activity location
        if (activity.location) {
          setMapCenter({
            lng: activity.location.x,
            lat: activity.location.y
          });
        }

        // Open the activity modal
        openModal(activity);

        // Set the filter to show activities
        setActiveFilter('activities');
        setActiveSidebarTab('plans');
      }
    }
  }, [location.search, activitiesData, openModal]);

  const handleFilterChange = (value: string) => {
    setActiveFilter(value as 'all' | 'people' | 'activities');
  };

  const handleSidebarTabChange = (value: MapRightSidebarTabValue) => {
    setActiveSidebarTab(value);
    // Update map filter based on sidebar tab selection
    if (value === 'nearby') {
      setActiveFilter('people');
    } else if (value === 'plans') {
      setActiveFilter('activities');
    } else {
      setActiveFilter('all');
    }
  };

  const mapRef = useRef<any>(null);
  const handleUserSelect = (user: UnifiedMapUser) => {
    if (user && user.location) {
      setMapCenter({
        lng: user.location.x,
        lat: user.location.y
      });
      setSelectedUser(user);
      toast({
        title: `${user.display_name || user.username || 'User'}`,
        description: user.bio || "No bio available"
      });
    }
  };
  const handleActivitySelect = (activity: any) => {
    if (activity && activity.location) {
      setMapCenter({
        lng: activity.location.x,
        lat: activity.location.y
      });
      toast({
        title: activity.title,
        description: activity.description || "No description available"
      });
    }
  };
  const handleLocationSelect = (location: {
    x: number;
    y: number;
    name: string;
  }) => {
    setMapCenter({
      lng: location.x,
      lat: location.y
    });
    toast({
      title: `Location: ${location.name}`,
      description: "Map centered on selected location"
    });
  };
  const handleMyLocation = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(position => {
        const {
          longitude,
          latitude
        } = position.coords;
        if (mapRef?.current) {
          mapRef.current.flyTo({
            center: [longitude, latitude],
            zoom: 14,
            duration: 2000
          });
        }
        updateMyLocation();
        setMyLocation({
          lng: longitude,
          lat: latitude
        });
      }, error => {
        console.error('Location error:', error);
        toast({
          title: "Location Error",
          description: "Could not retrieve your location",
          variant: "destructive"
        });
      });
    }
  };
  const handleLocationRefresh = async () => {
    setIsLocationUpdating(true);
    await updateMyLocation();
    setIsLocationUpdating(false);
  };
  const handleMapStyleChange = (style: string) => {
    setMapStyle(style);
    savePreferences({
      map_style: style,
      mapStyle: style
    });
    toast({
      title: "Map style changed",
      description: `Map style set to ${style.replace('-v9', '').replace('-v11', '')}`
    });
  };
  const handleZoomIn = () => {
    if (mapRef?.current) {
      const currentZoom = mapRef.current.getZoom();
      mapRef.current.zoomTo(currentZoom + 1, {
        duration: 500
      });
    }
  };
  const handleZoomOut = () => {
    if (mapRef?.current) {
      const currentZoom = mapRef.current.getZoom();
      mapRef.current.zoomTo(currentZoom - 1, {
        duration: 500
      });
    }
  };
  const handleResetBearing = () => {
    if (mapRef?.current) {
      mapRef.current.rotateTo(0, {
        duration: 1000
      });
    }
  };

  // Map display filters based on the active tab
  const showUsers = activeFilter === 'all' || activeFilter === 'people';
  const showActivities = activeFilter === 'all' || activeFilter === 'activities';

  useEffect(() => {
    const fetchMapboxToken = async () => {
      try {
        setIsLoading(true);
        const {
          data,
          error
        } = await supabase.functions.invoke('get-mapbox-token');
        if (error) {
          console.error('Error fetching Mapbox token:', error);
          toast({
            title: "Error fetching map token",
            description: "Please check that you've added your Mapbox token to Supabase Edge Function Secrets",
            variant: "destructive"
          });
          return;
        }
        if (data?.token) {
          setMapboxToken(data.token);
        } else {
          toast({
            title: "Map configuration error",
            description: "Please add your Mapbox token to Supabase Edge Function Secrets with the key MAPBOX_API_KEY",
            variant: "destructive"
          });
        }
      } catch (error) {
        toast({
          title: "Error loading map",
          description: "Make sure you've added your Mapbox token to Supabase Edge Function Secrets",
          variant: "destructive"
        });
      } finally {
        setIsLoading(false);
      }
    };
    fetchMapboxToken();
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(position => {
        setMyLocation({
          lng: position.coords.longitude,
          lat: position.coords.latitude
        });
        setInitialLocationLoaded(true);
      }, error => {
        console.error('Geolocation error:', error);
        toast({
          title: "Location Access Denied",
          description: "Please enable location services to see your location on the map.",
          variant: "destructive"
        });
        setInitialLocationLoaded(true);
      }, {
        enableHighAccuracy: true,
        timeout: 5000,
        maximumAge: 0
      });
    } else {
      toast({
        title: "Geolocation Not Supported",
        description: "Your browser does not support location services.",
        variant: "destructive"
      });
      setInitialLocationLoaded(true);
    }
  }, [toast]);
  useEffect(() => {
    if (mapCenter && mapRef.current) {
      mapRef.current.flyTo({
        center: [mapCenter.lng, mapCenter.lat],
        zoom: 15,
        pitch: 45,
        duration: 2000
      });
    }
  }, [mapCenter]);
  const getSidebarUsers = () => {
    return userLocations.map(u => ({
      ...u,
      id: u.id || u.user_id,
      created_at: u.created_at || new Date().toISOString(),
      updated_at: u.updated_at || new Date().toISOString(),
      distance: u.distance,
      is_online: u.is_online,
      avatar_url: u.avatar_url,
      username: u.display_name || u.username || `User ${u.user_id?.substring?.(0, 5)}` || "Buddy",
      bio: u.bio || ""
    }));
  };
  const handleMarkerClick = (user: UnifiedMapUser) => {
    setSelectedUser(user);
    if (user.location) {
      setMapCenter({
        lng: user.location.x,
        lat: user.location.y
      });
    }
    toast({
      title: `${user.display_name || user.username || (user.user_id?.substring?.(0, 5) ?? 'User')}`,
      description: user.bio || "No bio available"
    });
  };

  if (isLoading || !mapboxToken) {
    return <div className="fixed inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">
            {isLoading ? "Loading map resources..." : "Mapbox Token Required"}
          </p>
        </div>
      </div>;
  }

  return <div className="fixed inset-0 overflow-hidden bg-gradient-to-t from-[#f3e7e9] to-[#e3eeff] via-[#e3eeff]">
      <div className="absolute inset-0">
        <MapComponent
          mapboxToken={mapboxToken}
          userLocations={userLocations}
          onMarkerClick={handleMarkerClick}
          onActivityClick={handleActivitySelect}
          isLoading={isLoading || activitiesLoading}
          showUsers={showUsers}
          showActivities={showActivities}
          activeFilter={activeFilter}
          mapRef={mapRef}
          mapStyle={mapStyle}
          onMapStyleChange={handleMapStyleChange}
          enablePitch={preferences.enablePitch}
          enableRotation={preferences.enableRotation}
          showBuildings={preferences.showBuildings}
          initialLocation={myLocation}
          isLocationLoading={!initialLocationLoaded}
          showSavedLocations={true}
          showRightSidebar={false}
          activities={activitiesData || []}
        />

        {/* Right sidebar with tabs */}
        <div className="absolute top-0 bottom-0 right-0 z-30 hidden md:block">
          <MapRightSidebar
            users={getSidebarUsers()}
            activities={activitiesData || []}
            onUserSelect={handleUserSelect}
            onActivitySelect={handleActivitySelect}
            activeTab={activeSidebarTab}
            onTabChange={handleSidebarTabChange}
          />
        </div>
      </div>

      <MeetMapSearchBar onUserSelect={user => {
      const unifiedUser = 'id' in user ? user : mapPresenceToUnifiedUser(user);
      handleUserSelect(unifiedUser as UnifiedMapUser);
    }} onLocationSelect={handleLocationSelect} />

      <div className="absolute top-28 right-5 z-50 pointer-events-auto">
        <Sheet>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="h-9 w-9 rounded-full bg-background/95">
              <Settings className="h-4 w-4" />
            </Button>
          </SheetTrigger>
          <SheetContent className="w-[300px] sm:w-[400px]">
            <SheetHeader>
              <SheetTitle>Map Settings</SheetTitle>
              <SheetDescription>
                Customize your map experience
              </SheetDescription>
            </SheetHeader>
            <div className="py-4 space-y-4">
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Map Style</h3>
                <MapStyleSelector currentStyle={mapStyle} onChange={handleMapStyleChange} className="w-full" />
              </div>

              <div className="space-y-4 pt-2">
                <h3 className="text-sm font-medium">Map Features</h3>
                <div className="flex items-center justify-between">
                  <Label htmlFor="show-buildings">Show 3D Buildings</Label>
                  <Switch id="show-buildings" checked={preferences.showBuildings} onCheckedChange={checked => savePreferences({
                  showBuildings: checked
                })} />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="enable-pitch">Enable Pitch (Tilt)</Label>
                  <Switch id="enable-pitch" checked={preferences.enablePitch} onCheckedChange={checked => savePreferences({
                  enablePitch: checked
                })} />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="enable-rotation">Enable Rotation</Label>
                  <Switch id="enable-rotation" checked={preferences.enableRotation} onCheckedChange={checked => savePreferences({
                  enableRotation: checked
                })} />
                </div>
                <div className="flex items-center justify-between">
                  <Label htmlFor="save-location">Save Last Location</Label>
                  <Switch id="save-location" checked={preferences.saveLocation} onCheckedChange={checked => savePreferences({
                  saveLocation: checked
                })} />
                </div>
              </div>
            </div>
            <SheetFooter className="mt-4">
              <Button variant="outline" onClick={() => savePreferences({
              map_style: 'streets-v11',
              mapStyle: 'streets-v11',
              showBuildings: true,
              enablePitch: true,
              enableRotation: true,
              saveLocation: true
            })}>
                Reset to Defaults
              </Button>
            </SheetFooter>
          </SheetContent>
        </Sheet>
      </div>

      <div className="absolute bottom-24 right-5 z-50 w-64 pointer-events-auto">
        <MapLocationStatus className="bg-background/95" />
      </div>

      {/* Add the PeriodicLocationUpdater component */}
      <div className="absolute top-20 left-1/2 -translate-x-1/2 z-50 w-full max-w-md px-4">
        <PeriodicLocationUpdater
          interval={5 * 60 * 1000}
          onLocationUpdate={() => {
            // Refresh user data when location is updated
            if (myLocation) {
              setMapCenter({
                lng: myLocation.lng,
                lat: myLocation.lat
              });
            }
          }}
        />
      </div>

      <div className="absolute top-1/2 -translate-y-1/2 right-5 z-40 pointer-events-auto">
        <MapZoomControls onZoomIn={handleZoomIn} onZoomOut={handleZoomOut} onResetBearing={handleResetBearing} className="bg-background/10" />
      </div>

      <div className="absolute bottom-24 left-5 z-50 pointer-events-auto">
        <MapStyleSelector currentStyle={mapStyle} onChange={handleMapStyleChange} className="bg-background/95" />
      </div>

      {selectedUser && <div className="fixed bottom-24 left-1/2 -translate-x-1/2 z-50 pointer-events-auto">
          <EnhancedUserProfileCard user={selectedUser} onClose={() => setSelectedUser(null)} myLocation={myLocation ? lngLatToXY(myLocation.lng, myLocation.lat) : undefined} onConnect={() => {
        toast({
          title: "Connection request sent",
          description: `You've sent a connection request to ${selectedUser.display_name || selectedUser.username || 'this user'}`
        });
      }} onMessage={() => {
        toast({
          title: "Chat opened",
          description: `Starting a conversation with ${selectedUser.display_name || selectedUser.username || 'this user'}`
        });
      }} />
        </div>}
    </div>;
}
