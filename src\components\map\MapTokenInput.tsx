
import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface MapTokenInputProps {
  onSubmitToken: (token: string) => void;
}

const MapTokenInput = ({ onSubmitToken }: MapTokenInputProps) => {
  const [tokenInput, setTokenInput] = useState<string>("");

  const handleTokenSubmit = () => {
    if (tokenInput.trim()) {
      onSubmitToken(tokenInput.trim());
    }
  };

  return (
    <div className="absolute inset-0 flex flex-col items-center justify-center z-10 p-4 bg-background/80">
      <div className="max-w-md w-full space-y-4">
        <h2 className="text-lg font-semibold text-center">Mapbox Token Required</h2>
        <p className="text-sm text-center text-muted-foreground">
          Please enter your Mapbox public token to view the interactive map. 
          You can get one from <a href="https://mapbox.com/" target="_blank" rel="noopener noreferrer" className="text-primary underline">mapbox.com</a>
        </p>
        <div className="flex items-center gap-2">
          <Input
            type="text"
            placeholder="Enter Mapbox public token"
            value={tokenInput}
            onChange={(e) => setTokenInput(e.target.value)}
            className="flex-1"
          />
          <Button onClick={handleTokenSubmit}>Submit</Button>
        </div>
      </div>
    </div>
  );
};

export default MapTokenInput;
