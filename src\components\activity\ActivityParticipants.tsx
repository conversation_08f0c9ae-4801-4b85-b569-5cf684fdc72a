
import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { ActivityQueueEntry } from '@/types/activity';
import { ActivityQueueParticipants } from '@/components/activity/ActivityQueueParticipants';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';

interface ActivityParticipantsProps {
  activityId: string;
  isHost: boolean;
  queueStats?: {
    confirmed: number;
    pending: number;
    waitlisted: number;
    total: number;
    entries: ActivityQueueEntry[];
  };
  queueEntries?: ActivityQueueEntry[];
  onUpdateStatus?: (entryId: string, status: 'confirmed' | 'cancelled') => void;
  onBulkUpdate?: (status: 'confirmed' | 'cancelled') => void;
}

export function ActivityParticipants({
  activityId,
  isHost,
  queueStats,
  queueEntries = [],
  onUpdateStatus,
  onBulkUpdate
}: ActivityParticipantsProps) {
  const { toast } = useToast();

  const handleBulkAction = (status: 'confirmed' | 'cancelled') => {
    if (onBulkUpdate) {
      onBulkUpdate(status);
      toast({
        title: status === 'confirmed' ? 'Participants approved' : 'Participants rejected',
        description: status === 'confirmed' 
          ? 'All pending participants have been approved' 
          : 'All pending participants have been rejected',
        variant: status === 'confirmed' ? 'default' : 'destructive'
      });
    }
  };
  
  if (!queueStats) {
    return (
      <div className="space-y-4">
        <Skeleton className="h-12 w-full" />
        <Skeleton className="h-40 w-full" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {queueEntries && queueEntries.length > 0 ? (
        <ActivityQueueParticipants 
          entries={queueEntries}
          isHost={isHost}
          onUpdateStatus={onUpdateStatus}
          className="space-y-4"
        />
      ) : (
        <Card>
          <CardContent className="py-6 text-center text-muted-foreground">
            No participants have joined this activity yet.
          </CardContent>
        </Card>
      )}
      
      {isHost && queueStats.pending > 0 && (
        <div className="flex justify-end gap-2">
          <button 
            onClick={() => handleBulkAction('confirmed')}
            className="text-sm px-3 py-1.5 bg-green-100 text-green-700 rounded hover:bg-green-200"
          >
            Approve All
          </button>
          <button 
            onClick={() => handleBulkAction('cancelled')}
            className="text-sm px-3 py-1.5 bg-red-100 text-red-700 rounded hover:bg-red-200"
          >
            Reject All
          </button>
        </div>
      )}
    </div>
  );
}
