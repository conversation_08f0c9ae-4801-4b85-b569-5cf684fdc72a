-- SQL script to enhance profile features without redundancy
-- This script adds new tables and columns for the enhanced profile features

-- 1. Enhance the Profiles Table
ALTER TABLE public.profiles
  ADD COLUMN IF NOT EXISTS cover_url TEXT,                   -- Cover photo/banner
  ADD COLUMN IF NOT EXISTS social_links J<PERSON><PERSON><PERSON> DEFAULT '[]',  -- Social media links
  ADD COLUMN IF NOT EXISTS profile_theme TEXT,               -- Theme preference
  ADD COLUMN IF NOT EXISTS last_seen_at TIMESTAMPTZ,         -- For online status
  ADD COLUMN IF NOT EXISTS gallery_order JSONB DEFAULT '[]', -- For custom gallery ordering
  ADD COLUMN IF NOT EXISTS profile_visibility JSONB DEFAULT '{"followers": true, "following": true, "activities": true, "gallery": true, "about": true}'; -- Granular privacy settings

-- 2. Create a Profile Media Table (for better gallery management)
CREATE TABLE IF NOT EXISTS public.profile_media (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  url TEXT NOT NULL,
  thumbnail_url TEXT,
  type TEXT NOT NULL DEFAULT 'image',  -- image, video, etc.
  caption TEXT,
  position INTEGER,                    -- For ordering
  is_featured BOOLEAN DEFAULT FALSE,   -- Featured in profile
  metadata JSONB,                      -- For additional metadata (dimensions, etc.)
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_profile_media_profile_id ON public.profile_media(profile_id);
CREATE INDEX IF NOT EXISTS idx_profile_media_position ON public.profile_media(position);

-- 3. Create a User Blocks Table (for connection management)
CREATE TABLE IF NOT EXISTS public.user_blocks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  blocker_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  blocked_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  reason TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(blocker_id, blocked_id)
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_user_blocks_blocker_id ON public.user_blocks(blocker_id);
CREATE INDEX IF NOT EXISTS idx_user_blocks_blocked_id ON public.user_blocks(blocked_id);

-- 4. Create a Profile Views Table (for analytics)
CREATE TABLE IF NOT EXISTS public.profile_views (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  profile_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
  viewer_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
  viewed_at TIMESTAMPTZ DEFAULT NOW(),
  is_anonymous BOOLEAN DEFAULT FALSE
);

-- Add indexes
CREATE INDEX IF NOT EXISTS idx_profile_views_profile_id ON public.profile_views(profile_id);
CREATE INDEX IF NOT EXISTS idx_profile_views_viewer_id ON public.profile_views(viewer_id);
CREATE INDEX IF NOT EXISTS idx_profile_views_viewed_at ON public.profile_views(viewed_at);

-- 5. Create Functions for Profile Statistics
-- Function to get follower count
CREATE OR REPLACE FUNCTION get_follower_count(user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM public.follows
    WHERE following_id = user_id
  );
END;
$$;

-- Function to get following count
CREATE OR REPLACE FUNCTION get_following_count(user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM public.follows
    WHERE follower_id = user_id
  );
END;
$$;

-- Function to get activities count
CREATE OR REPLACE FUNCTION get_activities_count(user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM public.activities
    WHERE host_id = user_id
  );
END;
$$;

-- Function to get participated activities count
CREATE OR REPLACE FUNCTION get_participated_activities_count(user_id UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM public.activity_participants
    WHERE user_id = user_id AND status = 'confirmed'
  );
END;
$$;

-- Function to get mutual connections
CREATE OR REPLACE FUNCTION get_mutual_connections(user_id1 UUID, user_id2 UUID)
RETURNS INTEGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN (
    SELECT COUNT(*)
    FROM (
      SELECT following_id
      FROM public.follows
      WHERE follower_id = user_id1
      INTERSECT
      SELECT following_id
      FROM public.follows
      WHERE follower_id = user_id2
    ) AS mutual
  );
END;
$$;

-- 6. Create Views for Efficient Querying
-- View for profile statistics
CREATE OR REPLACE VIEW profile_statistics AS
SELECT
  p.id,
  p.username,
  p.display_name,
  p.avatar_url,
  p.cover_url,
  p.is_verified,
  p.last_seen_at,
  (SELECT COUNT(*) FROM follows WHERE following_id = p.id) AS follower_count,
  (SELECT COUNT(*) FROM follows WHERE follower_id = p.id) AS following_count,
  (SELECT COUNT(*) FROM activities WHERE host_id = p.id) AS hosted_activities_count,
  (SELECT COUNT(*) FROM activity_participants WHERE user_id = p.id AND status = 'confirmed') AS participated_activities_count,
  (SELECT COUNT(*) FROM profile_media WHERE profile_id = p.id) AS media_count
FROM
  profiles p;

-- View for profile connections
CREATE OR REPLACE VIEW profile_connections AS
SELECT
  f.id,
  f.follower_id,
  f.following_id,
  f.created_at,
  p1.username AS follower_username,
  p1.display_name AS follower_display_name,
  p1.avatar_url AS follower_avatar_url,
  p1.is_verified AS follower_is_verified,
  p2.username AS following_username,
  p2.display_name AS following_display_name,
  p2.avatar_url AS following_avatar_url,
  p2.is_verified AS following_is_verified,
  (SELECT COUNT(*) FROM follows WHERE follower_id = f.following_id AND following_id = f.follower_id) > 0 AS is_mutual
FROM
  follows f
JOIN
  profiles p1 ON f.follower_id = p1.id
JOIN
  profiles p2 ON f.following_id = p2.id;

-- 7. Create RLS Policies for Security
-- Enable RLS on all tables
ALTER TABLE public.profile_media ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_blocks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profile_views ENABLE ROW LEVEL SECURITY;

-- RLS policies for profile_media
CREATE POLICY "Users can view public profile media"
ON public.profile_media
FOR SELECT
USING (
  profile_id IN (
    SELECT id FROM profiles WHERE is_profile_public = TRUE
  )
  OR
  profile_id IN (
    SELECT following_id FROM follows WHERE follower_id = auth.uid()
  )
  OR
  profile_id = auth.uid()
);

CREATE POLICY "Users can manage their own profile media"
ON public.profile_media
FOR ALL
USING (profile_id = auth.uid())
WITH CHECK (profile_id = auth.uid());

-- RLS policies for user_blocks
CREATE POLICY "Users can view their own blocks"
ON public.user_blocks
FOR SELECT
USING (blocker_id = auth.uid());

CREATE POLICY "Users can manage their own blocks"
ON public.user_blocks
FOR ALL
USING (blocker_id = auth.uid())
WITH CHECK (blocker_id = auth.uid());

-- RLS policies for profile_views
CREATE POLICY "Users can view their profile views"
ON public.profile_views
FOR SELECT
USING (profile_id = auth.uid());

CREATE POLICY "Anyone can create profile views"
ON public.profile_views
FOR INSERT
WITH CHECK (true);

-- 8. Create Triggers for Data Integrity
-- Trigger to update last_seen_at when a user is active
CREATE OR REPLACE FUNCTION update_last_seen()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE profiles
  SET last_seen_at = NOW()
  WHERE id = auth.uid();
  RETURN NEW;
END;
$$;

-- Create a trigger on any table that indicates user activity
CREATE TRIGGER update_user_last_seen
AFTER INSERT OR UPDATE ON messages
FOR EACH ROW
EXECUTE FUNCTION update_last_seen();

-- Trigger to update updated_at on profile_media
CREATE OR REPLACE FUNCTION update_profile_media_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

CREATE TRIGGER set_profile_media_updated_at
BEFORE UPDATE ON profile_media
FOR EACH ROW
EXECUTE FUNCTION update_profile_media_updated_at();
