import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { Gig } from '@/hooks/use-gigs';

export interface ServiceSearchFilters {
  searchQuery?: string;
  categoryIds?: string[];
  priceRange?: {
    min: number;
    max: number;
  };
  ratingRange?: {
    min: number;
    max: number;
  };
  locationRadius?: {
    center: { lat: number; lng: number };
    radiusKm: number;
  };
  deliveryTime?: number; // max delivery time in days
  experienceLevel?: ('beginner' | 'intermediate' | 'expert')[];
  availability?: ('available' | 'busy')[];
  isFeatured?: boolean;
  isVerified?: boolean;
  sortBy?: 'relevance' | 'price_low' | 'price_high' | 'rating' | 'popularity' | 'newest';
  limit?: number;
  offset?: number;
}

export interface ServiceSearchResult {
  gigs: Gig[];
  totalCount: number;
  hasMore: boolean;
  facets: {
    categories: { id: string; name: string; count: number }[];
    priceRanges: { range: string; count: number }[];
    ratings: { rating: number; count: number }[];
    experienceLevels: { level: string; count: number }[];
  };
}

// Hook for advanced service search with filters
export function useAdvancedServiceSearch(filters: ServiceSearchFilters) {
  return useQuery({
    queryKey: ['advanced-service-search', filters],
    queryFn: async (): Promise<ServiceSearchResult> => {
      let query = supabase
        .from('gigs')
        .select(`
          *,
          provider:service_providers(
            id,
            name,
            rating,
            total_reviews,
            is_verified,
            location,
            user:profiles!service_providers_user_id_fkey(
              id,
              display_name,
              avatar_url,
              username
            )
          ),
          category:categories(id, name, icon, type),
          featured:featured_services(
            id,
            featured_type,
            priority,
            is_active
          )
        `, { count: 'exact' });

      // Apply category filter
      if (filters.categoryIds && filters.categoryIds.length > 0) {
        query = query.in('category_id', filters.categoryIds);
      }

      // Apply search query
      if (filters.searchQuery) {
        query = query.or(`title.ilike.%${filters.searchQuery}%,description.ilike.%${filters.searchQuery}%,tags.cs.{${filters.searchQuery}}`);
      }

      // Apply price range filter
      if (filters.priceRange) {
        query = query
          .gte('price_starting', filters.priceRange.min)
          .lte('price_starting', filters.priceRange.max);
      }

      // Apply rating filter
      if (filters.ratingRange) {
        query = query
          .gte('rating', filters.ratingRange.min)
          .lte('rating', filters.ratingRange.max);
      }

      // Apply delivery time filter
      if (filters.deliveryTime) {
        query = query.lte('delivery_time', filters.deliveryTime);
      }

      // Apply experience level filter
      if (filters.experienceLevel && filters.experienceLevel.length > 0) {
        query = query.in('experience_level', filters.experienceLevel);
      }

      // Apply availability filter
      if (filters.availability && filters.availability.length > 0) {
        query = query.in('availability', filters.availability);
      }

      // Apply featured filter
      if (filters.isFeatured) {
        query = query.eq('is_featured', true);
      }

      // Apply sorting
      switch (filters.sortBy) {
        case 'price_low':
          query = query.order('price_starting', { ascending: true });
          break;
        case 'price_high':
          query = query.order('price_starting', { ascending: false });
          break;
        case 'rating':
          query = query.order('rating', { ascending: false });
          break;
        case 'popularity':
          query = query.order('total_orders', { ascending: false });
          break;
        case 'newest':
          query = query.order('created_at', { ascending: false });
          break;
        default:
          // Relevance sorting - featured first, then by rating and popularity
          query = query.order('is_featured', { ascending: false })
                      .order('rating', { ascending: false })
                      .order('total_orders', { ascending: false });
      }

      // Apply pagination
      const limit = filters.limit || 20;
      const offset = filters.offset || 0;
      query = query.range(offset, offset + limit - 1);

      const { data, error, count } = await query;

      if (error) throw error;

      // Map database fields to interface fields
      const gigs = (data || []).map(gig => ({
        ...gig,
        delivery_time_days: gig.delivery_time || 1,
        gig_type: 'service' as const,
        skill_level: gig.experience_level || 'intermediate',
        gallery_images: gig.media_urls || [],
        gallery_videos: [],
        packages: [],
        add_ons: [],
        faq: [],
        average_rating: Number(gig.rating) || 0,
        total_reviews: gig.total_orders || 0
      })) as Gig[];

      // Get facets for filtering UI
      const facets = await getFacets(filters);

      return {
        gigs,
        totalCount: count || 0,
        hasMore: (count || 0) > (offset + limit),
        facets
      };
    },
    enabled: true
  });
}

// Helper function to get facets for filtering
async function getFacets(filters: ServiceSearchFilters) {
  // Get category facets
  const { data: categoryFacets } = await supabase
    .from('categories')
    .select(`
      id,
      name,
      gigs:gigs(count)
    `)
    .eq('type', 'service');

  // Get price range facets
  const priceRanges = [
    { range: '$0-$25', min: 0, max: 25 },
    { range: '$25-$50', min: 25, max: 50 },
    { range: '$50-$100', min: 50, max: 100 },
    { range: '$100-$250', min: 100, max: 250 },
    { range: '$250+', min: 250, max: 999999 }
  ];

  const priceRangeFacets = await Promise.all(
    priceRanges.map(async (range) => {
      const { count } = await supabase
        .from('gigs')
        .select('*', { count: 'exact', head: true })
        .gte('price_starting', range.min)
        .lte('price_starting', range.max);
      
      return { range: range.range, count: count || 0 };
    })
  );

  // Get rating facets
  const ratingFacets = await Promise.all(
    [5, 4, 3, 2, 1].map(async (rating) => {
      const { count } = await supabase
        .from('gigs')
        .select('*', { count: 'exact', head: true })
        .gte('rating', rating);
      
      return { rating, count: count || 0 };
    })
  );

  // Get experience level facets
  const experienceLevels = ['beginner', 'intermediate', 'expert'];
  const experienceLevelFacets = await Promise.all(
    experienceLevels.map(async (level) => {
      const { count } = await supabase
        .from('gigs')
        .select('*', { count: 'exact', head: true })
        .eq('experience_level', level);
      
      return { level, count: count || 0 };
    })
  );

  return {
    categories: (categoryFacets || []).map(cat => ({
      id: cat.id,
      name: cat.name,
      count: cat.gigs?.[0]?.count || 0
    })),
    priceRanges: priceRangeFacets,
    ratings: ratingFacets,
    experienceLevels: experienceLevelFacets
  };
}

// Hook to track service interactions for recommendations
export function useTrackServiceInteraction() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      gigId,
      categoryId,
      interactionType,
      weight = 1
    }: {
      gigId: string;
      categoryId?: string;
      interactionType: 'view' | 'like' | 'contact' | 'book' | 'review';
      weight?: number;
    }) => {
      if (!user) return;

      const { error } = await supabase
        .from('service_interactions')
        .insert({
          user_id: user.id,
          gig_id: gigId,
          category_id: categoryId,
          interaction_type: interactionType,
          interaction_weight: weight,
          created_at: new Date().toISOString()
        });

      if (error) throw error;
    },
    onSuccess: () => {
      // Invalidate recommendations
      queryClient.invalidateQueries({ queryKey: ['service-recommendations'] });
    }
  });
}

// Hook to get service recommendations based on user behavior
export function useServiceRecommendations(limit = 10) {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['service-recommendations', user?.id, limit],
    queryFn: async (): Promise<Gig[]> => {
      if (!user) return [];

      // Get user's interaction history to determine preferences
      const { data: interactions } = await supabase
        .from('service_interactions')
        .select('category_id, interaction_type, interaction_weight')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(100);

      // Calculate category preferences based on interactions
      const categoryScores: Record<string, number> = {};
      interactions?.forEach(interaction => {
        if (interaction.category_id) {
          const weight = interaction.interaction_weight || 1;
          const typeMultiplier = {
            view: 1,
            like: 2,
            contact: 3,
            book: 5,
            review: 4
          }[interaction.interaction_type] || 1;

          categoryScores[interaction.category_id] = 
            (categoryScores[interaction.category_id] || 0) + (weight * typeMultiplier);
        }
      });

      // Get top categories
      const topCategories = Object.entries(categoryScores)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([categoryId]) => categoryId);

      if (topCategories.length === 0) {
        // If no interaction history, return trending/featured services
        const { data } = await supabase
          .from('gigs')
          .select(`
            *,
            provider:service_providers(
              id,
              name,
              rating,
              total_reviews,
              user:profiles!service_providers_user_id_fkey(
                id,
                display_name,
                avatar_url,
                username
              )
            ),
            category:categories(id, name, icon, type)
          `)
          .eq('is_featured', true)
          .order('rating', { ascending: false })
          .limit(limit);

        return (data || []).map(gig => ({
          ...gig,
          delivery_time_days: gig.delivery_time || 1,
          gig_type: 'service' as const,
          skill_level: gig.experience_level || 'intermediate',
          gallery_images: gig.media_urls || [],
          gallery_videos: [],
          packages: [],
          add_ons: [],
          faq: [],
          average_rating: Number(gig.rating) || 0,
          total_reviews: gig.total_orders || 0
        })) as Gig[];
      }

      // Get recommended services from preferred categories
      const { data } = await supabase
        .from('gigs')
        .select(`
          *,
          provider:service_providers(
            id,
            name,
            rating,
            total_reviews,
            user:profiles!service_providers_user_id_fkey(
              id,
              display_name,
              avatar_url,
              username
            )
          ),
          category:categories(id, name, icon, type)
        `)
        .in('category_id', topCategories)
        .gte('rating', 4.0) // Only recommend highly rated services
        .order('rating', { ascending: false })
        .order('total_orders', { ascending: false })
        .limit(limit);

      return (data || []).map(gig => ({
        ...gig,
        delivery_time_days: gig.delivery_time || 1,
        gig_type: 'service' as const,
        skill_level: gig.experience_level || 'intermediate',
        gallery_images: gig.media_urls || [],
        gallery_videos: [],
        packages: [],
        add_ons: [],
        faq: [],
        average_rating: Number(gig.rating) || 0,
        total_reviews: gig.total_orders || 0
      })) as Gig[];
    },
    enabled: !!user
  });
}

// Hook to get featured services
export function useFeaturedServices(type?: 'premium' | 'sponsored' | 'trending' | 'editor_choice', limit = 10) {
  return useQuery({
    queryKey: ['featured-services', type, limit],
    queryFn: async (): Promise<Gig[]> => {
      let query = supabase
        .from('featured_services')
        .select(`
          gig:gigs(
            *,
            provider:service_providers(
              id,
              name,
              rating,
              total_reviews,
              user:profiles!service_providers_user_id_fkey(
                id,
                display_name,
                avatar_url,
                username
              )
            ),
            category:categories(id, name, icon, type)
          )
        `)
        .eq('is_active', true)
        .order('priority', { ascending: false })
        .order('created_at', { ascending: false });

      if (type) {
        query = query.eq('featured_type', type);
      }

      // Filter by date range
      const now = new Date().toISOString();
      query = query
        .lte('start_date', now)
        .or(`end_date.is.null,end_date.gte.${now}`)
        .limit(limit);

      const { data, error } = await query;

      if (error) throw error;

      return (data || [])
        .map(item => item.gig)
        .filter(Boolean)
        .map(gig => ({
          ...gig,
          delivery_time_days: gig.delivery_time || 1,
          gig_type: 'service' as const,
          skill_level: gig.experience_level || 'intermediate',
          gallery_images: gig.media_urls || [],
          gallery_videos: [],
          packages: [],
          add_ons: [],
          faq: [],
          average_rating: Number(gig.rating) || 0,
          total_reviews: gig.total_orders || 0
        })) as Gig[];
    }
  });
}
