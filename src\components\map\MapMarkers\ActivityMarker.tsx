
import React from 'react';
import { Activity } from '@/types/activity';
import { cn } from '@/lib/utils';
import { Calendar, MapPin, DollarSign } from 'lucide-react';

interface ActivityMarkerProps {
  activity: Activity;
  onClick?: () => void;
  element?: HTMLElement;
  highlight?: boolean;
  isNew?: boolean;
}

export function ActivityMarker({
  activity,
  onClick,
  element,
  highlight = false,
  isNew = false
}: ActivityMarkerProps) {
  // Determine which icon to use based on category or paid status
  const getActivityIcon = () => {
    if (activity.category?.name?.toLowerCase().includes('sports')) {
      return '🏀';
    } else if (activity.category?.name?.toLowerCase().includes('music')) {
      return '🎵';
    } else if (activity.category?.name?.toLowerCase().includes('food')) {
      return '🍽️';
    } else if (activity.category?.name?.toLowerCase().includes('art')) {
      return '🎨';
    } else if (activity.is_paid) {
      return '💰';
    } else {
      return '📅';
    }
  };

  // When used as a standalone React component (not with mapbox)
  if (!element) {
    return (
      <div
        className={cn(
          "activity-marker cursor-pointer relative transition-all duration-300",
          "hover:scale-110 active:scale-95",
          isNew && "new-activity",
          highlight && "selected-activity scale-110"
        )}
        data-activity-id={activity.id}
        onClick={onClick}
      >
        <div className={cn(
          "w-10 h-10 rounded-full flex items-center justify-center shadow-lg",
          "bg-white border-2",
          highlight ? "border-primary" : "border-orange-400",
          isNew && "animate-bounce"
        )}>
          <span className="text-lg" role="img" aria-label="activity">
            {getActivityIcon()}
          </span>
        </div>

        {activity.is_paid && (
          <div className="absolute -right-1 -top-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center text-white text-[10px] font-bold border border-white">
            $
          </div>
        )}

        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full pointer-events-none">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-lg shadow-md text-sm mt-2 font-medium">
            {activity.title}
          </div>
        </div>
      </div>
    );
  }

  // If used with Mapbox, apply the content directly to the provided element
  if (element) {
    // Create marker HTML structure
    const activityIcon = getActivityIcon();
    const markerHTML = `
      <div class="activity-marker cursor-pointer relative transition-all duration-300 ${isNew ? 'new-activity' : ''} ${highlight ? 'selected-activity scale-110' : ''}" data-activity-id="${activity.id}">
        <div class="w-12 h-12 rounded-full flex items-center justify-center shadow-lg bg-white border-2 ${highlight ? 'border-primary' : 'border-orange-400'} ${isNew ? 'animate-bounce' : ''}">
          <span class="text-xl" role="img" aria-label="activity">${activityIcon}</span>
        </div>

        ${activity.is_paid ? `
          <div class="absolute -right-1 -top-1 w-5 h-5 bg-yellow-500 rounded-full flex items-center justify-center text-white text-[10px] font-bold border border-white">
            $
          </div>
        ` : ''}

        <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full">
          <div class="marker-tooltip">
            ${activity.title}
          </div>
        </div>
      </div>
    `;

    // Set the inner HTML of the element
    element.innerHTML = markerHTML;

    // Add click event listener
    if (onClick) {
      element.addEventListener('click', onClick);
    }

    return null;
  }

  return null;
}

// Function to create a marker element for use with Mapbox
export function createActivityMarker(
  activity: Activity,
  onClick?: () => void,
  highlight?: boolean,
  isNew?: boolean
) {
  const element = document.createElement('div');
  ActivityMarker({ activity, onClick, element, highlight, isNew });
  return element;
}
