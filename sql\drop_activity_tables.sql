-- SQL script to drop activity-related tables
-- This script removes all activity-related functionality from the database

-- First, drop any foreign key constraints that might prevent table deletion
DO $$ 
DECLARE
    r RECORD;
BEGIN
    -- Find all foreign keys referencing the activity tables
    FOR r IN (
        SELECT
            tc.constraint_name,
            tc.table_name
        FROM
            information_schema.table_constraints tc
            JOIN information_schema.constraint_column_usage ccu ON tc.constraint_name = ccu.constraint_name
        WHERE
            tc.constraint_type = 'FOREIGN KEY'
            AND (
                ccu.table_name = 'activities'
                OR ccu.table_name = 'activity_media'
                OR ccu.table_name = 'activity_participants'
            )
    ) LOOP
        EXECUTE 'ALTER TABLE ' || r.table_name || ' DROP CONSTRAINT ' || r.constraint_name || ' CASCADE';
    END LOOP;
END $$;

-- Drop any triggers on the tables
DROP TRIGGER IF EXISTS on_activity_created ON activities;
DROP TRIGGER IF EXISTS on_activity_updated ON activities;
DROP TRIGGER IF EXISTS on_activity_deleted ON activities;
DROP TRIGGER IF EXISTS on_activity_participant_created ON activity_participants;
DROP TRIGGER IF EXISTS on_activity_participant_updated ON activity_participants;

-- Drop any functions related to activities
DROP FUNCTION IF EXISTS get_activity_queue_stats(p_activity_id text, p_max_participants integer);
DROP FUNCTION IF EXISTS get_activity_analytics(p_activity_id text);
DROP FUNCTION IF EXISTS get_activities_count(user_id text);
DROP FUNCTION IF EXISTS get_activity_statistics();

-- Drop RLS policies on the tables
DROP POLICY IF EXISTS "Users can view all activities" ON activities;
DROP POLICY IF EXISTS "Users can create activities" ON activities;
DROP POLICY IF EXISTS "Users can update their own activities" ON activities;
DROP POLICY IF EXISTS "Users can delete their own activities" ON activities;
DROP POLICY IF EXISTS "Admins can manage all activities" ON activities;

DROP POLICY IF EXISTS "Users can view all activity participants" ON activity_participants;
DROP POLICY IF EXISTS "Users can join activities" ON activity_participants;
DROP POLICY IF EXISTS "Users can update their own participation" ON activity_participants;
DROP POLICY IF EXISTS "Hosts can update participants for their activities" ON activity_participants;
DROP POLICY IF EXISTS "Users can delete their own participation" ON activity_participants;
DROP POLICY IF EXISTS "Admins can manage all participants" ON activity_participants;

DROP POLICY IF EXISTS "Users can view all activity media" ON activity_media;
DROP POLICY IF EXISTS "Users can add media to their activities" ON activity_media;
DROP POLICY IF EXISTS "Users can update their own activity media" ON activity_media;
DROP POLICY IF EXISTS "Users can delete their own activity media" ON activity_media;
DROP POLICY IF EXISTS "Admins can manage all activity media" ON activity_media;

-- Drop the tables
DROP TABLE IF EXISTS activity_participants CASCADE;
DROP TABLE IF EXISTS activity_media CASCADE;
DROP TABLE IF EXISTS activities CASCADE;

-- Remove any references in other tables
-- Update profiles table to remove activity-related columns if they exist
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'activity_count') THEN
        ALTER TABLE profiles DROP COLUMN activity_count;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'profiles' AND column_name = 'hosted_activities_count') THEN
        ALTER TABLE profiles DROP COLUMN hosted_activities_count;
    END IF;
END $$;

-- Update chat_conversations table to remove activity-related columns if they exist
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'chat_conversations' AND column_name = 'activity_id') THEN
        ALTER TABLE chat_conversations DROP COLUMN activity_id;
    END IF;
END $$;

-- Clean up any views that might reference the dropped tables
DROP VIEW IF EXISTS activity_participants_view CASCADE;
DROP VIEW IF EXISTS activity_details_view CASCADE;

-- Notify of completion
DO $$ 
BEGIN
    RAISE NOTICE 'Activity-related tables and dependencies have been removed successfully.';
END $$;
