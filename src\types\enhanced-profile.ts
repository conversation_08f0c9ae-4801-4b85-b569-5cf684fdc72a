
import { UserProfile } from './user-profile';

// Enhanced profile with additional fields
export interface EnhancedUserProfile extends UserProfile {
  // New fields for enhanced profile
  cover_url?: string;
  social_links?: SocialLink[];
  profile_theme?: string;
  last_seen_at?: string;
  gallery_order?: GalleryOrderItem[];
  profile_visibility?: ProfileVisibilitySettings;
  trust_score?: number; // Added trust_score property
  
  // Statistics (computed)
  follower_count?: number;
  following_count?: number;
  hosted_activities_count?: number;
  participated_activities_count?: number;
  media_count?: number;
}

// Social media link
export interface SocialLink {
  platform: string;
  url: string;
  username?: string;
  icon?: string;
}

// Gallery order item
export interface GalleryOrderItem {
  id: string;
  position: number;
}

// Profile visibility settings
export interface ProfileVisibilitySettings {
  followers: boolean;
  following: boolean;
  activities: boolean;
  gallery: boolean;
  about: boolean;
}

// Profile media item
export interface ProfileMedia {
  id: string;
  profile_id: string;
  url: string;
  thumbnail_url?: string;
  type: 'image' | 'video' | 'audio' | 'document';
  caption?: string;
  position: number;
  is_featured: boolean;
  metadata?: Record<string, any>;
  created_at: string;
  updated_at: string;
}

// Profile connection (follower/following)
export interface ProfileConnection {
  id: string;
  follower_id: string;
  following_id: string;
  created_at: string;
  follower_username: string;
  follower_display_name: string;
  follower_avatar_url?: string;
  follower_is_verified: boolean;
  following_username: string;
  following_display_name: string;
  following_avatar_url?: string;
  following_is_verified: boolean;
  is_mutual: boolean;
}

// User block
export interface UserBlock {
  id: string;
  blocker_id: string;
  blocked_id: string;
  reason?: string;
  created_at: string;
}

// Profile view
export interface ProfileView {
  id: string;
  profile_id: string;
  viewer_id?: string;
  viewed_at: string;
  is_anonymous: boolean;
}

// Profile statistics
export interface ProfileStatistics {
  id: string;
  username: string;
  display_name: string;
  avatar_url?: string;
  cover_url?: string;
  is_verified: boolean;
  last_seen_at?: string;
  follower_count: number;
  following_count: number;
  hosted_activities_count: number;
  participated_activities_count: number;
  media_count: number;
}
