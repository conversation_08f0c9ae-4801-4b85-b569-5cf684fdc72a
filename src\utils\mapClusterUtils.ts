
import Supercluster from 'supercluster';
import { Location, CoordinateLocation } from '@/types/activity';

export function createMapCluster(features: any[], radius: number = 40) {
  const index = new Supercluster({
    log: false,
    radius: radius,
    extent: 256,
    maxZoom: 17,
    minZoom: 0,
  }).load(features);

  return index;
}

/**
 * Calculate the distance between two points
 */
export function calculateDistance(point1: CoordinateLocation, point2: CoordinateLocation): number {
  const R = 6371; // Radius of the Earth in kilometers
  const lat1 = (point1.latitude || point1.lat || point1.y) * Math.PI / 180;
  const lon1 = (point1.longitude || point1.lng || point1.x) * Math.PI / 180;
  const lat2 = (point2.latitude || point2.lat || point2.y) * Math.PI / 180;
  const lon2 = (point2.longitude || point2.lng || point2.x) * Math.PI / 180;

  const dlon = lon2 - lon1;
  const dlat = lat2 - lat1;

  const a = Math.sin(dlat / 2)**2 + Math.cos(lat1) * Math.cos(lat2) * Math.sin(dlon / 2)**2;
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  const distance = R * c; // Distance in kilometers
  return distance;
}

/**
 * Convert a location object to a coordinate point
 */
export function toCoordinatePoint(location: any): CoordinateLocation {
  if (!location) {
    return { x: 0, y: 0 };
  }

  // Check if location is already in the correct format
  if (typeof location.x === 'number' && typeof location.y === 'number') {
    return {
      x: location.x,
      y: location.y,
      latitude: location.latitude,
      longitude: location.longitude,
      lat: location.lat,
      lng: location.lng,
    };
  }

  // Handle different location formats
  if (typeof location === 'string') {
    try {
      const parsed = JSON.parse(location);
      const lng = parsed.x || parsed.lng || 0;
      const lat = parsed.y || parsed.lat || 0;
      return { x: lng, y: lat, latitude: lat, longitude: lng };
    } catch (e) {
      return { x: 0, y: 0 };
    }
  }

  const lng = location.x !== undefined ? location.x : (location.lng !== undefined ? location.lng : 0);
  const lat = location.y !== undefined ? location.y : (location.lat !== undefined ? location.lat : 0);

  return { x: lng, y: lat, longitude: lng, latitude: lat };
}

/**
 * Convert a Location to a coordinate point ensuring x and y are defined
 */
export function locationToCoordinatePoint(location: Location): CoordinateLocation {
  return {
    x: location.x,
    y: location.y,
    latitude: location.latitude,
    longitude: location.longitude
  };
}
