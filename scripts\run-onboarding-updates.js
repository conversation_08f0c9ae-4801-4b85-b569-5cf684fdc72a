/**
 * This script runs all the SQL updates needed for the onboarding integration
 * 
 * Usage:
 * 1. Make sure you have the Supabase service key in your .env file
 * 2. Run this script with Node.js: node scripts/run-onboarding-updates.js
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY; // Use service key for admin operations

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function runOnboardingUpdates() {
  try {
    console.log('Running onboarding updates...');
    
    // 1. First run the update_profiles_table.sql
    console.log('Step 1: Updating profiles table...');
    const profilesTableSqlPath = path.join(__dirname, '../sql/update_profiles_table.sql');
    const profilesTableSqlContent = fs.readFileSync(profilesTableSqlPath, 'utf8');
    
    const { error: profilesTableError } = await supabase.sql(profilesTableSqlContent);
    
    if (profilesTableError) {
      console.error('Error updating profiles table:', profilesTableError);
      process.exit(1);
    }
    
    console.log('Profiles table updated successfully!');
    
    // 2. Run the update_onboarding_completed.sql
    console.log('Step 2: Creating update_onboarding_completed function...');
    const onboardingCompletedSqlPath = path.join(__dirname, '../sql/update_onboarding_completed.sql');
    const onboardingCompletedSqlContent = fs.readFileSync(onboardingCompletedSqlPath, 'utf8');
    
    const { error: onboardingCompletedError } = await supabase.sql(onboardingCompletedSqlContent);
    
    if (onboardingCompletedError) {
      console.error('Error creating update_onboarding_completed function:', onboardingCompletedError);
      process.exit(1);
    }
    
    console.log('update_onboarding_completed function created successfully!');
    
    // 3. Run the improved_rls_policies.sql
    console.log('Step 3: Updating RLS policies...');
    const rlsPoliciesSqlPath = path.join(__dirname, '../sql/improved_rls_policies.sql');
    const rlsPoliciesSqlContent = fs.readFileSync(rlsPoliciesSqlPath, 'utf8');
    
    const { error: rlsPoliciesError } = await supabase.sql(rlsPoliciesSqlContent);
    
    if (rlsPoliciesError) {
      console.error('Error updating RLS policies:', rlsPoliciesError);
      process.exit(1);
    }
    
    console.log('RLS policies updated successfully!');
    
    // 4. Verify the update_onboarding_completed function
    console.log('Step 4: Verifying update_onboarding_completed function...');
    
    const { data: functions, error: functionsError } = await supabase.sql(`
      SELECT proname, proargnames, prosrc 
      FROM pg_proc 
      WHERE proname = 'update_onboarding_completed';
    `);
    
    if (functionsError) {
      console.error('Error verifying function:', functionsError);
    } else if (!functions || functions.length === 0) {
      console.error('Function update_onboarding_completed not found!');
    } else {
      console.log('Function update_onboarding_completed exists:', functions[0]);
    }
    
    console.log('All onboarding updates completed successfully!');
  } catch (error) {
    console.error('Error running onboarding updates:', error);
    process.exit(1);
  }
}

runOnboardingUpdates();
