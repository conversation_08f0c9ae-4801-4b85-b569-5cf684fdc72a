"use client"

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { format, addDays, isValid, isBefore, startOfDay } from "date-fns";

interface ActivityDatePickerProps {
  value?: Date;
  onChange: (date: Date | undefined) => void;
  className?: string;
  placeholder?: string;
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  id?: string;
  name?: string;
}

export function ActivityDatePicker({
  value,
  onChange,
  className,
  placeholder = "Select date",
  label,
  description,
  error,
  required = false,
  id,
  name
}: ActivityDatePickerProps) {
  // Generate a unique ID if not provided
  const uniqueId = id || `date-picker-${Math.random().toString(36).substring(2, 9)}`;
  // Use the name prop or fallback to the ID
  const fieldName = name || uniqueId;
  const [date, setDate] = useState<Date | undefined>(value);
  const [view, setView] = useState<'month' | 'date'>('date'); // Start with date view for activities
  const [selectedMonth, setSelectedMonth] = useState<number>(date?.getMonth() ?? new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState<number>(date?.getFullYear() ?? new Date().getFullYear());
  const [isOpen, setIsOpen] = useState(false);

  // Today's date at the start of the day (for comparison)
  const today = startOfDay(new Date());

  // Update the internal date when the value prop changes
  useEffect(() => {
    setDate(value);
    if (value) {
      setSelectedMonth(value.getMonth());
      setSelectedYear(value.getFullYear());
    }
  }, [value]);

  // Months array for selection
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Navigate between months
  const prevMonth = () => {
    if (selectedMonth === 0) {
      setSelectedMonth(11);
      setSelectedYear(selectedYear - 1);
    } else {
      setSelectedMonth(selectedMonth - 1);
    }
  };

  const nextMonth = () => {
    if (selectedMonth === 11) {
      setSelectedMonth(0);
      setSelectedYear(selectedYear + 1);
    } else {
      setSelectedMonth(selectedMonth + 1);
    }
  };

  // Handle day selection
  const handleDaySelect = (day: number) => {
    const newDate = new Date(selectedYear, selectedMonth, day);
    setDate(newDate);
    onChange(newDate);
    setIsOpen(false); // Close the popover after selection
  };

  // Get days in the selected month
  const getDaysInMonth = () => {
    const daysInMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate();
    return Array.from({ length: daysInMonth }, (_, i) => i + 1);
  };

  // Get the first day of the month (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = () => {
    return new Date(selectedYear, selectedMonth, 1).getDay();
  };

  // Render the date selection view
  const renderDateView = () => {
    const days = getDaysInMonth();
    const firstDay = getFirstDayOfMonth();
    const dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

    // Create empty cells for days before the first day of the month
    const emptyCells = Array.from({ length: firstDay }, (_, i) => (
      <div key={`empty-${i}`} className="h-8 w-8"></div>
    ));

    return (
      <div className="p-3">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            className="h-7 w-7 p-0"
            onClick={prevMonth}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="text-sm font-medium">
            {months[selectedMonth]} {selectedYear}
          </div>
          <Button
            variant="ghost"
            className="h-7 w-7 p-0"
            onClick={nextMonth}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="grid grid-cols-7 gap-1 mb-2">
          {dayNames.map(day => (
            <div key={day} className="h-8 w-8 flex items-center justify-center text-xs text-muted-foreground">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1">
          {emptyCells}
          {days.map(day => {
            const currentDate = new Date(selectedYear, selectedMonth, day);
            const isSelected = date && date.getDate() === day &&
                              date.getMonth() === selectedMonth &&
                              date.getFullYear() === selectedYear;
            
            // Disable past dates (before today)
            const isPastDate = isBefore(currentDate, today);
            const isDisabled = isPastDate;

            return (
              <Button
                key={day}
                variant={isSelected ? "default" : "ghost"}
                className={cn(
                  "h-8 w-8 p-0 font-normal",
                  isDisabled && "opacity-50 cursor-not-allowed",
                  isSelected && "text-primary-foreground",
                  // Highlight today's date
                  currentDate.getDate() === today.getDate() && 
                  currentDate.getMonth() === today.getMonth() && 
                  currentDate.getFullYear() === today.getFullYear() && 
                  "border border-primary"
                )}
                disabled={isDisabled}
                onClick={() => !isDisabled && handleDaySelect(day)}
              >
                {day}
              </Button>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <div className="flex items-center">
          <label htmlFor={uniqueId} className="text-sm font-medium">{label}</label>
          {required && <span className="text-red-500 ml-1">*</span>}
        </div>
      )}

      {description && (
        <p className="text-xs text-gray-500" id={`${uniqueId}-description`}>{description}</p>
      )}

      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id={uniqueId}
            name={fieldName}
            type="button"
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !date && "text-muted-foreground",
              error && "border-red-500"
            )}
            aria-describedby={description ? `${uniqueId}-description` : undefined}
            aria-required={required}
            aria-invalid={!!error}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, "EEEE, MMMM d, yyyy") : placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          {renderDateView()}
        </PopoverContent>
      </Popover>

      {error && (
        <p className="text-sm text-red-500" id={`${uniqueId}-error`}>{error}</p>
      )}

      {/* Hidden input to store the actual date value for form submission */}
      <input
        type="hidden"
        id={`${uniqueId}-hidden`}
        name={`${fieldName}-hidden`}
        value={date ? date.toISOString() : ''}
        aria-hidden="true"
      />
    </div>
  );
}
