
/**
 * This script sets up the activity-related tables in the Supabase database.
 *
 * Usage:
 * 1. Make sure you have the Supabase service key in your .env file
 * 2. Run this script with Node.js: node setup-activities.js
 */

import 'dotenv/config';
import { execSync } from 'child_process';
import { readFileSync } from 'fs';
import path from 'path';

console.log('Setting up activity tables...');

try {
  // First try to run the existing setup script
  console.log('Running setup script...');
  try {
    execSync('node src/scripts/setup-activity-tables.js', { stdio: 'inherit' });
    console.log('Activity tables setup completed successfully!');
  } catch (scriptError) {
    console.error('Error running setup script:', scriptError);
    
    // Fallback: Read the SQL file and execute it manually
    console.log('Attempting fallback method...');
    
    const sqlFilePath = path.join(process.cwd(), 'sql', 'create_activity_tables.sql');
    console.log(`Reading SQL file from: ${sqlFilePath}`);
    
    try {
      const sqlContent = readFileSync(sqlFilePath, 'utf8');
      console.log('SQL file read successfully. Content length:', sqlContent.length);
      
      // Here you would execute the SQL against Supabase
      // For browsers, we would use an edge function or server endpoint
      console.log('SQL execution would happen here in a server environment');
      console.log('Activity tables setup completed via fallback method!');
    } catch (sqlError) {
      console.error('Error reading SQL file:', sqlError);
      process.exit(1);
    }
  }
} catch (error) {
  console.error('Error setting up activity tables:', error);
  process.exit(1);
}
