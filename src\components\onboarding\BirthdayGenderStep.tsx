
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormMessage, FormControl } from "@/components/ui/form";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { OnboardingFormValues, Gender } from "@/types/onboarding";
import { YearFirstDatePicker } from "@/components/ui/year-first-date-picker";
import { useToast } from '@/hooks/use-toast';

interface BirthdayGenderStepProps {
  form: UseFormReturn<OnboardingFormValues>;
  previewUrl?: string | null;
  handleThumbnailClick?: () => void;
  fileInputRef?: React.RefObject<HTMLInputElement>;
  handleFileChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  user?: any;
  // Add this property to match what OnboardingModal is passing
  onSubmit: (stepData?: Partial<OnboardingFormValues>) => Promise<void>;
  initialValues: Partial<OnboardingFormValues>;
  isLoading: boolean;
}

export const BirthdayGenderStep = ({
  form,
  previewUrl,
  handleThumbnailClick,
  fileInputRef,
  handleFileChange,
  user,
  onSubmit,
  initialValues,
  isLoading
}: BirthdayGenderStepProps) => {
  const { toast } = useToast();
  const birthday = form.watch('birthday');

  const handleDateChange = (date: Date | undefined) => {
    form.setValue('birthday', date);
  };

  // No longer need handlePhotoChange as we've moved the photo upload to the first step

  return (
    <div className="space-y-8 max-w-md mx-auto">
      {/* Simplified header */}
      <div className="text-center">
        <h2 className="text-xl font-semibold text-primary-purple">Birthday & Gender</h2>
        <div className="inline-flex items-center mt-1 text-xs text-gray-500">
          <span className="bg-red-100 text-red-500 rounded-full w-4 h-4 inline-flex items-center justify-center mr-1.5">*</span>
          Required information
        </div>
      </div>

      {/* Birthday field with improved date picker */}
      <FormField
        control={form.control}
        name="birthday"
        render={({ field, fieldState }) => (
          <FormItem className="bg-white rounded-xl p-4 shadow-sm border border-gray-100">
            <FormControl>
              <YearFirstDatePicker
                value={field.value}
                onChange={handleDateChange}
                label="When's your birthday?"
                description="We use this to show your age to other users"
                placeholder="Select your birthday"
                minAge={13}
                maxAge={100}
                required={true}
                error={fieldState.error?.message}
                className="pt-1"
                id="birthday-picker"
                name="birthday"
              />
            </FormControl>
          </FormItem>
        )}
      />

      {/* Gender selection with modern design */}
      <FormField
        control={form.control}
        name="gender"
        render={({ field }) => (
          <FormItem className="space-y-3">
            <div className="mb-1">
              <FormLabel className="text-sm font-medium flex items-center">
                Gender
                <span className="text-red-500 ml-1">*</span>
              </FormLabel>
            </div>
            <RadioGroup
              onValueChange={field.onChange}
              defaultValue={field.value}
              className="grid grid-cols-2 gap-3"
            >
              {/* Man option */}
              <div>
                <RadioGroupItem
                  value="man"
                  id="gender-man"
                  className="peer sr-only"
                />
                <label
                  htmlFor="gender-man"
                  className="flex flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-3 hover:border-primary-purple/50 hover:bg-primary-purple/5 peer-data-[state=checked]:border-primary-purple peer-data-[state=checked]:bg-primary-purple/10 [&:has([data-state=checked])]:border-primary-purple [&:has([data-state=checked])]:bg-primary-purple/10 cursor-pointer transition-all h-[90px]"
                >
                  <div className="w-10 h-10 rounded-full bg-blue-50 flex items-center justify-center text-xl">👨</div>
                  <span className="mt-2 text-sm font-medium">Man</span>
                </label>
              </div>

              {/* Woman option */}
              <div>
                <RadioGroupItem
                  value="woman"
                  id="gender-woman"
                  className="peer sr-only"
                />
                <label
                  htmlFor="gender-woman"
                  className="flex flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-3 hover:border-primary-purple/50 hover:bg-primary-purple/5 peer-data-[state=checked]:border-primary-purple peer-data-[state=checked]:bg-primary-purple/10 [&:has([data-state=checked])]:border-primary-purple [&:has([data-state=checked])]:bg-primary-purple/10 cursor-pointer transition-all h-[90px]"
                >
                  <div className="w-10 h-10 rounded-full bg-pink-50 flex items-center justify-center text-xl">👩</div>
                  <span className="mt-2 text-sm font-medium">Woman</span>
                </label>
              </div>

              {/* Non-Binary option */}
              <div>
                <RadioGroupItem
                  value="non_binary"
                  id="gender-non-binary"
                  className="peer sr-only"
                />
                <label
                  htmlFor="gender-non-binary"
                  className="flex flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-3 hover:border-primary-purple/50 hover:bg-primary-purple/5 peer-data-[state=checked]:border-primary-purple peer-data-[state=checked]:bg-primary-purple/10 [&:has([data-state=checked])]:border-primary-purple [&:has([data-state=checked])]:bg-primary-purple/10 cursor-pointer transition-all h-[90px]"
                >
                  <div className="w-10 h-10 rounded-full bg-purple-50 flex items-center justify-center text-xl">⭐</div>
                  <span className="mt-2 text-sm font-medium">Non-Binary</span>
                </label>
              </div>

              {/* Prefer not to say option */}
              <div>
                <RadioGroupItem
                  value="prefer_not_to_say"
                  id="gender-prefer-not-to-say"
                  className="peer sr-only"
                />
                <label
                  htmlFor="gender-prefer-not-to-say"
                  className="flex flex-col items-center justify-center rounded-xl border border-gray-200 bg-white p-3 hover:border-primary-purple/50 hover:bg-primary-purple/5 peer-data-[state=checked]:border-primary-purple peer-data-[state=checked]:bg-primary-purple/10 [&:has([data-state=checked])]:border-primary-purple [&:has([data-state=checked])]:bg-primary-purple/10 cursor-pointer transition-all h-[90px]"
                >
                  <div className="w-10 h-10 rounded-full bg-gray-50 flex items-center justify-center text-xl">🤐</div>
                  <span className="mt-2 text-sm font-medium">Prefer not to say</span>
                </label>
              </div>
            </RadioGroup>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
