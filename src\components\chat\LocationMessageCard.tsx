
import React, { useRef, useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { MapPin, Clock, EyeOff } from 'lucide-react';
import { LocationMessage } from '@/types/location';
import mapboxgl from 'mapbox-gl';
import { useMapboxToken } from '@/hooks/use-mapbox-token';
import { useUserLocation } from '@/hooks/use-user-location';
import { useToast } from '@/hooks/use-toast';

// Separate utility functions to avoid circular dependencies
function formatDistance(distanceInKm: number): string {
  if (distanceInKm < 0.1) {
    return 'Very close';
  } else if (distanceInKm < 1) {
    return `${Math.round(distanceInKm * 1000)} meters away`;
  } else {
    return `${distanceInKm.toFixed(1)} km away`;
  }
}

function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Radius of the Earth in km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km
  
  return distance;
}

function formatLastSeen(date: string): string {
  const messageDate = new Date(date);
  const now = new Date();
  const diffInMinutes = Math.floor((now.getTime() - messageDate.getTime()) / (1000 * 60));

  if (diffInMinutes < 1) {
    return 'just now';
  } else if (diffInMinutes < 60) {
    return `in ${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''}`;
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60);
    return `in ${hours} hour${hours !== 1 ? 's' : ''}`;
  } else {
    const days = Math.floor(diffInMinutes / 1440);
    return `in ${days} day${days !== 1 ? 's' : ''}`;
  }
}

interface LocationMessageCardProps {
  message: LocationMessage;
  onExpire?: (messageId: string) => void;
  className?: string;
  showMap?: boolean;
  // Add these props for backward compatibility
  onDelete?: (messageId: string) => void;
}

export function LocationMessageCard({
  message,
  onExpire,
  className = "",
  showMap = true,
  onDelete
}: LocationMessageCardProps) {
  const mapContainer = useRef<HTMLDivElement | null>(null);
  const [map, setMap] = useState<mapboxgl.Map | null>(null);
  const { toast } = useToast();
  const { currentLocation } = useUserLocation();
  const mapboxToken = useMapboxToken();
  
  // Check if the message has expired
  const isExpired = message.is_expired || new Date(message.expiration) < new Date();
  
  // Format the expiration time
  const expirationTime = formatLastSeen(message.expiration);
  
  useEffect(() => {
    if (!mapboxToken?.token || !showMap || isExpired || !mapContainer.current) {
      return;
    }
    
    // Initialize map only once
    if (!map) {
      const newMap = new mapboxgl.Map({
        container: mapContainer.current,
        style: 'mapbox://styles/mapbox/streets-v12',
        center: [message.lng, message.lat],
        zoom: 14,
        accessToken: mapboxToken.token
      });
      
      // Add marker for the shared location
      new mapboxgl.Marker({ color: '#FF0000' })
        .setLngLat([message.lng, message.lat])
        .addTo(newMap);
        
      // Add marker for current user location if available
      if (currentLocation) {
        new mapboxgl.Marker({ color: '#0000FF' })
          .setLngLat([currentLocation.lng, currentLocation.lat])
          .addTo(newMap);
      }
      
      setMap(newMap);
    }
    
    return () => {
      if (map) {
        map.remove();
      }
    };
  }, [mapboxToken, message, showMap, isExpired, map, currentLocation]);
  
  const handleExpire = () => {
    const handler = onExpire || onDelete;
    if (handler) {
      handler(message.id);
      toast({
        title: "Location expired",
        description: "This location is no longer visible to others"
      });
    }
  };
  
  // Calculate distance if current location available
  const distance = currentLocation ? 
    formatDistance(
      calculateDistance(
        currentLocation.lat,
        currentLocation.lng,
        message.lat,
        message.lng
      )
    ) : null;
  
  return (
    <Card className={`overflow-hidden ${className} ${isExpired ? 'opacity-60' : ''}`}>
      {showMap && !isExpired && (
        <div 
          ref={mapContainer} 
          className="h-40 w-full"
        />
      )}
      
      <CardContent className="p-3">
        <div className="flex justify-between items-start mb-2">
          <div>
            <h4 className="font-medium flex items-center">
              <MapPin className="h-4 w-4 mr-1 text-red-500" />
              {message.label || "Shared Location"}
            </h4>
            <p className="text-sm text-muted-foreground">
              From {message.sender_name}
            </p>
          </div>
          
          {isExpired ? (
            <Badge variant="outline" className="text-xs">Expired</Badge>
          ) : (
            <Badge variant="outline" className="text-xs flex items-center">
              <Clock className="h-3 w-3 mr-1" />
              Expires {expirationTime}
            </Badge>
          )}
        </div>
        
        {distance && !isExpired && (
          <p className="text-sm mt-1">
            <span className="font-medium">{distance}</span> away from you
          </p>
        )}
        
        {!isExpired && (onExpire || onDelete) && (
          <Button 
            variant="ghost" 
            size="sm" 
            className="mt-2 w-full text-xs"
            onClick={handleExpire}
          >
            <EyeOff className="h-3 w-3 mr-1" />
            Stop sharing
          </Button>
        )}
      </CardContent>
    </Card>
  );
}
