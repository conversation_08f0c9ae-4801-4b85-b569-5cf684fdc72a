import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ActivityCreationValues } from '@/schemas/activity-creation-schema';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Loader2 } from 'lucide-react';
import { ActivityCategory } from '@/types/activity';

interface ActivityBasicInfoStepProps {
  form: UseFormReturn<ActivityCreationValues>;
  categories: ActivityCategory[];
  isLoading: boolean;
}

export function ActivityBasicInfoStep({ form, categories, isLoading }: ActivityBasicInfoStepProps) {
  // Handle category change - if dating category is selected, set max participants to 1
  const handleCategoryChange = (categoryId: string) => {
    form.setValue('category_id', categoryId);

    // Find the selected category
    const selectedCategory = categories.find(cat => cat.id === categoryId);

    // If it's a dating category, set max participants to 1
    if (selectedCategory?.name?.toLowerCase().includes('dating')) {
      form.setValue('max_participants', 1);
    }
  };
  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-xl font-medium text-center text-primary-purple">Basic Information</h3>
        <p className="text-sm text-muted-foreground text-center">
          <span className="text-red-500">*</span> Required information
        </p>
      </div>

      <FormField
        control={form.control}
        name="title"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Title <span className="text-red-500">*</span></FormLabel>
            <FormControl>
              <Input placeholder="Enter a catchy title" {...field} />
            </FormControl>
            <FormDescription>
              A short, descriptive title for your activity
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="description"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Description <span className="text-red-500">*</span></FormLabel>
            <FormControl>
              <Textarea
                placeholder="Describe your activity in detail"
                className="min-h-[120px]"
                {...field}
              />
            </FormControl>
            <FormDescription>
              Provide details about what participants can expect
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="category_id"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Category <span className="text-red-500">*</span></FormLabel>
            <Select
              onValueChange={handleCategoryChange}
              defaultValue={field.value?.toString() || undefined}
            >
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                {isLoading ? (
                  <div className="flex items-center justify-center p-2">
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Loading categories...
                  </div>
                ) : (
                  categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
            <FormDescription>
              Choose the category that best describes your activity
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
