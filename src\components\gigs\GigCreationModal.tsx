import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useCreateGig } from "@/hooks/use-gigs";
import { useServiceCategories } from "@/hooks/use-service-provider";

interface GigCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export function GigCreationModal({ isOpen, onClose }: GigCreationModalProps) {
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    short_description: '',
    price_starting: '',
    delivery_time_days: '7',
    category_id: '',
    gig_type: 'service' as const,
    skill_level: 'intermediate' as const,
    tags: '',
    requirements_from_buyer: ''
  });

  const createGig = useCreateGig();
  const { data: serviceCategories } = useServiceCategories();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    createGig.mutate({
      ...formData,
      price_starting: parseFloat(formData.price_starting),
      delivery_time_days: parseInt(formData.delivery_time_days),
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
      gallery_images: [],
      gallery_videos: [],
      packages: [],
      add_ons: [],
      faq: []
    }, {
      onSuccess: () => {
        onClose();
        setFormData({
          title: '',
          description: '',
          short_description: '',
          price_starting: '',
          delivery_time_days: '7',
          category_id: '',
          gig_type: 'service',
          skill_level: 'intermediate',
          tags: '',
          requirements_from_buyer: ''
        });
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Create New Gig</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="md:col-span-2">
              <Label htmlFor="title">Gig Title</Label>
              <Input
                id="title"
                value={formData.title}
                onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                placeholder="I will..."
                required
              />
            </div>

            <div>
              <Label htmlFor="category">Category</Label>
              <Select value={formData.category_id} onValueChange={(value) => setFormData({ ...formData, category_id: value })}>
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {serviceCategories?.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="price">Starting Price ($)</Label>
              <Input
                id="price"
                type="number"
                min="5"
                value={formData.price_starting}
                onChange={(e) => setFormData({ ...formData, price_starting: e.target.value })}
                placeholder="25"
                required
              />
            </div>

            <div>
              <Label htmlFor="delivery_time">Delivery Time (days)</Label>
              <Select value={formData.delivery_time_days} onValueChange={(value) => setFormData({ ...formData, delivery_time_days: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1">1 day</SelectItem>
                  <SelectItem value="3">3 days</SelectItem>
                  <SelectItem value="7">7 days</SelectItem>
                  <SelectItem value="14">14 days</SelectItem>
                  <SelectItem value="30">30 days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="gig_type">Gig Type</Label>
              <Select value={formData.gig_type} onValueChange={(value: any) => setFormData({ ...formData, gig_type: value })}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="service">Service</SelectItem>
                  <SelectItem value="digital">Digital</SelectItem>
                  <SelectItem value="consultation">Consultation</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="short_description">Short Description</Label>
            <Input
              id="short_description"
              value={formData.short_description}
              onChange={(e) => setFormData({ ...formData, short_description: e.target.value })}
              placeholder="Brief summary of your gig"
            />
          </div>

          <div>
            <Label htmlFor="description">Full Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Detailed description of what you'll deliver..."
              className="h-32"
              required
            />
          </div>

          <div>
            <Label htmlFor="tags">Tags (comma separated)</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
              placeholder="web design, logo, branding"
            />
          </div>

          <div>
            <Label htmlFor="requirements">Requirements from Buyer</Label>
            <Textarea
              id="requirements"
              value={formData.requirements_from_buyer}
              onChange={(e) => setFormData({ ...formData, requirements_from_buyer: e.target.value })}
              placeholder="What information do you need from the buyer?"
              className="h-24"
            />
          </div>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={createGig.isPending}>
              {createGig.isPending ? 'Creating...' : 'Create Gig'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
