# Location Data Normalization in BuddySurf

This document explains how location data is normalized and used consistently across the BuddySurf application.

## Location Data Formats

BuddySurf deals with several location data formats:

1. **PostgreSQL Point Format** - Used in the database
   ```
   (x, y)
   ```
   Where:
   - `x` represents longitude
   - `y` represents latitude

2. **Unified Location Format** - Used in the application
   ```typescript
   interface Location {
     x: number;         // longitude in point format
     y: number;         // latitude in point format
     latitude: number;  // latitude in geographic format
     longitude: number; // longitude in geographic format
   }
   ```

3. **Mapbox Format** - Used with mapping libraries
   ```typescript
   interface MapboxLocation {
     lng: number; // longitude
     lat: number; // latitude
   }
   ```

## Utility Functions

The following utility functions are available for converting between different location formats:

### Basic Conversion Functions

- `toPoint(location)`: Converts any location format to Point (x/y) format
- `toLatLng(location)`: Converts any location format to LatLng (latitude/longitude) format
- `toUnifiedLocation(location)`: Converts any location format to our unified Location format
- `toMapboxFormat(location)`: Converts a location to Mapbox format (lng/lat)
- `fromMapboxFormat(mapboxLocation)`: Converts from Mapbox format to our unified Location format

### PostgreSQL Specific Functions

- `locationToPostgresPoint(location)`: Converts a unified Location to a PostgreSQL point string
- `postgresPointToLocation(pointString)`: Converts a PostgreSQL point string to our unified Location format

## Usage in Hooks

The `useLocationFormatter` hook provides methods for normalizing location data:

```typescript
const { 
  normalizeUserLocation, 
  normalizeActivityLocation,
  convertToMapboxFormat,
  convertFromMapboxFormat,
  convertToPostgresPoint,
  convertFromPostgresPoint
} = useLocationFormatter();
```

### Example Usage

#### Converting User Location Data

```typescript
const normalizedUser = normalizeUserLocation(user);
// Now user.location has both x/y and latitude/longitude properties
```

#### Converting Activity Location Data

```typescript
const normalizedActivity = normalizeActivityLocation(activity);
// Now activity.location has both x/y and latitude/longitude properties
```

#### Working with Mapbox

```typescript
// Convert to Mapbox format for displaying on a map
const mapboxLocation = convertToMapboxFormat(activity.location);
map.setCenter([mapboxLocation.lng, mapboxLocation.lat]);

// Convert from Mapbox format after user selects a location
const unifiedLocation = convertFromMapboxFormat(mapboxLocation);
```

#### Working with PostgreSQL

```typescript
// Convert to PostgreSQL point string for database storage
const pointString = convertToPostgresPoint(activity.location);

// Convert from PostgreSQL point string after database retrieval
const location = convertFromPostgresPoint(pointString);
```

## Best Practices

1. Always use the unified Location format in your application code
2. Convert to specific formats only when needed (e.g., for database storage or map display)
3. Use the utility functions for all conversions to ensure consistency
4. When retrieving location data from the database, always normalize it using `toUnifiedLocation`
5. When storing location data in the database, always convert it using `locationToPostgresPoint`

## Implementation Details

The location normalization system is implemented in the following files:

- `src/types/activity.ts` - Defines the Location interface
- `src/utils/location-normalizer.ts` - Contains utility functions for location conversion
- `src/hooks/use-location-formatter.ts` - Provides hooks for normalizing location data
- `src/utils/format-point.ts` - Contains utility functions for PostgreSQL point format

## Troubleshooting

If you encounter issues with location data, check the following:

1. Make sure you're using the unified Location format in your application code
2. Verify that you're converting to the correct format for the specific use case
3. Check the console for warnings about invalid location formats
4. Ensure that location data is properly normalized when retrieved from the database
