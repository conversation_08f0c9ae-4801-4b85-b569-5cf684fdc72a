# Onboarding Integration Guide

This document provides instructions for completing the onboarding integration in BuddySurfNow.

## Overview

The onboarding process is a critical part of the user experience in BuddySurfNow. It ensures that all users have a complete profile with the required information before they can use the app. The onboarding process consists of several steps:

1. Basic Info (display_name, username, avatar_url)
2. Birthday & Gender
3. Default Location
4. Purpose Selection
5. Interests (optional)
6. Vibe Selection
7. Permissions (location, notifications)
8. Completion

## Required Fields

The following fields are required for a complete profile:

- `display_name`: User's display name
- `username`: Unique username
- `avatar_url`: Profile picture URL
- `default_location`: Default location as a PostgreSQL point
- `location_permission_granted`: <PERSON><PERSON>an indicating if location permission is granted
- `notifications_enabled`: <PERSON><PERSON><PERSON> indicating if notifications are enabled
- `vibes`: Array of selected vibes (at least one)

## Implementation Details

### Database Schema

The profiles table has been updated with all necessary columns for onboarding:

```sql
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  display_name TEXT,
  username TEXT UNIQUE,
  bio TEXT,
  avatar_url TEXT,
  birthday DATE,
  gender TEXT,
  purposes TEXT[] DEFAULT '{}',
  vibes TEXT[] DEFAULT '{}',
  gallery TEXT[] DEFAULT '{}',
  favorite_locations JSONB DEFAULT '[]',
  default_location POINT,
  location POINT,
  location_display TEXT,
  location_permission_granted BOOLEAN DEFAULT FALSE,
  notifications_enabled BOOLEAN DEFAULT FALSE,
  notifyactivities BOOLEAN DEFAULT TRUE,
  notifyfollows BOOLEAN DEFAULT TRUE,
  notifymessages BOOLEAN DEFAULT TRUE,
  is_verified BOOLEAN DEFAULT FALSE,
  is_admin BOOLEAN DEFAULT FALSE,
  is_banned BOOLEAN DEFAULT FALSE,
  is_suspended BOOLEAN DEFAULT FALSE,
  is_bot BOOLEAN DEFAULT FALSE,
  onboarding_completed BOOLEAN DEFAULT FALSE,
  interests TEXT[] DEFAULT '{}'
);
```

### RPC Functions

A new RPC function has been added to update the onboarding_completed flag:

```sql
CREATE OR REPLACE FUNCTION public.update_onboarding_completed(user_id UUID, completed BOOLEAN)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE public.profiles
  SET 
    onboarding_completed = completed,
    updated_at = NOW()
  WHERE id = user_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### RLS Policies

The RLS policies have been updated to use the more efficient `(SELECT auth.uid())` approach:

```sql
CREATE POLICY "Users can update their own profile" 
ON public.profiles
FOR UPDATE
USING (id = (SELECT auth.uid()))
WITH CHECK (id = (SELECT auth.uid()));
```

## Installation Instructions

To complete the onboarding integration, follow these steps:

1. Run the SQL updates:

```bash
node scripts/run-onboarding-updates.js
```

This script will:
- Update the profiles table with all necessary columns
- Create the update_onboarding_completed RPC function
- Update the RLS policies to use the more efficient approach

2. Test the onboarding flow:

- Create a new user account
- Verify that the onboarding modal appears automatically
- Complete all required steps
- Verify that the onboarding_completed flag is set to true in the database
- Verify that the user can access protected routes after completing onboarding

## Troubleshooting

If you encounter issues with the onboarding integration, check the following:

1. **Database Schema**: Verify that the profiles table has all the necessary columns.
2. **RPC Function**: Verify that the update_onboarding_completed function exists and is working correctly.
3. **RLS Policies**: Verify that the RLS policies are using the correct approach and allowing users to update their own profiles.
4. **Data Persistence**: Verify that data is being saved correctly between steps.
5. **Verification**: Verify that the onboarding_completed flag is being set correctly.

## Common Issues and Solutions

### Issue: Data not being saved between steps

**Solution**: Check the saveStepData function in OnboardingModal.tsx. Make sure it's handling all form fields correctly and saving them to the database.

### Issue: onboarding_completed flag not being set

**Solution**: Check the markOnboardingCompleted function in save-profile-data.ts. Make sure it's using multiple approaches to ensure the flag is set.

### Issue: RLS policies preventing updates

**Solution**: Test the RLS policies using the testRlsPolicy function. Make sure users have permission to update their own profiles.

### Issue: Onboarding modal not appearing for new users

**Solution**: Check the isProfileIncomplete function in OnboardingController.tsx. Make sure it's checking for all required fields.

## Conclusion

The onboarding integration is a critical part of the user experience in BuddySurfNow. By following these instructions, you can ensure that all users have a complete profile with the required information before they can use the app.
