
import { Button } from '@/components/ui/button';

interface MapCenterButtonProps {
  location: { longitude: number; latitude: number } | null;
  onCenter: () => void;
}

export function MapCenterButton({ location, onCenter }: MapCenterButtonProps) {
  if (!location) return null;
  
  return (
    <div className="absolute bottom-24 right-4 z-50">
      <Button
        variant="outline"
        size="sm"
        onClick={onCenter}
        className="bg-background/95"
      >
        Center on me
      </Button>
    </div>
  );
}
