
import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, MapPin } from 'lucide-react';
import { useDebounce } from '@/hooks/use-debounce';

export interface LocationSearchProps {
  onSelectLocation: (location: { name: string; coordinates: [number, number]; placeType?: string }) => void;
  placeholder?: string;
  className?: string;
  mapboxToken?: string;
}

export function LocationSearch({ 
  onSelectLocation, 
  placeholder = "Search locations...",
  className,
  mapboxToken
}: LocationSearchProps) {
  // Location search implementation
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [results, setResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  
  const debouncedSearch = useDebounce(async (term: string) => {
    if (!term || term.length < 2) {
      setResults([]);
      return;
    }
    
    try {
      setIsSearching(true);
      setError(null);
      
      // Use Mapbox Geocoding API to search for locations
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(term)}.json?access_token=${mapboxToken}&types=place,address,poi&limit=5`
      );
      
      if (!response.ok) {
        throw new Error('Failed to search locations');
      }
      
      const data = await response.json();
      setResults(data.features || []);
    } catch (err) {
      console.error('Location search error:', err);
      setError('Error searching locations. Please try again.');
      setResults([]);
    } finally {
      setIsSearching(false);
    }
  }, 500);
  
  useEffect(() => {
    debouncedSearch(searchTerm);
  }, [searchTerm, debouncedSearch]);
  
  const handleSelect = (item: any) => {
    onSelectLocation({
      name: item.place_name,
      coordinates: item.geometry.coordinates,
      placeType: item.place_type?.[0]
    });
    setResults([]);
    setSearchTerm(item.place_name);
  };
  
  return (
    <div className={`relative ${className}`}>
      <div className="relative">
        <Input
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full pr-8"
        />
        {isSearching && (
          <div className="absolute right-2 top-1/2 -translate-y-1/2">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        )}
      </div>
      
      {results.length > 0 && (
        <div className="absolute z-10 mt-1 w-full rounded-md border bg-background shadow-lg">
          <ScrollArea className="h-64">
            <div className="p-1">
              {results.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleSelect(item)}
                  className="flex w-full items-center space-x-2 rounded-sm px-2 py-2 text-left text-sm hover:bg-muted"
                >
                  <MapPin className="h-4 w-4 flex-shrink-0 text-muted-foreground" />
                  <span className="truncate">{item.place_name}</span>
                </button>
              ))}
            </div>
          </ScrollArea>
        </div>
      )}
      
      {error && <p className="mt-1 text-xs text-destructive">{error}</p>}
    </div>
  );
}
