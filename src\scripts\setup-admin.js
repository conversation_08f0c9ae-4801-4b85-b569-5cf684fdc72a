// This script sets up the admin user and initializes the admin conversation
// Run with: node src/scripts/setup-admin.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY; // Use service key for admin operations

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Admin email to set up
const ADMIN_EMAIL = '<EMAIL>';

async function setupAdmin() {
  try {
    console.log('Setting up admin user...');
    
    // Find the user by email
    const { data: users, error: userError } = await supabase
      .from('profiles')
      .select('id, email, is_admin')
      .eq('email', ADMIN_EMAIL);
    
    if (userError) {
      throw userError;
    }
    
    if (!users || users.length === 0) {
      console.log(`User with email ${ADMIN_EMAIL} not found. Please create the user first.`);
      return;
    }
    
    const user = users[0];
    console.log(`Found user: ${user.id}`);
    
    // Set user as admin if not already
    if (!user.is_admin) {
      console.log('Setting user as admin...');
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ is_admin: true })
        .eq('id', user.id);
      
      if (updateError) {
        throw updateError;
      }
      
      console.log('User set as admin successfully.');
    } else {
      console.log('User is already an admin.');
    }
    
    // Check if admin conversation exists
    console.log('Checking for admin conversation...');
    const { data: existingConversation, error: convError } = await supabase
      .from('chat_conversations')
      .select('*')
      .or('is_announcement_only.eq.true,is_admin_conversation.eq.true')
      .maybeSingle();
    
    if (convError) {
      throw convError;
    }
    
    let conversationId;
    
    if (existingConversation) {
      console.log(`Admin conversation already exists: ${existingConversation.id}`);
      conversationId = existingConversation.id;
    } else {
      console.log('Creating admin conversation...');
      const { data: newConversation, error: createError } = await supabase
        .from('chat_conversations')
        .insert({
          is_announcement_only: true,
          is_admin_conversation: true,
          is_group: true,
          last_message: 'Welcome to BuddySurf! This is the official announcement channel.',
          last_message_at: new Date().toISOString()
        })
        .select()
        .single();
      
      if (createError) {
        throw createError;
      }
      
      console.log(`Admin conversation created: ${newConversation.id}`);
      conversationId = newConversation.id;
    }
    
    // Add welcome messages if needed
    console.log('Checking for welcome messages...');
    const { data: messages, error: msgError } = await supabase
      .from('messages')
      .select('id')
      .eq('conversation_id', conversationId);
    
    if (msgError) {
      throw msgError;
    }
    
    if (!messages || messages.length === 0) {
      console.log('Adding welcome messages...');
      
      // Add first welcome message
      await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: user.id,
          recipient_id: user.id,
          content: 'Welcome to BuddySurf! 👋 This is the official announcement channel where you\'ll receive important updates and information. Stay tuned for exciting news and features!',
          is_read: false,
          is_admin_message: true,
          created_at: new Date().toISOString()
        });
      
      // Add second welcome message
      await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: user.id,
          recipient_id: user.id,
          content: 'Here you\'ll receive important announcements about new features, events, and updates. You can also find help and support by clicking the "Contact Support" button in the chat sidebar.',
          is_read: false,
          is_admin_message: true,
          created_at: new Date(Date.now() + 1000).toISOString() // 1 second later
        });
      
      console.log('Welcome messages added.');
    } else {
      console.log(`${messages.length} messages already exist in the conversation.`);
    }
    
    // Add all users as participants
    console.log('Adding all users as participants...');
    const { data: allUsers, error: allUsersError } = await supabase
      .from('profiles')
      .select('id');
    
    if (allUsersError) {
      throw allUsersError;
    }
    
    // Get existing participants
    const { data: existingParticipants, error: partError } = await supabase
      .from('chat_participants')
      .select('user_id')
      .eq('conversation_id', conversationId);
    
    if (partError) {
      throw partError;
    }
    
    // Create a set of existing participant user IDs for quick lookup
    const existingParticipantIds = new Set(existingParticipants.map(p => p.user_id));
    
    // Add users who are not already participants
    const newParticipants = allUsers
      .filter(user => !existingParticipantIds.has(user.id))
      .map(user => ({
        conversation_id: conversationId,
        user_id: user.id,
        joined_at: new Date().toISOString()
      }));
    
    if (newParticipants.length > 0) {
      console.log(`Adding ${newParticipants.length} new participants...`);
      const { error: insertError } = await supabase
        .from('chat_participants')
        .insert(newParticipants);
      
      if (insertError) {
        throw insertError;
      }
      
      console.log('New participants added successfully.');
    } else {
      console.log('All users are already participants.');
    }
    
    console.log('Admin setup completed successfully!');
  } catch (error) {
    console.error('Error setting up admin:', error);
  }
}

setupAdmin();
