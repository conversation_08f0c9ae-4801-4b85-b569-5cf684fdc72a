
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const logStep = (step: string, details?: any) => {
  const detailsStr = details ? ` - ${JSON.stringify(details)}` : '';
  console.log(`[CHECK-SUBSCRIPTION] ${step}${detailsStr}`);
};

serve(async (req) => {
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    logStep("Function started");

    // Initialize Supabase client with anon key for authentication
    const supabaseClient = createClient(
      Deno.env.get("SUPABASE_URL") ?? "",
      Deno.env.get("SUPABASE_ANON_KEY") ?? ""
    );

    // Authenticate the user from the request
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) throw new Error("No authorization header provided");
    
    const token = authHeader.replace("Bearer ", "");
    const { data: userData, error: userError } = await supabaseClient.auth.getUser(token);
    if (userError) throw new Error(`Authentication error: ${userError.message}`);
    
    const user = userData.user;
    if (!user) throw new Error("User not authenticated");
    logStep("User authenticated", { userId: user.id });

    // Check subscription status in the database
    const { data: subscriptionData, error: subscriptionError } = await supabaseClient
      .from("subscriptions")
      .select("*")
      .eq("user_id", user.id)
      .eq("status", "active")
      .maybeSingle();

    if (subscriptionError) throw new Error(`Error checking subscription: ${subscriptionError.message}`);
    
    const hasActiveSubscription = !!subscriptionData;
    
    // Get additional subscription details
    let additionalDetails = {};
    
    if (hasActiveSubscription) {
      // Check if subscription has expired based on expires_at field
      if (subscriptionData.expires_at) {
        const expiryDate = new Date(subscriptionData.expires_at);
        const now = new Date();
        if (expiryDate < now) {
          // Subscription has expired
          logStep("Subscription expired", { 
            expires_at: subscriptionData.expires_at,
            current_time: now.toISOString()
          });
          
          // Update the subscription status to inactive
          const { error: updateError } = await supabaseClient
            .from("subscriptions")
            .update({ status: "inactive" })
            .eq("id", subscriptionData.id);
            
          if (updateError) {
            logStep("Error updating expired subscription", { error: updateError.message });
          }
          
          return new Response(JSON.stringify({
            active: false,
            plan: null,
            subscription: null
          }), {
            headers: { ...corsHeaders, "Content-Type": "application/json" },
            status: 200,
          });
        }
      }
      
      // Calculate remaining days for subscription
      if (subscriptionData.expires_at) {
        const expiresAt = new Date(subscriptionData.expires_at);
        const today = new Date();
        const differenceInTime = expiresAt.getTime() - today.getTime();
        const daysRemaining = Math.ceil(differenceInTime / (1000 * 3600 * 24));
        
        additionalDetails = {
          ...additionalDetails,
          daysRemaining
        };
      }
      
      // Add subscription tier benefits info
      additionalDetails = {
        ...additionalDetails,
        benefits: {
          unlimitedActivities: true,
          priorityListing: subscriptionData.plan === 'monthly' || subscriptionData.plan === 'lifetime',
          advancedFilters: true,
          noAds: subscriptionData.plan === 'lifetime',
          premiumSupport: subscriptionData.plan === 'lifetime'
        }
      };
    }
    
    logStep("Subscription check complete", { 
      hasActiveSubscription,
      planDetails: hasActiveSubscription ? {
        plan: subscriptionData.plan,
        started_at: subscriptionData.started_at,
        expires_at: subscriptionData.expires_at,
        ...additionalDetails
      } : null
    });

    return new Response(JSON.stringify({
      active: hasActiveSubscription,
      plan: hasActiveSubscription ? subscriptionData.plan : null,
      subscription: subscriptionData || null,
      ...additionalDetails
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error: any) {
    logStep("ERROR", { message: error.message });
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 500,
    });
  }
});
