import React, { useState, useEffect } from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { UseFormReturn } from "react-hook-form";
import { OnboardingFormValues } from "@/types/onboarding";
import { MapPin, Navigation, CheckCircle } from "lucide-react";
import { EnhancedMapLocationPicker } from '@/components/map/EnhancedMapLocationPicker';
import { useMapboxToken } from '@/hooks/use-mapbox-token';
import { MapLocation } from '@/types/location';
import { useToast } from '@/hooks/use-toast';
import { Card, CardContent } from '@/components/ui/card';
import { LocationStepProps } from '@/types/onboarding-props';
export function DefaultLocationStep({
  onSubmit,
  initialValues,
  isLoading,
  form,
  userId,
  user
}: LocationStepProps) {
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | null>(null);
  const {
    token: mapboxToken,
    isLoading: mapboxIsLoading
  } = useMapboxToken();
  const {
    toast
  } = useToast();

  // Debug function to log form state
  const debugFormState = () => {
    if (!form) return;
    const values = form.getValues();
    const defaultLocation = values.defaultLocation;
    const errors = form.formState.errors;
    console.log("Form values:", values);
    console.log("Default location:", defaultLocation);
    console.log("Form errors:", errors);
    console.log("Form state:", {
      isDirty: form.formState.isDirty,
      isValid: form.formState.isValid,
      isSubmitting: form.formState.isSubmitting,
      isSubmitted: form.formState.isSubmitted,
      isValidating: form.formState.isValidating,
      isSubmitSuccessful: form.formState.isSubmitSuccessful
    });
  };

  // Only try to access form.watch if form is defined
  const defaultLocation = form?.watch?.('defaultLocation');

  // Check if we already have a default location in the form
  useEffect(() => {
    if (defaultLocation && !selectedLocation) {
      setSelectedLocation({
        lng: defaultLocation.x,
        lat: defaultLocation.y,
        x: defaultLocation.x,
        y: defaultLocation.y
      });
    }
  }, [defaultLocation, selectedLocation]);
  const handleLocationSelect = (location: MapLocation) => {
    // Ensure location has both lng/lat and x/y properties
    const normalizedLocation: MapLocation = {
      lng: location.lng,
      lat: location.lat,
      x: location.x || location.lng,
      y: location.y || location.lat,
      address: location.address
    };
    console.log("Location selected:", normalizedLocation);
    setSelectedLocation(normalizedLocation);

    // Update form values if form is defined
    if (form) {
      // Make sure we have valid numbers for x and y
      const locationData = {
        x: Number(normalizedLocation.x),
        y: Number(normalizedLocation.y)
      };

      // Set the values directly in the form WITHOUT validation
      form.setValue('defaultLocation', locationData, {
        shouldValidate: false,
        // Skip validation to ensure it works
        shouldDirty: true,
        shouldTouch: true
      });

      // Also set location permission granted
      form.setValue('locationPermissionGranted', true, {
        shouldValidate: false
      });

      // Store the location in localStorage as a backup
      try {
        localStorage.setItem('onboarding_location', JSON.stringify(locationData));
      } catch (e) {
        console.error("Failed to save location to localStorage", e);
      }
      toast({
        title: "Location Selected",
        description: "Your location has been selected. Click Save Location, then Continue to proceed.",
        variant: "default"
      });
    } else {
      toast({
        description: 'Location selected successfully.',
        variant: 'default'
      });
    }
  };
  const handleUseCurrentLocation = () => {
    if (navigator.geolocation) {
      toast({
        title: "Detecting location...",
        description: "Please allow location access when prompted."
      });
      navigator.geolocation.getCurrentPosition(async position => {
        try {
          const lng = position.coords.longitude;
          const lat = position.coords.latitude;

          // Get address for the location
          let address = '';
          if (mapboxToken) {
            try {
              const response = await fetch(`https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${mapboxToken}`);
              if (response.ok) {
                const data = await response.json();
                address = data.features?.[0]?.place_name || '';
              }
            } catch (error) {
              console.error('Error fetching address:', error);
            }
          }
          const location: MapLocation = {
            lng,
            lat,
            x: lng,
            y: lat,
            address
          };
          console.log("Current location detected:", location);
          setSelectedLocation(location);

          // Update form values if form is defined
          if (form) {
            // Make sure we have valid numbers for x and y
            const locationData = {
              x: Number(location.x),
              y: Number(location.y)
            };

            // Set the values directly in the form WITHOUT validation
            form.setValue('defaultLocation', locationData, {
              shouldValidate: false,
              // Skip validation to ensure it works
              shouldDirty: true,
              shouldTouch: true
            });

            // Also set location permission granted
            form.setValue('locationPermissionGranted', true, {
              shouldValidate: false
            });

            // Store the location in localStorage as a backup
            try {
              localStorage.setItem('onboarding_location', JSON.stringify(locationData));
            } catch (e) {
              console.error("Failed to save location to localStorage", e);
            }
            toast({
              title: "Location Detected",
              description: "Your location has been detected. Click Save Location, then Continue to proceed.",
              variant: "default"
            });
          } else {
            toast({
              description: 'Current location detected successfully.'
            });
          }

          // Force re-render of the map component
          setTimeout(() => {
            const mapContainer = document.querySelector('.map-container');
            if (mapContainer) {
              // Trigger a resize event to force the map to update
              window.dispatchEvent(new Event('resize'));
            }
          }, 100);
        } catch (error) {
          console.error('Error processing location:', error);
          toast({
            title: 'Location Error',
            description: 'Error processing your location. Please try again.',
            variant: 'destructive'
          });
        }
      }, error => {
        console.error('Error getting location:', error);
        toast({
          title: 'Location Error',
          description: 'Failed to get your current location. Please select manually.',
          variant: 'destructive'
        });
      }, {
        enableHighAccuracy: true,
        timeout: 15000,
        maximumAge: 0
      });
    } else {
      toast({
        title: 'Not Supported',
        description: 'Your browser does not support geolocation.',
        variant: 'destructive'
      });
    }
  };
  return <div className="space-y-6 max-w-md mx-auto" data-testid="location-step">
      {/* No emergency buttons - clean implementation */}

      <div className="text-center">
        <h2 className="text-xl font-semibold text-primary-purple">Set Your Location</h2>
        <div className="inline-flex items-center mt-1 text-xs text-gray-500">
          <span className="bg-red-100 text-red-500 rounded-full w-4 h-4 inline-flex items-center justify-center mr-1.5">*</span>
          Required information
        </div>
      </div>

      {form ? <FormField control={form.control} name="defaultLocation" render={({
      field
    }) => <FormItem>
              <div className="mb-2">
                <FormLabel className="text-sm font-medium flex items-center">
                  Your Location
                  <span className="text-red-500 ml-1">*</span>
                </FormLabel>
                <FormDescription className="text-xs text-gray-500">This is where you'll appear on the map to other users.  Allow BuddySurf to access your location to find nearby activities and connect with local buddies</FormDescription>
              </div>
              <FormControl>
                <div className="space-y-3">
                  <div className="h-[250px] rounded-xl overflow-hidden border border-gray-100 shadow-sm map-container">
                    {mapboxToken ? <EnhancedMapLocationPicker onLocationSelect={handleLocationSelect} mapboxToken={mapboxToken} defaultLocation={selectedLocation} key={selectedLocation ? `${selectedLocation.lng}-${selectedLocation.lat}` : 'default-map'} /> : <div className="flex items-center justify-center h-full bg-gray-50">
                        <p className="text-gray-400 text-sm">Loading map...</p>
                      </div>}
                  </div>

                  <div className="flex items-center justify-center gap-2">
                    <Button type="button" variant="default" size="sm" onClick={handleUseCurrentLocation} className="bg-primary-purple hover:bg-primary-deep-purple h-9 text-sm font-medium">
                      <Navigation className="h-3.5 w-3.5 mr-1.5" />
                      Use My Current Location
                    </Button>

                    {/* Debug button - only visible in development */}
                    {process.env.NODE_ENV === 'development' && <Button type="button" variant="outline" size="sm" onClick={() => {
              debugFormState();

              // Force validation
              if (form) {
                form.trigger('defaultLocation').then(isValid => {
                  console.log('Manual validation result:', isValid);

                  // Force update the form values if we have a selected location
                  if (selectedLocation && !isValid) {
                    form.setValue('defaultLocation', {
                      x: selectedLocation.x || selectedLocation.lng,
                      y: selectedLocation.y || selectedLocation.lat
                    }, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true
                    });
                    form.setValue('locationPermissionGranted', true, {
                      shouldValidate: true
                    });

                    // Check again
                    setTimeout(() => {
                      debugFormState();
                    }, 500);
                  }
                });
              }
            }} className="h-9 text-xs">
                        Fix Form
                      </Button>}
                  </div>

                  {selectedLocation ? <div className="bg-white rounded-xl border border-green-200 p-3 shadow-sm location-selected-indicator">
                      <div className="flex items-start">
                        <div className="bg-green-50 rounded-full p-1 mr-2 mt-0.5">
                          <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-green-700">Location selected</p>
                          {selectedLocation.address && <p className="text-xs text-gray-500 mt-0.5 truncate">
                              {selectedLocation.address}
                            </p>}
                          <p className="text-xs text-green-600 font-medium mt-1.5 animate-pulse">
                            Click Save Location, then Continue to App →
                          </p>
                        </div>
                      </div>
                    </div> : <div className="bg-amber-50 rounded-xl border border-amber-100 p-3 shadow-sm">
                      <div className="flex items-start">
                        <div className="bg-amber-100 rounded-full p-1 mr-2 mt-0.5">
                          <MapPin className="h-3.5 w-3.5 text-amber-500" />
                        </div>
                        <div>
                          <p className="text-sm font-medium text-amber-700">Please select a location</p>
                          <p className="text-xs text-amber-600 mt-0.5">
                            Either drag the pin on the map or use your current location
                          </p>
                        </div>
                      </div>
                    </div>}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>} /> :
    // Non-form version of the component
    <div className="space-y-3">
          <div className="mb-2">
            <p className="text-sm font-medium flex items-center">
              Your Location
              <span className="text-red-500 ml-1">*</span>
            </p>
            <p className="text-xs text-gray-500">
              This is where you'll appear on the map to other users
            </p>
          </div>

          <div className="h-[250px] rounded-xl overflow-hidden border border-gray-100 shadow-sm map-container">
            {mapboxToken ? <EnhancedMapLocationPicker onLocationSelect={handleLocationSelect} mapboxToken={mapboxToken} defaultLocation={selectedLocation} key={selectedLocation ? `${selectedLocation.lng}-${selectedLocation.lat}` : 'default-map'} /> : <div className="flex items-center justify-center h-full bg-gray-50">
                <p className="text-gray-400 text-sm">Loading map...</p>
              </div>}
          </div>

          <div className="flex items-center justify-center">
            <Button type="button" variant="default" size="sm" onClick={handleUseCurrentLocation} className="bg-primary-purple hover:bg-primary-deep-purple h-9 text-sm font-medium">
              <Navigation className="h-3.5 w-3.5 mr-1.5" />
              Use My Current Location
            </Button>
          </div>

          {selectedLocation ? <div className="bg-white rounded-xl border border-green-200 p-3 shadow-sm location-selected-indicator">
              <div className="flex items-start">
                <div className="bg-green-50 rounded-full p-1 mr-2 mt-0.5">
                  <CheckCircle className="h-3.5 w-3.5 text-green-500" />
                </div>
                <div>
                  <p className="text-sm font-medium text-green-700">Location selected</p>
                  {selectedLocation.address && <p className="text-xs text-gray-500 mt-0.5 truncate">
                      {selectedLocation.address}
                    </p>}
                  <p className="font-normal text-indigo-700">
                    Location saved successfully
                  </p>
                </div>
              </div>
            </div> : <div className="bg-amber-50 rounded-xl border border-amber-100 p-3 shadow-sm">
              <div className="flex items-start">
                <div className="bg-amber-100 rounded-full p-1 mr-2 mt-0.5">
                  <MapPin className="h-3.5 w-3.5 text-amber-500" />
                </div>
                <div>
                  <p className="text-sm font-medium text-amber-700">Please select a location</p>
                  <p className="text-xs text-amber-600 mt-0.5">
                    Either drag the pin on the map or use your current location
                  </p>
                </div>
              </div>
            </div>}
        </div>}

      {/* Save location button - always visible but only enabled when location is selected */}
      {form && <div className="flex justify-center mt-6">
          <Button type="button" variant="default" disabled={!selectedLocation} onClick={() => {
        console.log("Save location button clicked in DefaultLocationStep");

        // Save the location data
        const locationData = {
          x: Number(selectedLocation.x || selectedLocation.lng),
          y: Number(selectedLocation.y || selectedLocation.lat)
        };
        console.log("Setting location data:", locationData);

        // Set the values directly in the form without validation
        form.setValue('defaultLocation', locationData, {
          shouldValidate: false,
          shouldDirty: true,
          shouldTouch: true
        });

        // Set location permission granted
        form.setValue('locationPermissionGranted', true, {
          shouldValidate: false
        });

        // Store in localStorage as backup
        try {
          localStorage.setItem('onboarding_location', JSON.stringify(locationData));
          console.log("Saved location to localStorage");
        } catch (e) {
          console.error("Failed to save location to localStorage", e);
        }

        // Show success message
        toast({
          title: "Location Saved",
          description: "Your location has been saved. Click Continue to App below to proceed.",
          variant: "default"
        });

        // Debug form state after saving
        if (form) {
          const values = form.getValues();
          console.log("Form values after saving location:", values);
          console.log("Form state after saving location:", {
            isDirty: form.formState.isDirty,
            isValid: form.formState.isValid,
            isSubmitting: form.formState.isSubmitting,
            isSubmitted: form.formState.isSubmitted
          });
        }

        // Call the onSubmit prop to proceed to the next step
        if (onSubmit) {
          console.log("Calling onSubmit from DefaultLocationStep with data:", {
            defaultLocation: locationData,
            locationPermissionGranted: true
          });
          try {
            onSubmit({
              defaultLocation: locationData,
              locationPermissionGranted: true
            });
          } catch (error) {
            console.error("Error in onSubmit callback:", error);
            toast({
              title: "Error",
              description: "There was an error saving your location. Please try again.",
              variant: "destructive"
            });
          }
        } else {
          console.error("onSubmit function is not defined in DefaultLocationStep");
        }
      }} className={`text-white font-medium w-full sm:w-auto px-6 py-3 ${selectedLocation ? 'bg-green-600 hover:bg-green-700' : 'bg-gray-400'}`}>
            Save Location
          </Button>
        </div>}
    </div>;
}