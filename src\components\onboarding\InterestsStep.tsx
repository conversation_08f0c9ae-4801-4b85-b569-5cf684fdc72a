
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { OnboardingFormValues } from "@/types/onboarding";
import { Check, Heart, Star, MapPin, Music, Laptop, Theater, Brain, Sunset, PartyPopper } from "lucide-react";
import { cn } from "@/lib/utils";
import { Collapsible, CollapsibleTrigger, CollapsibleContent } from "@/components/ui/collapsible";
import { Button } from "@/components/ui/button";
import { InterestsStepProps } from "@/types/onboarding-props";

// Enhanced interest categories with icons and descriptions
const interestCategories = [
  { 
    id: 'sports', 
    name: 'Sports & Fitness',
    icon: <Star className="h-5 w-5" />,
    description: 'Athletic activities and exercise'
  },
  { 
    id: 'tech', 
    name: 'Technology',
    icon: <Laptop className="h-5 w-5" />,
    description: 'Gadgets, coding, and innovation'
  },
  { 
    id: 'art', 
    name: 'Art & Culture',
    icon: <Theater className="h-5 w-5" />,
    description: 'Creative expression and cultural experiences'
  },
  { 
    id: 'food', 
    name: 'Food & Dining',
    icon: <Heart className="h-5 w-5" />,
    description: 'Culinary experiences and restaurants'
  },
  { 
    id: 'travel', 
    name: 'Travel & Adventure',
    icon: <MapPin className="h-5 w-5" />,
    description: 'Exploring new places and adventures'
  },
  { 
    id: 'music', 
    name: 'Music & Entertainment',
    icon: <Music className="h-5 w-5" />,
    description: 'Concerts, shows, and performances'
  },
  { 
    id: 'learning', 
    name: 'Learning & Education',
    icon: <Brain className="h-5 w-5" />,
    description: 'Personal growth and knowledge'
  },
  { 
    id: 'nature', 
    name: 'Nature & Outdoors',
    icon: <Sunset className="h-5 w-5" />,
    description: 'Outdoor activities and environmental interests'
  },
  { 
    id: 'social', 
    name: 'Social Events',
    icon: <PartyPopper className="h-5 w-5" />,
    description: 'Gatherings and community activities'
  },
];

export function InterestsStep({ form, onSubmit, isLoading, initialValues }: InterestsStepProps) {
  const selectedVibes = form.watch('vibes') || [];
  
  const toggleInterest = (interestId: string) => {
    const currentVibes = [...selectedVibes];
    const index = currentVibes.indexOf(interestId);
    
    if (index === -1) {
      if (currentVibes.length < 5) {
        currentVibes.push(interestId);
      }
    } else {
      currentVibes.splice(index, 1);
    }
    
    form.setValue('vibes', currentVibes);
  };
  
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold">Your interests</h2>
        <p className="text-sm text-muted-foreground mt-1">
          Select topics that interest you
        </p>
      </div>
      
      <FormField
        control={form.control}
        name="vibes"
        render={() => (
          <FormItem>
            <FormLabel>Select up to 5 interests</FormLabel>
            <FormControl>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-2">
                {interestCategories.map(category => (
                  <div
                    key={category.id}
                    className={`
                      flex items-center justify-between p-3 rounded-md border cursor-pointer
                      transition-all duration-300 hover:shadow-md
                      ${selectedVibes.includes(category.id) ? 
                        'border-primary-purple bg-primary-purple/10 shadow-sm' : 
                        'border-gray-200 hover:border-gray-300'}
                    `}
                    onClick={() => toggleInterest(category.id)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={`
                        p-2 rounded-full
                        ${selectedVibes.includes(category.id) ? 
                          'bg-primary-purple/20 text-primary-purple' : 
                          'bg-gray-100 text-gray-500'}
                      `}>
                        {category.icon}
                      </div>
                      <div>
                        <div className="font-medium">{category.name}</div>
                        <div className="text-xs text-muted-foreground">{category.description}</div>
                      </div>
                    </div>
                    {selectedVibes.includes(category.id) && (
                      <Check className="w-5 h-5 text-primary-purple" />
                    )}
                  </div>
                ))}
              </div>
            </FormControl>
            <div className="mt-4 p-2 bg-primary-purple/5 rounded-lg flex items-center justify-between">
              <span className="text-sm">Selected interests</span>
              <span className="font-medium text-primary-purple px-2 py-1 bg-white rounded-md border border-primary-purple/20">
                {selectedVibes.length}/5
              </span>
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
