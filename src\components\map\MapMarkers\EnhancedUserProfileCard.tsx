
import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, <PERSON>Header } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { BadgeCheck, X, MessageSquare, UserPlus, Clock, MapPin } from 'lucide-react';
import { UnifiedMapUser } from '@/types/map';
import { formatDistance, formatLastSeen, getActivityStatus } from '@/utils/enhanced-distance';

interface EnhancedUserProfileCardProps {
  user: UnifiedMapUser;
  onClose?: () => void;
  onConnect?: () => void;
  onMessage?: () => void;
  myLocation?: { x: number; y: number };
}

export function EnhancedUserProfileCard({
  user,
  onClose,
  onConnect,
  onMessage,
  myLocation
}: EnhancedUserProfileCardProps) {
  // Extract user information
  const displayName = user.display_name || user.username || `User ${user.user_id?.substring?.(0, 5)}`;
  const avatarUrl = user.avatar_url;
  const bio = user.bio || 'No bio available';
  const isVerified = user.is_verified === true;
  
  // Calculate distance if we have both locations
  const distance = typeof user.distance === 'number' ? user.distance : 
    (user.location && myLocation ? 
      calculateDistance(
        user.location.y, user.location.x, 
        myLocation.y, myLocation.x
      ) : undefined);
  
  // Get activity status
  const activityStatus = user.updated_at ? getActivityStatus(new Date(user.updated_at)) : null;
  const isOnline = user.is_online || (activityStatus?.status === 'online');
  
  // Generate initials from display name
  const getInitials = () => {
    return displayName
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };
  
  // Generate a consistent profile color based on user ID
  const getProfileColor = () => {
    let hash = 0;
    for (let i = 0; i < user.user_id.length; i++) {
      hash = user.user_id.charCodeAt(i) + ((hash << 5) - hash);
    }
    const hue = Math.abs(hash) % 360;
    return `hsl(${hue}, 70%, 60%)`;
  };
  
  return (
    <Card className="w-full max-w-sm bg-background/95 backdrop-blur-sm shadow-lg overflow-hidden animation-fade-in">
      <CardHeader className="relative p-0">
        {/* Profile background - could be a gradient or pattern */}
        <div 
          className="h-24 bg-gradient-to-r from-blue-100 to-indigo-100" 
          style={{ backgroundColor: getProfileColor() + '30' }}
        />
        
        {/* Avatar - positioned to overlap the background */}
        <div className="absolute -bottom-10 left-4">
          <div className="relative">
            <Avatar className="h-20 w-20 border-4 border-background">
              <AvatarImage src={avatarUrl} />
              <AvatarFallback style={{ backgroundColor: getProfileColor() }}>
                {getInitials()}
              </AvatarFallback>
            </Avatar>
            {isOnline && (
              <span className="absolute bottom-1 right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-background animate-pulse"></span>
            )}
          </div>
        </div>
        
        {/* Close button */}
        <Button 
          variant="ghost" 
          size="icon" 
          className="absolute top-2 right-2 h-7 w-7 rounded-full bg-background/50 hover:bg-background/80"
          onClick={onClose}
        >
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>
      
      <CardContent className="pt-12 pb-4">
        <div className="flex items-center gap-2 mb-1">
          <h3 className="font-medium text-lg">{displayName}</h3>
          {isVerified && (
            <BadgeCheck className="h-5 w-5 text-blue-500" />
          )}
        </div>
        
        <div className="flex flex-wrap gap-2 mb-3">
          {distance !== undefined && (
            <div className="flex items-center gap-1 bg-muted/50 px-2 py-1 rounded-full text-xs">
              <MapPin className="h-3 w-3" />
              <span>{formatDistance(distance)}</span>
            </div>
          )}
          
          {user.updated_at && (
            <div className={`flex items-center gap-1 px-2 py-1 rounded-full text-xs ${
              isOnline ? 'bg-green-100 text-green-800' : 'bg-muted/50'
            }`}>
              <Clock className="h-3 w-3" />
              <span>{isOnline ? 'Online now' : formatLastSeen(user.updated_at)}</span>
            </div>
          )}
        </div>
        
        <div className="bg-muted/30 p-3 rounded-lg">
          <p className="text-sm whitespace-pre-wrap line-clamp-4">
            {bio}
          </p>
        </div>
      </CardContent>
      
      <CardFooter className="flex gap-2 pt-0">
        <Button variant="outline" className="flex-1" onClick={onConnect}>
          <UserPlus className="mr-2 h-4 w-4" />
          Connect
        </Button>
        <Button className="flex-1" onClick={onMessage}>
          <MessageSquare className="mr-2 h-4 w-4" />
          Message
        </Button>
      </CardFooter>
    </Card>
  );
}

// Helper function to calculate distance
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  // Convert degrees to radians
  const toRadian = (angle: number) => (Math.PI / 180) * angle;
  
  const dLat = toRadian(lat2 - lat1);
  const dLon = toRadian(lon2 - lon1);
  
  // Haversine formula
  const a = 
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRadian(lat1)) * Math.cos(toRadian(lat2)) * 
    Math.sin(dLon / 2) * Math.sin(dLon / 2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  
  // Earth's radius in miles
  const radius = 3958.8;
  
  // Calculate the distance
  return radius * c;
}
