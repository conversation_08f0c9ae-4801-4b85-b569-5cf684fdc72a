import React from 'react';
import { PermissionsStepProps } from "@/types/onboarding-props";
import { Button } from "@/components/ui/button";
import { MapPin, Bell } from "lucide-react";

export function PermissionsStep({ onSubmit, isLoading, initialValues }: PermissionsStepProps) {
  const handleLocationPermission = async () => {
    try {
      // Request location permission
      const position = await new Promise<GeolocationPosition>((resolve, reject) => {
        navigator.geolocation.getCurrentPosition(resolve, reject, {
          enableHighAccuracy: true,
          timeout: 5000,
          maximumAge: 0
        });
      });
      
      // If successful, save the permission status and location
      const locationData = {
        locationPermissionGranted: true,
        defaultLocation: {
          x: position.coords.longitude,
          y: position.coords.latitude
        }
      };
      
      // Submit the data and move to next step
      await onSubmit(locationData);
    } catch (error) {
      console.error("Error getting location:", error);
      // If user denies permission or there's an error, still move to next step
      // but mark permission as not granted
      await onSubmit({ locationPermissionGranted: false });
    }
  };
  
  const handleNotificationPermission = async () => {
    try {
      // Request notification permission
      const permission = await Notification.requestPermission();
      
      // Submit the data and move to next step
      await onSubmit({ 
        notificationsEnabled: permission === 'granted'
      });
    } catch (error) {
      console.error("Error requesting notification permission:", error);
      // If there's an error, still move to next step but mark as not enabled
      await onSubmit({ notificationsEnabled: false });
    }
  };
  
  const skipPermissions = async () => {
    // User skips permissions, mark both as not granted/enabled
    await onSubmit({
      locationPermissionGranted: false,
      notificationsEnabled: false
    });
  };
  
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold">Enhance your experience</h2>
        <p className="text-sm text-muted-foreground mt-1">
          Enable these permissions for the best experience
        </p>
      </div>
      
      <div className="space-y-4">
        <div className="border rounded-lg p-4 bg-muted/30">
          <div className="flex items-start gap-3">
            <div className="bg-primary-purple/10 p-2 rounded-full">
              <MapPin className="h-6 w-6 text-primary-purple" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium">Location Services</h3>
              <p className="text-sm text-muted-foreground mb-3">
                Allow access to your location to find nearby activities and connect with people in your area.
              </p>
              <Button 
                onClick={handleLocationPermission}
                disabled={isLoading}
                className="w-full"
              >
                Enable Location
              </Button>
            </div>
          </div>
        </div>
        
        <div className="border rounded-lg p-4 bg-muted/30">
          <div className="flex items-start gap-3">
            <div className="bg-primary-purple/10 p-2 rounded-full">
              <Bell className="h-6 w-6 text-primary-purple" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium">Notifications</h3>
              <p className="text-sm text-muted-foreground mb-3">
                Get notified about new messages, nearby activities, and updates from people you follow.
              </p>
              <Button 
                onClick={handleNotificationPermission}
                disabled={isLoading}
                className="w-full"
              >
                Enable Notifications
              </Button>
            </div>
          </div>
        </div>
      </div>
      
      <div className="pt-4 text-center">
        <Button
          variant="ghost"
          onClick={skipPermissions}
          disabled={isLoading}
        >
          Skip for now
        </Button>
        <p className="text-xs text-muted-foreground mt-2">
          You can always enable these later in settings
        </p>
      </div>
    </div>
  );
}
