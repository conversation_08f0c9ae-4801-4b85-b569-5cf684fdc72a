
import { useEffect, useState, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

// Define types for user presence
export interface UserPresence {
  user_id: string;
  location?: { x: number; y: number };
  online_at: string;
  avatar_url?: string;
  display_name?: string;
  username?: string;
  bio?: string;
  status?: 'online' | 'away' | 'offline';
}

export interface PresenceState {
  [key: string]: UserPresence[];
}

/**
 * A hook that manages all real-time connections including presence
 * and provides centralized access to real-time data
 */
export function useRealtimeManager() {
  const [users, setUsers] = useState<UserPresence[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [lastError, setLastError] = useState<string | null>(null);
  const channelRef = useRef<any>(null);
  const { toast } = useToast();
  
  // Initialize the connection
  useEffect(() => {
    const channel = supabase.channel('map-presence', {
      config: { 
        presence: { key: 'user-presence' },
        broadcast: { self: true }
      }
    });
    
    // Subscribe to presence events
    channel
      .on('presence', { event: 'sync' }, () => {
        const state = channel.presenceState();
        const presenceUsers: UserPresence[] = [];
        
        Object.values(state).forEach((userStates: any) => {
          userStates.forEach((userState: any) => {
            presenceUsers.push(userState);
          });
        });
        
        setUsers(presenceUsers);
        setIsConnected(true);
      })
      .on('presence', { event: 'join' }, ({ key, newPresences }: any) => {
        console.log('User joined:', key, newPresences);
        
        // Only show toast for other users joining, not ourselves
        if (newPresences.length > 0 && key !== 'user-presence') {
          const user = newPresences[0];
          toast({
            title: "User online",
            description: `${user.display_name || user.username || 'Someone'} is now online`,
          });
        }
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }: any) => {
        console.log('User left:', key, leftPresences);
      });
    
    // Subscribe to database changes
    channel
      .on(
        'postgres_changes',
        { event: '*', schema: 'public', table: 'user_locations' },
        (payload) => {
          console.log('Location update:', payload);
          
          // Update the users array with the new location data
          if (payload.eventType === 'INSERT' || payload.eventType === 'UPDATE') {
            const locationData = payload.new;
            
            setUsers(prev => {
              // Find if user exists in our current list
              const existingUserIndex = prev.findIndex(u => u.user_id === locationData.user_id);
              
              if (existingUserIndex >= 0) {
                // Update existing user
                const updatedUsers = [...prev];
                updatedUsers[existingUserIndex] = {
                  ...updatedUsers[existingUserIndex],
                  location: {
                    x: typeof locationData.location === 'object' ? Number(locationData.location.x || 0) : 0,
                    y: typeof locationData.location === 'object' ? Number(locationData.location.y || 0) : 0
                  },
                  online_at: new Date().toISOString()
                };
                return updatedUsers;
              } else {
                // Add new user
                return [...prev, {
                  user_id: locationData.user_id,
                  location: {
                    x: typeof locationData.location === 'object' ? Number(locationData.location.x || 0) : 0,
                    y: typeof locationData.location === 'object' ? Number(locationData.location.y || 0) : 0
                  },
                  online_at: new Date().toISOString(),
                  bio: locationData.bio
                }];
              }
            });
          }
          
          if (payload.eventType === 'DELETE') {
            const userId = payload.old.user_id;
            setUsers(prev => prev.filter(u => u.user_id !== userId));
          }
        }
      );

    // Subscribe to the channel
    channel.subscribe(async (status) => {
      if (status === 'SUBSCRIBED') {
        console.log('Successfully subscribed to real-time channel!');
        setIsConnected(true);
        
        // Get the current user and track their presence
        const { data } = await supabase.auth.getSession();
        if (data.session?.user) {
          const user = data.session.user;
          
          // Get user profile data
          const { data: profileData } = await supabase
            .from('profiles')
            .select('username, display_name, avatar_url, bio')
            .eq('id', user.id)
            .single();
            
          // Track the user's presence
          channel.track({
            user_id: user.id,
            online_at: new Date().toISOString(),
            status: 'online',
            avatar_url: profileData?.avatar_url,
            display_name: profileData?.display_name,
            username: profileData?.username,
            bio: profileData?.bio
          });
        }
      } else if (status === 'CHANNEL_ERROR') {
        console.error('Failed to connect to real-time channel');
        setLastError('Failed to connect to real-time channel');
        setIsConnected(false);
      }
    });
    
    // Store the channel reference
    channelRef.current = channel;
    
    // Cleanup function
    return () => {
      channel.unsubscribe();
    };
  }, [toast]);
  
  // Function to update user location
  const updateUserLocation = async (location: { x: number; y: number }) => {
    const { data: sessionData } = await supabase.auth.getSession();
    if (!sessionData.session) {
      setLastError('User not authenticated');
      return false;
    }
    
    const userId = sessionData.session.user.id;
    
    try {
      // Update user location in the database
      const { error } = await supabase
        .from('user_locations')
        .upsert({ 
          user_id: userId,
          location,
          updated_at: new Date().toISOString()
        });
        
      if (error) {
        console.error('Error updating location:', error);
        setLastError(error.message);
        return false;
      }
      
      // Update presence state
      if (channelRef.current) {
        const { error: presenceError } = await channelRef.current.track({
          user_id: userId,
          location,
          online_at: new Date().toISOString(),
          status: 'online'
        });
        
        if (presenceError) {
          console.error('Error updating presence:', presenceError);
          setLastError(presenceError.message);
          return false;
        }
      }
      
      return true;
    } catch (error) {
      console.error('Unexpected error updating location:', error);
      setLastError('Unexpected error updating location');
      return false;
    }
  };
  
  // Calculate distances between users using Haversine formula
  const calculateDistance = (
    location1: { x: number; y: number } | undefined, 
    location2: { x: number; y: number } | undefined
  ): number | null => {
    if (!location1 || !location2) return null;
    
    // Haversine formula to calculate distance between two points on Earth
    const toRadians = (deg: number) => deg * Math.PI / 180;
    const R = 3958.8; // Earth radius in miles
    
    const dLat = toRadians(location2.y - location1.y);
    const dLng = toRadians(location2.x - location1.x);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(toRadians(location1.y)) * Math.cos(toRadians(location2.y)) * 
      Math.sin(dLng / 2) * Math.sin(dLng / 2);
      
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    const distance = R * c;
    
    return distance;
  };
  
  return { 
    users, 
    isConnected, 
    lastError,
    updateUserLocation,
    calculateDistance
  };
}
