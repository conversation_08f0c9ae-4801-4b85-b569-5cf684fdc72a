
import React from 'react';

/**
 * Hook to format user bios with proper line breaks and emoji support
 * @param bio The raw bio text to format
 * @returns Formatted bio as React elements
 */
export function useFormattedBio(bio: string | null | undefined) {
  return React.useMemo(() => {
    if (!bio) return null;
    
    // Split the text by line breaks
    return bio.split('\n').map((line, i) => (
      <React.Fragment key={i}>
        {formatEmojis(line)}
        {i < bio.split('\n').length - 1 && <br />}
      </React.Fragment>
    ));
  }, [bio]);
}

/**
 * Replaces common emoji shortcodes with actual emoji characters
 * @param text Text containing emoji shortcodes
 * @returns Text with emoji shortcodes replaced with emoji characters
 */
function formatEmojis(text: string): string {
  const emojiMap: Record<string, string> = {
    ':)': '😊',
    ':D': '😁',
    ':(': '😔',
    ':P': '😛',
    ';)': '😉',
    '<3': '❤️',
    ':heart:': '❤️',
    ':thumbsup:': '👍',
    ':fire:': '🔥',
    ':+1:': '👍',
    ':wave:': '👋',
    ':check:': '✅',
    ':star:': '⭐',
    ':sun:': '☀️',
    ':moon:': '🌙',
    ':coffee:': '☕',
    ':music:': '🎵',
    ':book:': '📚',
  };
  
  return text.replace(/:\)|:D|:\(|:P|;\)|<3|:\w+:/g, match => emojiMap[match] || match);
}

/**
 * Formats the last seen timestamp into a human-readable format
 * @param timestamp ISO string timestamp of when the user was last seen
 * @returns Formatted last seen string
 */
export function useFormattedLastSeen(timestamp: string | null | undefined) {
  return React.useMemo(() => {
    if (!timestamp) return "Unknown";
    
    try {
      const date = new Date(timestamp);
      const now = new Date();
      const diffMinutes = (now.getTime() - date.getTime()) / (1000 * 60);
      
      if (diffMinutes < 1) {
        return "Just now";
      } else if (diffMinutes < 60) {
        const mins = Math.floor(diffMinutes);
        return `${mins} ${mins === 1 ? 'minute' : 'minutes'} ago`;
      } else if (diffMinutes < 24 * 60) {
        const hours = Math.floor(diffMinutes / 60);
        return `${hours} ${hours === 1 ? 'hour' : 'hours'} ago`;
      } else if (diffMinutes < 48 * 60) {
        return "Yesterday";
      } else {
        // Format as MM/DD/YYYY for dates older than 2 days
        const month = date.getMonth() + 1;
        const day = date.getDate();
        const year = date.getFullYear();
        return `${month}/${day}/${year}`;
      }
    } catch (error) {
      console.error("Error formatting timestamp:", error);
      return "Unknown";
    }
  }, [timestamp]);
}
