
import { Location, CoordinateLocation } from '@/types/activity';

/**
 * Normalize a location object to ensure it has both x,y and latitude,longitude formats
 */
export function toUnifiedLocation(location: any): Location {
  if (!location) {
    return { x: 0, y: 0, latitude: 0, longitude: 0 };
  }

  // Handle string-formatted location (e.g. from JSON)
  if (typeof location === 'string') {
    try {
      const parsed = JSON.parse(location);
      return toUnifiedLocation(parsed);
    } catch (e) {
      console.error('Failed to parse location string:', e);
      return { x: 0, y: 0, latitude: 0, longitude: 0 };
    }
  }

  // Handle PostgreSQL point format
  if (location && typeof location === 'object') {
    // Extract coordinates depending on the format
    const lat = location.y !== undefined ? location.y :
                (location.latitude !== undefined ? location.latitude :
                (location.lat !== undefined ? location.lat : 0));

    const lng = location.x !== undefined ? location.x :
               (location.longitude !== undefined ? location.longitude :
               (location.lng !== undefined ? location.lng : 0));

    return {
      x: lng,
      y: lat,
      latitude: lat,
      longitude: lng
    };
  }

  return { x: 0, y: 0, latitude: 0, longitude: 0 };
}

/**
 * Format location to Mapbox format [lng, lat]
 */
export function toMapboxFormat(location: Location): [number, number] {
  return [location.longitude || location.x, location.latitude || location.y];
}

/**
 * Convert Mapbox coordinates to Location object
 */
export function fromMapboxFormat(coords: { lng: number; lat: number }): Location {
  return {
    x: coords.lng,
    y: coords.lat,
    longitude: coords.lng,
    latitude: coords.lat
  };
}

/**
 * Convert a Location object to PostgreSQL point format
 */
export function locationToPostgresPoint(location: Location): any {
  // Return the format expected by PostgreSQL for point type
  return {
    x: location.longitude || location.x,
    y: location.latitude || location.y
  };
}

/**
 * Convert a PostgreSQL point to a Location object
 */
export function postgresPointToLocation(point: any): Location {
  if (!point) {
    return { x: 0, y: 0, latitude: 0, longitude: 0 };
  }

  return {
    x: point.x || 0,
    y: point.y || 0,
    longitude: point.x || 0,
    latitude: point.y || 0
  };
}

/**
 * Normalize user location data
 */
export function normalizeUserLocation(location: any): Location {
  return toUnifiedLocation(location);
}
