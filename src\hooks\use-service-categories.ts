
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface ServiceCategory {
  id: string;
  name: string;
  icon?: string;
  type?: string;
  description?: string;
  keywords?: string[];
  average_price_range?: string;
  popularity_score?: number;
  is_trending?: boolean;
  parent_category_id?: string;
  sort_order?: number;
  gig_count?: number;
}

export interface CategoryGroup {
  name: string;
  categories: ServiceCategory[];
}

export function useServiceCategories() {
  return useQuery({
    queryKey: ['service-categories'],
    queryFn: async (): Promise<ServiceCategory[]> => {
      const { data, error } = await supabase
        .from('categories')
        .select(`
          *,
          metadata:category_metadata(
            description,
            keywords,
            average_price_range,
            popularity_score,
            is_trending,
            parent_category_id,
            sort_order
          ),
          gig_count:gigs(count)
        `)
        .eq('type', 'service')
        .order('name');

      if (error) throw error;

      return (data || []).map(category => ({
        id: category.id,
        name: category.name,
        icon: category.icon,
        type: category.type,
        description: category.metadata?.[0]?.description,
        keywords: category.metadata?.[0]?.keywords,
        average_price_range: category.metadata?.[0]?.average_price_range,
        popularity_score: category.metadata?.[0]?.popularity_score || 0,
        is_trending: category.metadata?.[0]?.is_trending || false,
        parent_category_id: category.metadata?.[0]?.parent_category_id,
        sort_order: category.metadata?.[0]?.sort_order || 0,
        gig_count: category.gig_count?.[0]?.count || 0
      }));
    }
  });
}

export function useServiceCategoriesGrouped() {
  return useQuery({
    queryKey: ['service-categories-grouped'],
    queryFn: async (): Promise<CategoryGroup[]> => {
      const { data, error } = await supabase
        .from('categories')
        .select(`
          *,
          metadata:category_metadata(
            description,
            keywords,
            average_price_range,
            popularity_score,
            is_trending,
            parent_category_id,
            sort_order
          ),
          gig_count:gigs(count)
        `)
        .eq('type', 'service')
        .order('name');

      if (error) throw error;

      const categories = (data || []).map(category => ({
        id: category.id,
        name: category.name,
        icon: category.icon,
        type: category.type,
        description: category.metadata?.[0]?.description,
        keywords: category.metadata?.[0]?.keywords,
        average_price_range: category.metadata?.[0]?.average_price_range,
        popularity_score: category.metadata?.[0]?.popularity_score || 0,
        is_trending: category.metadata?.[0]?.is_trending || false,
        parent_category_id: category.metadata?.[0]?.parent_category_id,
        sort_order: category.metadata?.[0]?.sort_order || 0,
        gig_count: category.gig_count?.[0]?.count || 0
      }));

      // Group categories by common themes
      const groups: CategoryGroup[] = [
        {
          name: 'Technology & Programming',
          categories: categories.filter(cat =>
            ['Web Development', 'Mobile App Development', 'Software Development',
             'Database Administration', 'DevOps & Cloud', 'Cybersecurity',
             'Data Science & Analytics', 'AI & Machine Learning', 'Blockchain Development',
             'Game Development'].includes(cat.name)
          )
        },
        {
          name: 'Design & Creative',
          categories: categories.filter(cat =>
            ['Graphic Design', 'UI/UX Design', 'Logo & Brand Design', 'Web Design',
             'Video Editing', 'Animation', 'Photography', 'Illustration',
             '3D Modeling', 'Interior Design'].includes(cat.name)
          )
        },
        {
          name: 'Writing & Content',
          categories: categories.filter(cat =>
            ['Content Writing', 'Copywriting', 'Technical Writing', 'Blog Writing',
             'SEO Writing', 'Social Media Content', 'Translation',
             'Proofreading & Editing', 'Resume Writing', 'Creative Writing'].includes(cat.name)
          )
        },
        {
          name: 'Marketing & Sales',
          categories: categories.filter(cat =>
            ['Digital Marketing', 'Social Media Marketing', 'SEO Services', 'PPC Advertising',
             'Email Marketing', 'Content Marketing', 'Influencer Marketing',
             'Market Research', 'Sales Funnel Creation', 'Brand Strategy'].includes(cat.name)
          )
        },
        {
          name: 'Business & Consulting',
          categories: categories.filter(cat =>
            ['Business Consulting', 'Financial Planning', 'Legal Consulting', 'HR Consulting',
             'Project Management', 'Business Plan Writing', 'Market Analysis',
             'Operations Consulting', 'Startup Consulting', 'Tax Preparation'].includes(cat.name)
          )
        },
        {
          name: 'Education & Training',
          categories: categories.filter(cat =>
            ['Online Tutoring', 'Language Learning', 'Music Lessons', 'Fitness Training',
             'Cooking Classes', 'Art Classes', 'Professional Training',
             'Test Preparation', 'Career Coaching', 'Life Coaching'].includes(cat.name)
          )
        },
        {
          name: 'Health & Wellness',
          categories: categories.filter(cat =>
            ['Personal Training', 'Nutrition Consulting', 'Mental Health Counseling', 'Massage Therapy',
             'Yoga Instruction', 'Physical Therapy', 'Wellness Coaching',
             'Meditation Guidance', 'Alternative Medicine', 'Beauty Services'].includes(cat.name)
          )
        },
        {
          name: 'Home & Lifestyle',
          categories: categories.filter(cat =>
            ['Home Cleaning', 'Handyman Services', 'Gardening & Landscaping', 'Pet Care',
             'Personal Shopping', 'Event Planning', 'Moving Services',
             'Home Organization', 'Childcare', 'Elder Care'].includes(cat.name)
          )
        },
        {
          name: 'Professional Services',
          categories: categories.filter(cat =>
            ['Accounting', 'Bookkeeping', 'Virtual Assistant', 'Customer Service',
             'Data Entry', 'Research Services', 'Administrative Support',
             'Transcription', 'Lead Generation', 'CRM Management'].includes(cat.name)
          )
        },
        {
          name: 'Creative Arts',
          categories: categories.filter(cat =>
            ['Music Production', 'Voice Over', 'Podcast Production', 'Video Production',
             'Audio Editing', 'Songwriting', 'Script Writing',
             'Stand-up Comedy', 'Performance Art', 'Craft Making'].includes(cat.name)
          )
        }
      ];

      return groups.filter(group => group.categories.length > 0);
    }
  });
}

export function useTrendingCategories(limit = 5) {
  return useQuery({
    queryKey: ['trending-categories', limit],
    queryFn: async (): Promise<ServiceCategory[]> => {
      const { data, error } = await supabase
        .from('categories')
        .select(`
          *,
          metadata:category_metadata(
            description,
            keywords,
            average_price_range,
            popularity_score,
            is_trending,
            parent_category_id,
            sort_order
          ),
          gig_count:gigs(count)
        `)
        .eq('type', 'service')
        .order('metadata.popularity_score', { ascending: false })
        .limit(limit);

      if (error) throw error;

      return (data || []).map(category => ({
        id: category.id,
        name: category.name,
        icon: category.icon,
        type: category.type,
        description: category.metadata?.[0]?.description,
        keywords: category.metadata?.[0]?.keywords,
        average_price_range: category.metadata?.[0]?.average_price_range,
        popularity_score: category.metadata?.[0]?.popularity_score || 0,
        is_trending: category.metadata?.[0]?.is_trending || false,
        parent_category_id: category.metadata?.[0]?.parent_category_id,
        sort_order: category.metadata?.[0]?.sort_order || 0,
        gig_count: category.gig_count?.[0]?.count || 0
      }));
    }
  });
}
