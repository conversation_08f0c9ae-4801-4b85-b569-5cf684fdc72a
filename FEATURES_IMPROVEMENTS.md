# BuddySurf – Features, Improvements & Integration Overview

---

## Core Principles
- **Onboarding is mandatory for all new users.**
- **All required profile fields and permissions must be completed before accessing the main app.**
- **No skipping:** Users cannot bypass onboarding or required fields.
- **All profile and app data is stored securely in the database.**
- **No hardcoded data:** All features and sample/demo data must use the database.
- **All features (Map, Activity, Hire, Chat, Admin, etc.) must be integrated and work together.**
- **All page functions must update systematically and in real time.**
- **Navigation and user experience must be seamless across all devices.**
- **Location data is handled with appropriate privacy and fallback measures.**
- **The Pricing modal should be triggered automatically after onboarding is complete.**

---

## Onboarding Steps

1. **Basic Info**
   - Display Name (**required**)
   - Profile Photo/Avatar (**required**)
   - *No username field (already collected at signup)*

2. **Birthday & Gender**
   - Birthday (**required**, must be 13+)
   - Gender (**required**)
     - Options: Man, Woman, Non-binary, Prefer not to say

3. **Default Location**
   - Map-based location picker (**required**)
   - "Use My Current Location" option
   - Sets user's default location for map and activity features

4. **Purpose Selection** (*optional*)
   - Why are you here?
   - Options: Make Friends, Talk, Find Love, Meet Nearby, Hangout, Earn Money
   - Can be skipped

5. **Interests** (*optional*)
   - Select interests for better matching
   - Can be skipped

6. **Vibe Selection** (**required**)
   - Select up to 5 vibes
   - Categories: Personality, Lifestyle, Tastes, Food, Romantic, etc.

7. **Permissions** (**required**)
   - Location permission
   - Notification permission (*optional*)
   - Privacy settings

---

## Core Onboarding Principles

### Mandatory Completion
- Onboarding is mandatory for all new users
- All required fields must be completed before accessing the main app

### Required Fields
- Display Name
- Avatar/Profile Photo
- Birthday (13+ age requirement)
- Gender
- Default Location
- Vibes (at least one)
- Location Permission

### Optional Fields
- Bio
- Purposes
- Interests
- Notification settings

### Progressive Disclosure
- Required steps cannot be skipped
- Optional steps can be skipped
- Progress is saved at each step

### User Experience
- Clear indicators for required vs. optional fields
- Helpful error messages
- Preview of profile as it's being built
- Responsive design for all devices

### Data Security
- All profile data is stored securely
- Location data is handled with appropriate privacy measures
- Age verification ensures 13+ requirement

### Onboarding Trigger
- Automatically triggered after signup if required fields are missing
- Cannot be dismissed until all required fields are completed
- Can be re-entered from profile settings after completion

### Profile Completeness
- Visual indicator of profile completion percentage
- Encouragement to complete optional fields after accessing the app

### Validation
- Real-time validation of inputs
- Appropriate error messages for invalid inputs
- Age verification for 13+ requirement

### Accessibility
- Clear, readable text
- Sufficient color contrast
- Keyboard navigation support
- Screen reader compatibility

- **The Pricing modal should be triggered automatically after onboarding is complete.**

---

## Updated Onboarding Steps (8 steps total)

### 1. Basic Info Step
- Collects display name (required, if not set at signup)
- Avatar upload (optional, if not set at signup)



### 2. Birthday & Gender Step
- Collects birthday (with age verification, must be 13+)
- Gender selection (man, woman, non-binary, prefer not to say)

### 3. Default Location Step
- Sets user's primary location on the map
- Uses Mapbox for location selection
- Offers "Use My Current Location" option
- This is where users appear on the map to others

### 4. Purpose Selection Step (optional)
- Users select why they're using the app
- Options include: Make Friends, Talk, Find Love, Meet Nearby, Hangout, Earn Money
- Auto-suggests "Hangout" if user hesitates for 10+ seconds
-

### 5. Interests Step (optional)
- Users select their interests for better matching


### 6. Vibe Selection Step (required)
- Users select up to 5 vibes that represent them
- Categories include: personality, lifestyle, tastes, food, etc.
- Used for personality matching and filtering

### 7. Permissions Step (required)
- Location permission (required)
- Notification permissions (optional)
- Privacy settings

### 8. Completion Step
- Summary of profile information
- "Start Exploring" button to enter the app

---

## Onboarding Steps Summary Table

| Step                | Required | Notes                                      |
|---------------------|----------|--------------------------------------------|
| Basic Info          | Yes      | Display name (if not set), avatar (if not) |
| Birthday & Gender   | Yes      | Age 13+, gender                            |
| Default Location    | Yes      | Map picker, fallback to current location   |
| Permissions         | Yes      | Location required, notifications optional  |
Purpose Selection   | No       | Optional                                   |
| Interests           | No       | Optional                                   |
| Vibe Selection      | Yes      | Up to 5 vibes                              |
| 
| Completion          | -        | Profile summary, enter app                 |

---

## Additional Notes
- **Username is collected at signup and not part of onboarding.**
- **No additional photo upload or Meetup Plan step in onboarding.**
- **Meetup Plan and other advanced features are set up after onboarding, within the main app.**
- **Onboarding modal is triggered after signup if required fields are missing.**
- **All required fields must be completed before entering the app.**
- **Optional steps (Purpose, Interests) can be skipped.**
- **Progress is saved at each step.**

---

## How to Use the Onboarding System

1. **Triggering Onboarding**
   - The onboarding modal is automatically shown to new users through the OnboardingController component, which:
     - Checks if a user's profile is incomplete
     - Shows the onboarding modal if required fields are missing
     - Can be manually triggered for profile updates

## Integration & Implementation Principles

- **All new features (Map, Activity, Hire/Marketplace, Chat, Admin) must be integrated and work together.**
- **Always use the database for all features and sample/demo data. Never hardcode data.**
- **All page functions must update systematically and in real time.**
- **After creating an account, onboarding modal should activate.**
- **Navigation must always work; Profile/Account should be in the main nav.**
- **All features must be accessible and up-to-date on all pages.**
- **The onboarded user location (`default_location`) must be used as the default for activity creation and map features.**

---

## Detailed Verification & What Needs Work

### 1. Map User Visibility
- Test and improve PeriodicLocationUpdater for edge cases (e.g., location permission denied, background updates). Ensure fallback to default location is robust and user feedback is clear.
- Enhance MapLocationStatus with more granular error/status messages and better UI feedback for connection issues.
- Ensure MeetMapPage integration refreshes user data everywhere location is updated, and that all map markers update in real time for all users.
- Update MapComponent to show real user avatars, accurate distance calculations, online status indicators, and enhanced user cards with bio previews.

### 2. Default Location in Onboarding
- Add more user guidance and validation for location selection in DefaultLocationStep. Ensure fallback logic is consistent across the app.
- Enforce default_location as required for profile completion in OnboardingController. Review debug logging for clarity.
- Ensure all map/location features (including chat, activities, and services) use default location as fallback everywhere.

### 3. Onboarding Notification System
- Improve OnboardingBanner dismissal logic and mobile UX.
- Ensure ProfileOnboardingButton appears only when needed and triggers onboarding correctly.
- Expand notification system with more notification types, better real-time updates, and user preferences for notification delivery.

### 4. Additional Improvements
- Add more comprehensive error messages for all location and network issues.
- Add more visual feedback for location updates, improve status indicators, and ensure toast notifications are accessible and actionable.

**All these components must work together to:**
- Show users on the map with regular 5-minute updates (test for reliability and edge cases).
- Use default location as fallback everywhere (verify in all flows).
- Include default location selection in onboarding (enforce as required step).
- Provide notifications and reminders for users to complete their profiles (expand notification types and triggers).

---

## Features in Progress & Next Steps

- **Nearby User List in Chat:** ⬜ Not yet implemented.
- **Suggestion Bar for Chat:** ⬜ Not yet implemented.
- **Live Admin Conversation:** ⬜ Not yet implemented for all users.
- **Advanced Geofencing:** ⬜ Not yet implemented for activities/events.
- **Location Sharing in Chat:** ⬜ Not yet integrated.
- **Activity Booking from Map:** ⬜ Not yet integrated.
- **Service Providers on Map:** ⬜ Not yet integrated.
- **User Role Management:** ⬜ Not complete (admin/verified/user roles).
- **Content Reporting/Moderation:** ⬜ Not complete.
- **Transaction Management:** ⬜ Not complete (wallet, payment integration, financial oversight).
- **Platform Analytics:** ⬜ Not implemented.
- **Verification/Dispute Management:** ⬜ Not implemented.
- **Admin Notifications:** ⬜ Not implemented.
- **Activity Chat Integration:** ⬜ Not complete.
- **Proposal Cards:** ⬜ Not complete (accept/reject in chat).
- **Media Sharing:** ⬜ Not complete (images/files in chat and proposals).
- **Service Booking:** ⬜ Not complete (booking flow, payment, fulfillment tracking, reviews).
- **Gig/Hire Marketplace:** ⬜ Not complete (any user can create a gig; gig and hire are the same page).
- **Map Enhancements:** ⬜ Not complete (avatars, distance, status, bio previews, 3D buildings, style switching, favorites).
- **Activity System Enhancements:** ⬜ Not complete (creation modal, queue, share modal, filters, waitlist, analytics, recommendations).
- **Search & Discovery:** ⬜ Not complete (geocoding, search history, suggestions).
- **Booking & Service System:** ⬜ Not complete (booking, reviews, file attachments, fulfillment tracking, analytics, categories, chat integration).
- **Wallet & Payments:** ⬜ Not complete (transaction history, payment processing, Stripe, donation system).
- **Security & Stability:** ⬜ Not complete (RLS policies, error handling, data validation).

---

## MVP Launch Focus

- ⬜ Map with user locations and basic activity markers (verify all markers and updates are real-time and reliable).
- ⬜ Activity creation and joining functionality (test all flows, including queue and booking).
- ⬜ Basic real-time chat with simple proposals (ensure chat is real-time and proposals can be sent/accepted/rejected).
- ⬜ User profiles with avatar and bio (test editing, uploading, and display everywhere).
- ⬜ Simple admin controls for moderation (test all admin actions and permissions).

---

## What's Left

- Full integration of all features (Map, Activity, Hire, Chat, Admin).
- Systematic, real-time updates everywhere.
- All advanced features listed above.
- Complete documentation and sample data in DB (no hardcoding).

---

# BuddySurf – Features & Improvements Overview

## 1. Activity Page – Improvements

**What's Missing / Needs Improvement:**
- **Full Modal View:** Clicking an activity card should open a modal with all details (not just a page).
- **Host & Participants:** Show host profile (with avatar, badge, distance), and a list of participants (avatars, names, join status).
- **Queue System:** Show queue position, join/leave buttons, and status (pending/confirmed).
- **Chat Integration:** Button to open chat with host/participants directly from modal.
- **Share Modal:** Button to share activity (copy link, share via email/SMS).
- **Map Preview:** Embedded mini-map showing activity location.
- **Category & Emoji:** Prominent category icon/emoji.
- **Price & Spots:** Show price (free/paid), spots left, and participant limit.
- **Date/Time:** Clear, formatted date/time.
- **Flag/Report:** Button to report inappropriate activities.
- **Responsive Design:** Modal should be mobile-friendly and accessible.

**Design Suggestions:**
- Use a card layout inside the modal: left side for map & host, right for details.
- Use color-coded badges for category, price, and status.
- Add a sticky action bar at the bottom (Join, Chat, Share, Report).

## Activity Modal – Detailed Feature Checklist

1. **Full Modal View**
   - Clicking an activity card should open a modal with all details (not just a page).
2. **Host & Participants**
   - Show host profile (avatar, badge, distance).
   - List of participants (avatars, names, join status).
3. **Queue System**
   - Show queue position.
   - Join/leave buttons.
   - Status (pending/confirmed).
4. **Chat Integration**
   - Button to open chat with host/participants directly from modal.
5. **Share Modal**
   - Button to share activity (copy link, share via email/SMS).
6. **Map Preview**
   - Embedded mini-map showing activity location.
7. **Category & Emoji**
   - Prominent category icon/emoji.
8. **Price & Spots**
   - Show price (free/paid).
   - Spots left and participant limit.
9. **Date/Time**
   - Clear, formatted date/time.
10. **Flag/Report**
    - Button to report inappropriate activities.
11. **Responsive Design**
    - Modal should be mobile-friendly and accessible.

**Design Suggestions:**
- Card layout inside modal: left side for map & host, right for details.
- Color-coded badges for category, price, and status.
- Sticky action bar at the bottom (Join, Chat, Share, Report).

---

## Activity Review System

- After completing an activity, users can leave a star rating and written review.
- Reviews are shown on user profiles (host/participant), with average rating and review count.
- Users can hide or unhide their own reviews from public view (but cannot delete them).
- Hidden reviews stay in the database and can be made visible again.
- Admins can moderate reviews if needed.

## 2. Help & Support Page – Design

**What to Include:**
- **Search Bar:** "How can we help you?" with instant suggestions.
- **FAQ Section:** Collapsible panels for common questions.
- **Contact Options:** Email, live chat, and support ticket form.
- **Guides & Tutorials:** Links to onboarding, activity creation, wallet, etc.
- **Status Banner:** Show if there are any known issues/outages.
- **Feedback Form:** "Was this helpful?" thumbs up/down with comment box.
- **Responsive Design:** Easy to use on mobile.

**Design Suggestions:**
- Use a soft blue/white background with card-style sections.
- Icons for each FAQ category (Account, Meetups, Payments, Safety, etc.).
- Prominent "Contact Support" button.
- Animated transitions for expanding/collapsing FAQ.

---

## 3. Profile Page – What's Missing

**Based on requirements, missing or incomplete features:**
- Avatar Upload & Cropping
- Bio Editor (with formatting, emoji, and preview)
- Followers/Following Lists (modal or tab)
- Gallery/Media Management (upload, delete, reorder)
- Gigs/Services Section (list, create, edit, delete gigs)
- Wallet Section (balance, transaction history, send/receive money)
- Meetup History (past and upcoming activities)
- Score/Rating System (user reviews, average rating)
- Verified Badge (if user is verified)
- Social Links (optional)
- Edit Profile Modal (all fields, validation, save/cancel)
- Referral/Rewards Section
- Recent Meetups Display
- Location Privacy Settings (show/hide location)
- Service Offerings Hire and Gig are the same feature: all users can create, list, and book gigs/services from a single unified page.
- **Profile Privacy Controls:**
  - By default, all user profiles are public and viewable by others.
  - Users can make their profile private at any time through the Settings page.
  - Private profiles are only visible to their owners; other users cannot view them.
  - When a user attempts to view a private profile that is not their own, a clear error message is displayed (e.g., "This profile is private.").


---

## 4. Settings Page – What's Needed for Profile

**Settings to Include:**
- Account Settings: Change email, password, delete account, manage login methods.
- Profile Privacy: Show/hide profile, control who can message, block users.
- Notification Preferences: Email, push, SMS toggles for different events (messages, activities, bookings, etc.).
- Payment Methods: Add/remove cards, view payment history.
- App Customization: Theme (light/dark), language, map style.
- Location Sharing: Toggle live location, set default location, manage saved locations.
- Security: 2FA, login history, device management.
- Data Export: Download profile/activity data.
- Legal: Links to privacy policy, terms, and support.

---

## 5. Meetup Page – Show All Users Section

**What to Add:**
- User List Sidebar/Section: Show all users who joined/interested in each activity.
- User Cards: Avatar, name, distance, online status, quick message button.
- Filters: By distance, activity type, online status.
- Join/Leave Buttons: For each activity.
- Map Integration: Option to view users on map.
- Responsive Design: Collapsible sidebar on mobile.

---

## 6. Notification Center – Design

**What to Include:**
- Tabs: All, Unread, Activities, Messages, Bookings, System.
- Notification Cards: Icon, title, short description, timestamp, read/unread status.
- Bulk Actions: Mark all as read, delete all.
- Settings Shortcut: Link to notification preferences.
- Real-Time Updates: New notifications appear instantly.
- Empty State: Friendly message when no notifications.
- Responsive Design: Stacked cards on mobile.

**Design Suggestions:**
- Use colored icons for notification types.
- Subtle background for unread notifications.
- Slide-in animation for new notifications.

---

## 7. Hire Page (Gigs Page) – What's Needed

**Since Gigs and Hire are the same:**
- Gig Creation Modal: Title, description, category, price, delivery time, images.
- Gig Listings: Grid/list of gigs with filters (category, price, rating, delivery time).
- Search Bar: Search gigs by keyword.
- Provider Cards: Avatar, name, rating, price, quick message button.
- Booking/Proposal System: Book gig, send proposal, accept/reject.
- Review System: Leave/read reviews for gigs/providers.
- Sort Options: Newest, popular, price, rating.
- Become a Provider: Button to create a gig.
- Responsive Design: Cards stack on mobile.

---

## 8. Chat Page – What's Left

**Missing/To-Do:**
- Group Chats: Create/join group conversations.
- Buddy Admin/Bot: Default admin/bot chat for all users.
- Proposal Cards: Accept/reject proposals in chat.
- Media Sharing: Images, files, with previews.
- Typing Indicators: Show when someone is typing.
- Read Receipts: Indicate when messages are read.
- Pinned/Starred Messages: For important info.
- Quick Replies/Suggestions: Above input.
- Activity/Service Context: Show context when chatting about an activity/gig.
- Notification Badges: For unread messages.
- Chat Search: Search within conversations.
- Responsive Design: Sidebar collapses on mobile.

---

## Summary Table

| Page/Feature         | What's Missing/Needed                                                                                   |
|----------------------|--------------------------------------------------------------------------------------------------------|
| **Activity Page**    | Modal view, host/participants, queue, chat, share, map, category, price, date, report, responsive      |
| **Help Page**        | Search, FAQ, contact, guides, status, feedback, responsive, modern design                              |
| **Profile Page**     | Avatar upload, bio editor, followers, gallery, gigs, wallet, meetups, reviews, social, privacy, etc.   |
| **Settings Page**    | Account, privacy, notifications, payment, customization, location, security, data export, legal        |
| **Meetup Page**      | User section, user cards, filters, join/leave, map, responsive                                         |
| **Notification Center** | Tabs, cards, bulk actions, settings, real-time, empty state, responsive, modern design              |
| **Hire/Gigs Page**   | Gig creation, listings, filters, search, provider cards, booking, reviews, sort, become provider       |
| **Chat Page**        | Group chat, admin/bot, proposals, media, typing, receipts, pinned, quick replies, context, search      |

---

# Global Design System

---

## Color Palette
- **Primary:** Ocean blue (#1E90FF)
- **Secondary:** Deep blue (#003366)
- **Accent:** Purple (#8B5CF6)
- **Highlight:** Coral (#F97316)
- **Background:** Light gradient from white to soft blue (#f3e7e9 to #e3eeff)
- **Card Background:** White (#FFFFFF)
- **Text:** Dark gray (#333333) for primary text, medium gray (#666666) for secondary text
- **Success:** Green (#10B981)
- **Warning:** Amber (#F59E0B)
- **Error:** Red (#EF4444)

## Typography
- **Headings:** Inter, sans-serif, bold
- **Body:** Inter, sans-serif, regular
- **Buttons:** Inter, sans-serif, medium
- **Font Sizes:**
  - XS: 0.75rem (12px)
  - SM: 0.875rem (14px)
  - Base: 1rem (16px)
  - LG: 1.125rem (18px)
  - XL: 1.25rem (20px)
  - 2XL: 1.5rem (24px)
  - 3XL: 1.875rem (30px)
  - 4XL: 2.25rem (36px)

## Components
- **Cards:** White background, soft shadow, rounded corners (0.5rem)
- **Buttons:** Rounded (0.375rem), with hover and active states
- **Inputs:** Rounded (0.375rem), with focus states
- **Modals:** White background, soft shadow, rounded corners (0.5rem)
- **Tabs:** Underlined active state, hover effects
- **Badges:** Small, rounded pills for status indicators
- **Avatars:** Circular, with fallback initials
- **Tooltips:** Small, dark background with white text
- **Alerts:** Colored backgrounds with matching icons

## Animations
- **Transitions:** 300ms ease for most UI elements
- **Hover Effects:** Scale (1.05) for interactive elements
- **Active Effects:** Scale (0.95) for click feedback
- **Loading States:** Subtle pulse animations
- **Page Transitions:** Fade in/out (200ms)

## Detailed Page Design Principles
- **Home Page (/home):**
  - Gradient hero, quick stats, feature cards grid, recent activity, community highlights, responsive stacking, animated text.
- **MeetUp Page (/meetup):**
  - Filters bar, activity cards grid, sidebar/bottom sheet for participants, create activity FAB, responsive filters.
- **Meet Map Page (/meetmap):**
  - Full-screen 3D Mapbox, user/activity markers, clustering, right sidebar tabs, map controls, search, responsive sidebar.
- **Activity Surf Page (/activity):**
  - Category tabs, advanced filters, activity grid, creation FAB, details modal, responsive filters.
- **Hire/Gigs Page (/hire or /gigs):**
  - Search, filters, service grid, featured providers, create gig button, service details modal, responsive grid.
- **Chat Page (/chat):**
  - Sidebar with conversations, main chat area, suggestion bar, nearby users panel, group chat, responsive layout.
- **Profile Page (/profile):**
  - Header with avatar, tabs for sections, edit modal, responsive tabs.
- **Settings Page (/settings):**
  - Sidebar navigation, tabbed content, responsive stacking.
- **Admin Panel (/admin):**
  - Dashboard stats, data tables, analytics, moderation tools, responsive tables.
- **Wallet Page (/wallet):**
  - Balance card, tabs for transactions/methods/subscriptions, responsive stacking.
- **Notifications Page (/notifications):**
  - Tabs, notification cards, bulk actions, responsive cards.
- **Earn Page (/earn):**
  - Earnings chart, stats cards, opportunities, recent earnings, responsive grid.
- **Bookings Page (/bookings):**
  - Tabs for client/provider, booking cards, calendar view, responsive stacking.
- **Network Page (/network):**
  - Tabs for followers/following, user grid, suggestions, responsive grid.
- **Help Page (/help):**
  - Search, categories grid, FAQ accordion, contact options, guides, responsive stacking.
- **404/NotFound:**
  - Centered creative 404, illustration, return home button, search, popular links.

## Shared UI Elements
- **Navigation:**
  - Top nav bar (logo, links, profile, notifications, search)
  - Mobile: Bottom tab bar, hamburger menu
  - Breadcrumbs where applicable
- **Modals:**
  - Standard structure: header, content, footer, blurred backdrop
  - Used for details, previews, confirmations, forms
- **Loading States:**
  - Skeleton loaders, progress indicators, transitions
- **Empty States:**
  - Friendly illustrations, helpful text, action buttons
- **Notifications:**
  - Toasts, badge indicators, in-app notification center

## Responsive Design Principles
- **Breakpoints:**
  - Mobile: < 640px
  - Tablet: 640px - 1024px
  - Desktop: > 1024px
- **Mobile:**
  - Navigation as bottom tab bar
  - Sidebars as full-screen or bottom sheets
  - Grids reduce columns, text sizes adjust, touch targets increase
- **Tablet:**
  - Hybrid layouts, collapsible sidebars, optimized for touch/mouse
- **Desktop:**
  - Multi-column layouts, hover states, keyboard shortcuts, expanded info density

## Accessibility Considerations
- **Color Contrast:** WCAG AA minimum
- **Keyboard Navigation:** Full support
- **Screen Reader Support:** Proper ARIA labels
- **Focus States:** Visible indicators
- **Text Sizing:** Scalable without breaking layouts
- **Alternative Text:** For all images and icons
- **Reduced Motion:** Option for users to minimize animations

---

# Completed UI/UX Implementations

---

## Home Page (HomePage.tsx)
- Modern hero section with gradient text
- Dashboard for logged-in users: profile completion, upcoming activities, notifications
- Visually appealing feature cards with hover effects
- Community highlights section with statistics
- Animated marquee text banner
- Enhanced CTA section with gradient background

## 404 Not Found Page (NotFound.tsx)
- Visually appealing error page with gradient header
- Search functionality for quick navigation
- Popular links section for easy access
- Decorative background elements
- Improved mobile responsiveness

## Help & Support Page (HelpPage.tsx)
- Comprehensive help center with search functionality
- Tabbed interface for FAQ, guides, and contact options
- Expandable FAQ sections with detailed answers
- Feedback mechanism for help content
- Guide cards with badges for difficulty levels
- Contact form and support options

## Notifications Page (NotificationsPage.tsx)
- Full-featured notifications center with tabs for different notification types
- Selection functionality for bulk actions (mark as read, delete)
- Visually distinct notification items with appropriate icons and colors
- Search functionality for notifications
- Empty state for when no notifications are available
- Fully responsive design

---

# UI/UX Design for All Pages

---

## Home Page (/home)
**Purpose:** Dashboard hub for all main features.
**Design:**
- Top nav: Logo, search bar ("Search locations or users…"), profile/avatar, notifications.
- Quick stats: Upcoming meetups, recent activities, wallet balance, notifications.
- "Explore" section: Cards for MeetUp, Map, Hire, Chat, Earn, Profile, Settings, Admin.
- Animated scrolling text: "EXPLORE · CONNECT · EXPERIENCE".
- Responsive grid layout; cards stack on mobile.
- Footer: Links to Help, Privacy, Terms.

---

## MeetUp Page (/meetup)
**Purpose:** Browse and filter all activities (free/paid).
**Design:**
- Top: Filters (category, price, distance, date, toggle latest/nearby).
- Activity cards grid: Each card shows emoji/category, title, host, date/time, price, spots left, join/chat buttons.
- Sidebar (desktop) or collapsible panel (mobile): List of users who joined/interested, with avatars, names, distance, online status, quick message.
- "View on Map" button for each activity.
- Responsive: Filters collapse into dropdowns on mobile.

---

## Meet Map Page (/meetmap)
**Purpose:** Interactive 3D map with user and activity markers.
**Design:**
- Full-screen Mapbox map (light mint green style, 3D buildings, 45° pitch).
- Right sidebar: Tabs for "Nearby" (users), "Plans" (activities), "Hire" (gigs/services).
- User markers: Circular avatars/initials, online status dot, distance tooltip.
- Activity markers: Category emoji, price, tooltip.
- Clicking marker: Profile/activity preview modal (with connect/message/join).
- Top center: Search bar ("Search locations or users…").
- Map controls: Zoom, pitch, style switcher, favorites.
- Responsive: Sidebar becomes sheet on mobile.

---

## Activity Surf Page (/activity)
**Purpose:** Browse all activities, dating plans, gigs/services.
**Design:**
- Grid of activity/gig cards (category, emoji, title, host, price, date, join/book).
- Filters: Category, price, date, popularity.
- "Create Activity" button (opens modal).
- Responsive: Cards stack on mobile.

---

## Hire Friend/Gigs Page (/hire or /gigs)
**Purpose:** Unified gig/service marketplace.
**Design:**
- Search bar ("Find a service or gig…").
- Filters: Category, price, rating, delivery time.
- Grid/list of gig cards: Avatar, name, rating, price, description, book/message.
- "Create Gig" button (opens modal for all users).
- Booking/proposal system: Book, send proposal, accept/reject.
- Reviews: Star rating, review count, read/write reviews.
- Responsive: Cards stack, filters become dropdowns on mobile.

---

## Chat Page (/chat)
**Purpose:** Real-time messaging, proposals, media, group chat.
**Design:**
- Left sidebar: List of conversations (avatar, name, last message, unread badge).
- Main chat area: Message bubbles (text, media, proposals, activity/service cards).
- Top: Chat title, participants, "Buddy Admin"/Bot indicator.
- Above input: Suggestion bar (quick replies).
- Input: Text, emoji, file upload, send button.
- Nearby users panel: Collapsible, shows users within 5km (click to start chat).
- Group chat: Add/remove participants, group name/avatar.
- Responsive: Sidebar collapses on mobile, floating action button to start new chat.

---

## Profile Page (/profile)
**Purpose:** User profile, editing, social, gigs, wallet.
**Design:**
- Header: Avatar (upload/crop), name, verified badge, location, edit button.
- Tabs/sections: About (bio, social links), Gallery (media grid), Gigs/Services, Meetups (history), Network (followers/following), Wallet (balance, transactions), Reviews, Referrals/Rewards.
- "Follow/Unfollow" button.
- Edit profile modal: Name, username, bio, avatar, location, privacy.
- Responsive: Tabs become accordion on mobile.

---

## Settings Page (/settings)
**Purpose:** Account, privacy, notifications, payments, customization.
**Design:**
- Tabbed interface: Account, Privacy, Notifications, Payments, Preferences, Security, Data Export, Legal.
- Each tab: Form fields, toggles, save/cancel.
- Responsive: Tabs become dropdown/accordion on mobile.

---

## Admin Panel (/admin)
**Purpose:** User/content moderation, analytics, platform management.
**Design:**
- Dashboard: Stats cards (users, revenue, meetups), flagged content alerts.
- Tabs: Users, Activities, Gigs, Transactions, Reports, Settings.
- Data tables: Sortable, filterable, bulk actions.
- User detail view: Full profile, activity history, moderation actions.
- Analytics: Charts, export buttons.
- Responsive: Tables become lists/cards on mobile.

---

## Other Key Pages

### Earn Page (/earn)
- Earnings dashboard, wallet balance, earnings chart, hosting stats, info cards.

### Notifications Page (/notifications)
- Tabs: All, Unread, Activities, Messages, Bookings, System.
- Notification cards: Icon, title, description, timestamp, read/unread.
- Bulk actions: Mark all as read, delete all.
- Settings shortcut.

### Wallet Page (/wallet)
- Balance, add/transfer/withdraw funds, transaction history, payment methods, subscriptions.

### Bookings Page (/bookings)
- Tabs: As client, as provider.
- Booking cards: Service, status, actions (cancel, complete, message).

### Disputes Page (/disputes)
- List of disputes, filter by status, dispute details, actions.

### Help Page (/help)
- Search bar, FAQ, contact options, guides, status banner, feedback form.

### Subscription Page (/subscription)
- Plan selection, features, pricing, subscribe/manage, Stripe integration.

### Network Page (/network)
- Tabs: Followers, Following. User cards, search/filter, follow/unfollow.

### 404/NotFound
- Large "404 Not Found", return to home button, friendly illustration.

---

## General UI/UX Principles

- **Color Palette:**
  - Primary: Ocean blue (#0EA5E9)
  - Secondary: Coral (#F97316)
  - Accent: Purple (#8B5CF6)
  - Background: Light, airy whites and soft blues

- **Typography:**
  - Modern, sans-serif, large headings, readable body text.

- **Buttons:**
  - Rounded, bold, clear labels, primary/secondary color usage.

- **Cards:**
  - Soft shadows, rounded corners, hover/active states.

- **Modals:**
  - For creation/editing, activity/gig details, onboarding, wallet actions.

- **Mobile First:**
  - All layouts stack, sidebars become sheets, large touch targets.

- **Accessibility:**
  - Sufficient color contrast, keyboard navigation, ARIA labels.

---

# Detailed Missing Components & Needs (⚠️/❌)

## MeetUp Page (/meetup) ⚠️
**Current Status:** Basic page structure exists but missing key components.

**Missing Components:**
- **User List Sidebar/Section:**
  - Collapsible sidebar (desktop) or bottom sheet (mobile)
  - Display all users who joined or expressed interest in each activity
  - Real-time updates when users join/leave
- **User Cards:**
  - Show user avatar, name, distance from current user
  - Online status indicator (online/offline/away)
  - Quick message button to initiate chat
  - Show user's basic info on hover/tap
- **Filters:**
  - Distance filter (nearby to far)
  - Activity type filter (categories)
  - Status filter (online/offline/all)
  - Date/time filter (today, tomorrow, this week, etc.)
  - Price filter (free/paid/all)
- **Map Integration:**
  - "View on Map" button for each activity
  - Mini-map preview for activities
  - Option to switch between list and map views
- **Responsive Design:**
  - Sidebar collapses on mobile
  - Filters become dropdown/sheet on mobile
  - User cards stack appropriately

## Hire/Gigs Page (/hire or /gigs) ⚠️
**Current Status:** Basic structure exists but missing most functionality.

**Missing Components:**
- **Gig Creation Modal:**
  - Form with fields for title, description, category
  - Price and delivery time inputs
  - Image upload capability
  - Tags/skills selection
  - Availability settings
  - Preview option before publishing
- **Comprehensive Listings:**
  - Grid/list view of available gigs
  - Filters for category, price range, rating, delivery time
  - Sort options (newest, popular, price low/high, rating)
  - Search functionality with autocomplete
  - Category navigation
- **Booking/Proposal System:**
  - Book now button for immediate booking
  - Send proposal option for custom requests
  - Calendar for availability selection
  - Payment integration (deposit/full payment options)
  - Confirmation flow
- **Review System:**
  - Star ratings (1-5)
  - Written reviews with moderation
  - Provider response capability
  - Helpful/not helpful voting on reviews
  - Filter reviews by rating
- **Provider Cards:**
  - Avatar and name
  - Rating display
  - Price information
  - Quick message button
  - Brief service description
  - Verification badge if applicable

## Wallet Page (/wallet) ❌
**Current Status:** Not implemented.

**Needs Implementation:**
- **Balance Display:**
  - Current balance prominently displayed
  - Currency selection
  - Income/expense summary
  - Available/pending funds distinction
- **Transaction History:**
  - Filterable list of all transactions
  - Search functionality
  - Export options (CSV, PDF)
  - Transaction details on click
  - Status indicators (completed, pending, failed)
- **Payment Methods:**
  - Add/remove credit cards
  - Connect bank accounts
  - PayPal integration
  - Default payment method selection
  - Payment method verification
- **Fund Management:**
  - Deposit funds button and flow
  - Withdraw funds to bank/PayPal
  - Transfer to other users
  - Automatic payments settings
  - Transaction fees display
- **Security Features:**
  - Transaction PIN/2FA for withdrawals
  - Activity alerts
  - Spending limits configuration
  - Suspicious activity detection

## Earn Page (/earn) ❌
**Current Status:** Not implemented.

**Needs Implementation:**
- **Earnings Dashboard:**
  - Total earnings display
  - Current month/week earnings
  - Comparison to previous periods
  - Projected earnings
- **Charts and Analytics:**
  - Earnings over time chart
  - Source of earnings breakdown
  - Peak earning periods
  - Performance metrics
- **Stats Cards:**
  - Number of completed gigs/activities
  - Average rating
  - Completion rate
  - Response time
  - Repeat client percentage
- **Earning Opportunities:**
  - Suggested gigs to create
  - Popular service categories
  - Tips to increase earnings
  - Featured opportunities
  - Referral program details
- **Payout Settings:**
  - Payout method configuration
  - Payout schedule (weekly, monthly)
  - Tax information
  - Earnings reports

## Bookings Page (/bookings) ❌
**Current Status:** Not implemented.

**Needs Implementation:**
- **Tabs for Different Views:**
  - "As Client" tab (bookings you've made)
  - "As Provider" tab (bookings you've received)
  - Calendar view option
  - List view option
- **Booking Cards:**
  - Service/activity details
  - Date and time
  - Status indicator (confirmed, pending, completed, cancelled)
  - Price information
  - Provider/client information
  - Quick actions (message, cancel, reschedule)
- **Status Management:**
  - Accept/decline incoming bookings
  - Cancel booking with reason
  - Mark as completed
  - Leave review prompt
  - Dispute initiation
- **Filtering and Sorting:**
  - By date (upcoming, past)
  - By status
  - By price
  - By provider/client
  - Search functionality
- **Calendar Integration:**
  - Add to Google/Apple calendar
  - Export booking details
  - Availability management
  - Booking clash detection

## Network Page (/network) ❌
**Current Status:** Not implemented.

**Needs Implementation:**
- **Followers/Following Tabs:**
  - Tab for people following you
  - Tab for people you follow
  - Mutual connections highlight
  - Follow/unfollow buttons
  - Follower count display
- **User Grid:**
  - Cards with avatar, name, bio snippet
  - Follow status indicator
  - Mutual connections count
  - Last activity timestamp
  - Distance information
- **Search and Filter:**
  - Search by name/username
  - Filter by location
  - Filter by interests/vibes
  - Filter by activity participation
  - Sort options (recent, alphabetical, etc.)
- **Connection Management:**
  - Follow/unfollow functionality
  - Block user option
  - Report user option
  - Message button
  - View full profile link
- **Suggestions Section:**
  - "People you may know" based on mutual connections
  - "Similar interests" based on profile data
  - "Nearby users" based on location
  - "Active users" based on recent activity
  - Refresh suggestions button

## Subscription Page (/subscription) ❌
**Current Status:** Not implemented.

**Needs Implementation:**
- **Plan Selection:**
  - Free tier display
  - Premium tier options (weekly, monthly, lifetime)
  - Feature comparison table
  - Current plan indicator
  - Recommended plan highlight
- **Features Display:**
  - Detailed list of features by plan
  - Premium-only features highlighted
  - Usage limits display
  - Feature tooltips with explanations
  - Feature demos/previews
- **Pricing Information:**
  - Clear pricing for each plan
  - Billing cycle options
  - Discount for longer commitments
  - Special offers/promotions
  - Money-back guarantee details
- **Stripe Integration:**
  - Secure payment processing
  - Credit card input form
  - Payment method saving
  - Subscription management
  - Receipt generation
- **Account Management:**
  - Upgrade/downgrade options
  - Cancel subscription flow
  - Billing history
  - Payment method update
  - Subscription status display

## Disputes Page (/disputes) ❌
**Current Status:** Not implemented.

**Needs Implementation:**
- **List of Disputes:**
  - All disputes involving the user
  - Basic details (parties involved, amount, date)
  - Status indicators (open, under review, resolved)
  - Priority indicators
  - Action required notifications
- **Status Filters:**
  - Open disputes
  - Under review
  - Resolved in your favor
  - Resolved against you
  - All disputes
- **Dispute Details:**
  - Full transaction information
  - Timeline of events
  - Messages between parties
  - Evidence submitted
  - Resolution details
- **Action Buttons:**
  - Open new dispute
  - Submit evidence
  - Accept resolution
  - Appeal decision
  - Contact support
- **Resolution Process:**
  - Step-by-step guide
  - Estimated resolution time
  - Required documentation list
  - FAQ section
  - Mediation options

---

# BuddySurf Subscription Plans

## 1. Free Tier (Default)
**Price:** $0

**Features:**
- Basic map access 
- Limited activity creation (1 per week)
- Standard chat (text-only, no proposals)
- Profile visibility in local searches
- Ads-supported
- Access to Help Center & FAQ
- Community support

---

## 2. Weekly Trial
**Price:** $3.99 for 14-day trial, then $3.99/week  
**Target:** Casual users testing premium features

**Features (All Free Tier +):**
- Basic map access 
- Activities: Up to 3 creations/week
- Chat: Media sharing + 5 proposal cards/day
- Visibility: Boosted in "Nearby" lists
- No ads
- Access to premium support (email/chat)
- Early access to select new features

---

## 3. Monthly Pro
**Price:** $15/month  
**Target:** Active social users

**Features (All Weekly +):**
- Unlimited activity creation
- Priority placement in activity queues
- Advanced filters (income level, verified users, distance, category)
- Wallet integration (send/receive payments)
- 10 proposal cards/day
- Custom map styles (night mode, satellite, etc.)
- Profile analytics (views, engagement)
- Access to exclusive activities/gigs
- Premium badge on profile

---

## 4. Lifetime Surf
**Price:** $35 one-time  
**Target:** Power users/early adopters

**Features (All Monthly +):**
- Permanent ad-free experience
- "Verified" badge
- Early access to all new features
- Dedicated support (priority queue)
- Unlimited proposals
- Exclusive map markers (custom icons/colors)
- Lifetime badge on profile
- Invitation to beta test new features

---

# Application Structure and Workflow

Based on the database structure and our previous discussions, here's an outline of how the BuddySurfNow application works:

## 1. User Management

### Registration and Onboarding
- Users register with email/password or social login
- Mandatory onboarding process collects:
  - display_name
  - username
  - avatar_url (profile photo)
  - birthday
  - gender
  - vibes (interests/preferences, up to 5)
  - default_location
  - location_permission_granted
- Onboarding must be completed before accessing the main app

### User Profiles
- Profiles store user information including:
  - Basic info (name, username, bio, avatar)
  - Demographics (birthday, gender)
  - Preferences (vibes, purposes)
  - Location settings
  - Gallery (additional photos)
  - Notification preferences

### Social Connections
- Users can follow each other (stored in follows table)
- Social graph enables activity discovery and friend finding

## 2. Location Features

### Location Tracking
- Real-time location tracking (with permission)
- Location data stored in user_locations table
- Location expiry mechanism (15 minutes)

### Saved Locations
- Users can save favorite locations
- Saved locations used for activity planning

### Geofencing
- Geofences define areas of interest
- Geofence events trigger when users enter/exit areas
- Used for activity check-ins and location-based notifications

## 3. Activities

### Activity Creation
- Users can create activities with:
  - Title, description, category
  - Location and address
  - Start/end times
  - Media (photos)
  - Participant limits
  - Visibility settings

### Activity Participation
- Users can join activities
- Participation tracked in activity_participants table
- Optional queue system for popular activities

### Activity Discovery
- Activities can be browsed by category
- Map-based discovery
- Activity feed based on location and social connections

## 4. Communication

### Messaging
- Direct messaging between users
- Group chats for activities
- Location sharing in messages

### Notifications
- System notifications for activity updates
- Follow notifications
- Message notifications
- Customizable notification preferences

## 5. Subscription System

### Subscription Tiers
- Free ($0)
- Weekly Trial ($3.99/14-days)
- Monthly Pro ($15/month)
- Lifetime Surf ($35 one-time)

### Feature Access
- Progressive feature access based on subscription tier
- Subscription status stored in subscriptions table

## Key Application Flows

### User Onboarding Flow
1. User registers
2. Onboarding modal appears automatically
3. User completes required fields
4. System verifies completeness
5. User gains access to main app

### Activity Creation Flow
1. User creates new activity
2. Selects category, location, time
3. Adds description and media
4. Sets participation limits and visibility
5. Activity is published and discoverable

### Activity Participation Flow
1. User discovers activity
2. Requests to join
3. Host approves or auto-join based on settings
4. User is added to activity participants
5. User gains access to activity chat

### Location-Based Discovery Flow
1. User grants location permission
2. System tracks user location
3. Nearby activities are displayed on map
4. User can filter by category, time, etc.
5. User can save locations for future reference

## Security Model

The application uses Supabase's Row Level Security (RLS) policies to ensure data security:

### Profile Security
- Public read access to basic profile information
- Users can only update their own profiles
- Admins have additional privileges

### Activity Security
- Activities are publicly viewable
- Only hosts can edit their activities
- Participants have access to activity details and chat

### Message Security
- Users can only access conversations they're part of
- Users can only update/delete messages they sent

### Location Security
- Users can only access their own location data
- Location sharing is explicit and permission-based

---

# Onboarding Data Model and Saving

## Main Table for Onboarding
The onboarding process primarily uses the `profiles` table in the public schema. This table stores all user profile information collected during onboarding.

## Key Fields in the Profiles Table
- **id**: UUID (Primary Key, references auth.users)
- **display_name**: User's display name
- **username**: Unique username
- **bio**: User biography (optional)
- **avatar_url**: Profile picture URL
- **birthday**: Date of birth
- **gender**: User gender
- **vibes**: Array of user interests/vibes
- **default_location**: PostgreSQL point type for location coordinates
- **location_display**: Human-readable location name
- **location_permission_granted**: Boolean for location permission
- **notifications_enabled**: Boolean for notification settings
- **onboarding_completed**: Boolean flag indicating if onboarding is complete

## How Data is Saved
The application uses several functions to save onboarding data:
- **saveProfileData()**: Saves individual profile fields
- **saveOnboardingData()**: Saves all onboarding data and marks onboarding as completed
- **markOnboardingCompleted()**: Specifically updates the onboarding_completed flag

# Profile Page Link Structure

The profile page for each user is accessible via the following URL structure:

`/username`

For example, a user with the username `alex` would have a profile page at `/alex`.

---

# Hire a Friend Page (/hire)

## Purpose
The `/hire` page allows users to browse, search, and hire other users for social purposes (e.g., hang out, show around town, attend an event, be a buddy for a day). This is distinct from the general gig/service marketplace, focusing on real-life social connections and experiences.

---

## Key Features

### 1. Friend-for-Hire Profile Showcase
- **Grid/List of Available Friends:** Display user cards for people open to being hired as a friend.
- **Profile Card Includes:**
  - Avatar/profile photo
  - Display name & username
  - Short bio/introduction
  - Vibes/interests (tags or badges)
  - Location (distance from current user)
  - Hourly/flat rate (if set)
  - Availability status (online/available, busy, offline)
  - Quick action buttons: View Profile, Message, Hire

### 2. Filters & Search
- **Search Bar:** Search by name, interest, or location.
- **Filters:**
  - Purpose (e.g., hang out, event buddy, local guide, etc.)
  - Price range
  - Distance (nearby, city, global)
  - Availability (now, today, this week)
  - Vibes/interests
  - Gender (optional)
- **Sort Options:** Closest, best rated, price (low-high), newest.

### 3. Profile Detail Modal/Page
- **Expanded Profile View:** Clicking a card opens a modal or navigates to a profile page with:
  - Full bio, gallery/photos, detailed interests
  - Reviews/ratings from previous hires
  - Calendar/availability
  - "Hire Me" button (opens booking flow)
  - Message button (opens chat)
  - Social proof (badges, verification, response rate)

### 4. Booking & Payment Flow
- **Hire Request Modal:**
  - Select date/time, duration, location/meeting point
  - Optional message/introduction
  - Price summary and confirmation
- **Payment Integration:** Secure payment (deposit or full), with wallet/Stripe integration.
- **Booking Confirmation:** Both parties receive notification and can chat to finalize details.

### 5. Reviews & Ratings
- **After Experience:** Both parties can leave a review and star rating.
- **Display:** Average rating and review count on profile cards and detail view.

### 6. Responsive & Accessible Design
- **Mobile:** Cards stack, filters become dropdowns/sheets, booking flow is mobile-friendly.
- **Accessibility:** Keyboard navigation, ARIA labels, color contrast.

---

## User Flow

1. **Browse:** User lands on `/hire` and sees a grid/list of available friends-for-hire.
2. **Filter/Search:** User filters by purpose, price, distance, etc.
3. **View Profile:** User clicks a card to see more details.
4. **Hire:** User clicks "Hire Me," selects time, and confirms booking/payment.
5. **Chat:** User and hired friend can message to coordinate.
6. **Meet:** Experience happens in real life.
7. **Review:** Both users leave reviews.

---

## Data Model (Suggested)

- **profiles** (extended for hireable users)
  - `id`
  - `display_name`
  - `username`
  - `bio`
  - `avatar_url`
  - `vibes` (array)
  - `location` (point, city, country)
  - `is_hireable` (boolean)
  - `hire_purposes` (array: e.g., hang out, guide, event)
  - `hourly_rate` (decimal)
  - `availability` (calendar or status)
  - `rating` (float)
  - `review_count` (int)
  - `last_active` (timestamp)
  - `verification_status` (enum)

- **hire_bookings**
  - `id`
  - `hirer_id`
  - `hiree_id`
  - `purpose`
  - `date_time`
  - `duration`
  - `location`
  - `status` (pending, confirmed, completed, cancelled)
  - `price`
  - `review_id`

- **hire_reviews**
  - `id`
  - `booking_id`
  - `reviewer_id`
  - `reviewee_id`
  - `rating`
  - `text`
  - `created_at`

---

## Wireframe (Textual)

```
---------------------------------------------------------
|  [Search bar]  [Purpose ▼] [Price ▼] [Distance ▼] ... |
---------------------------------------------------------
|  [UserCard]  [UserCard]  [UserCard]  [UserCard]      |
|  [UserCard]  [UserCard]  [UserCard]  [UserCard]      |
---------------------------------------------------------
UserCard:
+-------------------+
| [Avatar]          |
| Name (@username)  |
| ⭐ 4.9 (23)        |
| "Let's explore!"  |
| [Hangout][Guide]  |
| $15/hr  2km away  |
| [View] [Message] [Hire] |
+-------------------+

[Profile Modal/Page]
--------------------------
| [Avatar] Name          |
| @username              |
| ⭐ 4.9 (23 reviews)     |
| [Gallery]              |
| Bio: ...               |
| Vibes: ...             |
| Purposes: ...          |
| Price: $15/hr          |
| Availability: ...      |
| [Hire Me] [Message]    |
| [Reviews Section]      |
--------------------------
```

---

## Admin/Moderation
- Admins can verify users, moderate reviews, and handle disputes.
- Users can report inappropriate behavior.

---

## Notes
- This page is **not** for general gigs/services (that's `/gigs`).
- All data is dynamic, no hardcoded profiles.
- Users can opt-in to be hireable via profile settings.

---

## Future Enhancements
- Group bookings (hire multiple friends at once)
- Video intro/profile verification
- Advanced availability calendar
- In-app video call for pre-meeting

--- 