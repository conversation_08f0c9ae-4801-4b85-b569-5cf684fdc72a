
import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { ActivityCreationValues, activityCreationSchema } from '@/schemas/activity-creation-schema';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ActivityLocationPicker } from '@/components/activity/ActivityLocationPicker';
import { ActivityMediaUploader } from '@/components/activity/ActivityMediaUploader';
import { ActivityTimePicker } from '@/components/activity/ActivityTimePicker';
import { ActivityVisibilitySelect } from '@/components/activity/ActivityVisibilitySelect';
import { ActivityQueueTypeSelect } from '@/components/activity/ActivityQueueTypeSelect';
import { ActivityCreationData } from '@/types/activity';
import { useCreateActivity } from '@/hooks/use-create-activity';
import { useAuth } from '@/hooks/use-auth';
import { cn } from "@/lib/utils";

interface StepActivityCreationModalProps {
  onClose: () => void;
  onComplete?: (activityId: string) => void;
  isOpen?: boolean; // Added this prop for compatibility
  onSuccess?: (activityId: string) => void; // Added this prop for compatibility
}

interface LocationValues {
  latitude: number;
  longitude: number;
}

interface StepActivityCreationValues {
  title: string;
  description: string;
  categoryId: string;
  location: LocationValues | null;
  address: string;
  startTime: Date | null;
  endTime: Date | null;
  maxParticipants: number | null;
  isPaid: boolean;
  price: number;
  visibility: 'public' | 'private' | 'unlisted';
  queueType: 'fifo' | 'priority';
  allowWaitlist: boolean;
}

export function StepActivityCreationModal({
  onClose,
  onComplete,
  isOpen,
  onSuccess
}: StepActivityCreationModalProps) {
  const [isCreating, setCreating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [mediaFiles, setMediaFiles] = useState<any[]>([]);
  const navigate = useNavigate();
  const { toast } = useToast();

  const defaultValues: StepActivityCreationValues = {
    title: "",
    description: "",
    categoryId: "",
    location: null,
    address: "",
    startTime: null,
    endTime: null,
    maxParticipants: null,
    isPaid: false,
    price: 0,
    visibility: 'public',
    queueType: 'fifo',
    allowWaitlist: true,
  };

  const form = useForm<StepActivityCreationValues>({
    resolver: zodResolver(activityCreationSchema as any),
    defaultValues,
    mode: "onChange"
  });

  const { user } = useAuth();
  const { createActivity } = useCreateActivity();

  const handleFinish = async () => {
    if (!form.getValues().startTime || !form.getValues().location || !user) {
      setError("Please complete all required steps");
      return;
    }

    try {
      setCreating(true);
      const values = form.getValues();

      // Format the activity submission data
      const activityData: ActivityCreationData = {
        title: values.title,
        description: values.description,
        location: {
          x: values.location.longitude,
          y: values.location.latitude,
          latitude: values.location.latitude,
          longitude: values.location.longitude
        },
        address: values.address || "",
        start_time: values.startTime.toISOString(),
        end_time: values.endTime?.toISOString(),
        category_id: values.categoryId,
        is_paid: values.isPaid,
        price: values.isPaid ? values.price : undefined,
        max_participants: values.maxParticipants,
        visibility: values.visibility,
        queue_type: values.queueType,
        media_urls: mediaFiles.map(file => file.url),
      };

      const activityId = await createActivity(activityData);
      
      toast({
        title: "Activity Created",
        description: "Your activity has been successfully created!"
      });
      
      onClose();
      
      // Use the appropriate callback
      if (onComplete) {
        onComplete(activityId);
      } else if (onSuccess) {
        onSuccess(activityId);
      }

      // Navigate to the new activity
      navigate(`/activity/${activityId}`);
    } catch (error) {
      console.error("Error creating activity:", error);
      setError(error instanceof Error ? error.message : "Failed to create activity");
    } finally {
      setCreating(false);
    }
  };

  const handleMediaUpload = useCallback((files: any[]) => {
    setMediaFiles(files);
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>Create New Activity</CardTitle>
        <CardDescription>Fill in the details to create your activity.</CardDescription>
      </CardHeader>
      <CardContent className="grid gap-6">
        {error && (
          <div className="p-4 bg-red-100 text-red-800 rounded-md">
            {error}
          </div>
        )}

        <Form {...form}>
          <form className="space-y-6">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Activity title" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Write a description for your activity"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select a category" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="sports">Sports</SelectItem>
                      <SelectItem value="education">Education</SelectItem>
                      <SelectItem value="community">Community</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            <ActivityLocationPicker
              form={form}
            />

            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input placeholder="Street address" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Separator />

            <ActivityTimePicker form={form} />

            <Separator />

            <FormField
              control={form.control}
              name="maxParticipants"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Max Participants</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Maximum number of participants"
                      value={field.value || ''}
                      onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : null)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <FormField
                control={form.control}
                name="isPaid"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Paid Activity</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Charge participants a fee to join your activity.
                      </p>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              {form.getValues().isPaid && (
                <FormField
                  control={form.control}
                  name="price"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Price</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          placeholder="Enter the price"
                          {...field}
                          onChange={(e) => field.onChange(Number(e.target.value))}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              )}
            </div>

            <Separator />

            <ActivityVisibilitySelect form={form} />
            <ActivityQueueTypeSelect form={form} />

            <FormField
              control={form.control}
              name="allowWaitlist"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel className="text-base">Allow Waitlist</FormLabel>
                    <p className="text-sm text-muted-foreground">
                      Enable a waitlist for your activity.
                    </p>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />

            <Separator />

            <ActivityMediaUploader onMediaUpload={handleMediaUpload} />
          </form>
        </Form>
      </CardContent>
      <CardFooter className="flex justify-between">
        <Button variant="ghost" onClick={onClose}>Cancel</Button>
        <Button disabled={isCreating} onClick={handleFinish}>
          {isCreating ? "Creating..." : "Create Activity"}
        </Button>
      </CardFooter>
    </Card>
  );
}
