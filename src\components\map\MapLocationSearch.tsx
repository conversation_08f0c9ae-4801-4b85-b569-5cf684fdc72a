
import React, { useState, useEffect, useRef } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, Loader2, XCircle, MapPin } from "lucide-react";
import { cn } from "@/lib/utils";
import { useToast } from '@/hooks/use-toast';
import { MapLocation } from '@/types/location';

interface MapLocationSearchProps {
  onSearch: (query: string) => void;
  onLocationSelect?: (location: MapLocation) => void;
  isLoading?: boolean;
  className?: string;
  mapboxToken?: string;
}

export function MapLocationSearch({
  onSearch,
  onLocationSelect,
  isLoading = false,
  className,
  mapboxToken
}: MapLocationSearchProps) {
  const [query, setQuery] = useState('');
  const [searchResults, setSearchResults] = useState<any[]>([]);
  const [showResults, setShowResults] = useState(false);
  const [searchLoading, setSearchLoading] = useState(false);
  const { toast } = useToast();
  const resultsRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Handle clicks outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (resultsRef.current && !resultsRef.current.contains(event.target as Node) &&
          inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  const performSearch = async (searchQuery: string) => {
    if (!searchQuery.trim() || !mapboxToken) return;

    onSearch(searchQuery.trim());

    if (onLocationSelect) {
      try {
        setSearchLoading(true);

        // Search for location using Mapbox geocoding API with improved parameters
        const response = await fetch(
          `https://api.mapbox.com/geocoding/v5/mapbox.places/${encodeURIComponent(searchQuery)}.json?access_token=${mapboxToken}&types=place,locality,neighborhood,address,poi&limit=5&autocomplete=true&fuzzyMatch=true`
        );

        if (!response.ok) {
          throw new Error('Search request failed');
        }

        const data = await response.json();
        setSearchResults(data.features || []);
        setShowResults(data.features && data.features.length > 0);

        // If there's a result, select the first one automatically
        if (data.features && data.features.length > 0) {
          console.log("Found search results:", data.features.length);
        } else {
          toast({
            title: "No results found",
            description: "Try a different search term",
          });
        }
      } catch (error) {
        console.error('Error searching location:', error);
        toast({
          title: "Search Error",
          description: "Failed to search for location",
          variant: "destructive"
        });
      } finally {
        setSearchLoading(false);
      }
    }
  };

  // Debounce search to improve performance
  useEffect(() => {
    const timer = setTimeout(() => {
      if (query.trim().length >= 3) {
        performSearch(query);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [query]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim()) return;

    performSearch(query.trim());
  };

  const handleLocationSelect = (location: any) => {
    if (!location || !location.center) return;

    const [lng, lat] = location.center;

    // First, hide the results dropdown
    setShowResults(false);

    // Update input with selected location name
    setQuery(location.place_name || '');

    // Add a small delay before calling the parent's onLocationSelect
    // This prevents the map from being recreated while the dropdown is still visible
    // which was causing navigation issues
    setTimeout(() => {
      if (onLocationSelect) {
        console.log("Selecting location from search:", location.place_name);
        onLocationSelect({
          lng,
          lat,
          address: location.place_name || 'Selected Location',
          x: lng,
          y: lat
        });
      }
    }, 100);
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setQuery(value);

    // Clear results if input is empty
    if (!value.trim()) {
      setShowResults(false);
      setSearchResults([]);
    } else if (value.trim().length >= 3) {
      // Show loading indicator while debounce is in effect
      setSearchLoading(true);
    }
  };

  const clearSearch = () => {
    setQuery('');
    setShowResults(false);
    setSearchResults([]);
  };

  return (
    <div className={cn("relative", className)}>
      <form onSubmit={handleSubmit} className="relative">
        <div className="relative">
          <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
            <Search className="h-4 w-4" />
          </div>
          <Input
            ref={inputRef}
            type="text"
            placeholder="Search for a location..."
            value={query}
            onChange={handleChange}
            className="pl-10 pr-10"
            aria-label="Search for a location"
            autoComplete="off"
          />
          {query && (
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              {isLoading || searchLoading ? (
                <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
              ) : (
                <Button
                  type="button"
                  size="sm"
                  variant="ghost"
                  className="h-6 w-6 p-0"
                  onClick={clearSearch}
                >
                  <XCircle className="h-4 w-4 text-muted-foreground" />
                  <span className="sr-only">Clear</span>
                </Button>
              )}
            </div>
          )}
        </div>
        <Button
          type="submit"
          size="sm"
          variant="ghost"
          className="sr-only"
          disabled={isLoading || searchLoading || !query.trim()}
        >
          Search
        </Button>
      </form>

      {showResults && searchResults.length > 0 && (
        <div
          ref={resultsRef}
          className="absolute z-50 mt-1 w-full bg-background rounded-md shadow-lg border max-h-[200px] overflow-y-auto"
          style={{ zIndex: 100 }}
        >
          <ul className="py-1">
            {searchResults.map((result) => (
              <li
                key={result.id}
                className="px-4 py-2.5 hover:bg-accent cursor-pointer text-sm flex items-start gap-2 border-b border-gray-100 last:border-0"
                onClick={() => handleLocationSelect(result)}
              >
                <MapPin className="h-4 w-4 mt-0.5 flex-shrink-0 text-primary" />
                <div>
                  <div className="font-medium">{result.text}</div>
                  <div className="text-xs text-muted-foreground truncate">{result.place_name}</div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}

      {query.trim().length > 0 && query.trim().length < 3 && (
        <div className="text-xs text-muted-foreground mt-1 ml-1">
          Type at least 3 characters to search
        </div>
      )}
    </div>
  );
}
