
import React from 'react';
import { ChatMessage as ChatMessageType } from '@/types/chat';
import { useAuth } from '@/hooks/use-auth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { format } from 'date-fns';
import { getInitials } from '@/utils/get-initials';

interface ChatMessageProps {
  message: ChatMessageType;
  isLastMessage?: boolean;
  showSender?: boolean;
  conversationId: string;
}

export function ChatMessage({
  message,
  isLastMessage = false,
  showSender = true,
  conversationId
}: ChatMessageProps) {
  const { user } = useAuth();
  const isCurrentUser = user?.id === message.sender_id;

  const formatTime = (dateString: string) => {
    return format(new Date(dateString), 'h:mm a');
  };

  return (
    <div className={`flex gap-3 ${isCurrentUser ? 'flex-row-reverse' : ''}`}>
      {showSender && !isCurrentUser && (
        <Avatar className="h-8 w-8">
          <AvatarImage src={message.sender?.avatar_url} alt={message.sender?.display_name} />
          <AvatarFallback>
            {getInitials(message.sender?.display_name || '')}
          </AvatarFallback>
        </Avatar>
      )}
      {showSender && isCurrentUser && <div className="w-8" />}
      
      <div className={`space-y-1 ${isCurrentUser ? 'items-end' : ''} max-w-[75%]`}>
        {showSender && !isCurrentUser && (
          <p className="text-xs font-medium">
            {message.sender?.display_name || 'Unknown'}
          </p>
        )}
        
        <div className={`flex flex-col ${isCurrentUser ? 'items-end' : ''}`}>
          <div
            className={`p-3 rounded-lg ${
              isCurrentUser 
                ? 'bg-primary text-primary-foreground' 
                : 'bg-muted'
            }`}
          >
            {message.message_type === 'text' ? (
              <p className="text-sm whitespace-pre-wrap break-words">{message.content}</p>
            ) : (
              <p className="text-sm">[Media content]</p>
            )}
          </div>
          
          <div className={`flex text-xs text-muted-foreground mt-1 ${isCurrentUser ? 'justify-end' : ''}`}>
            <span>{formatTime(message.created_at)}</span>
          </div>
        </div>
      </div>
    </div>
  );
}
