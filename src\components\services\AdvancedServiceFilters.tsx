import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { 
  Search, 
  Filter, 
  X, 
  ChevronDown, 
  Star,
  MapPin,
  Clock,
  DollarSign,
  Award,
  TrendingUp
} from "lucide-react";
import { ServiceSearchFilters } from "@/hooks/use-advanced-service-search";
import { useServiceCategoriesGrouped } from "@/hooks/use-service-categories";
import { cn } from "@/lib/utils";

interface AdvancedServiceFiltersProps {
  filters: ServiceSearchFilters;
  onFiltersChange: (filters: ServiceSearchFilters) => void;
  onClearFilters: () => void;
  className?: string;
}

export function AdvancedServiceFilters({
  filters,
  onFiltersChange,
  onClearFilters,
  className
}: AdvancedServiceFiltersProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const { data: categoryGroups = [] } = useServiceCategoriesGrouped();

  const updateFilters = (updates: Partial<ServiceSearchFilters>) => {
    onFiltersChange({ ...filters, ...updates });
  };

  const toggleCategory = (categoryId: string) => {
    const currentCategories = filters.categoryIds || [];
    const newCategories = currentCategories.includes(categoryId)
      ? currentCategories.filter(id => id !== categoryId)
      : [...currentCategories, categoryId];
    
    updateFilters({ categoryIds: newCategories });
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.searchQuery) count++;
    if (filters.categoryIds?.length) count++;
    if (filters.priceRange) count++;
    if (filters.ratingRange) count++;
    if (filters.locationRadius) count++;
    if (filters.deliveryTime) count++;
    if (filters.experienceLevel?.length) count++;
    if (filters.availability?.length) count++;
    if (filters.isFeatured) count++;
    if (filters.isVerified) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  return (
    <Card className={cn("w-full", className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2 text-lg">
            <Filter className="h-5 w-5" />
            Search & Filters
            {activeFilterCount > 0 && (
              <Badge variant="secondary" className="ml-2">
                {activeFilterCount}
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {activeFilterCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onClearFilters}
                className="text-muted-foreground"
              >
                Clear All
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              <ChevronDown className={cn(
                "h-4 w-4 transition-transform",
                isExpanded && "rotate-180"
              )} />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {/* Search Bar */}
        <div className="relative mb-4">
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search services, skills, or keywords..."
            value={filters.searchQuery || ''}
            onChange={(e) => updateFilters({ searchQuery: e.target.value })}
            className="pl-10"
          />
          {filters.searchQuery && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1 h-8 w-8 p-0"
              onClick={() => updateFilters({ searchQuery: '' })}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Quick Filters */}
        <div className="flex flex-wrap gap-2 mb-4">
          <Button
            variant={filters.isFeatured ? "default" : "outline"}
            size="sm"
            onClick={() => updateFilters({ isFeatured: !filters.isFeatured })}
            className="h-8"
          >
            <Award className="h-3 w-3 mr-1" />
            Featured
          </Button>
          <Button
            variant={filters.isVerified ? "default" : "outline"}
            size="sm"
            onClick={() => updateFilters({ isVerified: !filters.isVerified })}
            className="h-8"
          >
            <Star className="h-3 w-3 mr-1" />
            Verified
          </Button>
          <Button
            variant={filters.sortBy === 'rating' ? "default" : "outline"}
            size="sm"
            onClick={() => updateFilters({ sortBy: filters.sortBy === 'rating' ? 'relevance' : 'rating' })}
            className="h-8"
          >
            <TrendingUp className="h-3 w-3 mr-1" />
            Top Rated
          </Button>
        </div>

        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleContent className="space-y-6">
            {/* Categories */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Categories</Label>
              <div className="space-y-4 max-h-60 overflow-y-auto">
                {categoryGroups.map((group) => (
                  <div key={group.name}>
                    <h4 className="text-sm font-medium text-muted-foreground mb-2">
                      {group.name}
                    </h4>
                    <div className="grid grid-cols-2 gap-2">
                      {group.categories.map((category) => (
                        <div key={category.id} className="flex items-center space-x-2">
                          <Checkbox
                            id={category.id}
                            checked={filters.categoryIds?.includes(category.id) || false}
                            onCheckedChange={() => toggleCategory(category.id)}
                          />
                          <Label
                            htmlFor={category.id}
                            className="text-sm cursor-pointer flex items-center gap-1"
                          >
                            <span>{category.icon}</span>
                            <span>{category.name}</span>
                            {category.gig_count > 0 && (
                              <span className="text-xs text-muted-foreground">
                                ({category.gig_count})
                              </span>
                            )}
                          </Label>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Price Range */}
            <div>
              <Label className="text-sm font-medium mb-3 block flex items-center gap-2">
                <DollarSign className="h-4 w-4" />
                Price Range
              </Label>
              <div className="space-y-3">
                <div className="flex items-center gap-4">
                  <div className="flex-1">
                    <Label htmlFor="min-price" className="text-xs text-muted-foreground">Min</Label>
                    <Input
                      id="min-price"
                      type="number"
                      placeholder="$0"
                      value={filters.priceRange?.min || ''}
                      onChange={(e) => updateFilters({
                        priceRange: {
                          min: parseInt(e.target.value) || 0,
                          max: filters.priceRange?.max || 1000
                        }
                      })}
                    />
                  </div>
                  <div className="flex-1">
                    <Label htmlFor="max-price" className="text-xs text-muted-foreground">Max</Label>
                    <Input
                      id="max-price"
                      type="number"
                      placeholder="$1000"
                      value={filters.priceRange?.max || ''}
                      onChange={(e) => updateFilters({
                        priceRange: {
                          min: filters.priceRange?.min || 0,
                          max: parseInt(e.target.value) || 1000
                        }
                      })}
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Rating Filter */}
            <div>
              <Label className="text-sm font-medium mb-3 block flex items-center gap-2">
                <Star className="h-4 w-4" />
                Minimum Rating
              </Label>
              <div className="space-y-2">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center space-x-2">
                    <Checkbox
                      id={`rating-${rating}`}
                      checked={filters.ratingRange?.min === rating}
                      onCheckedChange={(checked) => {
                        if (checked) {
                          updateFilters({ ratingRange: { min: rating, max: 5 } });
                        } else {
                          updateFilters({ ratingRange: undefined });
                        }
                      }}
                    />
                    <Label htmlFor={`rating-${rating}`} className="flex items-center gap-1 cursor-pointer">
                      <div className="flex">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={cn(
                              "h-3 w-3",
                              i < rating ? "fill-yellow-400 text-yellow-400" : "text-gray-300"
                            )}
                          />
                        ))}
                      </div>
                      <span className="text-sm">& up</span>
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Delivery Time */}
            <div>
              <Label className="text-sm font-medium mb-3 block flex items-center gap-2">
                <Clock className="h-4 w-4" />
                Delivery Time
              </Label>
              <Select
                value={filters.deliveryTime?.toString() || ''}
                onValueChange={(value) => updateFilters({ deliveryTime: parseInt(value) || undefined })}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Any delivery time" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="">Any delivery time</SelectItem>
                  <SelectItem value="1">1 day</SelectItem>
                  <SelectItem value="3">3 days</SelectItem>
                  <SelectItem value="7">1 week</SelectItem>
                  <SelectItem value="14">2 weeks</SelectItem>
                  <SelectItem value="30">1 month</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Experience Level */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Experience Level</Label>
              <div className="space-y-2">
                {['beginner', 'intermediate', 'expert'].map((level) => (
                  <div key={level} className="flex items-center space-x-2">
                    <Checkbox
                      id={level}
                      checked={filters.experienceLevel?.includes(level as any) || false}
                      onCheckedChange={(checked) => {
                        const currentLevels = filters.experienceLevel || [];
                        const newLevels = checked
                          ? [...currentLevels, level as any]
                          : currentLevels.filter(l => l !== level);
                        updateFilters({ experienceLevel: newLevels });
                      }}
                    />
                    <Label htmlFor={level} className="text-sm cursor-pointer capitalize">
                      {level}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Sort Options */}
            <div>
              <Label className="text-sm font-medium mb-3 block">Sort By</Label>
              <Select
                value={filters.sortBy || 'relevance'}
                onValueChange={(value) => updateFilters({ sortBy: value as any })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="relevance">Relevance</SelectItem>
                  <SelectItem value="rating">Highest Rated</SelectItem>
                  <SelectItem value="popularity">Most Popular</SelectItem>
                  <SelectItem value="price_low">Price: Low to High</SelectItem>
                  <SelectItem value="price_high">Price: High to Low</SelectItem>
                  <SelectItem value="newest">Newest First</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
