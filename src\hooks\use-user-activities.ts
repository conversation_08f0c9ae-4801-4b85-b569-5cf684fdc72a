
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Activity } from '@/types/activity';
import { useAuth } from './use-auth';
import { toUnifiedLocation } from '@/utils/location-normalizer';

export interface UseUserActivitiesOptions {
  limit?: number;
  filter?: 'hosting' | 'participating' | 'all';
  includeCompleted?: boolean;
  includeCancelled?: boolean;
}

export function useUserActivities(options: UseUserActivitiesOptions = {}) {
  const { 
    limit = 20, 
    filter = 'all', 
    includeCompleted = false,
    includeCancelled = false
  } = options;
  const { user } = useAuth();

  const query = useQuery({
    queryKey: ['user-activities', user?.id, limit, filter, includeCompleted, includeCancelled],
    queryFn: async (): Promise<Activity[]> => {
      if (!user?.id) return [];

      try {
        let query = supabase
          .from('activities')
          .select('*');

        if (filter === 'hosting') {
          query = query.eq('host_id', user.id);
        } else if (filter === 'participating') {
          return [];
        }

        const statusFilters: string[] = ['active'];
        if (includeCompleted) statusFilters.push('completed');
        if (includeCancelled) statusFilters.push('cancelled');
        query = query.in('status', statusFilters);

        query = query.order('start_time', { ascending: true }).limit(limit);

        const { data, error } = await query;
        
        if (error) throw error;

        return (data || []).map((activity: any): Activity => {
          const location = toUnifiedLocation(activity.location);

          return {
            id: activity.id,
            title: activity.title,
            description: activity.description || '',
            start_time: activity.start_time,
            end_time: activity.end_time,
            location: location,
            address: activity.address,
            host_id: activity.host_id,
            host: undefined,
            category: undefined,
            is_paid: activity.is_paid || false,
            price: activity.price,
            max_participants: activity.max_participants,
            current_participants: 0,
            media_urls: activity.media_urls || [],
            created_at: activity.created_at,
            updated_at: activity.updated_at,
            status: activity.status as 'active' | 'cancelled' | 'completed',
            visibility: (activity.visibility as 'public' | 'private' | 'unlisted') || 'public',
            queue_type: (activity.queue_type as 'fcfs' | 'priority' | 'fifo') || 'fifo',
            allow_waitlist: activity.allow_waitlist ?? true,
            group_chat_id: activity.group_chat_id,
            moderation_status: '',
            is_verified: false
          };
        });
      } catch (error) {
        console.error('Error fetching user activities:', error);
        throw error;
      }
    },
    enabled: !!user?.id,
  });

  return {
    data: query.data || [],
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch
  };
}
