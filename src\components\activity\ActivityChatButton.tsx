
import React from 'react';
import { Button } from "@/components/ui/button";
import { MessageSquare } from 'lucide-react';
import { Activity } from '@/types/activity';
import { useActivityRouter } from './ActivityRouter';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';

interface ActivityChatButtonProps {
  activity: Activity;
  className?: string;
  variant?: string;
  onClick?: (e: React.MouseEvent) => void;
  size?: "default" | "sm" | "lg";
}

export function ActivityChatButton({ 
  activity, 
  className = "", 
  variant = "outline",
  onClick,
  size = "default"
}: ActivityChatButtonProps) {
  const { toast } = useToast();
  const { openModal } = useActivityRouter();
  const { user } = useAuth();

  const handleClick = (e: React.MouseEvent) => {
    // Stop event propagation to prevent card click handler from firing
    e.stopPropagation();
    
    if (onClick) {
      onClick(e);
      return;
    }

    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to join the chat",
        variant: "destructive"
      });
      return;
    }

    // Open the activity modal with chat tab focused - only pass activity
    openModal(activity);
    toast({
      title: "Chat opened",
      description: `Joining chat for ${activity.title}`,
    });
  };

  return (
    <Button 
      variant={variant as any}
      size={size} 
      className={className} 
      onClick={handleClick}
      title="Join activity chat"
    >
      <MessageSquare className="h-4 w-4 mr-2" />
      CHAT
    </Button>
  );
}
