import React from 'react';
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, MapPin, Users, Clock, Share2, MessageSquare } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useRespondToActivityProposal } from '@/hooks/use-activity-proposals';
import { format } from 'date-fns';
import { Activity } from '@/types/activity';
import { useNavigate } from 'react-router-dom';

interface ChatActivityMessageProps {
  messageId: string;
  messageType: 'activity_proposal' | 'activity_share';
  activityId: string;
  activityTitle?: string;
  proposalStatus?: 'pending' | 'accepted' | 'declined';
  senderId: string;
  senderName?: string;
  content: string;
  createdAt: string;
  metadata?: any;
}

export function ChatActivityMessage({
  messageId,
  messageType,
  activityId,
  activityTitle,
  proposalStatus,
  senderId,
  senderName,
  content,
  createdAt,
  metadata
}: ChatActivityMessageProps) {
  const { user } = useAuth();
  const navigate = useNavigate();
  const respondToProposal = useRespondToActivityProposal();

  // Fetch full activity details
  const { data: activity, isLoading } = useQuery({
    queryKey: ['chat-activity-details', activityId],
    queryFn: async (): Promise<Activity | null> => {
      const { data, error } = await supabase
        .from('activities')
        .select(`
          *,
          host:host_id (
            id,
            display_name,
            username,
            avatar_url
          ),
          category:category_id (
            id,
            name,
            icon
          )
        `)
        .eq('id', activityId)
        .single();

      if (error) {
        console.error('Error fetching activity details:', error);
        return null;
      }

      return data as Activity;
    },
    enabled: !!activityId
  });

  const handleResponse = (response: 'accepted' | 'declined') => {
    respondToProposal.mutate({
      messageId,
      response
    });
  };

  const handleViewActivity = () => {
    navigate(`/activities/${activityId}`);
  };

  const handleJoinChat = () => {
    if (activity?.group_chat_id) {
      navigate(`/chat?conversation=${activity.group_chat_id}`);
    }
  };

  const isOwnMessage = user?.id === senderId;
  const canRespond = !isOwnMessage && messageType === 'activity_proposal' && proposalStatus === 'pending';
  const isProposal = messageType === 'activity_proposal';

  if (isLoading) {
    return (
      <Card className="max-w-sm">
        <CardContent className="p-4">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!activity) {
    return (
      <Card className="max-w-sm border-red-200">
        <CardContent className="p-4">
          <p className="text-red-600 text-sm">Activity not found or no longer available</p>
        </CardContent>
      </Card>
    );
  }

  const getStatusBadge = () => {
    if (!isProposal) return null;

    switch (proposalStatus) {
      case 'accepted':
        return <Badge variant="default" className="bg-green-100 text-green-800">Accepted</Badge>;
      case 'declined':
        return <Badge variant="secondary" className="bg-red-100 text-red-800">Declined</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  const getHeaderIcon = () => {
    if (isProposal) {
      return <Calendar className="w-4 h-4 text-blue-600" />;
    }
    return <Share2 className="w-4 h-4 text-green-600" />;
  };

  const getHeaderText = () => {
    if (isProposal) {
      return 'Activity Proposal';
    }
    return 'Shared Activity';
  };

  const getCardStyle = () => {
    if (isProposal) {
      return "border-blue-200 bg-blue-50/50";
    }
    return "border-green-200 bg-green-50/50";
  };

  return (
    <Card className={`max-w-sm ${getCardStyle()}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
              isProposal ? 'bg-blue-100' : 'bg-green-100'
            }`}>
              {getHeaderIcon()}
            </div>
            <div>
              <p className="text-sm font-medium">{getHeaderText()}</p>
              <p className="text-xs text-muted-foreground">
                from {senderName || 'Someone'} • {format(new Date(createdAt), 'MMM d, h:mm a')}
              </p>
            </div>
          </div>
          {getStatusBadge()}
        </div>
      </CardHeader>

      <CardContent className="pt-0">
        {content && (
          <p className="text-sm text-muted-foreground mb-3 italic">"{content}"</p>
        )}

        <div className="bg-white rounded-lg p-3 border">
          <h4 className="font-semibold text-sm mb-2">{activity.title}</h4>

          {activity.description && (
            <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
              {activity.description}
            </p>
          )}

          <div className="space-y-1">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Clock className="w-3 h-3" />
              <span>{format(new Date(activity.start_time), 'MMM d, yyyy • h:mm a')}</span>
            </div>

            {activity.address && (
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <MapPin className="w-3 h-3" />
                <span className="line-clamp-1">{activity.address}</span>
              </div>
            )}

            {activity.max_participants && (
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <Users className="w-3 h-3" />
                <span>Max {activity.max_participants} participants</span>
              </div>
            )}

            {activity.host && (
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <span>Hosted by {activity.host.display_name || activity.host.username}</span>
              </div>
            )}
          </div>
        </div>

        {/* Action buttons */}
        <div className="flex gap-2 mt-3">
          <Button
            size="sm"
            variant="outline"
            onClick={handleViewActivity}
            className="flex-1"
          >
            View Details
          </Button>

          {activity.group_chat_id && (
            <Button
              size="sm"
              variant="outline"
              onClick={handleJoinChat}
              className="flex-1"
            >
              <MessageSquare className="w-3 h-3 mr-1" />
              Chat
            </Button>
          )}
        </div>

        {/* Proposal response buttons */}
        {canRespond && (
          <div className="flex gap-2 mt-2">
            <Button
              size="sm"
              onClick={() => handleResponse('accepted')}
              disabled={respondToProposal.isPending}
              className="flex-1"
            >
              Accept
            </Button>
            <Button
              size="sm"
              variant="outline"
              onClick={() => handleResponse('declined')}
              disabled={respondToProposal.isPending}
              className="flex-1"
            >
              Decline
            </Button>
          </div>
        )}

        {isOwnMessage && isProposal && proposalStatus === 'pending' && (
          <p className="text-xs text-muted-foreground mt-2 text-center">
            Waiting for response...
          </p>
        )}
      </CardContent>
    </Card>
  );
}
