
import React from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AddressMinimap } from '@/components/map/MapMarkers/AddressMinimap';
import { useAddressMinimap } from '@/hooks/use-address-minimap';
import { MapPin } from 'lucide-react';

interface AddressLocationPickerProps {
  initialLocation?: { lng: number; lat: number };
  onLocationSelect?: (location: {
    lng: number;
    lat: number;
    address: string;
    coordinates: [number, number];
  }) => void;
  title?: string;
  description?: string;
  className?: string;
}

export function AddressLocationPicker({
  initialLocation,
  onLocationSelect,
  title = "Select Location",
  description = "Adjust the marker to select the exact location",
  className
}: AddressLocationPickerProps) {
  const {
    location,
    handleSaveLocation
  } = useAddressMinimap({
    initialLocation,
    onLocationSaved: onLocationSelect
  });

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center">
          <MapPin className="h-5 w-5 mr-2 text-primary" />
          {title}
        </CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <AddressMinimap
          initialLocation={initialLocation || location}
          onSaveLocation={handleSaveLocation}
          height="300px"
          editable={true}
          showStyleToggle={true}
        />
        
        {location?.address && (
          <div className="text-sm">
            <p className="font-medium">Selected address:</p>
            <p className="text-muted-foreground">{location.address}</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
