/**
 * This is a simplified script to fix the admin conversation.
 * It should be run in the browser console on the chat page.
 * 
 * Copy and paste this entire script into the browser console and press Enter.
 */

(async function() {
  try {
    console.log('Starting admin conversation fix script...');
    
    // Step 1: Find or create the admin conversation
    console.log('Checking for existing admin conversation...');
    const { data: existingConversation, error: queryError } = await window.supabase
      .from('chat_conversations')
      .select('*')
      .or('is_announcement_only.eq.true,is_admin_conversation.eq.true')
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (queryError) {
      console.error('Error querying admin conversation:', queryError);
      throw queryError;
    }

    let conversationId;

    if (existingConversation) {
      console.log(`Found existing admin conversation: ${existingConversation.id}`);
      conversationId = existingConversation.id;
      
      // Make sure both flags are set correctly
      if (!existingConversation.is_admin_conversation || !existingConversation.is_announcement_only) {
        console.log('Updating admin conversation flags...');
        await window.supabase
          .from('chat_conversations')
          .update({
            is_admin_conversation: true,
            is_announcement_only: true,
            is_group: true
          })
          .eq('id', conversationId);
      }
    } else {
      console.log('No admin conversation found, creating one...');
      const { data: newConversation, error: createError } = await window.supabase
        .from('chat_conversations')
        .insert({
          is_announcement_only: true,
          is_admin_conversation: true,
          is_group: true,
          last_message: 'Welcome to BuddySurf! This is the official announcement channel.',
          last_message_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      console.log(`Created new admin conversation: ${newConversation.id}`);
      conversationId = newConversation.id;
    }

    // Step 2: Check for welcome messages
    console.log('Checking for welcome messages...');
    const { data: messages, error: messagesError } = await window.supabase
      .from('messages')
      .select('id, content')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })
      .limit(5);

    if (messagesError) {
      console.error('Error checking messages:', messagesError);
      throw messagesError;
    }

    // Step 3: Add welcome message if none exists
    if (!messages || messages.length === 0) {
      console.log('No welcome messages found, adding one...');
      
      // Get current user
      const user = window.auth?.user;
      if (!user) {
        console.error('No user found. Please make sure you are logged in.');
        return;
      }
      
      // Add welcome message
      const { error: welcomeError } = await window.supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: user.id,
          recipient_id: user.id,
          content: 'Welcome to BuddySurf! 👋 This is the official announcement channel where you\'ll receive important updates and information. Stay tuned for exciting news and features!',
          is_read: false,
          is_admin_message: true,
          created_at: new Date().toISOString()
        });

      if (welcomeError) {
        console.error('Error adding welcome message:', welcomeError);
        throw welcomeError;
      }
      
      console.log('Welcome message added successfully.');
      
      // Update the conversation's last message
      await window.supabase
        .from('chat_conversations')
        .update({
          last_message: 'Welcome to BuddySurf! 👋 This is the official announcement channel.',
          last_message_at: new Date().toISOString()
        })
        .eq('id', conversationId);
    } else {
      console.log(`Found ${messages.length} existing messages in the admin conversation.`);
    }

    // Step 4: Ensure current user is a participant
    console.log('Ensuring current user is a participant in the admin conversation...');
    
    const user = window.auth?.user;
    if (!user) {
      console.error('No user found. Please make sure you are logged in.');
      return;
    }
    
    // Get existing participants
    const { data: existingParticipants, error: participantsError } = await window.supabase
      .from('chat_participants')
      .select('user_id')
      .eq('conversation_id', conversationId)
      .eq('user_id', user.id);
    
    if (participantsError) {
      throw participantsError;
    }
    
    // Add current user if not already a participant
    if (!existingParticipants || existingParticipants.length === 0) {
      console.log(`Adding current user as participant...`);
      const { error: insertError } = await window.supabase
        .from('chat_participants')
        .insert({
          conversation_id: conversationId,
          user_id: user.id,
          joined_at: new Date().toISOString()
        });
      
      if (insertError) {
        throw insertError;
      }
      
      console.log('Current user added as participant successfully.');
    } else {
      console.log('Current user is already a participant.');
    }

    console.log('Admin conversation fix completed successfully!');
    console.log('Please refresh the page to see the changes.');
    
    // Invalidate React Query cache if available
    if (window.queryClient) {
      window.queryClient.invalidateQueries(['admin-conversation']);
      window.queryClient.invalidateQueries(['chat-conversations']);
      console.log('React Query cache invalidated.');
    }
    
  } catch (error) {
    console.error('Error fixing admin conversation:', error);
  }
})();
