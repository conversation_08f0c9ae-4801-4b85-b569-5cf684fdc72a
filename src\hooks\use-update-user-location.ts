
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { UserLocation } from "./use-user-locations";
import { useAuth } from "./use-auth";
import { useToast } from "./use-toast";

export const useUpdateUserLocation = () => {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ x, y, bio }: { x: number; y: number; bio?: string }) => {
      if (!user) throw new Error("You must be logged in to update your location.");

      // Ensure x and y are valid numbers to prevent PostgreSQL point errors
      if (typeof x !== 'number' || isNaN(x) || typeof y !== 'number' || isNaN(y)) {
        throw new Error("Invalid coordinates provided");
      }

      // Update both user_locations and profiles tables
      const { data: userLocationData, error: userLocationError } = await supabase
        .from("user_locations")
        .upsert(
          [{
            user_id: user.id,
            location: `POINT(${x} ${y})`,
            bio: bio || null,
            updated_at: new Date().toISOString()
          }],
          { onConflict: "user_id" }
        )
        .select("*")
        .maybeSingle();
      
      if (userLocationError) throw userLocationError;
      
      // Also update the profiles table with the same location
      const { error: profileError } = await supabase
        .from("profiles")
        .update({
          location: `POINT(${x} ${y})`,
          updated_at: new Date().toISOString()
        })
        .eq("id", user.id);
      
      if (profileError) throw profileError;
      
      return userLocationData as UserLocation;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userLocations"] });
      queryClient.invalidateQueries({ queryKey: ["profile"] });
      toast({
        title: "Location Updated",
        description: "Your location has been updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error updating location",
        description: error.message,
        variant: "destructive",
      });
    }
  });
};
