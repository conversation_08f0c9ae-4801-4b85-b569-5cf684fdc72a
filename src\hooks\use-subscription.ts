
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/use-auth";

export interface Subscription {
  id: string;
  user_id: string;
  plan: "weekly" | "monthly" | "lifetime";
  status: "active" | "inactive" | "cancelled" | "pending";
  started_at: string;
  expires_at: string | null;
  payment_id: string | null;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionData {
  active: boolean;
  plan: string | null;
  subscription: Subscription | null;
  benefits: {
    unlimitedActivities: boolean;
    priorityListing: boolean;
    advancedFilters: boolean;
    noAds: boolean;
    premiumSupport: boolean;
  };
  daysRemaining: number | null;
}

export function useSubscription() {
  const { user, isLoading: isAuthLoading } = useAuth();

  return useQuery({
    queryKey: ["subscription", user?.id],
    queryFn: async (): Promise<SubscriptionData> => {
      if (!user) {
        return { 
          active: false, 
          plan: null, 
          subscription: null, 
          benefits: {
            unlimitedActivities: false,
            priorityListing: false,
            advancedFilters: false,
            noAds: false,
            premiumSupport: false
          },
          daysRemaining: null
        };
      }

      const { data, error } = await supabase.functions.invoke("check-subscription", {});

      if (error) {
        console.error("Error checking subscription:", error);
        throw error;
      }
      
      // Calculate days remaining if there's an expiration date
      let daysRemaining = null;
      if (data.subscription?.expires_at) {
        const expiresAt = new Date(data.subscription.expires_at);
        const today = new Date();
        const differenceInTime = expiresAt.getTime() - today.getTime();
        daysRemaining = Math.ceil(differenceInTime / (1000 * 3600 * 24));
      }
      
      // Define benefits based on plan
      const benefits = {
        unlimitedActivities: !!data.active,
        priorityListing: data.plan === 'monthly' || data.plan === 'lifetime',
        advancedFilters: !!data.active,
        noAds: data.plan === 'lifetime',
        premiumSupport: data.plan === 'lifetime'
      };

      return {
        ...data,
        benefits,
        daysRemaining
      };
    },
    enabled: !!user && !isAuthLoading,
  });
}
