
import { useEffect, useState, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { UserLocation } from "@/hooks/use-user-locations";

// Online status is managed via polling
export interface MapUserPresence {
  user_id: string;
  location: { x: number; y: number };
  avatar_url?: string;
  display_name?: string;
  username?: string;
  is_online: boolean;
  updated_at: string;
  bio?: string;
}

export function useMapPresence() {
  const [users, setUsers] = useState<MapUserPresence[]>([]);
  
  useEffect(() => {
    // Fetch initial users when the component mounts
    const fetchInitialUsers = async () => {
      try {
        const { data, error } = await supabase
          .from("user_locations")
          .select(`
            *,
            profiles:user_id(username, display_name, avatar_url, bio)
          `);
        
        if (error) {
          console.error('Error fetching user locations:', error);
          return;
        }
        
        if (data) {
          const formattedUsers: MapUserPresence[] = data.map(item => {
            // Safe access to location data
            const locationX = typeof item.location === 'object' && item.location ? Number((item.location as any).x || 0) : 0;
            const locationY = typeof item.location === 'object' && item.location ? Number((item.location as any).y || 0) : 0;
            
            // Safe access to profiles data
            const profiles = item.profiles as any || {};
            
            return {
              user_id: item.user_id,
              location: { 
                x: locationX, 
                y: locationY 
              },
              avatar_url: profiles.avatar_url || null,
              display_name: profiles.display_name || null,
              username: profiles.username || null,
              is_online: true,
              updated_at: item.updated_at,
              bio: profiles.bio || item.bio || null,
            };
          });
          
          setUsers(formattedUsers);
        }
      } catch (error) {
        console.error('Error fetching initial users:', error);
      }
    };

    fetchInitialUsers();
    
    // Set up polling for user updates
    const interval = setInterval(fetchInitialUsers, 15000);
    
    return () => {
      clearInterval(interval);
    };
  }, []);

  return { users };
}
