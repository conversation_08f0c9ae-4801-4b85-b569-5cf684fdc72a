
import React, { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import { useRealtimeLocationSystem } from '@/hooks/use-realtime-location-system';
import { UserAvatar } from '@/components/user/UserAvatar';
import { useToast } from '@/hooks/use-toast';

interface UserLocationMarkerProps {
  map: mapboxgl.Map;
}

export function UserLocationMarker({ map }: UserLocationMarkerProps) {
  const markerRef = useRef<mapboxgl.Marker | null>(null);
  const { myCoords, isConnected, lastUpdate } = useRealtimeLocationSystem();
  const { toast } = useToast();

  // Handle marker creation and updates
  useEffect(() => {
    console.log('Location update received:', myCoords);
    
    if (!map || !myCoords) return;

    const createMarkerElement = () => {
      const el = document.createElement('div');
      el.className = 'user-marker current-user';
      el.style.zIndex = '1000';
      
      const avatarContainer = document.createElement('div');
      avatarContainer.className = 'relative w-12 h-12 transform transition-transform duration-300 hover:scale-110';
      
      const pulseRing = document.createElement('div');
      pulseRing.className = 'absolute -inset-1 bg-primary/30 rounded-full animate-ping';
      
      const markerContent = document.createElement('div');
      markerContent.className = 'relative w-full h-full bg-primary rounded-full border-4 border-white shadow-lg';
      
      const locationIcon = document.createElement('div');
      locationIcon.className = 'absolute inset-0 flex items-center justify-center text-white';
      locationIcon.innerHTML = `
        <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="3"></circle>
          <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1-1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
        </svg>
      `;

      avatarContainer.appendChild(pulseRing);
      avatarContainer.appendChild(markerContent);
      markerContent.appendChild(locationIcon);
      el.appendChild(avatarContainer);
      
      return el;
    };

    // Update or create marker
    if (!markerRef.current) {
      const element = createMarkerElement();
      markerRef.current = new mapboxgl.Marker({
        element,
        anchor: 'center'
      })
        .setLngLat([myCoords.lng, myCoords.lat])
        .addTo(map);
        
      console.log('Created new user location marker');
    } else {
      markerRef.current.setLngLat([myCoords.lng, myCoords.lat]);
      console.log('Updated user location marker position');
    }

    return () => {
      if (markerRef.current) {
        markerRef.current.remove();
        markerRef.current = null;
      }
    };
  }, [map, myCoords]);

  // Connection status feedback
  useEffect(() => {
    if (!isConnected) {
      toast({
        title: "Location tracking disconnected",
        description: "Trying to reconnect...",
        variant: "destructive",
      });
    }
  }, [isConnected, toast]);

  return null;
}
