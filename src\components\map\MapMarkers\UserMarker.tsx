
import React from 'react';
import { UnifiedMapUser } from '@/types/map';
import { UserAvatar } from '@/components/user/UserAvatar';
import { cn } from '@/lib/utils';

interface UserMarkerProps {
  user: UnifiedMapUser;
  onClick?: () => void;
  element?: HTMLElement;
  highlight?: boolean;
}

export function UserMarker({ user, onClick, element, highlight = false }: UserMarkerProps) {
  const displayName = user.display_name || user.username || 'User';
  
  // When used as a standalone React component (not with mapbox)
  if (!element) {
    return (
      <div 
        className={cn(
          "user-marker cursor-pointer group relative",
          "transition-all duration-300 hover:scale-110 active:scale-95",
          highlight ? "z-20" : "z-10"
        )}
        data-user-id={user.user_id}
        onClick={onClick}
      >
        <div className={cn(
          "relative transform transition-transform duration-300",
          highlight ? "animate-pulse scale-110" : ""
        )}>
          <UserAvatar
            user={user}
            size="2xl"
            showVerified={user.is_verified}
            className={cn(
              "shadow-xl",
              highlight ? "border-primary" : "border-purple-600"
            )}
            showStatus={true}
            isOnline={user.is_online}
          />
        </div>
        
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full pointer-events-none">
          <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-lg shadow-md text-sm mt-2 font-medium">
            {displayName}
          </div>
        </div>
      </div>
    );
  }
  
  // If used with Mapbox, render into the element
  React.useEffect(() => {
    if (!element) return;
    
    // Create marker HTML structure with improved styling
    const markerHTML = `
      <div class="user-marker cursor-pointer transition-all group relative hover:scale-110 active:scale-95" data-user-id="${user.user_id}">
        <div class="relative ${highlight ? "animate-pulse" : ""}">
          <div class="w-20 h-20 rounded-full bg-white shadow-xl border-4 ${highlight ? "border-primary" : "border-purple-600"} overflow-hidden">
            ${user.avatar_url 
              ? `<img src="${user.avatar_url}" alt="${displayName}" class="w-full h-full object-cover" 
                  onerror="this.onerror=null; this.src=''; this.parentElement.innerHTML='<div class=\\'w-full h-full bg-secondary/10 text-primary flex items-center justify-center text-lg font-semibold\\'>${displayName.charAt(0).toUpperCase()}</div>';" />`
              : `<div class="w-full h-full bg-secondary/10 text-primary flex items-center justify-center text-lg font-semibold">
                  ${displayName.charAt(0).toUpperCase()}
                 </div>`
            }
          </div>
          ${user.is_online 
            ? `<div class="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-2 border-white shadow-md animate-pulse"></div>`
            : `<div class="absolute -bottom-1 -right-1 w-5 h-5 bg-gray-400 rounded-full border-2 border-white shadow-md"></div>`
          }
        </div>
        <div class="absolute bottom-0 left-1/2 transform -translate-x-1/2 translate-y-full pointer-events-none">
          <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap bg-white/95 backdrop-blur-sm px-3 py-1.5 rounded-lg shadow-md text-sm mt-2 font-medium z-50">
            ${displayName}
          </div>
        </div>
      </div>
    `;
    
    // Set the inner HTML of the element
    element.innerHTML = markerHTML;
    
    // Add click event listener
    if (onClick) {
      element.addEventListener('click', onClick);
    }
    
    return () => {
      if (onClick) {
        element.removeEventListener('click', onClick);
      }
    };
  }, [element, user, onClick, displayName, highlight]);
  
  // If used with mapbox, return null in React
  return null;
}
