
import React, { useState } from 'react';
import { Card } from "@/components/ui/card";
import { SavedLocation, useSavedLocations } from '@/hooks/use-saved-locations';
import { ScrollArea } from "@/components/ui/scroll-area";
import { Loader2, MapPin, Search, Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { SavedLocationCard } from "./SavedLocationCard";
import { useNavigate } from "react-router-dom";

interface SavedLocationsListProps {
  showMaps?: boolean;
}

export function SavedLocationsList({ showMaps = false }: SavedLocationsListProps) {
  const { savedLocations, isLoading, deleteLocation } = useSavedLocations();
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const navigate = useNavigate();
  
  const filteredLocations = savedLocations.filter(location => 
    location.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (location.description && location.description.toLowerCase().includes(searchQuery.toLowerCase())) ||
    (location.category && location.category.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handleNavigateToMap = (location: SavedLocation) => {
    // Navigate to the map page centered on this location
    navigate(`/meetmap?lng=${location.location.x}&lat=${location.location.y}&name=${encodeURIComponent(location.name)}`);
  };
  
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex gap-2">
        <div className="relative flex-1">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search saved locations..."
            className="pl-9"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button 
          variant="outline" 
          size="icon"
          onClick={() => setShowFilters(!showFilters)}
          className={showFilters ? "bg-primary/10" : ""}
        >
          <Filter className="h-4 w-4" />
        </Button>
      </div>

      {showFilters && (
        <div className="bg-muted/30 p-3 rounded-md">
          <h4 className="text-sm font-medium mb-2">Filter by category</h4>
          <div className="flex flex-wrap gap-2">
            {/* We'll implement this in future iterations */}
            <Button variant="outline" size="sm" className="text-xs">
              All
            </Button>
            <Button variant="ghost" size="sm" className="text-xs">
              Favorite
            </Button>
            <Button variant="ghost" size="sm" className="text-xs">
              Home
            </Button>
            <Button variant="ghost" size="sm" className="text-xs">
              Work
            </Button>
          </div>
        </div>
      )}

      <ScrollArea className="h-[600px]">
        <div className="space-y-4">
          {filteredLocations.map((location) => (
            <SavedLocationCard 
              key={location.id} 
              location={location} 
              showMap={showMaps}
              onNavigate={handleNavigateToMap}
              onDelete={(location) => {
                if (confirm("Are you sure you want to delete this saved location?")) {
                  deleteLocation(location.id);
                }
              }}
            />
          ))}
          
          {filteredLocations.length === 0 && (
            <div className="text-center py-8">
              <MapPin className="h-8 w-8 mx-auto text-muted-foreground opacity-50" />
              <p className="mt-2 text-sm text-muted-foreground">
                {savedLocations.length === 0 
                  ? "No saved locations yet" 
                  : "No locations match your search"}
              </p>
            </div>
          )}
        </div>
      </ScrollArea>
    </div>
  );
}
