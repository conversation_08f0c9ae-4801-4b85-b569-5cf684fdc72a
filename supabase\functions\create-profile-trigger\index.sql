
-- This SQL file will need to be manually executed in the Supabase SQL editor.
-- It creates a trigger to automatically create a profile when a new user signs up.

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into public.profiles
  INSERT INTO public.profiles (
    id, 
    username, 
    display_name, 
    avatar_url, 
    bio,
    updated_at,
    created_at
  )
  VALUES (
    NEW.id, 
    lower(split_part(NEW.email, '@', 1)), -- Generate a username from the email
    COALESCE(
      NEW.raw_user_meta_data->>'first_name' || ' ' || NEW.raw_user_meta_data->>'last_name',
      split_part(NEW.email, '@', 1)
    ), -- Use first_name and last_name from metadata if available
    NULL, -- No avatar initially
    NULL, -- No bio initially
    NOW(),
    NOW()
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger the function every time a user is created
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Make sure the profiles table can be read by anyone
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Allow anyone to read user profiles
CREATE POLICY "Anyone can read profiles" ON public.profiles
  FOR SELECT 
  USING (true);

-- Allow users to update their own profiles
CREATE POLICY "Users can update their own profiles" ON public.profiles
  FOR UPDATE
  USING (auth.uid() = id);
