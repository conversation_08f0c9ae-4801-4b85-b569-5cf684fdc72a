
import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useSupabaseRealtime } from './use-supabase-realtime';

interface ReadReceipt {
  user_id: string;
  display_name?: string;
  avatar_url?: string;
  last_read_at: string;
}

interface UseReadReceiptsResult {
  readReceipts: ReadReceipt[];
  markAsRead: (messageId: string) => Promise<void>;
}

/**
 * A hook for managing read receipts in a conversation
 */
export function useReadReceipts(conversationId: string): UseReadReceiptsResult {
  const { user } = useAuth();
  const [readReceipts, setReadReceipts] = useState<ReadReceipt[]>([]);
  const realtime = useSupabaseRealtime();

  // Set up realtime subscription to chat_participants table
  useEffect(() => {
    if (!conversationId || !user) return;

    // Initial fetch of read receipts
    const fetchReadReceipts = async () => {
      try {
        const { data, error } = await supabase
          .from('chat_participants')
          .select(`
            user_id,
            last_read_at,
            users:profiles!chat_participants_user_id_fkey(display_name, avatar_url)
          `)
          .eq('conversation_id', conversationId)
          .neq('user_id', user.id) // Don't include current user
          .not('last_read_at', 'is', null);
        
        if (error) {
          throw error;
        }

        // Format the data - making sure to handle potential missing join data
        const formatted = data.map(record => {
          let displayName: string | undefined;
          let avatarUrl: string | undefined;
          
          // Check if the users field exists and has the right structure
          if (record.users && typeof record.users === 'object') {
            const userObj = record.users as Record<string, any>;
            displayName = userObj.display_name;
            avatarUrl = userObj.avatar_url;
          }
          
          return {
            user_id: record.user_id,
            display_name: displayName,
            avatar_url: avatarUrl,
            last_read_at: record.last_read_at
          };
        });

        setReadReceipts(formatted);
      } catch (error) {
        console.error('Error fetching read receipts:', error);
      }
    };

    fetchReadReceipts();

    // Subscribe to changes
    const channelName = `read-receipts:${conversationId}`;
    const unsubscribe = realtime.subscribe(
      channelName,
      {
        table: 'chat_participants',
        filter: `conversation_id=eq.${conversationId}`
      },
      () => {
        // When chat_participants changes, refetch read receipts
        fetchReadReceipts();
      }
    );

    // Return cleanup function
    return unsubscribe;
  }, [conversationId, user, realtime]);

  // Function to mark a message as read
  const markAsRead = async (messageId: string) => {
    if (!conversationId || !user) return;

    try {
      const { error } = await supabase
        .from('chat_participants')
        .update({
          last_read_at: new Date().toISOString()
        })
        .eq('conversation_id', conversationId)
        .eq('user_id', user.id);

      if (error) {
        throw error;
      }
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  return { readReceipts, markAsRead };
}
