
import React, { useEffect, useState } from 'react';
import { useRealtimeLocationSystem } from '@/hooks/use-realtime-location-system';
import { useProfile } from '@/hooks/use-profile';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle, MapPin } from 'lucide-react';
import { Button } from "@/components/ui/button";

interface PeriodicLocationUpdaterProps {
  interval?: number; // in milliseconds, default 5 minutes
  onLocationUpdate?: () => void;
  showAlerts?: boolean;
}

export function PeriodicLocationUpdater({
  interval = 5 * 60 * 1000, // 5 minutes
  onLocationUpdate,
  showAlerts = true
}: PeriodicLocationUpdaterProps) {
  const { updateMyLocation, lastError } = useRealtimeLocationSystem();
  const { user } = useAuth();
  const { data: profile } = useProfile(user?.id);
  const { toast } = useToast();
  const [showPermissionAlert, setShowPermissionAlert] = useState(false);
  const [showDefaultLocationAlert, setShowDefaultLocationAlert] = useState(false);

  // Check if we have location permission
  useEffect(() => {
    if (!user) return;

    // Check if the browser supports geolocation
    if (!navigator.geolocation) {
      if (showAlerts) {
        setShowPermissionAlert(true);
      }
      return;
    }

    // Try to get the current position to check permissions
    navigator.permissions.query({ name: 'geolocation' }).then(result => {
      if (result.state === 'denied') {
        if (showAlerts) {
          setShowPermissionAlert(true);
        }
        
        // Check if we have a default location to fall back to
        if (profile && !profile.default_location) {
          setShowDefaultLocationAlert(true);
        }
      } else {
        setShowPermissionAlert(false);
      }
    });
  }, [user, profile, showAlerts]);

  // Set up periodic location updates
  useEffect(() => {
    if (!user) return;

    // Initial update
    updateMyLocation().then(success => {
      if (success && onLocationUpdate) {
        onLocationUpdate();
      }
    });

    // Set up interval
    const intervalId = setInterval(() => {
      updateMyLocation().then(success => {
        if (success && onLocationUpdate) {
          onLocationUpdate();
        }
      });
    }, interval);

    return () => clearInterval(intervalId);
  }, [user, updateMyLocation, interval, onLocationUpdate]);

  const handleRequestPermission = () => {
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        () => {
          setShowPermissionAlert(false);
          toast({
            title: "Location access granted",
            description: "Your location will now be updated periodically"
          });
          updateMyLocation();
        },
        (error) => {
          toast({
            title: "Location access denied",
            description: "Please enable location access in your browser settings",
            variant: "destructive"
          });
        }
      );
    }
  };

  if (!showAlerts) return null;

  return (
    <div className="space-y-4">
      {showPermissionAlert && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Location Permission Required</AlertTitle>
          <AlertDescription>
            We need your location permission to show you on the map.
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-2"
              onClick={handleRequestPermission}
            >
              <MapPin className="mr-2 h-4 w-4" />
              Enable Location
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {showDefaultLocationAlert && (
        <Alert>
          <MapPin className="h-4 w-4" />
          <AlertTitle>Set Default Location</AlertTitle>
          <AlertDescription>
            You haven't set a default location. This is used when your current location is unavailable.
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-2"
              onClick={() => {
                // Navigate to settings page to set default location
                window.location.href = '/settings?tab=location';
              }}
            >
              Set Default Location
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {lastError && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Location Update Error</AlertTitle>
          <AlertDescription>{lastError}</AlertDescription>
        </Alert>
      )}
    </div>
  );
}
