
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, DollarSign, Star } from "lucide-react";
import { Link } from "react-router-dom";
import type { Gig } from "@/hooks/use-gigs";

interface GigCardProps {
  gig: Gig;
}

export function GigCard({ gig }: GigCardProps) {
  return (
    <Card className="flex flex-col h-full hover:shadow-lg transition-shadow">
      <CardHeader className="flex-row gap-4 items-center pb-3">
        <Avatar className="h-10 w-10">
          <AvatarImage 
            src={gig.provider?.user?.avatar_url || ''} 
            alt={gig.provider?.user?.display_name || 'User'} 
          />
          <AvatarFallback>
            {gig.provider?.user?.display_name?.substring(0, 2).toUpperCase() || 
             gig.provider?.business_name?.substring(0, 2).toUpperCase() || 'U'}
          </AvatarFallback>
        </Avatar>
        <div className="flex flex-col min-w-0">
          <Link to={`/gigs/${gig.id}`} className="font-semibold hover:underline line-clamp-1">
            {gig.title}
          </Link>
          <p className="text-sm text-gray-500 truncate">
            {gig.provider?.user?.display_name || gig.provider?.business_name || 'Anonymous'}
          </p>
        </div>
      </CardHeader>
      
      <CardContent className="flex-1 pt-0">
        <p className="text-sm text-gray-600 line-clamp-3 mb-4">
          {gig.short_description || gig.description}
        </p>
        
        <div className="flex flex-wrap gap-2 mb-3">
          {gig.tags.slice(0, 3).map((tag) => (
            <Badge key={tag} variant="secondary" className="text-xs">
              {tag}
            </Badge>
          ))}
        </div>

        {gig.average_rating > 0 && (
          <div className="flex items-center gap-1 text-sm text-gray-600 mb-2">
            <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
            <span>{gig.average_rating.toFixed(1)}</span>
            <span>({gig.total_reviews})</span>
          </div>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-between items-center border-t pt-4">
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <Clock className="h-4 w-4" />
          <span>{gig.delivery_time_days} day{gig.delivery_time_days !== 1 ? 's' : ''}</span>
        </div>
        <div className="flex items-center gap-1">
          <DollarSign className="h-4 w-4" />
          <span className="font-semibold">From ${gig.price_starting}</span>
        </div>
      </CardFooter>
    </Card>
  );
}
