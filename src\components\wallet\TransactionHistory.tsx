
import React from 'react';
import { useAuth } from '@/hooks/use-auth';
import { useTransactions } from '@/hooks/use-transactions';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { ArrowUpRight, ArrowDownLeft, Clock } from 'lucide-react';
import { format } from 'date-fns';

export function TransactionHistory() {
  const { user } = useAuth();
  const { data: transactions = [], isLoading } = useTransactions(user?.id);

  if (isLoading) {
    return (
      <div className="space-y-4">
        {Array(3).fill(0).map((_, i) => (
          <Skeleton key={i} className="h-16 w-full" />
        ))}
      </div>
    );
  }

  if (transactions.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        <p>No transactions found</p>
        <p className="text-sm">Transactions will appear here once you make a payment or receive funds</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {transactions.map((transaction) => (
        <Card key={transaction.id} className="p-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`p-2 rounded-full ${getTransactionIconClass(transaction.type)}`}>
              {transaction.type === 'deposit' && <ArrowDownLeft className="h-5 w-5" />}
              {transaction.type === 'withdrawal' && <ArrowUpRight className="h-5 w-5" />}
              {transaction.type === 'pending' && <Clock className="h-5 w-5" />}
            </div>
            <div>
              <div className="font-medium">{transaction.description}</div>
              <div className="text-sm text-muted-foreground">
                {format(new Date(transaction.created_at), 'PPp')}
              </div>
            </div>
          </div>
          <div className="flex flex-col items-end">
            <span className={`font-semibold ${getAmountClass(transaction.type)}`}>
              {getAmountPrefix(transaction.type)}${transaction.amount.toFixed(2)}
            </span>
            <Badge variant={getStatusVariant(transaction.status)}>
              {transaction.status}
            </Badge>
          </div>
        </Card>
      ))}
    </div>
  );
}

function getTransactionIconClass(type: string) {
  switch (type) {
    case 'deposit':
      return 'bg-green-100 text-green-600';
    case 'withdrawal':
      return 'bg-red-100 text-red-600';
    case 'pending':
      return 'bg-yellow-100 text-yellow-600';
    default:
      return 'bg-gray-100 text-gray-600';
  }
}

function getAmountClass(type: string) {
  switch (type) {
    case 'deposit':
      return 'text-green-600';
    case 'withdrawal':
      return 'text-red-600';
    default:
      return '';
  }
}

function getAmountPrefix(type: string) {
  return type === 'deposit' ? '+' : '-';
}

function getStatusVariant(status: string): "default" | "destructive" | "outline" | "secondary" {
  switch (status) {
    case 'completed':
      return 'default';
    case 'pending':
      return 'secondary';
    case 'failed':
      return 'destructive';
    default:
      return 'outline';
  }
}
