import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { ImagePlus, X, Edit, Maximize, Loader2 } from "lucide-react";
import { EnhancedUserProfile, ProfileMedia } from "@/types/enhanced-profile";
import { useAuth } from "@/hooks/use-auth";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { cn } from "@/lib/utils";
import { ProfileLightbox } from './ProfileLightbox';

interface ProfileGalleryProps {
  profile: EnhancedUserProfile;
  isLoading?: boolean;
  onProfileUpdated?: () => void;
  className?: string;
}

export function ProfileGallery({
  profile,
  isLoading = false,
  onProfileUpdated,
  className
}: ProfileGalleryProps) {
  const { user } = useAuth();
  const { toast } = useToast();
  const [mediaItems, setMediaItems] = useState<ProfileMedia[]>([]);
  const [isLoadingMedia, setIsLoadingMedia] = useState(false);
  const [selectedMedia, setSelectedMedia] = useState<ProfileMedia | null>(null);
  const [isUploading, setIsUploading] = useState(false);
  const [isAddingMedia, setIsAddingMedia] = useState(false);
  const [selectedMediaIndex, setSelectedMediaIndex] = useState<number | null>(null);

  // Check if this is the current user's profile
  const isOwnProfile = user?.id === profile?.id;

  // Fetch media items
  useEffect(() => {
    if (!profile?.id) return;

    const fetchMedia = async () => {
      setIsLoadingMedia(true);
      try {
        // First try to fetch from profile_media table
        const { data, error } = await supabase
          .from('profile_media')
          .select('*')
          .eq('profile_id', profile.id)
          .order('position', { ascending: true });

        if (error) throw error;

        if (data && data.length > 0) {
          setMediaItems(data as ProfileMedia[]);
        } else {
          // Fallback to legacy gallery array in profiles table
          if (profile.gallery && profile.gallery.length > 0) {
            // Convert legacy gallery to ProfileMedia format
            const legacyMedia: ProfileMedia[] = profile.gallery.map((url, index) => ({
              id: `legacy-${index}`,
              profile_id: profile.id,
              url,
              type: 'image',
              position: index,
              is_featured: index === 0,
              created_at: profile.updated_at || new Date().toISOString(),
              updated_at: profile.updated_at || new Date().toISOString()
            }));
            setMediaItems(legacyMedia);
          } else {
            setMediaItems([]);
          }
        }
      } catch (error) {
        console.error('Error fetching profile media:', error);
        setMediaItems([]);
      } finally {
        setIsLoadingMedia(false);
      }
    };

    fetchMedia();
  }, [profile?.id, profile?.gallery, profile?.updated_at]);

  // Handle media upload
  const handleMediaUpload = async (file: File) => {
    if (!isOwnProfile || !profile?.id) return;

    try {
      setIsUploading(true);

      // Upload the media
      const fileName = `gallery_${profile.id}_${Date.now()}`;
      const { data, error } = await supabase.storage
        .from('avatars') // Using avatars bucket for now
        .upload(`public/${fileName}`, file, {
          cacheControl: '3600',
          upsert: true
        });

      if (error) throw error;

      // Get the public URL
      const { data: { publicUrl } } = supabase.storage
        .from('avatars')
        .getPublicUrl(`public/${fileName}`);

      // Create thumbnail (using the same URL for now)
      const thumbnailUrl = publicUrl;

      // Add to profile_media table
      const { data: mediaData, error: mediaError } = await supabase
        .from('profile_media')
        .insert({
          profile_id: profile.id,
          url: publicUrl,
          thumbnail_url: thumbnailUrl,
          type: 'image',
          position: mediaItems.length,
          is_featured: mediaItems.length === 0
        })
        .select()
        .single();

      if (mediaError) throw mediaError;

      // Update local state
      setMediaItems(prev => [...prev, mediaData as ProfileMedia]);

      toast({
        title: "Media uploaded",
        description: "Your photo has been added to your gallery."
      });

      // Refresh the profile
      if (onProfileUpdated) onProfileUpdated();

    } catch (error) {
      console.error('Error uploading media:', error);
      toast({
        title: "Error uploading media",
        description: "There was an error uploading your photo.",
        variant: "destructive"
      });
    } finally {
      setIsUploading(false);
      setIsAddingMedia(false);
    }
  };

  // Handle media deletion
  const handleDeleteMedia = async (mediaItem: ProfileMedia) => {
    if (!isOwnProfile) return;

    try {
      // If it's a legacy media item
      if (mediaItem.id.startsWith('legacy-')) {
        // Update the gallery array in profiles
        const updatedGallery = profile.gallery?.filter(url => url !== mediaItem.url) || [];

        const { error } = await supabase
          .from('profiles')
          .update({ gallery: updatedGallery })
          .eq('id', profile.id);

        if (error) throw error;

        // Update local state
        setMediaItems(prev => prev.filter(item => item.id !== mediaItem.id));

      } else {
        // Delete from profile_media table
        const { error } = await supabase
          .from('profile_media')
          .delete()
          .eq('id', mediaItem.id);

        if (error) throw error;

        // Update local state
        setMediaItems(prev => prev.filter(item => item.id !== mediaItem.id));
      }

      toast({
        title: "Media deleted",
        description: "The photo has been removed from your gallery."
      });

      // Refresh the profile
      if (onProfileUpdated) onProfileUpdated();

    } catch (error) {
      console.error('Error deleting media:', error);
      toast({
        title: "Error deleting media",
        description: "There was an error removing the photo.",
        variant: "destructive"
      });
    }
  };

  if (isLoading || isLoadingMedia) {
    return <ProfileGallerySkeleton className={className} />;
  }

  const hasGalleryItems = mediaItems.length > 0;

  // Create a helper function to open the lightbox
  const openLightbox = (index: number) => {
    setSelectedMediaIndex(index);
  };

  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Gallery</CardTitle>
          <CardDescription>Share your moments with others</CardDescription>
        </div>
        {isOwnProfile && (
          <Button
            size="sm"
            variant="outline"
            className="gap-1"
            onClick={() => setIsAddingMedia(true)}
          >
            <ImagePlus className="h-4 w-4" />
            <span>Add Photos</span>
          </Button>
        )}
      </CardHeader>
      <CardContent>
        {hasGalleryItems ? (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {mediaItems.map((item, index) => (
              <GalleryItem
                key={item.id}
                item={item}
                isOwnProfile={isOwnProfile}
                onView={() => openLightbox(index)}
                onDelete={() => handleDeleteMedia(item)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>No gallery items yet.</p>
            <p className="text-sm">
              {isOwnProfile
                ? "Add photos to showcase your adventures!"
                : "This user hasn't added any photos yet."}
            </p>
          </div>
        )}
      </CardContent>

      {/* Media Lightbox */}
      {selectedMediaIndex !== null && (
        <ProfileLightbox
          media={mediaItems}
          initialIndex={selectedMediaIndex}
          open={selectedMediaIndex !== null}
          onClose={() => setSelectedMediaIndex(null)}
        />
      )}

      {/* Media Upload Modal */}
      {isAddingMedia && (
        <MediaUploadModal
          onClose={() => setIsAddingMedia(false)}
          onUpload={handleMediaUpload}
          isUploading={isUploading}
        />
      )}
    </Card>
  );
}

interface GalleryItemProps {
  item: ProfileMedia;
  isOwnProfile: boolean;
  onView: () => void;
  onDelete: () => void;
}

function GalleryItem({ item, isOwnProfile, onView, onDelete }: GalleryItemProps) {
  const [isHovering, setIsHovering] = useState(false);

  return (
    <div
      className="relative aspect-square rounded-md overflow-hidden group"
      onMouseEnter={() => setIsHovering(true)}
      onMouseLeave={() => setIsHovering(false)}
    >
      <img
        src={item.url}
        alt={item.caption || 'Gallery item'}
        className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
      />

      {/* Overlay */}
      <div className={cn(
        "absolute inset-0 bg-gradient-to-t from-black/60 to-transparent transition-opacity duration-300",
        isHovering ? "opacity-100" : "opacity-0"
      )}>
        {/* Action buttons */}
        <div className="absolute bottom-2 right-2 flex gap-1">
          <Button
            variant="outline"
            size="icon"
            className="h-8 w-8 rounded-full bg-white/80 hover:bg-white"
            onClick={onView}
          >
            <Maximize className="h-4 w-4" />
          </Button>

          {isOwnProfile && (
            <Button
              variant="outline"
              size="icon"
              className="h-8 w-8 rounded-full bg-white/80 hover:bg-white hover:text-destructive"
              onClick={onDelete}
            >
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>

        {/* Caption */}
        {item.caption && (
          <div className="absolute bottom-2 left-2 right-12 truncate">
            <p className="text-xs text-white">{item.caption}</p>
          </div>
        )}
      </div>
    </div>
  );
}

interface MediaViewerModalProps {
  media: ProfileMedia;
  onClose: () => void;
}

function MediaViewerModal({ media, onClose }: MediaViewerModalProps) {
  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[800px] p-0 overflow-hidden bg-black/90">
        <div className="relative">
          <Button
            variant="outline"
            size="icon"
            className="absolute top-2 right-2 z-10 rounded-full bg-black/50 hover:bg-black/70 border-none text-white"
            onClick={onClose}
          >
            <X className="h-4 w-4" />
          </Button>

          <div className="flex items-center justify-center h-[80vh] max-h-[80vh]">
            <img
              src={media.url}
              alt={media.caption || 'Gallery item'}
              className="max-w-full max-h-full object-contain"
            />
          </div>

          {media.caption && (
            <div className="absolute bottom-0 left-0 right-0 bg-black/70 p-4">
              <p className="text-white">{media.caption}</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

interface MediaUploadModalProps {
  onClose: () => void;
  onUpload: (file: File) => void;
  isUploading: boolean;
}

function MediaUploadModal({ onClose, onUpload, isUploading }: MediaUploadModalProps) {
  const fileInputRef = React.useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [caption, setCaption] = useState('');
  const { toast } = useToast();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select an image under 5MB.",
        variant: "destructive"
      });
      return;
    }

    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please select an image file.",
        variant: "destructive"
      });
      return;
    }

    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(file);

    setSelectedFile(file);
  };

  const handleSelectClick = () => {
    fileInputRef.current?.click();
  };

  const handleUpload = () => {
    if (selectedFile) {
      onUpload(selectedFile);
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Add to Gallery</DialogTitle>
        </DialogHeader>

        <div className="my-4">
          {previewUrl ? (
            <div className="relative">
              <img
                src={previewUrl}
                alt="Preview"
                className="w-full h-64 object-contain rounded-md"
              />
            </div>
          ) : (
            <div
              className="w-full h-64 bg-muted/50 rounded-md flex flex-col items-center justify-center cursor-pointer hover:bg-muted/70 transition-colors"
              onClick={handleSelectClick}
            >
              <ImagePlus className="h-10 w-10 text-muted-foreground mb-2" />
              <p className="text-muted-foreground">Click to select a photo</p>
            </div>
          )}

          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />
        </div>

        <div className="flex justify-end gap-2 mt-4">
          <Button variant="outline" onClick={onClose} disabled={isUploading}>
            Cancel
          </Button>

          {previewUrl ? (
            <Button onClick={handleUpload} disabled={isUploading}>
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <ImagePlus className="h-4 w-4 mr-2" />
                  Add to Gallery
                </>
              )}
            </Button>
          ) : (
            <Button onClick={handleSelectClick} disabled={isUploading}>
              <ImagePlus className="h-4 w-4 mr-2" />
              Select Photo
            </Button>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

function ProfileGallerySkeleton({ className }: { className?: string }) {
  return (
    <Card className={className}>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <Skeleton className="h-6 w-24 mb-2" />
          <Skeleton className="h-4 w-40" />
        </div>
        <Skeleton className="h-9 w-28" />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Array(4).fill(0).map((_, i) => (
            <Skeleton key={i} className="aspect-square rounded-md" />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
