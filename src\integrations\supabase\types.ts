export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  public: {
    Tables: {
      activities: {
        Row: {
          address: string | null
          allow_waitlist: boolean | null
          category_id: string | null
          created_at: string | null
          description: string | null
          early_bird_price: number | null
          end_time: string | null
          group_chat_id: string | null
          group_discount_percentage: number | null
          group_discount_threshold: number | null
          host_id: string
          id: string
          is_paid: boolean | null
          location: unknown
          max_participants: number | null
          media_urls: string[] | null
          price: number | null
          queue_type: string | null
          start_time: string
          status: string | null
          title: string
          updated_at: string | null
          visibility: string | null
        }
        Insert: {
          address?: string | null
          allow_waitlist?: boolean | null
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          early_bird_price?: number | null
          end_time?: string | null
          group_chat_id?: string | null
          group_discount_percentage?: number | null
          group_discount_threshold?: number | null
          host_id: string
          id?: string
          is_paid?: boolean | null
          location: unknown
          max_participants?: number | null
          media_urls?: string[] | null
          price?: number | null
          queue_type?: string | null
          start_time: string
          status?: string | null
          title: string
          updated_at?: string | null
          visibility?: string | null
        }
        Update: {
          address?: string | null
          allow_waitlist?: boolean | null
          category_id?: string | null
          created_at?: string | null
          description?: string | null
          early_bird_price?: number | null
          end_time?: string | null
          group_chat_id?: string | null
          group_discount_percentage?: number | null
          group_discount_threshold?: number | null
          host_id?: string
          id?: string
          is_paid?: boolean | null
          location?: unknown
          max_participants?: number | null
          media_urls?: string[] | null
          price?: number | null
          queue_type?: string | null
          start_time?: string
          status?: string | null
          title?: string
          updated_at?: string | null
          visibility?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "activities_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_participants: {
        Row: {
          activity_id: string
          created_at: string | null
          id: string
          is_host: boolean | null
          joined_at: string | null
          payment_id: string | null
          payment_status: string | null
          status: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          activity_id: string
          created_at?: string | null
          id?: string
          is_host?: boolean | null
          joined_at?: string | null
          payment_id?: string | null
          payment_status?: string | null
          status?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          activity_id?: string
          created_at?: string | null
          id?: string
          is_host?: boolean | null
          joined_at?: string | null
          payment_id?: string | null
          payment_status?: string | null
          status?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_participants_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
        ]
      }
      activity_queue: {
        Row: {
          activity_id: string
          created_at: string | null
          id: string
          payment_id: string | null
          position: number
          status: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          activity_id: string
          created_at?: string | null
          id?: string
          payment_id?: string | null
          position: number
          status?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          activity_id?: string
          created_at?: string | null
          id?: string
          payment_id?: string | null
          position?: number
          status?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "activity_queue_activity_id_fkey"
            columns: ["activity_id"]
            isOneToOne: false
            referencedRelation: "activities"
            referencedColumns: ["id"]
          },
        ]
      }
      admin_audit_log: {
        Row: {
          action_details: string | null
          action_type: string
          admin_id: string
          created_at: string | null
          id: string
          ip_address: string | null
        }
        Insert: {
          action_details?: string | null
          action_type: string
          admin_id: string
          created_at?: string | null
          id?: string
          ip_address?: string | null
        }
        Update: {
          action_details?: string | null
          action_type?: string
          admin_id?: string
          created_at?: string | null
          id?: string
          ip_address?: string | null
        }
        Relationships: []
      }
      categories: {
        Row: {
          created_at: string
          icon: string | null
          id: string
          name: string
          type: string
        }
        Insert: {
          created_at?: string
          icon?: string | null
          id?: string
          name: string
          type: string
        }
        Update: {
          created_at?: string
          icon?: string | null
          id?: string
          name?: string
          type?: string
        }
        Relationships: []
      }
      category_metadata: {
        Row: {
          average_price_range: string | null
          category_id: string
          created_at: string | null
          description: string | null
          id: string
          is_trending: boolean | null
          keywords: string[] | null
          parent_category_id: string | null
          popularity_score: number | null
          sort_order: number | null
          updated_at: string | null
        }
        Insert: {
          average_price_range?: string | null
          category_id: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_trending?: boolean | null
          keywords?: string[] | null
          parent_category_id?: string | null
          popularity_score?: number | null
          sort_order?: number | null
          updated_at?: string | null
        }
        Update: {
          average_price_range?: string | null
          category_id?: string
          created_at?: string | null
          description?: string | null
          id?: string
          is_trending?: boolean | null
          keywords?: string[] | null
          parent_category_id?: string | null
          popularity_score?: number | null
          sort_order?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "category_metadata_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "category_metadata_parent_category_id_fkey"
            columns: ["parent_category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_conversations: {
        Row: {
          archive_reason: string | null
          archived_at: string | null
          archived_by: string | null
          created_at: string | null
          id: string
          is_admin_conversation: boolean | null
          is_announcement_only: boolean | null
          is_archived: boolean | null
          is_group: boolean | null
          is_support: boolean | null
          last_message: string | null
          last_message_at: string | null
          pinned_message_id: string | null
          updated_at: string | null
        }
        Insert: {
          archive_reason?: string | null
          archived_at?: string | null
          archived_by?: string | null
          created_at?: string | null
          id?: string
          is_admin_conversation?: boolean | null
          is_announcement_only?: boolean | null
          is_archived?: boolean | null
          is_group?: boolean | null
          is_support?: boolean | null
          last_message?: string | null
          last_message_at?: string | null
          pinned_message_id?: string | null
          updated_at?: string | null
        }
        Update: {
          archive_reason?: string | null
          archived_at?: string | null
          archived_by?: string | null
          created_at?: string | null
          id?: string
          is_admin_conversation?: boolean | null
          is_announcement_only?: boolean | null
          is_archived?: boolean | null
          is_group?: boolean | null
          is_support?: boolean | null
          last_message?: string | null
          last_message_at?: string | null
          pinned_message_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "chat_conversations_pinned_message_id_fkey"
            columns: ["pinned_message_id"]
            isOneToOne: false
            referencedRelation: "location_messages_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "chat_conversations_pinned_message_id_fkey"
            columns: ["pinned_message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
        ]
      }
      chat_participants: {
        Row: {
          conversation_id: string | null
          id: string
          joined_at: string | null
          last_read_at: string | null
          role: string | null
          user_id: string
        }
        Insert: {
          conversation_id?: string | null
          id?: string
          joined_at?: string | null
          last_read_at?: string | null
          role?: string | null
          user_id: string
        }
        Update: {
          conversation_id?: string | null
          id?: string
          joined_at?: string | null
          last_read_at?: string | null
          role?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "chat_participants_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "chat_conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      featured_services: {
        Row: {
          created_at: string | null
          end_date: string | null
          featured_type: string
          gig_id: string
          id: string
          is_active: boolean | null
          priority: number | null
          provider_id: string
          start_date: string | null
          updated_at: string | null
        }
        Insert: {
          created_at?: string | null
          end_date?: string | null
          featured_type: string
          gig_id: string
          id?: string
          is_active?: boolean | null
          priority?: number | null
          provider_id: string
          start_date?: string | null
          updated_at?: string | null
        }
        Update: {
          created_at?: string | null
          end_date?: string | null
          featured_type?: string
          gig_id?: string
          id?: string
          is_active?: boolean | null
          priority?: number | null
          provider_id?: string
          start_date?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "featured_services_gig_id_fkey"
            columns: ["gig_id"]
            isOneToOne: false
            referencedRelation: "gigs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "featured_services_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "service_providers"
            referencedColumns: ["id"]
          },
        ]
      }
      follows: {
        Row: {
          created_at: string
          follower_id: string
          following_id: string
          id: string
        }
        Insert: {
          created_at?: string
          follower_id: string
          following_id: string
          id?: string
        }
        Update: {
          created_at?: string
          follower_id?: string
          following_id?: string
          id?: string
        }
        Relationships: []
      }
      geofence_events: {
        Row: {
          event_type: string
          geofence_id: string
          id: string
          location: unknown | null
          metadata: Json | null
          timestamp: string | null
          user_id: string
        }
        Insert: {
          event_type: string
          geofence_id: string
          id?: string
          location?: unknown | null
          metadata?: Json | null
          timestamp?: string | null
          user_id: string
        }
        Update: {
          event_type?: string
          geofence_id?: string
          id?: string
          location?: unknown | null
          metadata?: Json | null
          timestamp?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "geofence_events_geofence_id_fkey"
            columns: ["geofence_id"]
            isOneToOne: false
            referencedRelation: "geofences"
            referencedColumns: ["id"]
          },
        ]
      }
      geofences: {
        Row: {
          center: unknown
          color: string | null
          created_at: string | null
          created_by: string
          description: string | null
          entry_message: string | null
          exit_message: string | null
          icon: string | null
          id: string
          is_active: boolean | null
          name: string
          polygon: unknown[] | null
          polygon_geojson: Json | null
          radius: number | null
          type: string
          updated_at: string | null
        }
        Insert: {
          center: unknown
          color?: string | null
          created_at?: string | null
          created_by: string
          description?: string | null
          entry_message?: string | null
          exit_message?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name: string
          polygon?: unknown[] | null
          polygon_geojson?: Json | null
          radius?: number | null
          type: string
          updated_at?: string | null
        }
        Update: {
          center?: unknown
          color?: string | null
          created_at?: string | null
          created_by?: string
          description?: string | null
          entry_message?: string | null
          exit_message?: string | null
          icon?: string | null
          id?: string
          is_active?: boolean | null
          name?: string
          polygon?: unknown[] | null
          polygon_geojson?: Json | null
          radius?: number | null
          type?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      gigs: {
        Row: {
          availability: string | null
          category_id: string | null
          created_at: string | null
          delivery_time: number | null
          description: string
          experience_level: string | null
          hourly_rate: number | null
          id: string
          is_featured: boolean | null
          media_urls: string[] | null
          price_starting: number | null
          provider_id: string | null
          rating: number | null
          requirements: string | null
          short_description: string | null
          tags: string[] | null
          title: string
          total_orders: number | null
          updated_at: string | null
          user_id: string
          what_you_get: string | null
        }
        Insert: {
          availability?: string | null
          category_id?: string | null
          created_at?: string | null
          delivery_time?: number | null
          description: string
          experience_level?: string | null
          hourly_rate?: number | null
          id?: string
          is_featured?: boolean | null
          media_urls?: string[] | null
          price_starting?: number | null
          provider_id?: string | null
          rating?: number | null
          requirements?: string | null
          short_description?: string | null
          tags?: string[] | null
          title: string
          total_orders?: number | null
          updated_at?: string | null
          user_id: string
          what_you_get?: string | null
        }
        Update: {
          availability?: string | null
          category_id?: string | null
          created_at?: string | null
          delivery_time?: number | null
          description?: string
          experience_level?: string | null
          hourly_rate?: number | null
          id?: string
          is_featured?: boolean | null
          media_urls?: string[] | null
          price_starting?: number | null
          provider_id?: string | null
          rating?: number | null
          requirements?: string | null
          short_description?: string | null
          tags?: string[] | null
          title?: string
          total_orders?: number | null
          updated_at?: string | null
          user_id?: string
          what_you_get?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "gigs_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "gigs_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "service_providers"
            referencedColumns: ["id"]
          },
        ]
      }
      message_reactions: {
        Row: {
          conversation_id: string
          created_at: string | null
          emoji: string
          id: string
          message_id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          conversation_id: string
          created_at?: string | null
          emoji: string
          id?: string
          message_id: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          conversation_id?: string
          created_at?: string | null
          emoji?: string
          id?: string
          message_id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "message_reactions_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "chat_conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "message_reactions_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "location_messages_view"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "message_reactions_message_id_fkey"
            columns: ["message_id"]
            isOneToOne: false
            referencedRelation: "messages"
            referencedColumns: ["id"]
          },
        ]
      }
      messages: {
        Row: {
          content: string
          conversation_id: string | null
          created_at: string
          id: string
          is_admin_message: boolean | null
          is_read: boolean | null
          is_system_message: boolean | null
          location_data: Json | null
          media_url: string | null
          message_type: string | null
          metadata: Json | null
          reactions: Json | null
          recipient_id: string | null
          sender_id: string
          updated_at: string
        }
        Insert: {
          content: string
          conversation_id?: string | null
          created_at?: string
          id?: string
          is_admin_message?: boolean | null
          is_read?: boolean | null
          is_system_message?: boolean | null
          location_data?: Json | null
          media_url?: string | null
          message_type?: string | null
          metadata?: Json | null
          reactions?: Json | null
          recipient_id?: string | null
          sender_id: string
          updated_at?: string
        }
        Update: {
          content?: string
          conversation_id?: string | null
          created_at?: string
          id?: string
          is_admin_message?: boolean | null
          is_read?: boolean | null
          is_system_message?: boolean | null
          location_data?: Json | null
          media_url?: string | null
          message_type?: string | null
          metadata?: Json | null
          reactions?: Json | null
          recipient_id?: string | null
          sender_id?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "chat_conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      notification_preferences: {
        Row: {
          booking_updates: boolean
          chat_messages: boolean
          created_at: string
          email_notifications: boolean
          id: string
          push_notifications: boolean
          system_announcements: boolean
          updated_at: string
          user_id: string
          wallet_transactions: boolean
        }
        Insert: {
          booking_updates?: boolean
          chat_messages?: boolean
          created_at?: string
          email_notifications?: boolean
          id?: string
          push_notifications?: boolean
          system_announcements?: boolean
          updated_at?: string
          user_id: string
          wallet_transactions?: boolean
        }
        Update: {
          booking_updates?: boolean
          chat_messages?: boolean
          created_at?: string
          email_notifications?: boolean
          id?: string
          push_notifications?: boolean
          system_announcements?: boolean
          updated_at?: string
          user_id?: string
          wallet_transactions?: boolean
        }
        Relationships: []
      }
      notifications: {
        Row: {
          action_url: string | null
          content: string
          created_at: string
          id: string
          is_read: boolean
          source_id: string | null
          source_type: string | null
          title: string
          type: string
          updated_at: string
          user_id: string
        }
        Insert: {
          action_url?: string | null
          content: string
          created_at?: string
          id?: string
          is_read?: boolean
          source_id?: string | null
          source_type?: string | null
          title: string
          type: string
          updated_at?: string
          user_id: string
        }
        Update: {
          action_url?: string | null
          content?: string
          created_at?: string
          id?: string
          is_read?: boolean
          source_id?: string | null
          source_type?: string | null
          title?: string
          type?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      profile_media: {
        Row: {
          caption: string | null
          created_at: string | null
          id: string
          is_featured: boolean | null
          metadata: Json | null
          position: number | null
          profile_id: string
          thumbnail_url: string | null
          type: string
          updated_at: string | null
          url: string
        }
        Insert: {
          caption?: string | null
          created_at?: string | null
          id?: string
          is_featured?: boolean | null
          metadata?: Json | null
          position?: number | null
          profile_id: string
          thumbnail_url?: string | null
          type?: string
          updated_at?: string | null
          url: string
        }
        Update: {
          caption?: string | null
          created_at?: string | null
          id?: string
          is_featured?: boolean | null
          metadata?: Json | null
          position?: number | null
          profile_id?: string
          thumbnail_url?: string | null
          type?: string
          updated_at?: string | null
          url?: string
        }
        Relationships: [
          {
            foreignKeyName: "profile_media_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profile_views: {
        Row: {
          id: string
          is_anonymous: boolean | null
          profile_id: string
          viewed_at: string | null
          viewer_id: string | null
        }
        Insert: {
          id?: string
          is_anonymous?: boolean | null
          profile_id: string
          viewed_at?: string | null
          viewer_id?: string | null
        }
        Update: {
          id?: string
          is_anonymous?: boolean | null
          profile_id?: string
          viewed_at?: string | null
          viewer_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "profile_views_profile_id_fkey"
            columns: ["profile_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "profile_views_viewer_id_fkey"
            columns: ["viewer_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      profiles: {
        Row: {
          admin_notes: string | null
          avatar_url: string | null
          ban_reason: string | null
          banned_until: string | null
          bio: string | null
          birthday: string | null
          cover_url: string | null
          created_at: string
          default_location: unknown | null
          display_name: string | null
          favorite_locations: Json | null
          gallery: string[] | null
          gallery_order: Json | null
          gender: string | null
          id: string
          interests: string[] | null
          is_admin: boolean | null
          is_banned: boolean | null
          is_bot: boolean | null
          is_profile_public: boolean | null
          is_suspended: boolean | null
          is_verified: boolean | null
          last_seen_at: string | null
          location: unknown | null
          location_display: string | null
          location_permission_granted: boolean | null
          notifications_enabled: boolean | null
          notifyactivities: boolean | null
          notifyfollows: boolean | null
          notifymessages: boolean | null
          onboarding_completed: boolean | null
          profile_theme: string | null
          profile_visibility: Json | null
          purposes: string[] | null
          social_links: Json | null
          suspended_by: string | null
          suspended_until: string | null
          suspension_reason: string | null
          trust_score: number | null
          updated_at: string
          username: string | null
          vibes: string[] | null
        }
        Insert: {
          admin_notes?: string | null
          avatar_url?: string | null
          ban_reason?: string | null
          banned_until?: string | null
          bio?: string | null
          birthday?: string | null
          cover_url?: string | null
          created_at?: string
          default_location?: unknown | null
          display_name?: string | null
          favorite_locations?: Json | null
          gallery?: string[] | null
          gallery_order?: Json | null
          gender?: string | null
          id: string
          interests?: string[] | null
          is_admin?: boolean | null
          is_banned?: boolean | null
          is_bot?: boolean | null
          is_profile_public?: boolean | null
          is_suspended?: boolean | null
          is_verified?: boolean | null
          last_seen_at?: string | null
          location?: unknown | null
          location_display?: string | null
          location_permission_granted?: boolean | null
          notifications_enabled?: boolean | null
          notifyactivities?: boolean | null
          notifyfollows?: boolean | null
          notifymessages?: boolean | null
          onboarding_completed?: boolean | null
          profile_theme?: string | null
          profile_visibility?: Json | null
          purposes?: string[] | null
          social_links?: Json | null
          suspended_by?: string | null
          suspended_until?: string | null
          suspension_reason?: string | null
          trust_score?: number | null
          updated_at?: string
          username?: string | null
          vibes?: string[] | null
        }
        Update: {
          admin_notes?: string | null
          avatar_url?: string | null
          ban_reason?: string | null
          banned_until?: string | null
          bio?: string | null
          birthday?: string | null
          cover_url?: string | null
          created_at?: string
          default_location?: unknown | null
          display_name?: string | null
          favorite_locations?: Json | null
          gallery?: string[] | null
          gallery_order?: Json | null
          gender?: string | null
          id?: string
          interests?: string[] | null
          is_admin?: boolean | null
          is_banned?: boolean | null
          is_bot?: boolean | null
          is_profile_public?: boolean | null
          is_suspended?: boolean | null
          is_verified?: boolean | null
          last_seen_at?: string | null
          location?: unknown | null
          location_display?: string | null
          location_permission_granted?: boolean | null
          notifications_enabled?: boolean | null
          notifyactivities?: boolean | null
          notifyfollows?: boolean | null
          notifymessages?: boolean | null
          onboarding_completed?: boolean | null
          profile_theme?: string | null
          profile_visibility?: Json | null
          purposes?: string[] | null
          social_links?: Json | null
          suspended_by?: string | null
          suspended_until?: string | null
          suspension_reason?: string | null
          trust_score?: number | null
          updated_at?: string
          username?: string | null
          vibes?: string[] | null
        }
        Relationships: []
      }
      reviews: {
        Row: {
          comment: string | null
          created_at: string | null
          gig_id: string | null
          id: string
          is_public: boolean | null
          provider_id: string
          rating: number
          reviewer_id: string
          title: string | null
          updated_at: string | null
        }
        Insert: {
          comment?: string | null
          created_at?: string | null
          gig_id?: string | null
          id?: string
          is_public?: boolean | null
          provider_id: string
          rating: number
          reviewer_id: string
          title?: string | null
          updated_at?: string | null
        }
        Update: {
          comment?: string | null
          created_at?: string | null
          gig_id?: string | null
          id?: string
          is_public?: boolean | null
          provider_id?: string
          rating?: number
          reviewer_id?: string
          title?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "reviews_gig_id_fkey"
            columns: ["gig_id"]
            isOneToOne: false
            referencedRelation: "gigs"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "reviews_provider_id_fkey"
            columns: ["provider_id"]
            isOneToOne: false
            referencedRelation: "service_providers"
            referencedColumns: ["id"]
          },
        ]
      }
      saved_locations: {
        Row: {
          category: string | null
          created_at: string
          description: string | null
          id: string
          is_shared: boolean | null
          location: unknown
          metadata: Json | null
          name: string
          shared_with: string[] | null
          tags: string[] | null
          updated_at: string
          user_id: string
        }
        Insert: {
          category?: string | null
          created_at?: string
          description?: string | null
          id?: string
          is_shared?: boolean | null
          location: unknown
          metadata?: Json | null
          name: string
          shared_with?: string[] | null
          tags?: string[] | null
          updated_at?: string
          user_id: string
        }
        Update: {
          category?: string | null
          created_at?: string
          description?: string | null
          id?: string
          is_shared?: boolean | null
          location?: unknown
          metadata?: Json | null
          name?: string
          shared_with?: string[] | null
          tags?: string[] | null
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      service_interactions: {
        Row: {
          category_id: string | null
          created_at: string | null
          gig_id: string
          id: string
          interaction_type: string
          interaction_weight: number | null
          user_id: string
        }
        Insert: {
          category_id?: string | null
          created_at?: string | null
          gig_id: string
          id?: string
          interaction_type: string
          interaction_weight?: number | null
          user_id: string
        }
        Update: {
          category_id?: string | null
          created_at?: string | null
          gig_id?: string
          id?: string
          interaction_type?: string
          interaction_weight?: number | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "service_interactions_category_id_fkey"
            columns: ["category_id"]
            isOneToOne: false
            referencedRelation: "categories"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "service_interactions_gig_id_fkey"
            columns: ["gig_id"]
            isOneToOne: false
            referencedRelation: "gigs"
            referencedColumns: ["id"]
          },
        ]
      }
      service_providers: {
        Row: {
          avatar_url: string | null
          bio: string | null
          created_at: string | null
          hourly_rate: number | null
          id: string
          is_verified: boolean | null
          location: string | null
          name: string
          rating: number | null
          tags: string[] | null
          total_reviews: number | null
          trust_score: number | null
          updated_at: string | null
          user_id: string
          verification_level_id: string | null
          verification_status: string | null
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          hourly_rate?: number | null
          id?: string
          is_verified?: boolean | null
          location?: string | null
          name: string
          rating?: number | null
          tags?: string[] | null
          total_reviews?: number | null
          trust_score?: number | null
          updated_at?: string | null
          user_id: string
          verification_level_id?: string | null
          verification_status?: string | null
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          created_at?: string | null
          hourly_rate?: number | null
          id?: string
          is_verified?: boolean | null
          location?: string | null
          name?: string
          rating?: number | null
          tags?: string[] | null
          total_reviews?: number | null
          trust_score?: number | null
          updated_at?: string | null
          user_id?: string
          verification_level_id?: string | null
          verification_status?: string | null
        }
        Relationships: []
      }
      subscriptions: {
        Row: {
          created_at: string
          expires_at: string | null
          id: string
          payment_id: string | null
          plan: string
          started_at: string
          status: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          expires_at?: string | null
          id?: string
          payment_id?: string | null
          plan: string
          started_at?: string
          status: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          expires_at?: string | null
          id?: string
          payment_id?: string | null
          plan?: string
          started_at?: string
          status?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
      typing_status: {
        Row: {
          conversation_id: string
          id: string
          is_typing: boolean
          updated_at: string | null
          user_id: string
        }
        Insert: {
          conversation_id: string
          id?: string
          is_typing?: boolean
          updated_at?: string | null
          user_id: string
        }
        Update: {
          conversation_id?: string
          id?: string
          is_typing?: boolean
          updated_at?: string | null
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "typing_status_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "chat_conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      user_blocks: {
        Row: {
          blocked_id: string
          blocker_id: string
          created_at: string | null
          id: string
          reason: string | null
        }
        Insert: {
          blocked_id: string
          blocker_id: string
          created_at?: string | null
          id?: string
          reason?: string | null
        }
        Update: {
          blocked_id?: string
          blocker_id?: string
          created_at?: string | null
          id?: string
          reason?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "user_blocks_blocked_id_fkey"
            columns: ["blocked_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "user_blocks_blocker_id_fkey"
            columns: ["blocker_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      user_locations: {
        Row: {
          avatar_url: string | null
          bio: string | null
          color: string | null
          created_at: string
          display_name: string | null
          id: string
          is_anonymous: boolean | null
          last_seen_at: string | null
          location: unknown
          updated_at: string
          user_id: string
        }
        Insert: {
          avatar_url?: string | null
          bio?: string | null
          color?: string | null
          created_at?: string
          display_name?: string | null
          id?: string
          is_anonymous?: boolean | null
          last_seen_at?: string | null
          location: unknown
          updated_at?: string
          user_id: string
        }
        Update: {
          avatar_url?: string | null
          bio?: string | null
          color?: string | null
          created_at?: string
          display_name?: string | null
          id?: string
          is_anonymous?: boolean | null
          last_seen_at?: string | null
          location?: unknown
          updated_at?: string
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      location_messages_view: {
        Row: {
          content: string | null
          conversation_id: string | null
          created_at: string | null
          expiration: string | null
          id: string | null
          is_expired: boolean | null
          label: string | null
          lat: number | null
          lng: number | null
          sender_id: string | null
          updated_at: string | null
        }
        Insert: {
          content?: string | null
          conversation_id?: string | null
          created_at?: string | null
          expiration?: never
          id?: string | null
          is_expired?: never
          label?: never
          lat?: never
          lng?: never
          sender_id?: string | null
          updated_at?: string | null
        }
        Update: {
          content?: string | null
          conversation_id?: string | null
          created_at?: string | null
          expiration?: never
          id?: string | null
          is_expired?: never
          label?: never
          lat?: never
          lng?: never
          sender_id?: string | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "chat_conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      profile_connections: {
        Row: {
          created_at: string | null
          follower_avatar_url: string | null
          follower_display_name: string | null
          follower_id: string | null
          follower_is_verified: boolean | null
          follower_username: string | null
          following_avatar_url: string | null
          following_display_name: string | null
          following_id: string | null
          following_is_verified: boolean | null
          following_username: string | null
          id: string | null
          is_mutual: boolean | null
        }
        Relationships: []
      }
    }
    Functions: {
      add_merchant_response: {
        Args: { p_dispute_id: string; p_response: string }
        Returns: boolean
      }
      add_message_reaction: {
        Args:
          | { message_id: string; user_id: string; emoji: string }
          | { message_id: string; user_id: string; emoji: string }
        Returns: Json
      }
      add_user_to_activity_chat: {
        Args: { p_activity_id: string; p_user_id: string }
        Returns: boolean
      }
      add_verification_step: {
        Args: {
          p_workflow_id: string
          p_step_number: number
          p_step_type: string
          p_step_name: string
          p_step_description?: string
          p_step_data?: Json
        }
        Returns: string
      }
      appeal_report_resolution: {
        Args: {
          p_report_id: string
          p_report_type: string
          p_appeal_reason: string
        }
        Returns: boolean
      }
      archive_notification: {
        Args: { p_notification_id: string }
        Returns: boolean
      }
      assign_content_report: {
        Args: { p_report_id: string; p_moderator_id: string; p_notes?: string }
        Returns: boolean
      }
      assign_dispute: {
        Args: { p_dispute_id: string; p_assigned_to: string; p_notes?: string }
        Returns: boolean
      }
      ban_user: {
        Args: { p_user_id: string; p_reason: string; p_duration?: unknown }
        Returns: boolean
      }
      batch_update_queue_status: {
        Args: { p_activity_id: string; p_updates: Json }
        Returns: undefined
      }
      calculate_distance: {
        Args:
          | { lat1: number; lng1: number; lat2: number; lng2: number }
          | { point1: unknown; point2: unknown }
        Returns: number
      }
      categorize_dispute: {
        Args: {
          p_dispute_id: string
          p_category: string
          p_subcategory?: string
          p_priority?: string
        }
        Returns: boolean
      }
      check_activities_feature_available: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      check_expired_location_messages: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      check_onboarding_completed: {
        Args: { user_id: string }
        Returns: boolean
      }
      check_table_exists: {
        Args: { table_name: string }
        Returns: boolean
      }
      column_exists: {
        Args: { p_table_name: string; p_column_name: string }
        Returns: boolean
      }
      complete_verification_step: {
        Args: {
          p_step_id: string
          p_status: string
          p_result_data?: Json
          p_notes?: string
        }
        Returns: boolean
      }
      create_activity_group_chat: {
        Args: { p_activity_id: string }
        Returns: string
      }
      create_admin_notification: {
        Args: {
          p_title: string
          p_message: string
          p_notification_type: string
          p_severity: string
          p_entity_type?: string
          p_entity_id?: string
          p_action_url?: string
          p_recipient_id?: string
          p_recipient_role?: string
          p_expires_at?: string
        }
        Returns: string
      }
      create_notification: {
        Args: {
          p_user_id: string
          p_type: string
          p_title: string
          p_content: string
          p_source_id?: string
          p_source_type?: string
          p_action_url?: string
        }
        Returns: string
      }
      create_provider_verification_workflow: {
        Args: {
          p_provider_id: string
          p_verification_type: string
          p_document_url?: string
          p_verification_data?: Json
        }
        Returns: string
      }
      create_verification_workflow: {
        Args: {
          p_provider_id: string
          p_verification_id: string
          p_workflow_type: string
          p_workflow_data?: Json
          p_total_steps?: number
        }
        Returns: string
      }
      exec_sql: {
        Args: { sql: string }
        Returns: undefined
      }
      find_activities_within_radius: {
        Args: {
          center_lat: number
          center_lng: number
          radius_km: number
          category_id_filter?: string
          is_paid_filter?: boolean
          start_date?: string
          end_date?: string
          search_query?: string
          max_results?: number
        }
        Returns: {
          id: string
          host_id: string
          title: string
          description: string
          category_id: string
          location: unknown
          address: string
          start_time: string
          end_time: string
          is_paid: boolean
          price: number
          max_participants: number
          created_at: string
          updated_at: string
          media_urls: string[]
          distance_km: number
          host_name: string
          host_avatar_url: string
          participant_count: number
          category_name: string
        }[]
      }
      find_geofences_containing_point: {
        Args: { point_lng: number; point_lat: number; user_id?: string }
        Returns: {
          center: unknown
          color: string | null
          created_at: string | null
          created_by: string
          description: string | null
          entry_message: string | null
          exit_message: string | null
          icon: string | null
          id: string
          is_active: boolean | null
          name: string
          polygon: unknown[] | null
          polygon_geojson: Json | null
          radius: number | null
          type: string
          updated_at: string | null
        }[]
      }
      find_nearby_locations: {
        Args: {
          current_lat: number
          current_lng: number
          max_distance?: number
          max_results?: number
        }
        Returns: {
          id: string
          conversation_id: string
          sender_id: string
          created_at: string
          label: string
          lat: number
          lng: number
          distance: number
          sender_name: string
        }[]
      }
      find_users_within_radius: {
        Args: {
          center_lat: number
          center_lng: number
          radius_km: number
          interests_filter?: string[]
          is_online_filter?: boolean
          is_service_provider_filter?: boolean
          search_query?: string
          max_results?: number
          exclude_user_id?: string
        }
        Returns: {
          user_id: string
          username: string
          display_name: string
          avatar_url: string
          location: Json
          bio: string
          last_active: string
          is_online: boolean
          distance_km: number
          interests: string[]
          is_service_provider: boolean
          service_categories: string[]
          rating: number
        }[]
      }
      flag_transaction: {
        Args: {
          p_transaction_id: string
          p_reason: string
          p_description?: string
          p_priority?: string
        }
        Returns: string
      }
      generate_notification: {
        Args: {
          p_user_id: string
          p_type: string
          p_title: string
          p_content: string
          p_source_id?: string
          p_source_type?: string
          p_action_url?: string
        }
        Returns: string
      }
      get_activities_count: {
        Args: { user_id: string }
        Returns: number
      }
      get_activity_group_chat: {
        Args: { activity_id: string }
        Returns: Json
      }
      get_admin_activities_list: {
        Args: {
          p_moderation_status?: string
          p_is_verified?: boolean
          p_sort?: string
          p_sort_direction?: string
          p_limit?: number
          p_offset?: number
        }
        Returns: Json
      }
      get_admin_dashboard_data: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_admin_dashboard_summary: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_admin_disputes_list: {
        Args: {
          p_status?: string
          p_category?: string
          p_priority?: string
          p_sort?: string
          p_sort_direction?: string
          p_limit?: number
          p_offset?: number
        }
        Returns: Json
      }
      get_admin_notifications_list: {
        Args: {
          p_is_read?: boolean
          p_severity?: string
          p_notification_type?: string
          p_sort?: string
          p_sort_direction?: string
          p_limit?: number
          p_offset?: number
        }
        Returns: Json
      }
      get_admin_reports_list: {
        Args: {
          p_report_type?: string
          p_status?: string
          p_priority?: string
          p_sort?: string
          p_sort_direction?: string
          p_limit?: number
          p_offset?: number
        }
        Returns: Json
      }
      get_admin_transactions_list: {
        Args: {
          p_status?: string
          p_type?: string
          p_is_flagged?: boolean
          p_sort?: string
          p_sort_direction?: string
          p_limit?: number
          p_offset?: number
        }
        Returns: Json
      }
      get_admin_users_list: {
        Args: {
          p_search?: string
          p_filter?: string
          p_sort?: string
          p_sort_direction?: string
          p_limit?: number
          p_offset?: number
        }
        Returns: Json
      }
      get_admin_verifications_list: {
        Args: {
          p_status?: string
          p_verification_type?: string
          p_sort?: string
          p_sort_direction?: string
          p_limit?: number
          p_offset?: number
        }
        Returns: Json
      }
      get_all_users: {
        Args: Record<PropertyKey, never>
        Returns: {
          admin_notes: string | null
          avatar_url: string | null
          ban_reason: string | null
          banned_until: string | null
          bio: string | null
          birthday: string | null
          cover_url: string | null
          created_at: string
          default_location: unknown | null
          display_name: string | null
          favorite_locations: Json | null
          gallery: string[] | null
          gallery_order: Json | null
          gender: string | null
          id: string
          interests: string[] | null
          is_admin: boolean | null
          is_banned: boolean | null
          is_bot: boolean | null
          is_profile_public: boolean | null
          is_suspended: boolean | null
          is_verified: boolean | null
          last_seen_at: string | null
          location: unknown | null
          location_display: string | null
          location_permission_granted: boolean | null
          notifications_enabled: boolean | null
          notifyactivities: boolean | null
          notifyfollows: boolean | null
          notifymessages: boolean | null
          onboarding_completed: boolean | null
          profile_theme: string | null
          profile_visibility: Json | null
          purposes: string[] | null
          social_links: Json | null
          suspended_by: string | null
          suspended_until: string | null
          suspension_reason: string | null
          trust_score: number | null
          updated_at: string
          username: string | null
          vibes: string[] | null
        }[]
      }
      get_analytics_dashboard: {
        Args: { p_start_date?: string; p_end_date?: string }
        Returns: Json
      }
      get_content_report_statistics: {
        Args: Record<PropertyKey, never>
        Returns: {
          pending_reports: number
          reports_this_month: number
          reports_last_month: number
          growth_percentage: number
        }[]
      }
      get_follower_count: {
        Args: { user_id: string }
        Returns: number
      }
      get_following_count: {
        Args: { user_id: string }
        Returns: number
      }
      get_gig_rating_stats: {
        Args: { p_gig_id: string }
        Returns: {
          average_rating: number
          total_reviews: number
          rating_1: number
          rating_2: number
          rating_3: number
          rating_4: number
          rating_5: number
        }[]
      }
      get_moderation_analytics: {
        Args: { p_start_date?: string; p_end_date?: string }
        Returns: Json
      }
      get_monthly_revenue: {
        Args: { months_back?: number }
        Returns: {
          month: string
          revenue: number
        }[]
      }
      get_mutual_connections: {
        Args: { user_id1: string; user_id2: string }
        Returns: number
      }
      get_participated_activities_count: {
        Args: { user_id: string }
        Returns: number
      }
      get_provider_rating_stats: {
        Args: { p_provider_id: string }
        Returns: {
          average_rating: number
          total_reviews: number
          rating_1: number
          rating_2: number
          rating_3: number
          rating_4: number
          rating_5: number
        }[]
      }
      get_total_revenue: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      get_transaction_analytics: {
        Args: { p_start_date?: string; p_end_date?: string }
        Returns: Json
      }
      get_unread_notification_count: {
        Args: Record<PropertyKey, never>
        Returns: Json
      }
      get_user_admin_details: {
        Args: { p_user_id: string }
        Returns: Json
      }
      get_user_statistics: {
        Args: Record<PropertyKey, never>
        Returns: {
          total_users: number
          new_users_this_month: number
          new_users_last_month: number
          growth_percentage: number
        }[]
      }
      grant_role: {
        Args: {
          p_user_id: string
          p_role: string
          p_reason?: string
          p_expires_at?: string
        }
        Returns: boolean
      }
      has_permission: {
        Args: { p_permission: string }
        Returns: boolean
      }
      insert_activity: {
        Args: {
          p_title: string
          p_description: string
          p_category_id: string
          p_location_x: number
          p_location_y: number
          p_address: string
          p_start_time: string
          p_end_time: string
          p_is_paid: boolean
          p_price: number
          p_max_participants: number
          p_host_id: string
          p_media_urls: Json
          p_queue_type: string
          p_allow_waitlist: boolean
          p_visibility: string
          p_status: string
        }
        Returns: {
          address: string | null
          allow_waitlist: boolean | null
          category_id: string | null
          created_at: string | null
          description: string | null
          early_bird_price: number | null
          end_time: string | null
          group_chat_id: string | null
          group_discount_percentage: number | null
          group_discount_threshold: number | null
          host_id: string
          id: string
          is_paid: boolean | null
          location: unknown
          max_participants: number | null
          media_urls: string[] | null
          price: number | null
          queue_type: string | null
          start_time: string
          status: string | null
          title: string
          updated_at: string | null
          visibility: string | null
        }[]
      }
      is_admin: {
        Args: Record<PropertyKey, never>
        Returns: boolean
      }
      is_following: {
        Args: { follower: string; following: string }
        Returns: boolean
      }
      is_location_expired: {
        Args:
          | { location_data: Json }
          | { location_data: Json }
          | { location_time: string }
        Returns: boolean
      }
      is_point_in_geofence: {
        Args: { point_lng: number; point_lat: number; geofence_id: string }
        Returns: boolean
      }
      issue_refund: {
        Args: { p_transaction_id: string; p_amount: number; p_reason: string }
        Returns: string
      }
      log_analytics_event: {
        Args: {
          p_event_type: string
          p_event_source: string
          p_event_data?: Json
          p_session_id?: string
          p_device_info?: Json
        }
        Returns: string
      }
      log_geofence_event: {
        Args: {
          p_geofence_id: string
          p_user_id: string
          p_event_type: string
          p_lng: number
          p_lat: number
          p_metadata?: Json
        }
        Returns: Json
      }
      mark_all_notifications_read: {
        Args: Record<PropertyKey, never>
        Returns: number
      }
      mark_notification_read: {
        Args: { p_notification_id: string }
        Returns: boolean
      }
      mark_onboarding_completed: {
        Args: { user_id: string }
        Returns: undefined
      }
      moderate_content: {
        Args: { content_text: string }
        Returns: Json
      }
      process_verification_request: {
        Args: {
          p_verification_id: string
          p_status: string
          p_notes?: string
          p_verification_level_id?: string
        }
        Returns: boolean
      }
      recalculate_queue_positions: {
        Args: { p_activity_id: string }
        Returns: undefined
      }
      remove_message_reaction: {
        Args:
          | { message_id: string; user_id: string; emoji: string }
          | { message_id: string; user_id: string; emoji: string }
        Returns: Json
      }
      remove_user_from_activity_chat: {
        Args: { p_activity_id: string; p_user_id: string }
        Returns: boolean
      }
      report_safety_incident: {
        Args: {
          p_activity_id: string
          p_incident_type: string
          p_description: string
          p_severity: string
          p_location_description?: string
          p_incident_time?: string
        }
        Returns: string
      }
      resolve_content_report: {
        Args: {
          p_report_id: string
          p_status: string
          p_action_taken?: string
          p_review_notes?: string
        }
        Returns: boolean
      }
      resolve_dispute: {
        Args: {
          p_dispute_id: string
          p_resolution_type: string
          p_resolution_amount?: number
          p_resolution_notes?: string
        }
        Returns: boolean
      }
      resolve_transaction_flag: {
        Args: { p_flag_id: string; p_status: string; p_notes?: string }
        Returns: boolean
      }
      resolve_user_report: {
        Args: {
          p_report_id: string
          p_status: string
          p_action_taken?: string
          p_review_notes?: string
        }
        Returns: boolean
      }
      respond_to_activity_proposal: {
        Args: { p_message_id: string; p_user_id: string; p_response: string }
        Returns: boolean
      }
      review_transaction: {
        Args: { p_transaction_id: string; p_status: string; p_notes?: string }
        Returns: string
      }
      revoke_role: {
        Args: { p_user_id: string; p_role: string; p_reason?: string }
        Returns: boolean
      }
      send_activity_proposal: {
        Args: {
          p_conversation_id: string
          p_sender_id: string
          p_activity_id: string
          p_message?: string
        }
        Returns: string
      }
      send_activity_update_notification: {
        Args: {
          p_activity_id: string
          p_update_type: string
          p_message: string
        }
        Returns: boolean
      }
      send_location_message: {
        Args:
          | {
              p_conversation_id: string
              p_sender_id: string
              p_lat: number
              p_lng: number
              p_label: string
              p_expiration: string
              p_content?: string
            }
          | {
              p_conversation_id: string
              p_sender_id: string
              p_lat: number
              p_lng: number
              p_label: string
              p_expiration: string
              p_content?: string
            }
        Returns: string
      }
      set_onboarding_completed: {
        Args: { user_id: string }
        Returns: undefined
      }
      submit_content_report: {
        Args: {
          p_content_type: string
          p_content_id: string
          p_reported_user_id: string
          p_reason: string
          p_description?: string
          p_category?: string
          p_subcategory?: string
          p_severity?: string
          p_impact_scope?: string
          p_evidence_urls?: string[]
        }
        Returns: string
      }
      submit_user_report: {
        Args: {
          p_reported_user_id: string
          p_reason: string
          p_description?: string
          p_category?: string
          p_subcategory?: string
          p_severity?: string
          p_impact_scope?: string
          p_evidence_urls?: string[]
        }
        Returns: string
      }
      unban_user: {
        Args: { p_user_id: string; p_reason: string }
        Returns: boolean
      }
      update_avatar_url: {
        Args: { user_id: string; new_url: string }
        Returns: undefined
      }
      update_gig_rating: {
        Args: { p_gig_id: string }
        Returns: undefined
      }
      update_last_seen: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      update_onboarding_completed: {
        Args: { user_id: string; completed: boolean }
        Returns: undefined
      }
      update_provider_rating: {
        Args: { p_provider_id: string }
        Returns: undefined
      }
      update_queue_positions: {
        Args: { p_activity_id: string; p_updates: Json }
        Returns: undefined
      }
      update_transaction_status: {
        Args: { p_transaction_id: string; p_status: string; p_notes?: string }
        Returns: boolean
      }
      update_user_analytics: {
        Args: Record<PropertyKey, never>
        Returns: undefined
      }
      verify_onboarding_status: {
        Args: { user_id: string }
        Returns: boolean
      }
      warn_user: {
        Args: { p_user_id: string; p_reason: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      location_message_type: {
        lat: number | null
        lng: number | null
        label: string | null
        expiration: string | null
      }
    }
  }
}

type DefaultSchema = Database[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
