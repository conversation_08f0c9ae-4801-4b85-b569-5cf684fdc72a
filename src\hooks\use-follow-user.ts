
import { useState, useEffect } from 'react';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';

export function useFollowUser(userId?: string) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const [isFollowing, setIsFollowing] = useState<boolean | undefined>(undefined);

  // Check if the current user is following the target user
  const { data, isLoading, error } = useQuery({
    queryKey: ['follow-status', user?.id, userId],
    queryFn: async () => {
      if (!user?.id || !userId || user.id === userId) {
        return { isFollowing: false };
      }

      const { data, error } = await supabase
        .from('follows')
        .select('*')
        .eq('follower_id', user.id)
        .eq('following_id', userId)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned"
        console.error('Error checking follow status:', error);
        throw error;
      }

      return { isFollowing: !!data };
    },
    enabled: !!user?.id && !!userId && user.id !== userId,
  });

  // Update local state when the query data changes
  useEffect(() => {
    if (data !== undefined) {
      setIsFollowing(data.isFollowing);
    }
  }, [data]);

  // Follow a user
  const followMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id || !userId || user.id === userId) {
        throw new Error("Can't follow yourself or invalid user ID");
      }

      const { data, error } = await supabase
        .from('follows')
        .insert({
          follower_id: user.id,
          following_id: userId,
          created_at: new Date().toISOString(),
        })
        .select();

      if (error) {
        console.error('Error following user:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['follow-status', user?.id, userId] });
      queryClient.invalidateQueries({ queryKey: ['profile', userId] });
      queryClient.invalidateQueries({ queryKey: ['profile', user?.id] });
    },
  });

  // Unfollow a user
  const unfollowMutation = useMutation({
    mutationFn: async () => {
      if (!user?.id || !userId || user.id === userId) {
        throw new Error("Can't unfollow yourself or invalid user ID");
      }

      const { data, error } = await supabase
        .from('follows')
        .delete()
        .eq('follower_id', user.id)
        .eq('following_id', userId);

      if (error) {
        console.error('Error unfollowing user:', error);
        throw error;
      }

      return data;
    },
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['follow-status', user?.id, userId] });
      queryClient.invalidateQueries({ queryKey: ['profile', userId] });
      queryClient.invalidateQueries({ queryKey: ['profile', user?.id] });
    },
  });

  const follow = () => {
    return followMutation.mutateAsync();
  };

  const unfollow = () => {
    return unfollowMutation.mutateAsync();
  };

  // Toggle follow status
  const toggleFollow = async () => {
    if (isFollowing) {
      await unfollow();
    } else {
      await follow();
    }
  };

  return {
    isFollowing,
    follow,
    unfollow,
    toggleFollow,
    isLoading: isLoading || followMutation.isPending || unfollowMutation.isPending,
    error: error || followMutation.error || unfollowMutation.error,
  };
}
