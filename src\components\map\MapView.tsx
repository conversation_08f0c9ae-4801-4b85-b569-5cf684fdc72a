import React, { useState } from 'react';
import { MapComponent } from './MapComponent';
import { MapRightSidebar } from './MapRightSidebar';
import { MapActivityFilters } from './MapActivityFilters';
import { ActivityMapFilters } from '@/hooks/use-map-activities';
import { MapRightSidebarTabValue } from './MapRightSidebar/MapRightSidebarTypes';

// Define a proper interface for map filters
interface MapFilters {
  categories: string[];
  distance?: number;
  price?: { min: number; max: number };
}

// Define props for MapView component
interface MapViewProps {
  mapboxToken?: string;
  defaultCenter?: { lat: number; lng: number };
  activeTab?: MapRightSidebarTabValue;
  activityFilters?: ActivityMapFilters;
  locations?: any[];
  isLoading?: boolean;
}

export function MapView({ 
  mapboxToken = '', 
  defaultCenter = { lat: 37.7749, lng: -122.4194 },
  activeTab = 'users',
  activityFilters,
  locations = [],
  isLoading = false
}: MapViewProps) {
  const [filters, setFilters] = useState<MapFilters>({
    categories: [],
    distance: 5,
    price: { min: 0, max: 100 }
  });

  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [userLocations, setUserLocations] = useState<any[]>(locations || []);

  const handleFilterChange = (newFilters: MapFilters) => {
    setFilters(newFilters);

    // Example of how this would be used with a real search function
    console.log("Searching with filters:", {
      radius: newFilters.distance || 5,
      center: defaultCenter,
      categories: newFilters.categories,
      priceRange: newFilters.price
    });
  };

  const handleMarkerClick = (user: any) => {
    setSelectedUser(user);
  };

  const handleTabChange = (value: MapRightSidebarTabValue) => {
    // This will be enhanced in future updates
  };

  return (
    <div className="h-full flex overflow-hidden">
      {/* Map interface */}
      <div className="flex-1 relative">
        <MapComponent
          mapboxToken={mapboxToken}
          userLocations={userLocations.length > 0 ? userLocations : locations}
          onMarkerClick={handleMarkerClick}
        />

        {/* Filter overlay */}
        <div className="absolute top-16 left-4 w-64 bg-white rounded-lg shadow-lg">
          <MapActivityFilters onFilterChange={handleFilterChange} />
        </div>
      </div>

      {/* Right sidebar */}
      <MapRightSidebar
        users={userLocations.length > 0 ? userLocations : locations}
        onUserSelect={handleMarkerClick}
        selectedUser={selectedUser}
        activeTab={activeTab as MapRightSidebarTabValue}
        onTabChange={handleTabChange}
      />
    </div>
  );
}

// Add a default export for compatibility
export default MapView;
