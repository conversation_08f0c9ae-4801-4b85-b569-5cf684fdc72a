-- SQL script to update the profiles table with all necessary columns for onboarding

-- First, check if the profiles table exists, if not create it
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add all the necessary columns if they don't exist
ALTER TABLE public.profiles
  ADD COLUMN IF NOT EXISTS display_name TEXT,
  ADD COLUMN IF NOT EXISTS username TEXT UNIQUE,
  ADD COLUMN IF NOT EXISTS bio TEXT,
  ADD COLUMN IF NOT EXISTS avatar_url TEXT,
  ADD COLUMN IF NOT EXISTS birthday DATE,
  ADD COLUMN IF NOT EXISTS gender TEXT,
  ADD COLUMN IF NOT EXISTS purposes TEXT[] DEFAULT '{}',
  ADD COLUMN IF NOT EXISTS vibes TEXT[] DEFAULT '{}',
  ADD COLUMN IF NOT EXISTS gallery TEXT[] DEFAULT '{}',
  ADD COLUMN IF NOT EXISTS favorite_locations JSONB DEFAULT '[]',
  ADD COLUMN IF NOT EXISTS default_location POINT,
  ADD COLUMN IF NOT EXISTS location POINT,
  ADD COLUMN IF NOT EXISTS location_display TEXT,
  ADD COLUMN IF NOT EXISTS location_permission_granted BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS notifications_enabled BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS notifyactivities BOOLEAN DEFAULT TRUE,
  ADD COLUMN IF NOT EXISTS notifyfollows BOOLEAN DEFAULT TRUE,
  ADD COLUMN IF NOT EXISTS notifymessages BOOLEAN DEFAULT TRUE,
  ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS is_banned BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS is_suspended BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS is_bot BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS onboarding_completed BOOLEAN DEFAULT FALSE,
  ADD COLUMN IF NOT EXISTS interests TEXT[] DEFAULT '{}';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS profiles_username_idx ON public.profiles (username);
CREATE INDEX IF NOT EXISTS profiles_display_name_idx ON public.profiles (display_name);
CREATE INDEX IF NOT EXISTS profiles_location_idx ON public.profiles USING GIST (location);
CREATE INDEX IF NOT EXISTS profiles_default_location_idx ON public.profiles USING GIST (default_location);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Anyone can read profiles" ON public.profiles;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.profiles;
DROP POLICY IF EXISTS "Admins can update any profile" ON public.profiles;

-- Create RLS policies
CREATE POLICY "Anyone can read profiles"
ON public.profiles
FOR SELECT
USING (true);

CREATE POLICY "Users can update their own profile"
ON public.profiles
FOR UPDATE
USING (id = (SELECT auth.uid()))
WITH CHECK (id = (SELECT auth.uid()));

CREATE POLICY "Users can insert their own profile"
ON public.profiles
FOR INSERT
WITH CHECK (id = (SELECT auth.uid()));

-- Create or replace the function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into public.profiles
  INSERT INTO public.profiles (
    id,
    username,
    display_name,
    avatar_url,
    bio,
    updated_at,
    created_at
  )
  VALUES (
    NEW.id,
    lower(split_part(NEW.email, '@', 1)), -- Generate a username from the email
    COALESCE(
      NEW.raw_user_meta_data->>'first_name' || ' ' || NEW.raw_user_meta_data->>'last_name',
      split_part(NEW.email, '@', 1)
    ), -- Use first_name and last_name from metadata if available
    NULL, -- No avatar initially
    NULL, -- No bio initially
    NOW(),
    NOW()
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_trigger WHERE tgname = 'on_auth_user_created'
  ) THEN
    CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();
  END IF;
END
$$;

-- Create a function to check if a user is an admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    SELECT EXISTS (
      SELECT 1 FROM public.profiles
      WHERE id = (SELECT auth.uid()) AND is_admin = TRUE
    )
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a policy for admins to update any profile
CREATE POLICY "Admins can update any profile"
ON public.profiles
FOR UPDATE
USING (is_admin())
WITH CHECK (is_admin());

-- Create a policy for admins to insert any profile
CREATE POLICY "Admins can insert any profile"
ON public.profiles
FOR INSERT
WITH CHECK (is_admin());
