
import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { MapPin, Plus } from 'lucide-react';
import { SavedLocationModal } from './SavedLocationModal';

interface SaveLocationButtonProps {
  currentLocation: { x: number; y: number } | null;
  className?: string;
}

export function SaveLocationButton({ currentLocation, className }: SaveLocationButtonProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className={`flex items-center gap-1 bg-background/95 backdrop-blur-sm shadow-md ${className}`}
        disabled={!currentLocation}
        onClick={() => setIsModalOpen(true)}
      >
        <MapPin className="h-4 w-4" />
        <Plus className="h-3 w-3" />
        <span className="sr-only md:not-sr-only md:inline-block md:ml-1">Save Location</span>
      </Button>

      {currentLocation && (
        <SavedLocationModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          location={currentLocation}
        />
      )}
    </>
  );
}
