
import React, { useState, useRef } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, Di<PERSON>T<PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Slider } from "@/components/ui/slider";
import { Camera, Upload, RefreshCw, Trash2, CheckCircle, Move, ZoomIn, ZoomOut } from "lucide-react";
import { cn } from "@/lib/utils";

interface CoverPhotoEditorProps {
  open: boolean;
  onClose: () => void;
  currentCoverUrl?: string | null;
  onSave: (file: File, dataUrl: string) => Promise<void>;
  isUploading?: boolean;
}

export function CoverPhotoEditor({
  open,
  onClose,
  currentCoverUrl,
  onSave,
  isUploading = false
}: CoverPhotoEditorProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(currentCoverUrl);
  const [zoom, setZoom] = useState<number>(1);
  const [position, setPosition] = useState<{ x: number; y: number }>({ x: 0, y: 0 });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const imageContainerRef = useRef<HTMLDivElement>(null);
  const isDraggingRef = useRef<boolean>(false);
  const lastPositionRef = useRef<{ x: number; y: number }>({ x: 0, y: 0 });

  // Handle file selection
  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result as string;
      setPreviewUrl(result);
      setSelectedFile(file);
      setZoom(1);
      setPosition({ x: 0, y: 0 });
    };
    reader.readAsDataURL(file);
  };

  // Trigger file input click
  const handleSelectClick = () => {
    fileInputRef.current?.click();
  };

  // Handle zoom change
  const handleZoomChange = (value: number[]) => {
    setZoom(value[0]);
  };

  // Handle save
  const handleSave = async () => {
    if (!selectedFile || !previewUrl) return;
    
    try {
      await onSave(selectedFile, previewUrl);
    } catch (error) {
      console.error('Error saving cover photo:', error);
      // You could add a toast notification here
    }
  };

  // Handle image dragging for repositioning
  const handleMouseDown = (e: React.MouseEvent<HTMLImageElement>) => {
    if (!previewUrl) return;
    
    isDraggingRef.current = true;
    lastPositionRef.current = { x: e.clientX, y: e.clientY };
    e.preventDefault();
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (!isDraggingRef.current) return;
    
    const deltaX = e.clientX - lastPositionRef.current.x;
    const deltaY = e.clientY - lastPositionRef.current.y;
    
    setPosition(prev => ({
      x: prev.x + deltaX,
      y: prev.y + deltaY
    }));
    
    lastPositionRef.current = { x: e.clientX, y: e.clientY };
  };

  const handleMouseUp = () => {
    isDraggingRef.current = false;
  };

  // Handle touch events for mobile
  const handleTouchStart = (e: React.TouchEvent<HTMLImageElement>) => {
    if (!previewUrl || e.touches.length !== 1) return;
    
    isDraggingRef.current = true;
    lastPositionRef.current = {
      x: e.touches[0].clientX,
      y: e.touches[0].clientY
    };
    e.preventDefault();
  };

  const handleTouchMove = (e: React.TouchEvent<HTMLDivElement>) => {
    if (!isDraggingRef.current || e.touches.length !== 1) return;
    
    const deltaX = e.touches[0].clientX - lastPositionRef.current.x;
    const deltaY = e.touches[0].clientY - lastPositionRef.current.y;
    
    setPosition(prev => ({
      x: prev.x + deltaX,
      y: prev.y + deltaY
    }));
    
    lastPositionRef.current = {
      x: e.touches[0].clientX,
      y: e.touches[0].clientY
    };
  };

  const handleTouchEnd = () => {
    isDraggingRef.current = false;
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Edit Cover Photo</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Hidden file input */}
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            accept="image/*"
            onChange={handleFileSelect}
          />
          
          {/* Preview area */}
          <div 
            ref={imageContainerRef}
            className="relative w-full h-48 bg-muted rounded-md overflow-hidden cursor-move"
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
            onTouchMove={handleTouchMove}
            onTouchEnd={handleTouchEnd}
            onTouchCancel={handleTouchEnd}
          >
            {previewUrl ? (
              <>
                <img
                  src={previewUrl}
                  alt="Cover Preview"
                  className="absolute w-full h-full object-cover transition-transform select-none"
                  style={{
                    transform: `scale(${zoom}) translate(${position.x}px, ${position.y}px)`,
                  }}
                  onMouseDown={handleMouseDown}
                  onTouchStart={handleTouchStart}
                  draggable="false"
                />
                <div className="absolute bottom-2 left-2 bg-black/60 text-white text-xs px-2 py-1 rounded-full">
                  <Move className="h-3 w-3 inline mr-1" /> Drag to reposition
                </div>
              </>
            ) : (
              <div className="flex flex-col items-center justify-center h-full">
                <Camera className="h-10 w-10 text-muted-foreground mb-2" />
                <p className="text-muted-foreground">No cover photo selected</p>
              </div>
            )}
          </div>
          
          {/* Controls */}
          {previewUrl && (
            <div className="space-y-4">
              <div className="flex items-center justify-between gap-4">
                <ZoomOut className="h-4 w-4 text-muted-foreground" />
                <Slider
                  value={[zoom]}
                  min={1}
                  max={2}
                  step={0.01}
                  onValueChange={handleZoomChange}
                  className="flex-1"
                />
                <ZoomIn className="h-4 w-4 text-muted-foreground" />
              </div>
            </div>
          )}
          
          {/* Action buttons */}
          <div className="flex gap-2 justify-center">
            <Button
              type="button"
              variant="outline"
              onClick={handleSelectClick}
              disabled={isUploading}
              className="gap-2"
            >
              <Upload className="h-4 w-4" />
              {previewUrl ? 'Change Image' : 'Upload Image'}
            </Button>
            
            {previewUrl && (
              <Button
                type="button"
                variant="outline"
                onClick={() => setPreviewUrl(null)}
                disabled={isUploading}
                className="gap-2 text-destructive"
              >
                <Trash2 className="h-4 w-4" />
                Remove
              </Button>
            )}
          </div>
        </div>
        
        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isUploading}
          >
            Cancel
          </Button>
          
          <Button
            type="button"
            onClick={handleSave}
            disabled={!selectedFile || isUploading}
            className={cn("gap-2", isUploading && "opacity-80")}
          >
            {isUploading ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Uploading...
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4" />
                Save Cover Photo
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
