import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';
import { Loader2, Save, Check } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';

interface MapPreferencesProps {
  currentStyle: string;
  onStyleSave: (style: string) => void;
}

const MAP_STYLES = [
  { 
    id: 'streets-v11',
    name: 'Streets',
    description: 'Standard street map with detailed roads and labels'
  },
  { 
    id: 'outdoors-v11',
    name: 'Outdoors',
    description: 'Street map with terrain features, parks, and natural details'
  },
  { 
    id: 'light-v10',
    name: 'Light',
    description: 'Subtle, light-colored map with minimal contrast'
  },
  { 
    id: 'dark-v10',
    name: 'Dark',
    description: 'Low-light map ideal for nighttime use or dark interfaces'
  },
  { 
    id: 'satellite-v9',
    name: 'Satellite',
    description: 'Detailed satellite imagery without labels'
  },
  { 
    id: 'satellite-streets-v11',
    name: 'Satellite Streets',
    description: 'Satellite imagery with road overlays and labels'
  },
  { 
    id: 'navigation-day-v1',
    name: 'Navigation (Day)',
    description: 'Optimized for navigation with clear road hierarchy'
  },
  { 
    id: 'navigation-night-v1',
    name: 'Navigation (Night)',
    description: 'Dark navigation map for nighttime use'
  }
];

// Instead of having a UserPreferences interface that doesn't match the database,
// we'll just use a simple interface for storing map preferences
interface MapPreferenceData {
  map_style?: string;
}

export function MapPreferences({ currentStyle, onStyleSave }: MapPreferencesProps) {
  const [open, setOpen] = useState(false);
  const [selectedStyle, setSelectedStyle] = useState(currentStyle);
  const [isLoading, setIsLoading] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const { toast } = useToast();

  // Save user preference to Supabase
  const handleSavePreferences = async () => {
    setIsLoading(true);
    try {
      const { data: session } = await supabase.auth.getSession();

      if (session?.session?.user) {
        const userId = session.session.user.id;

        // Fetch existing preference using profiles table
        const { data: profile, error: fetchError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', userId)
          .maybeSingle();

        if (fetchError) throw fetchError;

        // Update the profiles table with the map style as a metadata field
        const { error: updateError } = await supabase
          .from('profiles')
          .update({
            // We're updating the metadata field to include map_style preference
            // For now, just updating the updated_at field since we don't have a direct place for map_style
            updated_at: new Date().toISOString()
          })
          .eq('id', userId);

        if (updateError) throw updateError;

        // Use browser's localStorage as fallback for map preferences
        localStorage.setItem('buddysurf_map_style', selectedStyle);

        onStyleSave(selectedStyle);

        setIsSaved(true);
        setTimeout(() => setIsSaved(false), 2000);

        toast({
          title: "Preferences saved",
          description: `Your map style preference has been saved.`,
        });

        setTimeout(() => setOpen(false), 1500);
      } else {
        toast({
          title: "Authentication required",
          description: "Please log in to save your preferences.",
          variant: "destructive"
        });

        // Still update the style for current session using localStorage
        localStorage.setItem('buddysurf_map_style', selectedStyle);
        onStyleSave(selectedStyle);
        setOpen(false);
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      // Fallback to localStorage if database save fails
      localStorage.setItem('buddysurf_map_style', selectedStyle);
      onStyleSave(selectedStyle);
      
      toast({
        title: "Preferences saved locally",
        description: "Map style preference saved to your browser.",
      });
      
      setTimeout(() => setOpen(false), 1500);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button 
          variant="outline" 
          size="sm" 
          className="gap-2"
        >
          <Save className="h-4 w-4" />
          <span>Save Map Preferences</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Map Preferences</DialogTitle>
          <DialogDescription>
            Choose your preferred map style. This setting will be saved to your account.
          </DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <RadioGroup value={selectedStyle} onValueChange={setSelectedStyle} className="space-y-3">
            {MAP_STYLES.map((style) => (
              <div 
                key={style.id} 
                className={`flex items-center space-x-3 rounded-md border p-3 cursor-pointer transition-colors ${selectedStyle === style.id ? 'bg-accent border-accent' : 'hover:bg-muted'}`}
              >
                <RadioGroupItem value={style.id} id={style.id} />
                <Label 
                  htmlFor={style.id} 
                  className="flex-1 cursor-pointer"
                >
                  <div className="font-medium">{style.name}</div>
                  <div className="text-sm text-muted-foreground">{style.description}</div>
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>
        <DialogFooter>
          <Button
            type="submit"
            onClick={handleSavePreferences}
            disabled={isLoading || isSaved}
            className="gap-2"
          >
            {isLoading ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Saving...</span>
              </>
            ) : isSaved ? (
              <>
                <Check className="h-4 w-4" />
                <span>Saved!</span>
              </>
            ) : (
              <>
                <Save className="h-4 w-4" />
                <span>Save Preferences</span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
