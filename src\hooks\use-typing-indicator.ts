
import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { supabase } from '@/integrations/supabase/client';

interface TypingUser {
  id: string;
  display_name?: string;
  username?: string;
  avatar_url?: string;
}

export function useTypingIndicator(conversationId: string | undefined) {
  const { user } = useAuth();
  const [typingUsers, setTypingUsers] = useState<TypingUser[]>([]);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Set up real-time subscription for typing indicators
  useEffect(() => {
    if (!conversationId || !user) return;

    const channel = supabase.channel(`typing:${conversationId}`)
      .on('broadcast', { event: 'typing' }, (payload) => {
        const { user_id, is_typing, user_info } = payload.payload;

        if (user_id === user.id) return; // Don't show own typing indicator

        setTypingUsers(prev => {
          if (is_typing) {
            // Add user to typing list if not already there
            const exists = prev.find(u => u.id === user_id);
            if (!exists) {
              return [...prev, { id: user_id, ...user_info }];
            }
            return prev;
          } else {
            // Remove user from typing list
            return prev.filter(u => u.id !== user_id);
          }
        });
      })
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [conversationId, user]);

  const startTyping = useCallback(() => {
    if (!user || !conversationId) return;

    // Clear any existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Broadcast typing status
    const channel = supabase.channel(`typing:${conversationId}`);
    channel.send({
      type: 'broadcast',
      event: 'typing',
      payload: {
        user_id: user.id,
        is_typing: true,
        user_info: {
          display_name: user.user_metadata?.display_name,
          username: user.user_metadata?.username,
          avatar_url: user.user_metadata?.avatar_url
        }
      }
    });

    // Auto-stop typing after 3 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      stopTyping();
    }, 3000);
  }, [user, conversationId]);

  const stopTyping = useCallback(() => {
    if (!user || !conversationId) return;

    // Clear timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }

    // Broadcast stop typing
    const channel = supabase.channel(`typing:${conversationId}`);
    channel.send({
      type: 'broadcast',
      event: 'typing',
      payload: {
        user_id: user.id,
        is_typing: false
      }
    });
  }, [user, conversationId]);

  useEffect(() => {
    // Cleanup typing status when component unmounts
    return () => {
      if (user && conversationId) {
        stopTyping();
      }
    };
  }, [user, conversationId, stopTyping]);

  return {
    typingUsers,
    startTyping,
    stopTyping
  };
}
