
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { getInitials } from '@/utils/get-initials';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { formatDistanceToNow } from 'date-fns';

interface ReadReceiptProps {
  users: Array<{
    user_id: string;
    display_name?: string;
    avatar_url?: string;
    last_read_at: string;
  }>;
  messageTimestamp: string;
  className?: string;
  align?: 'left' | 'right';
}

export function ReadReceipt({ 
  users, 
  messageTimestamp, 
  className = '',
  align = 'right'
}: ReadReceiptProps) {
  if (!users || users.length === 0) {
    return null;
  }

  // Filter users who have read this message (their last_read_at is after the message timestamp)
  const usersWhoRead = users.filter(user => {
    const messageTime = new Date(messageTimestamp).getTime();
    const readTime = new Date(user.last_read_at).getTime();
    return readTime >= messageTime;
  });

  if (usersWhoRead.length === 0) {
    return null;
  }

  // Limit to showing max 3 users
  const displayUsers = usersWhoRead.slice(0, 3);
  const remainingCount = Math.max(0, usersWhoRead.length - 3);

  return (
    <div className={`flex items-center ${align === 'right' ? 'justify-end' : 'justify-start'} ${className}`}>
      <div className={`flex ${align === 'right' ? '-space-x-2' : 'space-x-1'} ${align === 'right' ? 'flex-row-reverse' : 'flex-row'}`}>
        {displayUsers.map((user) => (
          <TooltipProvider key={user.user_id}>
            <Tooltip>
              <TooltipTrigger asChild>
                <Avatar className="h-4 w-4 border border-background">
                  {user.avatar_url ? (
                    <AvatarImage src={user.avatar_url} alt={user.display_name || 'User'} />
                  ) : (
                    <AvatarFallback className="text-[8px]">
                      {getInitials(user.display_name || 'User')}
                    </AvatarFallback>
                  )}
                </Avatar>
              </TooltipTrigger>
              <TooltipContent side="bottom" align="center">
                <p>{user.display_name || 'User'} read {formatDistanceToNow(new Date(user.last_read_at), { addSuffix: true })}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        ))}
        
        {remainingCount > 0 && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <div className="h-4 w-4 rounded-full bg-muted flex items-center justify-center text-[8px] font-medium">
                  +{remainingCount}
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" align="center">
                <p>{remainingCount} more {remainingCount === 1 ? 'person' : 'people'} read this message</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  );
}
