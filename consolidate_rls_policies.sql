-- Script to consolidate duplicate RLS policies
-- This script will identify and consolidate duplicate policies for the same role and action

-- Function to consolidate policies for a specific table
CREATE OR REPLACE FUNCTION consolidate_table_policies(table_name text)
RET<PERSON><PERSON> void AS $$
DECLARE
    role_record RECORD;
    action_record RECORD;
    policy_records RECORD;
    new_policy_name text;
    policy_count integer;
    policy_names text[];
    consolidated_using text;
    consolidated_check text;
    policy_name text;
    i integer;
BEGIN
    -- Loop through each role that has policies on this table
    FOR role_record IN
        SELECT DISTINCT policyname, cmd
        FROM pg_policies
        WHERE schemaname = 'public' AND tablename = table_name
        ORDER BY cmd
    LOOP
        -- Get the count of policies for this role and action
        SELECT COUNT(*) INTO policy_count
        FROM pg_policies
        WHERE schemaname = 'public'
          AND tablename = table_name
          AND cmd = role_record.cmd;

        -- If there's more than one policy for this role and action, consolidate them
        IF policy_count > 1 THEN
            RAISE NOTICE 'Consolidating % policies for % on table %',
                policy_count, role_record.cmd, table_name;

            -- Create a new policy name
            new_policy_name := 'Consolidated_' || role_record.cmd || '_policy';

            -- Get all the policy names and expressions for this role and action
            SELECT array_agg(policyname),
                   string_agg(COALESCE(qual, 'true'), ' OR ')
            INTO policy_names, consolidated_using
            FROM pg_policies
            WHERE schemaname = 'public'
              AND tablename = table_name
              AND cmd = role_record.cmd;

            -- For INSERT and UPDATE, also get the WITH CHECK expressions
            IF role_record.cmd IN ('INSERT', 'UPDATE') THEN
                SELECT string_agg(COALESCE(with_check, 'true'), ' OR ')
                INTO consolidated_check
                FROM pg_policies
                WHERE schemaname = 'public'
                  AND tablename = table_name
                  AND cmd = role_record.cmd;
            END IF;

            -- Create the new consolidated policy
            IF role_record.cmd = 'ALL' THEN
                EXECUTE format(
                    'CREATE POLICY %I ON %I FOR ALL TO public USING (%s) WITH CHECK (%s)',
                    new_policy_name,
                    table_name,
                    COALESCE(consolidated_using, 'true'),
                    COALESCE(consolidated_check, 'true')
                );
            ELSIF role_record.cmd = 'SELECT' THEN
                EXECUTE format(
                    'CREATE POLICY %I ON %I FOR SELECT TO public USING (%s)',
                    new_policy_name,
                    table_name,
                    COALESCE(consolidated_using, 'true')
                );
            ELSIF role_record.cmd = 'INSERT' THEN
                EXECUTE format(
                    'CREATE POLICY %I ON %I FOR INSERT TO public WITH CHECK (%s)',
                    new_policy_name,
                    table_name,
                    COALESCE(consolidated_check, 'true')
                );
            ELSIF role_record.cmd = 'UPDATE' THEN
                EXECUTE format(
                    'CREATE POLICY %I ON %I FOR UPDATE TO public USING (%s) WITH CHECK (%s)',
                    new_policy_name,
                    table_name,
                    COALESCE(consolidated_using, 'true'),
                    COALESCE(consolidated_check, 'true')
                );
            ELSIF role_record.cmd = 'DELETE' THEN
                EXECUTE format(
                    'CREATE POLICY %I ON %I FOR DELETE TO public USING (%s)',
                    new_policy_name,
                    table_name,
                    COALESCE(consolidated_using, 'true')
                );
            END IF;

            -- Drop the old policies
            FOR i IN 1..array_length(policy_names, 1) LOOP
                policy_name := policy_names[i];
                EXECUTE format(
                    'DROP POLICY IF EXISTS %I ON %I',
                    policy_name,
                    table_name
                );
            END LOOP;

            RAISE NOTICE 'Created consolidated policy % for % on table %',
                new_policy_name, role_record.cmd, table_name;
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to consolidate policies for all tables
CREATE OR REPLACE FUNCTION consolidate_all_policies()
RETURNS void AS $$
DECLARE
    table_record RECORD;
BEGIN
    -- Get all tables with multiple policies for the same role and action
    FOR table_record IN
        SELECT DISTINCT tablename
        FROM pg_policies
        WHERE schemaname = 'public'
        GROUP BY schemaname, tablename
        HAVING COUNT(*) > 1
    LOOP
        PERFORM consolidate_table_policies(table_record.tablename);
    END LOOP;

    RAISE NOTICE 'All policies have been consolidated.';
END;
$$ LANGUAGE plpgsql;

-- Execute the function to consolidate all policies
SELECT consolidate_all_policies();

-- Clean up by dropping the functions
DROP FUNCTION consolidate_table_policies(text);
DROP FUNCTION consolidate_all_policies();
