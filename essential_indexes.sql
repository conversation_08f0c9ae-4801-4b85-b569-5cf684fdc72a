-- Core activities table indexes
CREATE INDEX IF NOT EXISTS idx_activities_host_id ON public.activities(host_id);
CREATE INDEX IF NOT EXISTS idx_activities_category_id ON public.activities(category_id);
CREATE INDEX IF NOT EXISTS idx_activities_group_chat_id ON public.activities(group_chat_id);

-- Activity participants and queue - critical for activity functionality
CREATE INDEX IF NOT EXISTS idx_activity_participants_user_id ON public.activity_participants(user_id);
CREATE INDEX IF NOT EXISTS idx_activity_queue_user_id ON public.activity_queue(user_id);

-- Chat-related indexes - critical for messaging functionality
CREATE INDEX IF NOT EXISTS idx_chat_messages_conversation_id ON public.chat_messages(conversation_id);
CREATE INDEX IF NOT EXISTS idx_chat_messages_sender_id ON public.chat_messages(sender_id);
CREATE INDEX IF NOT EXISTS idx_chat_conversations_activity_id ON public.chat_conversations(activity_id);

-- User-related indexes - core to the application
CREATE INDEX IF NOT EXISTS idx_profiles_suspended_by ON public.profiles(suspended_by);
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON public.notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_subscriptions_user_id ON public.subscriptions(user_id);

-- Analytics - important for tracking user behavior
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON public.analytics_events(user_id);
CREATE INDEX IF NOT EXISTS idx_analytics_page_views_user_id ON public.analytics_page_views(user_id);

-- Activity views - important for tracking engagement
CREATE INDEX IF NOT EXISTS idx_activity_views_activity_id ON public.activity_views(activity_id);
CREATE INDEX IF NOT EXISTS idx_activity_views_user_id ON public.activity_views(user_id);
