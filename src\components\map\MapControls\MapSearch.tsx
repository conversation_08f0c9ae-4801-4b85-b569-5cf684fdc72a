
import React, { useState, useEffect, useRef, KeyboardEvent } from 'react';
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, X, Loader2 } from "lucide-react";
import { cn } from '@/lib/utils';

interface MapSearchProps {
  onSearch?: (query: string) => void;
  placeholder?: string;
  className?: string;
  loading?: boolean;
  onKeyDown?: (e: React.KeyboardEvent) => void;
  onClear?: () => void;
  autoFocus?: boolean;
}

const MapSearch = ({ 
  onSearch, 
  placeholder = "Search location...",
  className,
  loading = false,
  onKeyDown,
  onClear,
  autoFocus = false
}: MapSearchProps) => {
  const [query, setQuery] = useState('');
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (autoFocus && inputRef.current) {
      inputRef.current.focus();
    }
  }, [autoFocus]);

  const handleSearch = () => {
    if (onSearch && query.trim()) {
      onSearch(query.trim());
    }
  };

  const handleClear = () => {
    setQuery('');
    if (onClear) {
      onClear();
    }
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    // Allow parent component to handle key events first
    if (onKeyDown) {
      onKeyDown(e);
    }
    
    if (e.key === 'Enter' && query.trim()) {
      handleSearch();
    } else if (e.key === 'Escape') {
      handleClear();
    }
  };

  // Handle click outside to unfocus
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (inputRef.current && !inputRef.current.contains(event.target as Node)) {
        setIsSearchFocused(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className={cn("w-full", className)}>
      <div 
        className={cn(
          "flex items-center bg-background/90 backdrop-blur-md rounded-full shadow-md border border-border overflow-hidden transition-all duration-200",
          isSearchFocused && "ring-2 ring-primary ring-offset-1"
        )}
      >
        <Search className="h-4 w-4 mx-3 text-muted-foreground" />
        <Input 
          ref={inputRef}
          placeholder={placeholder}
          className="border-none shadow-none h-12 focus-visible:ring-0 bg-transparent"
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          onKeyDown={handleKeyDown}
          onFocus={() => setIsSearchFocused(true)}
          aria-label="Search locations or users"
        />
        {query && !loading && (
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-10 w-10 mr-1 rounded-full hover:bg-muted"
            onClick={handleClear}
            type="button"
            aria-label="Clear search"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
        <Button 
          variant={isSearchFocused ? "default" : "ghost"} 
          size="icon" 
          className={cn("h-11 mr-1 rounded-full", loading && "pointer-events-none")}
          onClick={handleSearch}
          disabled={loading || !query.trim()}
          aria-label="Search"
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <Search className="h-4 w-4" />
          )}
        </Button>
      </div>
    </div>
  );
};

export default MapSearch;
