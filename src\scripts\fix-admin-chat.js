/**
 * This script fixes any issues with the admin conversation in the chat system.
 * It ensures that:
 * 1. An admin conversation exists
 * 2. All users are participants in the admin conversation
 * 3. There is at least one welcome message in the messages table
 *
 * Usage:
 * 1. Run this script with Node.js: node src/scripts/fix-admin-chat.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixAdminChat() {
  try {
    console.log('Starting admin conversation fix script...');

    // Step 1: Find or create the admin conversation
    console.log('Checking for existing admin conversation...');
    const { data: existingConversation, error: queryError } = await supabase
      .from('chat_conversations')
      .select('*')
      .or('is_announcement_only.eq.true,is_admin_conversation.eq.true')
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (queryError) {
      console.error('Error querying admin conversation:', queryError);
      throw queryError;
    }

    let conversationId;

    if (existingConversation) {
      console.log(`Found existing admin conversation: ${existingConversation.id}`);
      conversationId = existingConversation.id;

      // Make sure both flags are set correctly
      if (!existingConversation.is_admin_conversation || !existingConversation.is_announcement_only) {
        console.log('Updating admin conversation flags...');
        await supabase
          .from('chat_conversations')
          .update({
            is_admin_conversation: true,
            is_announcement_only: true,
            is_group: true
          })
          .eq('id', conversationId);
      }
    } else {
      console.log('No admin conversation found, creating one...');
      const { data: newConversation, error: createError } = await supabase
        .from('chat_conversations')
        .insert({
          is_announcement_only: true,
          is_admin_conversation: true,
          is_group: true,
          last_message: 'Welcome to BuddySurf! This is the official announcement channel.',
          last_message_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      console.log(`Created new admin conversation: ${newConversation.id}`);
      conversationId = newConversation.id;
    }

    // Step 2: Check for welcome messages
    console.log('Checking for welcome messages...');
    const { data: messages, error: messagesError } = await supabase
      .from('messages')
      .select('id, content')
      .eq('conversation_id', conversationId)
      .order('created_at', { ascending: true })
      .limit(5);

    if (messagesError) {
      console.error('Error checking messages:', messagesError);
      throw messagesError;
    }

    // Step 3: Add welcome message if none exists
    if (!messages || messages.length === 0) {
      console.log('No welcome messages found, adding one...');

      // Get the first user to use as sender (or create a placeholder)
      const { data: firstUser } = await supabase
        .from('profiles')
        .select('id')
        .limit(1)
        .single();

      const senderId = firstUser?.id || '00000000-0000-0000-0000-000000000000';

      // Add welcome message
      const { error: welcomeError } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: senderId,
          recipient_id: senderId,
          content: 'Welcome to BuddySurf! 👋 This is the official announcement channel where you\'ll receive important updates and information. Stay tuned for exciting news and features!',
          is_read: false,
          is_admin_message: true,
          created_at: new Date().toISOString()
        });

      if (welcomeError) {
        console.error('Error adding welcome message:', welcomeError);
        throw welcomeError;
      }

      console.log('Welcome message added successfully.');

      // Update the conversation's last message
      await supabase
        .from('chat_conversations')
        .update({
          last_message: 'Welcome to BuddySurf! 👋 This is the official announcement channel.',
          last_message_at: new Date().toISOString()
        })
        .eq('id', conversationId);
    } else {
      console.log(`Found ${messages.length} existing messages in the admin conversation.`);
    }

    // Step 4: Ensure all users are participants
    console.log('Ensuring all users are participants in the admin conversation...');

    // Get all users
    const { data: allUsers, error: usersError } = await supabase
      .from('profiles')
      .select('id');

    if (usersError) {
      throw usersError;
    }

    // Get existing participants
    const { data: existingParticipants, error: participantsError } = await supabase
      .from('chat_participants')
      .select('user_id')
      .eq('conversation_id', conversationId);

    if (participantsError) {
      throw participantsError;
    }

    // Create a set of existing participant user IDs for quick lookup
    const existingParticipantIds = new Set(existingParticipants.map(p => p.user_id));

    // Add users who are not already participants
    const newParticipants = allUsers
      .filter(user => !existingParticipantIds.has(user.id))
      .map(user => ({
        conversation_id: conversationId,
        user_id: user.id,
        joined_at: new Date().toISOString()
      }));

    if (newParticipants.length > 0) {
      console.log(`Adding ${newParticipants.length} new participants...`);
      const { error: insertError } = await supabase
        .from('chat_participants')
        .insert(newParticipants);

      if (insertError) {
        throw insertError;
      }

      console.log('New participants added successfully.');
    } else {
      console.log('All users are already participants.');
    }

    console.log('Admin conversation fix completed successfully!');
  } catch (error) {
    console.error('Error fixing admin conversation:', error);
  }
}

fixAdminChat();
