
import { create } from 'zustand';

interface EmergencyContactData {
  title: string;
  location: string;
  startTime: string;
  endTime?: string;
  hostName: string;
}

interface EmergencyContactStore {
  emergencyContact: EmergencyContactData | null;
  hasEmergencyContact: boolean;
  setEmergencyContact: (data: EmergencyContactData) => void;
  clearEmergencyContact: () => void;
}

export const useEmergencyContact = create<EmergencyContactStore>((set) => ({
  emergencyContact: null,
  hasEmergencyContact: false,
  setEmergencyContact: (data: EmergencyContactData) => 
    set({ emergencyContact: data, hasEmergencyContact: true }),
  clearEmergencyContact: () => 
    set({ emergencyContact: null, hasEmergencyContact: false }),
}));
