
import React from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Label } from "@/components/ui/label";
import { Plus, Trash2, Link as LinkIcon, Instagram, Twitter, Facebook, Linkedin, Globe, Github, Youtube, Twitch } from "lucide-react";
import { SocialLink } from "@/types/enhanced-profile";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface SocialLinksEditorProps {
  links: SocialLink[];
  onChange: (links: SocialLink[]) => void;
}

// Platform options with icons
const platformOptions = [
  { value: 'website', label: 'Website', icon: <Globe className="h-4 w-4" /> },
  { value: 'instagram', label: 'Instagram', icon: <Instagram className="h-4 w-4" /> },
  { value: 'twitter', label: 'Twitter/X', icon: <Twitter className="h-4 w-4" /> },
  { value: 'facebook', label: 'Facebook', icon: <Facebook className="h-4 w-4" /> },
  { value: 'linkedin', label: 'LinkedIn', icon: <Linkedin className="h-4 w-4" /> },
  { value: 'github', label: 'GitHub', icon: <Github className="h-4 w-4" /> },
  { value: 'youtube', label: 'YouTube', icon: <Youtube className="h-4 w-4" /> },
  { value: 'twitch', label: 'Twitch', icon: <Twitch className="h-4 w-4" /> },
];

export function SocialLinksEditor({ links, onChange }: SocialLinksEditorProps) {
  // Add a new empty link
  const handleAddLink = () => {
    const newLinks = [...links, { platform: '', url: '', username: '' }];
    onChange(newLinks);
  };

  // Remove a link at the given index
  const handleRemoveLink = (index: number) => {
    const newLinks = [...links];
    newLinks.splice(index, 1);
    onChange(newLinks);
  };

  // Update a specific field of a link
  const handleUpdateLink = (index: number, field: keyof SocialLink, value: string) => {
    const newLinks = [...links];
    newLinks[index] = { ...newLinks[index], [field]: value };
    
    // When platform changes, help the user by formatting the URL
    if (field === 'platform' && newLinks[index].url === '') {
      switch(value) {
        case 'instagram':
          newLinks[index].url = 'https://instagram.com/';
          break;
        case 'twitter':
          newLinks[index].url = 'https://twitter.com/';
          break;
        case 'facebook':
          newLinks[index].url = 'https://facebook.com/';
          break;
        case 'linkedin':
          newLinks[index].url = 'https://linkedin.com/in/';
          break;
        case 'github':
          newLinks[index].url = 'https://github.com/';
          break;
        case 'youtube':
          newLinks[index].url = 'https://youtube.com/c/';
          break;
        case 'twitch':
          newLinks[index].url = 'https://twitch.tv/';
          break;
        default:
          newLinks[index].url = 'https://';
      }
    }
    
    onChange(newLinks);
  };

  // Get icon component for a platform
  const getPlatformIcon = (platform: string) => {
    const option = platformOptions.find(opt => opt.value === platform);
    return option?.icon || <LinkIcon className="h-4 w-4" />;
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Label className="text-base">Social Links</Label>
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleAddLink}
          className="gap-1"
        >
          <Plus className="h-4 w-4" />
          Add Link
        </Button>
      </div>

      <Separator />

      {links.length === 0 ? (
        <div className="text-center py-4 text-muted-foreground">
          No social links added yet. Click "Add Link" to add your social media profiles.
        </div>
      ) : (
        <div className="space-y-4">
          {links.map((link, index) => (
            <div key={index} className="grid gap-3 sm:grid-cols-[1fr,2fr,auto] items-start">
              <div>
                <Select
                  value={link.platform}
                  onValueChange={(value) => handleUpdateLink(index, 'platform', value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select platform">
                      {link.platform ? (
                        <div className="flex items-center gap-2">
                          {getPlatformIcon(link.platform)}
                          <span>{platformOptions.find(opt => opt.value === link.platform)?.label || link.platform}</span>
                        </div>
                      ) : (
                        "Select platform"
                      )}
                    </SelectValue>
                  </SelectTrigger>
                  <SelectContent>
                    {platformOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          {option.icon}
                          <span>{option.label}</span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <Input
                placeholder="URL"
                value={link.url}
                onChange={(e) => handleUpdateLink(index, 'url', e.target.value)}
              />
              
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={() => handleRemoveLink(index)}
                className="h-10 w-10 text-destructive"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
