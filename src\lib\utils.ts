
import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export function getActivityGradient(categoryName?: string, isBackground = true) {
  const category = categoryName?.toLowerCase();
  
  if (isBackground) {
    if (category === 'sports' || category === 'fitness') 
      return 'from-blue-500/10 to-cyan-500/10 border-blue-200/30';
    if (category === 'music') 
      return 'from-indigo-500/10 to-purple-500/10 border-purple-200/30';
    if (category === 'social' || category === 'dating') 
      return 'from-pink-500/10 to-rose-500/10 border-pink-200/30';
    if (category === 'outdoors') 
      return 'from-green-500/10 to-emerald-500/10 border-green-200/30';
    if (category === 'dining') 
      return 'from-amber-500/10 to-orange-500/10 border-amber-200/30';
    return 'from-primary/10 to-primary-purple/10 border-primary/20'; // default
  } else {
    if (category === 'sports' || category === 'fitness') 
      return 'from-blue-500 to-cyan-500 text-white';
    if (category === 'music') 
      return 'from-indigo-500 to-purple-500 text-white';
    if (category === 'social' || category === 'dating') 
      return 'from-pink-500 to-rose-500 text-white';
    if (category === 'outdoors') 
      return 'from-green-500 to-emerald-500 text-white';
    if (category === 'dining') 
      return 'from-amber-500 to-orange-500 text-white';
    return 'from-primary-blue to-primary-deep-purple text-white'; // default
  }
}

export function getCategoryEmoji(categoryName: string): string {
  const categoryEmojis: Record<string, string> = {
    sports: '🏀',
    dining: '🍽️',
    music: '🎵',
    outdoors: '🌳',
    art: '🎨',
    technology: '💻',
    gaming: '🎮',
    education: '📚',
    fitness: '💪',
    social: '🎉',
    volunteering: '🤝',
    dating: '❤️',
    professional: '💼',
    entertainment: '🎬',
    travel: '✈️',
    shopping: '🛍️',
    wellness: '🧘‍♀️',
    default: '📌'
  };
  
  return categoryEmojis[categoryName.toLowerCase()] || categoryEmojis.default;
}
