
export { UserMarker } from './UserMarker';
export { EnhancedUserMarker } from './EnhancedUserMarker';
export { UserMarkerCard } from './UserMarkerCard';
export { ActivityMapMarker } from './ActivityMapMarker';
export { ActivityMarker } from './ActivityMarker';
export { createActivityMarker, createUserMarker } from './createMapMarker';
export { AddressMinimap } from './AddressMinimap';
export { AddressLocationPicker } from '../AddressLocationPicker';

// Define common location types to ensure consistency
export interface MapLocation {
  lng: number;
  lat: number;
  address?: string;
  x?: number; // For backward compatibility
  y?: number; // For backward compatibility
}

export interface SearchLocation {
  name: string;
  coordinates: [number, number];
  placeType?: string;
}
