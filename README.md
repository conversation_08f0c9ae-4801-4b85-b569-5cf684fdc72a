
# BuddySurf

BuddySurf is a social platform to discover activities, friends, gigs, and more — all powered by location and real-time connections.

## Main Features

- **Interactive 3D Map**: Find people and activities around you in real-time
- **Activity Planning**: Create, join, and share activities with others
- **Hiring Marketplace**: Find local buddies offering services (coming soon)
- **Real-time Chat**: Connect with others through messaging and proposals (coming soon)
- **Profile System**: Showcase your interests, services, and availability

## Key Technologies

- **Frontend**: React, TypeScript, Tailwind CSS, Shadcn UI
- **Backend**: Supabase (Auth, Database, Storage, Realtime)
- **Maps**: Mapbox GL JS with 3D buildings and custom styling
- **Payments**: Stripe integration (upcoming)

## Getting Started

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables (see `.env.example`)
4. Start the development server: `npm run dev`

## Project Structure

- `src/components`: Reusable UI components
- `src/hooks`: Custom React hooks
- `src/pages`: Main application pages
- `src/utils`: Utility functions
- `src/types`: TypeScript type definitions
- `src/integrations`: Third-party service integrations

## Documentation

- [Developer Documentation](./src/DOCUMENTATION.md)
- [Project Roadmap](./ROADMAP.md)
- [Implementation Status](./STATUS.md)
- [API Documentation](./API_DOCS.md)

## Features & Pages

- **Landing Page** (`/`): Entry point to the application
- **Home** (`/home`): Dashboard with quick access to features
- **MeetUp** (`/meetup`): Discover and join activities
- **Meet Map** (`/meetmap`): Interactive 3D map with real-time locations
- **Activity Surf** (`/activity`): Browse all available activities
- **Hire Friend** (`/hire`): Find and book local services (coming soon)
- **Chat** (`/chat`): Message other users and send proposals (coming soon)
- **Profile** (`/profile`): View and edit your profile
- **Settings** (`/settings`): Manage account preferences
- **Admin** (`/admin`): Platform management (admin only)

## Subscription Plans

- **Weekly**: $3.99 with 14-day trial
- **Monthly**: $15
- **Lifetime**: $35

_See `/docs` and in-app links for detailed guides._
