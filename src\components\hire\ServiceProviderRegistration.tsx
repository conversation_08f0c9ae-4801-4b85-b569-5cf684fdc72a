
import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useCreateServiceProvider } from "@/hooks/use-service-provider";

interface ServiceProviderRegistrationProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ServiceProviderRegistration({ isOpen, onClose }: ServiceProviderRegistrationProps) {
  const [formData, setFormData] = useState({
    business_name: '',
    description: '',
    hourly_rate: '',
    location: '',
    tags: ''
  });

  const createProvider = useCreateServiceProvider();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    createProvider.mutate({
      ...formData,
      hourly_rate: parseFloat(formData.hourly_rate),
      tags: formData.tags.split(',').map(tag => tag.trim()).filter(Boolean),
      service_area_radius: 25,
      availability_schedule: {},
      portfolio_images: [],
      certifications: [],
      insurance_info: {},
      business_documents: {}
    }, {
      onSuccess: () => {
        onClose();
        setFormData({
          business_name: '',
          description: '',
          hourly_rate: '',
          location: '',
          tags: ''
        });
      }
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle>Become a Buddy Provider</DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <Label htmlFor="business_name">Business Name</Label>
            <Input
              id="business_name"
              value={formData.business_name}
              onChange={(e) => setFormData({ ...formData, business_name: e.target.value })}
              placeholder="Your business or service name"
              required
            />
          </div>

          <div>
            <Label htmlFor="description">Description</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              placeholder="Describe your services..."
              className="h-24"
              required
            />
          </div>

          <div>
            <Label htmlFor="hourly_rate">Hourly Rate ($)</Label>
            <Input
              id="hourly_rate"
              type="number"
              min="10"
              value={formData.hourly_rate}
              onChange={(e) => setFormData({ ...formData, hourly_rate: e.target.value })}
              placeholder="50"
              required
            />
          </div>

          <div>
            <Label htmlFor="location">Location</Label>
            <Input
              id="location"
              value={formData.location}
              onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              placeholder="City, State"
              required
            />
          </div>

          <div>
            <Label htmlFor="tags">Skills/Tags (comma separated)</Label>
            <Input
              id="tags"
              value={formData.tags}
              onChange={(e) => setFormData({ ...formData, tags: e.target.value })}
              placeholder="tour guide, photography, local expert"
            />
          </div>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={createProvider.isPending}>
              {createProvider.isPending ? 'Registering...' : 'Register'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
}
