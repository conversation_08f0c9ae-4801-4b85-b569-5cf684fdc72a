# BuddySurf System Integration Documentation

This document outlines how the Map, Activity, Hire Marketplace, Chat, and Admin systems are integrated to work together seamlessly.

## 1. Overview

BuddySurf is a platform that connects users for activities, services, and social interactions. The core systems are:

- **Map System**: Displays user locations, activities, and service providers on an interactive map
- **Activity System**: Allows users to create, join, and manage activities
- **Hire Marketplace**: Enables users to offer and book services
- **Chat System**: Provides real-time messaging between users, activity participants, and service providers
- **Admin System**: Offers platform management tools for administrators

## 2. Map Integration

### 2.1 User Location System

- Users' locations are tracked and displayed on the map
- Location updates occur periodically (every 5 minutes) or manually
- Default location is used as a fallback when current location is unavailable
- Privacy controls allow users to control their visibility

### 2.2 Map-Activity Integration

- Activities are displayed on the map with category-specific markers
- Clicking an activity marker shows details and options to join/chat
- Activities can be filtered by category, price, date, etc.
- The map sidebar includes an "Activities" tab showing nearby activities

### 2.3 Map-Hire Integration

- Service providers can be displayed on the map
- The map sidebar includes a "Hire" tab showing nearby service providers
- Clicking a provider marker shows details and options to contact/book

## 3. Activity-Chat Integration

### 3.1 Activity Group Chats

- Each activity automatically has an associated group chat
- All participants are added to the group chat when they join
- Activity status updates are posted in the group chat
- Chat includes location sharing for meetup coordination

### 3.2 Activity Proposals

- Users can send activity invitations through chat
- Rich activity cards in chat show details and options
- Recipients can accept/reject proposals directly in chat
- Accepted proposals automatically add users to activities

## 4. Hire Marketplace Integration

### 4.1 Service Provider Profiles

- Service providers are linked to user profiles
- Providers can be verified for trust and safety
- Profiles include ratings, reviews, and service offerings
- Services can be categorized and priced accordingly

### 4.2 Booking System

- Users can book services directly from provider profiles
- Bookings have status tracking (pending, confirmed, completed)
- Payment integration handles financial transactions
- Calendar integration manages scheduling

### 4.3 Service-Chat Integration

- Each booking creates a dedicated chat conversation
- Service proposals can be sent and accepted in chat
- Milestone tracking is visible in the booking chat
- Payment requests can be sent through chat

## 5. Admin System Integration

### 5.1 Buddy Admin Conversation

- Every user has access to the "Buddy Admin" conversation
- This conversation appears by default in the chat sidebar
- Administrators can send announcements to all users
- Users receive important updates and information

### 5.2 User Management

- Admins can view and manage all users
- Verification requests can be processed
- User analytics provide insights into platform usage
- Role management controls access to features

### 5.3 Content Moderation

- Activities and service listings can be moderated
- Content reporting system flags inappropriate content
- Automated moderation with manual review
- Admins can approve, reject, or modify content

## 6. Database Integration

### 6.1 Key Tables and Relationships

- `profiles`: User information
- `user_locations`: Real-time location data
- `activities`: Activity details
- `activity_participants`: User-activity relationships
- `chat_conversations`: Conversation metadata
- `messages`: Chat messages
- `service_providers`: Provider profiles
- `services`: Service offerings
- `service_bookings`: Booking records

### 6.2 Row-Level Security

- RLS policies ensure users can only access their authorized data
- Different policies for different user roles (regular, admin)
- Secure access to sensitive information
- Proper authentication checks

### 6.3 Real-time Updates

- Supabase Realtime provides real-time data synchronization
- Chat messages appear instantly
- Location updates are reflected on the map
- Activity status changes are propagated to all participants

## 7. Implementation Details

### 7.1 Admin Conversation

The admin conversation is a special chat conversation that:
- Is created automatically for all users
- Appears at the top of the chat sidebar
- Cannot be deleted or left
- Only allows admins to send messages
- Serves as an announcement channel

Implementation:
- `useAdminConversation` hook manages the admin conversation
- Admin messages are displayed with a special styling
- All users are automatically added as participants
- The conversation is always visible in the chat sidebar

### 7.2 Activity Group Chats

Activity group chats are created when:
- A new activity is created
- A user joins an activity

Implementation:
- `ActivityGroupChat` component handles the chat UI
- Activity details are displayed in the chat header
- Location sharing is available for coordination
- Chat is linked to the activity through the `activity_id` field

### 7.3 Service Booking Chats

Service booking chats facilitate communication between:
- Service providers and clients
- Before, during, and after service delivery

Implementation:
- Booking status is displayed in the chat
- Payment requests can be sent and processed
- Service completion can be confirmed
- Reviews can be submitted after completion

## 8. Security Considerations

- All data access is controlled through Row-Level Security
- Authentication is required for sensitive operations
- Admin privileges are strictly controlled
- Payment information is handled securely
- User location data is protected with privacy controls

## 9. Future Enhancements

- Enhanced geofencing for activities
- Advanced payment splitting for group activities
- AI-powered content moderation
- Improved recommendation system
- Enhanced analytics dashboard for admins
