
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { useEffect } from 'react';

export interface FollowUser {
  id: string;
  username: string | null;
  avatar_url: string | null;
  display_name: string | null;
}

export interface FollowData {
  followers: number;
  following: number;
  isFollowing: boolean;
  followersList: FollowUser[];
  followingList: FollowUser[];
}

export function useFollows(userId: string | undefined) {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const followsQuery = useQuery({
    queryKey: ['follows', userId],
    queryFn: async () => {
      if (!userId) return { 
        followers: 0, 
        following: 0, 
        isFollowing: false,
        followersList: [],
        followingList: []
      };
      
      // Get follower and following counts and check if the current user follows the profile
      const [
        { data: followerCountData }, 
        { data: followingCountData },
        { data: isFollowingData }
      ] = await Promise.all([
        supabase.rpc('get_follower_count', { user_id: userId }),
        supabase.rpc('get_following_count', { user_id: userId }),
        user ? supabase.rpc('is_following', { 
          follower: user.id, 
          following: userId 
        }) : { data: false }
      ]);

      // First fetch follower IDs
      const { data: followersData, error: followersError } = await supabase
        .from('follows')
        .select('follower_id')
        .eq('following_id', userId)
        .limit(10);

      if (followersError) {
        toast({
          title: 'Error fetching followers',
          description: followersError.message,
          variant: 'destructive'
        });
      }

      // Fetch profile data for each follower separately
      const followersList: FollowUser[] = [];
      if (followersData && followersData.length > 0) {
        const followerIds = followersData.map(item => item.follower_id);
        const { data: followerProfiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, username, avatar_url, display_name')
          .in('id', followerIds);

        if (profilesError) {
          toast({
            title: 'Error fetching follower profiles',
            description: profilesError.message,
            variant: 'destructive'
          });
        } else if (followerProfiles) {
          // TypeScript now correctly recognizes these as profile objects, not error objects
          followerProfiles.forEach(profile => {
            followersList.push({
              id: profile.id,
              username: profile.username,
              avatar_url: profile.avatar_url,
              display_name: profile.display_name
            });
          });
        }
      }

      // Fetch following IDs
      const { data: followingData, error: followingError } = await supabase
        .from('follows')
        .select('following_id')
        .eq('follower_id', userId)
        .limit(10);

      if (followingError) {
        toast({
          title: 'Error fetching following',
          description: followingError.message,
          variant: 'destructive'
        });
      }

      // Fetch profile data for each following separately
      const followingList: FollowUser[] = [];
      if (followingData && followingData.length > 0) {
        const followingIds = followingData.map(item => item.following_id);
        const { data: followingProfiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, username, avatar_url, display_name')
          .in('id', followingIds);

        if (profilesError) {
          toast({
            title: 'Error fetching following profiles',
            description: profilesError.message,
            variant: 'destructive'
          });
        } else if (followingProfiles) {
          // TypeScript now correctly recognizes these as profile objects, not error objects
          followingProfiles.forEach(profile => {
            followingList.push({
              id: profile.id,
              username: profile.username,
              avatar_url: profile.avatar_url,
              display_name: profile.display_name
            });
          });
        }
      }

      return {
        followers: followerCountData || 0,
        following: followingCountData || 0,
        isFollowing: isFollowingData || false,
        followersList,
        followingList
      };
    },
    enabled: !!userId
  });

  // Setup real-time listener for follows table updates
  useEffect(() => {
    if (!userId) return;

    const channel = supabase.channel(`follows_${userId}`)
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'follows',
          filter: `follower_id=eq.${userId} OR following_id=eq.${userId}`
        },
        () => {
          queryClient.invalidateQueries({ queryKey: ['follows', userId] });
        }
      )
      .subscribe();
      
    return () => {
      supabase.removeChannel(channel);
    };
  }, [userId, queryClient]);

  const followMutation = useMutation({
    mutationFn: async (userToFollowId: string) => {
      if (!user) throw new Error("You must be logged in to follow users");
      
      const { error } = await supabase
        .from('follows')
        .insert({ follower_id: user.id, following_id: userToFollowId });
      
      if (error) throw error;
    },
    onSuccess: (_, userToFollowId) => {
      queryClient.invalidateQueries({ queryKey: ['follows', userId] });
      queryClient.invalidateQueries({ queryKey: ['follows', userToFollowId] });
      toast({
        title: "Success",
        description: "You are now following this user",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to follow user: " + error.message,
        variant: "destructive"
      });
    }
  });

  const unfollowMutation = useMutation({
    mutationFn: async (userToUnfollowId: string) => {
      if (!user) throw new Error("You must be logged in to unfollow users");
      
      const { error } = await supabase
        .from('follows')
        .delete()
        .match({ follower_id: user.id, following_id: userToUnfollowId });
      
      if (error) throw error;
    },
    onSuccess: (_, userToUnfollowId) => {
      queryClient.invalidateQueries({ queryKey: ['follows', userId] });
      queryClient.invalidateQueries({ queryKey: ['follows', userToUnfollowId] });
      toast({
        title: "Success",
        description: "You have unfollowed this user",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: "Failed to unfollow user: " + error.message,
        variant: "destructive"
      });
    }
  });

  return {
    followers: followsQuery.data?.followers || 0,
    following: followsQuery.data?.following || 0,
    isFollowing: followsQuery.data?.isFollowing || false,
    followersList: followsQuery.data?.followersList || [],
    followingList: followsQuery.data?.followingList || [],
    isLoading: followsQuery.isLoading,
    follow: followMutation.mutate,
    unfollow: unfollowMutation.mutate,
    isFollowLoading: followMutation.isPending || unfollowMutation.isPending,
  };
}
