
import { MapLocationAccuracy } from '../MapLocationAccuracy';

interface MapAccuracyOverlayProps {
  accuracy: number | null;
}

export function MapAccuracyOverlay({ accuracy }: MapAccuracyOverlayProps) {
  if (!accuracy) return null;
  
  return (
    <div className="absolute top-20 right-4 z-50">
      <MapLocationAccuracy 
        accuracy={accuracy} 
        className="bg-background/95 shadow-md"
      />
    </div>
  );
}
