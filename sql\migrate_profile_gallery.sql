-- SQL script to migrate existing gallery data to the new profile_media table
-- This script should be run after enhance_profile_features.sql

-- Create a function to migrate gallery data
CREATE OR REPLACE FUNCTION migrate_profile_galleries()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  profile_record RECORD;
  gallery_url TEXT;
  position_counter INTEGER;
BEGIN
  -- Loop through all profiles with gallery data
  FOR profile_record IN 
    SELECT id, gallery 
    FROM profiles 
    WHERE gallery IS NOT NULL AND array_length(gallery, 1) > 0
  LOOP
    -- Reset position counter for each profile
    position_counter := 0;
    
    -- Loop through each gallery URL
    FOREACH gallery_url IN ARRAY profile_record.gallery
    LOOP
      -- Insert into profile_media
      INSERT INTO profile_media (
        profile_id,
        url,
        thumbnail_url,
        type,
        position,
        created_at,
        updated_at
      ) VALUES (
        profile_record.id,
        gallery_url,
        gallery_url, -- Use same URL for thumbnail (can be updated later)
        'image',
        position_counter,
        NOW(),
        NOW()
      );
      
      -- Increment position counter
      position_counter := position_counter + 1;
    END LOOP;
    
    -- Update gallery_order in profiles
    UPDATE profiles
    SET gallery_order = (
      SELECT jsonb_agg(jsonb_build_object('id', id, 'position', position))
      FROM profile_media
      WHERE profile_id = profile_record.id
      ORDER BY position
    )
    WHERE id = profile_record.id;
  END LOOP;
END;
$$;

-- Execute the migration function
SELECT migrate_profile_galleries();

-- Drop the function after use
DROP FUNCTION migrate_profile_galleries();

-- Create a function to handle profile media ordering
CREATE OR REPLACE FUNCTION reorder_profile_media(
  p_profile_id UUID,
  p_media_ids UUID[],
  p_positions INTEGER[]
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  i INTEGER;
BEGIN
  -- Check if arrays have the same length
  IF array_length(p_media_ids, 1) != array_length(p_positions, 1) THEN
    RETURN FALSE;
  END IF;
  
  -- Update positions
  FOR i IN 1..array_length(p_media_ids, 1) LOOP
    UPDATE profile_media
    SET position = p_positions[i]
    WHERE id = p_media_ids[i] AND profile_id = p_profile_id;
  END LOOP;
  
  -- Update gallery_order in profiles
  UPDATE profiles
  SET gallery_order = (
    SELECT jsonb_agg(jsonb_build_object('id', id, 'position', position))
    FROM profile_media
    WHERE profile_id = p_profile_id
    ORDER BY position
  )
  WHERE id = p_profile_id;
  
  RETURN TRUE;
END;
$$;

-- Create a function to add a media item to a profile
CREATE OR REPLACE FUNCTION add_profile_media(
  p_profile_id UUID,
  p_url TEXT,
  p_thumbnail_url TEXT,
  p_type TEXT DEFAULT 'image',
  p_caption TEXT DEFAULT NULL,
  p_is_featured BOOLEAN DEFAULT FALSE
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_position INTEGER;
  new_media_id UUID;
BEGIN
  -- Get the next position
  SELECT COALESCE(MAX(position) + 1, 0)
  INTO new_position
  FROM profile_media
  WHERE profile_id = p_profile_id;
  
  -- Insert the new media
  INSERT INTO profile_media (
    profile_id,
    url,
    thumbnail_url,
    type,
    caption,
    position,
    is_featured,
    created_at,
    updated_at
  ) VALUES (
    p_profile_id,
    p_url,
    p_thumbnail_url,
    p_type,
    p_caption,
    new_position,
    p_is_featured,
    NOW(),
    NOW()
  ) RETURNING id INTO new_media_id;
  
  -- Update gallery_order in profiles
  UPDATE profiles
  SET gallery_order = (
    SELECT jsonb_agg(jsonb_build_object('id', id, 'position', position))
    FROM profile_media
    WHERE profile_id = p_profile_id
    ORDER BY position
  )
  WHERE id = p_profile_id;
  
  RETURN new_media_id;
END;
$$;
