
import React from 'react';
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { UserAvatar } from "@/components/user/UserAvatar";
import { useFollows } from "@/hooks/use-follows";
import { Button } from "@/components/ui/button";
import { FollowButton } from "@/components/user/FollowButton";
import { useNavigate } from 'react-router-dom';
import { Badge } from "@/components/ui/badge";
import { UserPlus, Users } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from '@/hooks/use-auth';

interface ProfileFollowersProps {
  userId?: string;
}

export function ProfileFollowers({ userId }: ProfileFollowersProps) {
  const navigate = useNavigate();
  const { user } = useAuth();
  const {
    followersList,
    followingList,
    followers,
    following,
    isLoading
  } = useFollows(userId);

  const handleUserClick = (username: string, id: string) => {
    // Use clean URL structure if username is available, otherwise fall back to /profile/id
    navigate(username ? `/${username}` : `/profile/${id}`);
  };

  const handleViewAll = (type: 'followers' | 'following') => {
    if (userId) {
      navigate(`/network/${type}/${userId}`);
    }
  };

  if (isLoading) {
    return <LoadingSkeleton />;
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle>Network</CardTitle>
            <CardDescription>Your connections on BuddySurf</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Users className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">{followers} followers · {following} following</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="followers">
          <TabsList className="mb-4 w-full">
            <TabsTrigger value="followers" className="flex-1">
              Followers <Badge variant="outline" className="ml-2">{followers}</Badge>
            </TabsTrigger>
            <TabsTrigger value="following" className="flex-1">
              Following <Badge variant="outline" className="ml-2">{following}</Badge>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="followers">
            {followersList.length > 0 ? (
              <div className="space-y-4">
                {followersList.map((follower) => (
                  <div key={follower.id} className="flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg">
                    <div
                      className="flex items-center gap-3 flex-1 cursor-pointer"
                      onClick={() => handleUserClick(follower.username || '', follower.id)}
                    >
                      <UserAvatar user={follower} size="md" />
                      <div>
                        <p className="font-medium">{follower.display_name || follower.username}</p>
                        {follower.username && (
                          <p className="text-sm text-gray-500">@{follower.username}</p>
                        )}
                      </div>
                    </div>
                    {user && follower.id !== user.id && (
                      <FollowButton userId={follower.id} className="ml-2" />
                    )}
                  </div>
                ))}

                {followers > followersList.length && (
                  <Button
                    variant="outline"
                    className="w-full mt-4"
                    onClick={() => handleViewAll('followers')}
                  >
                    View All {followers} Followers
                  </Button>
                )}
              </div>
            ) : (
              <EmptyState
                title="No followers yet"
                description="Connect with other users to grow your network!"
              />
            )}
          </TabsContent>

          <TabsContent value="following">
            {followingList.length > 0 ? (
              <div className="space-y-4">
                {followingList.map((following) => (
                  <div key={following.id} className="flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg">
                    <div
                      className="flex items-center gap-3 flex-1 cursor-pointer"
                      onClick={() => handleUserClick(following.username || '', following.id)}
                    >
                      <UserAvatar user={following} size="md" />
                      <div>
                        <p className="font-medium">{following.display_name || following.username}</p>
                        {following.username && (
                          <p className="text-sm text-gray-500">@{following.username}</p>
                        )}
                      </div>
                    </div>
                    {user && following.id !== user.id && (
                      <FollowButton userId={following.id} className="ml-2" />
                    )}
                  </div>
                ))}

                {following > followingList.length && (
                  <Button
                    variant="outline"
                    className="w-full mt-4"
                    onClick={() => handleViewAll('following')}
                  >
                    View All {following} Following
                  </Button>
                )}
              </div>
            ) : (
              <EmptyState
                title="Not following anyone yet"
                description="Find and follow users to see their content in your feed!"
                actionLabel="Find Users"
                actionIcon={<UserPlus className="h-4 w-4" />}
                onAction={() => navigate('/discover')}
              />
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

const EmptyState = ({
  title,
  description,
  actionLabel,
  actionIcon,
  onAction
}: {
  title: string;
  description: string;
  actionLabel?: string;
  actionIcon?: React.ReactNode;
  onAction?: () => void;
}) => (
  <div className="text-center py-8 text-gray-500">
    <p className="font-medium text-gray-700">{title}</p>
    <p className="mt-1 mb-4">{description}</p>
    {actionLabel && (
      <Button
        variant="outline"
        onClick={onAction}
        size="sm"
        className="mt-2"
      >
        {actionIcon}
        {actionLabel}
      </Button>
    )}
  </div>
);

const LoadingSkeleton = () => (
  <Card>
    <CardHeader>
      <div className="flex justify-between">
        <div>
          <Skeleton className="h-6 w-24 mb-2" />
          <Skeleton className="h-4 w-40" />
        </div>
        <div>
          <Skeleton className="h-5 w-40" />
        </div>
      </div>
    </CardHeader>
    <CardContent>
      <div className="mb-4">
        <Skeleton className="h-10 w-full mb-6" />
      </div>
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <div key={i} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3 flex-1">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div>
                <Skeleton className="h-4 w-24 mb-1" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
            <Skeleton className="h-9 w-20" />
          </div>
        ))}
      </div>
    </CardContent>
  </Card>
);
