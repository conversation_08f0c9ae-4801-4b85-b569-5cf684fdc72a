
import { supabase } from '@/integrations/supabase/client';

/**
 * Checks if a specific table exists in the database
 * @param tableName The name of the table to check
 * @returns Promise<boolean> true if the table exists, false otherwise
 */
export async function checkTableExists(tableName: string): Promise<boolean> {
  try {
    // Use a direct query approach to check if a table exists
    const { count, error } = await supabase
      .from(tableName as any)
      .select('*', { count: 'exact', head: true });
      
    // If we can query the table without error, it exists
    return !error;
  } catch (error) {
    console.error('Error checking table existence:', error);
    return false;
  }
}

/**
 * Retrieves database schema information to help with debugging
 * @returns Promise<object> Schema information
 */
export async function getDatabaseSchema(): Promise<any> {
  try {
    // Try to use a more generic approach
    const { data, error } = await supabase
      .rpc('check_table_exists', { table_name: 'activities' });
    
    if (error) {
      console.error('Error retrieving schema:', error);
      return null;
    }
    
    return data;
  } catch (error) {
    console.error('Error retrieving schema:', error);
    return null;
  }
}

/**
 * Setup activities tables using the setup-activities.js script
 */
export async function setupActivitiesTables(): Promise<boolean> {
  try {
    // This would call an edge function or server endpoint that runs the setup script
    // For now we'll just return a placeholder
    console.log('Activity tables setup would be triggered here');
    return true;
  } catch (error) {
    console.error('Failed to setup activity tables:', error);
    return false;
  }
}
