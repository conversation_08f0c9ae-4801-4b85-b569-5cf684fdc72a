-- Function to create the message_reactions table if it doesn't exist
CREATE OR REP<PERSON>CE FUNCTION public.create_message_reactions_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the table already exists
  IF NOT EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public'
    AND tablename = 'message_reactions'
  ) THEN
    -- Create the message_reactions table
    CREATE TABLE public.message_reactions (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      message_id UUID NOT NULL,
      user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      emoji TEXT NOT NULL,
      count INTEGER DEFAULT 1,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );

    -- Add indexes
    CREATE INDEX idx_message_reactions_message_id ON public.message_reactions(message_id);
    CREATE INDEX idx_message_reactions_user_id ON public.message_reactions(user_id);
    
    -- Add a unique constraint to prevent duplicate reactions
    CREATE UNIQUE INDEX idx_message_reactions_unique ON public.message_reactions(message_id, user_id, emoji);

    -- Add RLS policies
    ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;

    -- Users can read all reactions
    CREATE POLICY "Anyone can read message reactions"
      ON public.message_reactions
      FOR SELECT
      USING (true);

    -- Users can only insert their own reactions
    CREATE POLICY "Users can insert their own reactions"
      ON public.message_reactions
      FOR INSERT
      WITH CHECK (user_id = auth.uid());

    -- Users can only update their own reactions
    CREATE POLICY "Users can update their own reactions"
      ON public.message_reactions
      FOR UPDATE
      USING (user_id = auth.uid())
      WITH CHECK (user_id = auth.uid());

    -- Users can only delete their own reactions
    CREATE POLICY "Users can delete their own reactions"
      ON public.message_reactions
      FOR DELETE
      USING (user_id = auth.uid());
  END IF;
END;
$$;

-- Function to create the location_messages table if it doesn't exist
CREATE OR REPLACE FUNCTION public.create_location_messages_table()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Check if the table already exists
  IF NOT EXISTS (
    SELECT FROM pg_tables
    WHERE schemaname = 'public'
    AND tablename = 'location_messages'
  ) THEN
    -- Create the location_messages table
    CREATE TABLE public.location_messages (
      id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
      conversation_id UUID NOT NULL,
      sender_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
      sender_name TEXT,
      sender_avatar_url TEXT,
      lat FLOAT NOT NULL,
      lng FLOAT NOT NULL,
      label TEXT,
      expiration TIMESTAMPTZ NOT NULL,
      is_expired BOOLEAN DEFAULT FALSE,
      created_at TIMESTAMPTZ DEFAULT NOW(),
      updated_at TIMESTAMPTZ DEFAULT NOW()
    );

    -- Add indexes
    CREATE INDEX idx_location_messages_conversation_id ON public.location_messages(conversation_id);
    CREATE INDEX idx_location_messages_sender_id ON public.location_messages(sender_id);
    CREATE INDEX idx_location_messages_expiration ON public.location_messages(expiration);

    -- Add RLS policies
    ALTER TABLE public.location_messages ENABLE ROW LEVEL SECURITY;

    -- Users can read location messages in conversations they're part of
    CREATE POLICY "Users can read location messages in their conversations"
      ON public.location_messages
      FOR SELECT
      USING (
        EXISTS (
          SELECT 1 FROM public.chat_participants
          WHERE chat_participants.conversation_id = location_messages.conversation_id
          AND chat_participants.user_id = auth.uid()
        )
      );

    -- Users can insert location messages in conversations they're part of
    CREATE POLICY "Users can insert location messages in their conversations"
      ON public.location_messages
      FOR INSERT
      WITH CHECK (
        sender_id = auth.uid() AND
        EXISTS (
          SELECT 1 FROM public.chat_participants
          WHERE chat_participants.conversation_id = location_messages.conversation_id
          AND chat_participants.user_id = auth.uid()
        )
      );

    -- Users can update their own location messages
    CREATE POLICY "Users can update their own location messages"
      ON public.location_messages
      FOR UPDATE
      USING (sender_id = auth.uid())
      WITH CHECK (sender_id = auth.uid());

    -- Users can delete their own location messages
    CREATE POLICY "Users can delete their own location messages"
      ON public.location_messages
      FOR DELETE
      USING (sender_id = auth.uid());
  END IF;
END;
$$;
