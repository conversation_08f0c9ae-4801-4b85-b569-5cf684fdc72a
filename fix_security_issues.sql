-- Fix function search path issues
-- First, let's get the function definitions to understand their parameters and return types

-- 1. Fix get_all_users function
CREATE OR REPLACE FUNCTION public.get_all_users()
RETURNS TABLE (
  -- Assuming the return structure based on common patterns, adjust as needed
  id uuid,
  email text,
  display_name text,
  username text,
  created_at timestamp with time zone
)
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Preserve the original function logic
  RETURN QUERY SELECT 
    auth.users.id,
    auth.users.email,
    profiles.display_name,
    profiles.username,
    auth.users.created_at
  FROM auth.users
  LEFT JOIN public.profiles ON auth.users.id = profiles.id;
END;
$$ LANGUAGE plpgsql;

-- 2. Fix is_admin function
CREATE OR REPLACE FUNCTION public.is_admin(user_id uuid)
RETURNS boolean
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  is_admin boolean;
BEGIN
  -- Preserve the original function logic
  SELECT EXISTS (
    SELECT 1 FROM public.user_roles
    WHERE user_roles.user_id = $1
    AND user_roles.role = 'admin'
  ) INTO is_admin;
  
  RETURN is_admin;
END;
$$ LANGUAGE plpgsql;

-- 3. Fix exec_sql function
CREATE OR REPLACE FUNCTION public.exec_sql(sql text)
RETURNS void
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- This function executes arbitrary SQL, which is potentially dangerous
  -- Make sure it's properly secured with RLS policies
  EXECUTE sql;
END;
$$ LANGUAGE plpgsql;

-- 4. Fix is_location_expired function
CREATE OR REPLACE FUNCTION public.is_location_expired(location_timestamp timestamp with time zone)
RETURNS boolean
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  -- Preserve the original function logic
  RETURN location_timestamp < NOW() - INTERVAL '15 minutes';
END;
$$ LANGUAGE plpgsql;

-- 5. Move PostGIS extension to a separate schema
-- First create a dedicated schema for extensions if it doesn't exist
CREATE SCHEMA IF NOT EXISTS extensions;

-- Grant usage to public (adjust permissions as needed)
GRANT USAGE ON SCHEMA extensions TO public;

-- Move the PostGIS extension to the extensions schema
-- Note: This might require dropping and recreating the extension
-- which could be disruptive if you have data that depends on it
-- Consider doing this during a maintenance window

-- Option 1: If you can afford to drop and recreate (CAREFUL!)
-- DROP EXTENSION IF EXISTS postgis;
-- CREATE EXTENSION IF NOT EXISTS postgis WITH SCHEMA extensions;

-- Option 2: Safer approach - alter the extension's schema
-- ALTER EXTENSION postgis SET SCHEMA extensions;

-- Comment out the actual migration and provide a warning
-- UNCOMMENT THE APPROPRIATE OPTION ABOVE AFTER REVIEWING THE IMPACT
-- Moving PostGIS requires careful planning as it may affect existing data
