
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface Wallet {
  id: string;
  user_id: string;
  balance: number;
  created_at: string;
  updated_at: string;
}

export function useWallet(userId: string | undefined) {
  const { toast } = useToast();

  return useQuery({
    queryKey: ['wallet', userId],
    queryFn: async (): Promise<Wallet | null> => {
      if (!userId) return null;

      try {
        // Use mock data instead of querying the actual database
        const mockWallet: Wallet = {
          id: 'wallet_' + userId,
          user_id: userId,
          balance: 100.00,
          created_at: new Date(Date.now() - 90 * 86400000).toISOString(), // 90 days ago
          updated_at: new Date().toISOString()
        };

        return mockWallet;
      } catch (error) {
        toast({
          title: 'Error fetching wallet',
          description: error instanceof Error ? error.message : 'Unknown error',
          variant: 'destructive'
        });
        throw error;
      }
    },
    enabled: !!userId
  });
}
