// <PERSON>ript to fix duplicate functions in the database
import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Make sure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_SERVICE_ROLE_KEY are set in your .env file.');
  process.exit(1);
}

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixDuplicateFunctions() {
  try {
    console.log('Fixing duplicate functions...');
    
    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../sql/fix_duplicate_functions.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');
    
    // Execute the SQL
    const { error } = await supabase.sql(sqlContent);
    
    if (error) {
      console.error('Error fixing functions:', error);
      process.exit(1);
    }
    
    console.log('Functions fixed successfully!');
    
    // Verify the functions exist
    const { data, error: verifyError } = await supabase
      .rpc('verify_onboarding_status', { user_id: '00000000-0000-0000-0000-000000000000' });
    
    if (verifyError) {
      console.log('Function exists but returned an error (expected for non-existent user):', verifyError);
    } else {
      console.log('Function executed successfully:', data);
    }
    
    process.exit(0);
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the function
fixDuplicateFunctions();
