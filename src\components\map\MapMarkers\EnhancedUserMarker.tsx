import React from 'react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { UnifiedMapUser } from '@/types/map';
import { BadgeCheck, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';
import { formatDistance, formatLastSeen, getActivityStatus } from '@/utils/enhanced-distance';

interface EnhancedUserMarkerProps {
  user: UnifiedMapUser;
  isOnline?: boolean;
  isSelected?: boolean;
  onClick?: () => void;
  size?: 'sm' | 'md' | 'lg';
  showVerification?: boolean;
  showPreview?: boolean;
  showDistance?: boolean;
  distance?: number;
  lastSeen?: Date | string | null;
}

export function EnhancedUserMarker({
  user,
  isOnline,
  isSelected = false,
  onClick,
  size = 'md',
  showVerification = true,
  showPreview = false,
  showDistance = false,
  distance,
  lastSeen
}: EnhancedUserMarkerProps) {
  const avatarUrl = user.avatar_url || `https://i.pravatar.cc/150?u=${user.user_id}`;
  const displayName = user.display_name || user.username || `User ${user.user_id?.substring?.(0, 5)}`;
  const isVerified = user.is_verified === true;
  
  const getInitials = () => {
    return displayName
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };
  
  const getProfileColor = () => {
    let hash = 0;
    for (let i = 0; i < user.user_id.length; i++) {
      hash = user.user_id.charCodeAt(i) + ((hash << 5) - hash);
    }
    const hue = Math.abs(hash) % 360;
    return `hsl(${hue}, 70%, 60%)`;
  };
  
  const profileColor = user.profile_color || getProfileColor();
  
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-14 h-14'
  };
  
  const statusSizes = {
    sm: 'w-2 h-2',
    md: 'w-3 h-3',
    lg: 'w-4 h-4'
  };
  
  const badgeSizes = {
    sm: 'w-3 h-3',
    md: 'w-4 h-4',
    lg: 'w-5 h-5'
  };

  // Get status info based on lastSeen timestamp
  const activityStatus = lastSeen ? getActivityStatus(lastSeen) : null;
  const isCurrentlyOnline = isOnline || (activityStatus?.status === 'online');
  
  return (
    <div 
      className="relative group cursor-pointer"
      onClick={onClick}
    >
      <Avatar 
        className={cn(
          sizeClasses[size],
          'border-2 transition-all duration-300 group-hover:scale-110',
          isSelected ? 'border-primary ring-2 ring-primary ring-offset-2' : `border-[${profileColor}]`
        )}
      >
        <AvatarImage src={avatarUrl} alt={displayName} />
        <AvatarFallback style={{ backgroundColor: profileColor }}>
          {getInitials()}
        </AvatarFallback>
      </Avatar>
      
      {/* Enhanced status indicators */}
      {isCurrentlyOnline ? (
        <div className={cn(
          statusSizes[size],
          'absolute bottom-0 right-0 bg-green-500 rounded-full border-2 border-background animate-pulse'
        )}></div>
      ) : activityStatus?.status === 'away' ? (
        <div className={cn(
          statusSizes[size],
          'absolute bottom-0 right-0 bg-amber-500 rounded-full border-2 border-background'
        )}></div>
      ) : (
        <div className={cn(
          statusSizes[size],
          'absolute bottom-0 right-0 bg-gray-400 rounded-full border-2 border-background flex items-center justify-center'
        )}>
          {size !== 'sm' && <Clock className="w-2 h-2 text-gray-100" />}
        </div>
      )}
      
      {showVerification && isVerified && (
        <div className={cn(
          badgeSizes[size],
          'absolute -top-1 -right-1 bg-blue-500 rounded-full flex items-center justify-center'
        )}>
          <BadgeCheck className="w-full h-full text-white" />
        </div>
      )}

      {/* Optional distance and preview tooltip */}
      {(showPreview || showDistance) && (
        <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
          <div className="bg-background/80 backdrop-blur-sm text-xs rounded-md px-2 py-1 shadow-sm">
            {showDistance && typeof distance === 'number' && (
              <span>{formatDistance(distance)}</span>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

// Function to create a DOM element for the user marker
export function createUserMarker(user: UnifiedMapUser, isSelected: boolean = false): HTMLElement {
  const element = document.createElement('div');
  element.className = 'user-marker';
  
  // Create an element for React to render into
  const markerEl = document.createElement('div');
  
  // Get user avatar and profile information
  const avatarUrl = user.avatar_url || `https://i.pravatar.cc/150?u=${user.user_id}`;
  const displayName = user.display_name || user.username || `User ${user.user_id?.substring?.(0, 5)}`;
  const isVerified = user.is_verified === true;
  
  // Generate profile color for the marker
  let hash = 0;
  for (let i = 0; i < user.user_id.length; i++) {
    hash = user.user_id.charCodeAt(i) + ((hash << 5) - hash);
  }
  const hue = Math.abs(hash) % 360;
  const profileColor = user.profile_color || `hsl(${hue}, 70%, 60%)`;
  
  // Get initials for the avatar fallback
  const initials = displayName
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
  
  // Create HTML structure for the marker
  const borderClass = isSelected ? 'border-primary ring-2 ring-primary ring-offset-2' : 'border-white';
  
  // Enhanced marker with status display
  markerEl.innerHTML = `
    <div class="relative group cursor-pointer">
      <div class="w-12 h-12 rounded-full overflow-hidden border-2 ${borderClass} transition-all duration-300 group-hover:scale-110 bg-${profileColor.replace('#', '')}">
        ${avatarUrl ? 
          `<img src="${avatarUrl}" alt="${displayName}" class="w-full h-full object-cover" onerror="this.style.display='none'; this.parentNode.innerHTML = '${initials}';" />` : 
          `<div class="w-full h-full flex items-center justify-center font-medium text-white" style="background-color: ${profileColor}">${initials}</div>`
        }
      </div>
      
      ${user.is_online ? 
        `<div class="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-background animate-pulse"></div>` : 
        `<div class="absolute bottom-0 right-0 w-3 h-3 bg-gray-400 rounded-full border-2 border-background flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-2 h-2 text-gray-100" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <circle cx="12" cy="12" r="10"></circle>
            <polyline points="12 6 12 12 16 14"></polyline>
          </svg>
        </div>`
      }
      
      ${isVerified ? 
        `<div class="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-3 h-3 text-white" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>` : 
        ''
      }
      
      ${user.distance ?
        `<div class="absolute -bottom-6 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-opacity">
          <div class="bg-background/80 backdrop-blur-sm text-xs rounded-md px-2 py-1 shadow-sm">
            ${user.distance < 0.1 ? 'Nearby' : user.distance < 10 ? `${user.distance.toFixed(1)} mi` : `${Math.round(user.distance)} mi`}
          </div>
        </div>` :
        ''
      }
    </div>
  `;
  
  element.appendChild(markerEl);
  return element;
}
