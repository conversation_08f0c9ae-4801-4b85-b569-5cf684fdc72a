
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useRealtimeManager } from '@/hooks/use-realtime-manager';
import { useToast } from '@/hooks/use-toast';
import { Loader2, MapPin, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { lngLatToXY } from '@/types/map';

interface UserLocationUpdaterProps {
  onLocationUpdated?: (location: { x: number; y: number }) => void;
  autoUpdate?: boolean;
  updateInterval?: number; // in milliseconds
  className?: string;
}

export function UserLocationUpdater({
  onLocationUpdated,
  autoUpdate = false,
  updateInterval = 60000, // Default: update every minute
  className
}: UserLocationUpdaterProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const [locationError, setLocationError] = useState<string | null>(null);
  const [lastLocation, setLastLocation] = useState<{ x: number; y: number } | null>(null);
  
  const { updateUserLocation } = useRealtimeManager();
  const { toast } = useToast();
  
  // Function to get and update user location
  const updateLocation = async () => {
    setIsUpdating(true);
    setLocationError(null);
    
    if (!navigator.geolocation) {
      setLocationError('Geolocation is not supported by your browser');
      setIsUpdating(false);
      return;
    }
    
    try {
      navigator.geolocation.getCurrentPosition(
        async (position) => {
          const location = {
            x: position.coords.longitude,
            y: position.coords.latitude
          };
          
          setLastLocation(location);
          
          // Update the location in the database
          const success = await updateUserLocation(location);
          
          if (success) {
            toast({
              title: 'Location updated',
              description: 'Your live location is now visible on the map',
            });
            
            if (onLocationUpdated) {
              onLocationUpdated(location);
            }
          } else {
            toast({
              title: 'Failed to update location',
              description: 'Please make sure you are logged in',
              variant: 'destructive',
            });
          }
          
          setIsUpdating(false);
        },
        (error) => {
          console.error('Error getting location:', error);
          let errorMessage = 'Failed to get your location';
          
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = 'Location permission denied. Please enable location services.';
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = 'Location information is unavailable.';
              break;
            case error.TIMEOUT:
              errorMessage = 'Location request timed out.';
              break;
          }
          
          setLocationError(errorMessage);
          setIsUpdating(false);
        },
        { enableHighAccuracy: true, timeout: 10000, maximumAge: 0 }
      );
    } catch (error) {
      console.error('Unexpected error getting location:', error);
      setLocationError('Unexpected error occurred while getting your location');
      setIsUpdating(false);
    }
  };
  
  // Auto-update effect
  useEffect(() => {
    if (!autoUpdate) return;
    
    // Update location immediately
    updateLocation();
    
    // Set interval for periodic updates
    const interval = setInterval(updateLocation, updateInterval);
    
    // Cleanup interval
    return () => {
      clearInterval(interval);
    };
  }, [autoUpdate, updateInterval]);
  
  return (
    <div className={className}>
      {locationError && (
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Location Error</AlertTitle>
          <AlertDescription>{locationError}</AlertDescription>
        </Alert>
      )}
      
      <Button 
        onClick={updateLocation} 
        disabled={isUpdating}
        className="w-full gap-2"
      >
        {isUpdating ? (
          <>
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Updating Location...</span>
          </>
        ) : (
          <>
            <MapPin className="h-4 w-4" />
            <span>{lastLocation ? 'Update My Location' : 'Share My Location'}</span>
          </>
        )}
      </Button>
    </div>
  );
}
