
import React, { useState } from 'react';
import { Activity } from "@/types/activity";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { useToast } from "@/hooks/use-toast";
import { Check, Copy, Mail, Smartphone, Share2 } from "lucide-react";

interface ActivityShareModuleProps {
  activity: Activity;
}

export function ActivityShareModule({ activity }: ActivityShareModuleProps) {
  const { toast } = useToast();
  const [copied, setCopied] = useState(false);

  // Prepare sharing content
  const shareUrl = `${window.location.origin}/activity/${activity.id}`;
  const shareTitle = `Join me at: ${activity.title}`;
  const shareText = `${activity.title} - ${activity.address || 'Location details in link'}. ${activity.is_paid ? `$${activity.price}` : 'Free'}.`;

  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareUrl);
    setCopied(true);

    toast({
      description: "Link copied to clipboard"
    });

    setTimeout(() => setCopied(false), 2000);
  };

  const handleEmailShare = () => {
    const emailBody = `${shareTitle}\n\n${shareText}\n\n${shareUrl}`;
    window.open(`mailto:?subject=${encodeURIComponent(shareTitle)}&body=${encodeURIComponent(emailBody)}`);
  };

  const handleSMSShare = () => {
    const smsText = `${shareTitle}: ${shareUrl}`;
    // Using SMS URI scheme - works on most mobile devices
    window.open(`sms:?&body=${encodeURIComponent(smsText)}`);
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: shareTitle,
          text: shareText,
          url: shareUrl
        });

        toast({
          description: "Shared successfully"
        });
      } catch (error) {
        if ((error as Error).name !== 'AbortError') {
          console.error('Error sharing:', error);
        }
      }
    } else {
      handleCopyLink();
    }
  };

  return (
    <div className="space-y-4">
      <div>
        <h3 className="text-lg font-medium">Share "{activity.title}"</h3>
        <p className="text-sm text-muted-foreground">
          Invite others to join this activity
        </p>
      </div>

      <Tabs defaultValue="link" className="w-full">
        <TabsList className="grid grid-cols-3 mb-2">
          <TabsTrigger value="link">Link</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="sms">SMS</TabsTrigger>
        </TabsList>

        <TabsContent value="link" className="space-y-4">
          <div className="flex items-center space-x-2">
            <Input
              value={shareUrl}
              readOnly
              onClick={(e) => (e.target as HTMLInputElement).select()}
            />
            <Button size="sm" onClick={handleCopyLink}>
              {copied ? <Check className="h-4 w-4" /> : <Copy className="h-4 w-4" />}
            </Button>
          </div>

          {navigator.share && (
            <Button onClick={handleShare} className="w-full">
              <Share2 className="h-4 w-4 mr-2" />
              Share using device
            </Button>
          )}
        </TabsContent>

        <TabsContent value="email">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Email subject</Label>
              <Input value={shareTitle} readOnly />
            </div>
            <div className="space-y-2">
              <Label>Email preview</Label>
              <div className="p-3 border rounded-md text-sm">
                <p className="font-medium">{shareTitle}</p>
                <p className="my-2">{shareText}</p>
                <p className="text-blue-600">{shareUrl}</p>
              </div>
            </div>
            <Button onClick={handleEmailShare} className="w-full">
              <Mail className="h-4 w-4 mr-2" />
              Open Email
            </Button>
          </div>
        </TabsContent>

        <TabsContent value="sms">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>SMS preview</Label>
              <div className="p-3 border rounded-md text-sm">
                <p>{shareTitle}: {shareUrl}</p>
              </div>
            </div>
            <Button onClick={handleSMSShare} className="w-full">
              <Smartphone className="h-4 w-4 mr-2" />
              Open SMS
            </Button>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
