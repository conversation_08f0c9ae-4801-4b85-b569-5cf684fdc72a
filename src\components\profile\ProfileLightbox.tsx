
import React, { useState } from 'react';
import { <PERSON><PERSON>, DialogContent } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, X, Download, Share2 } from "lucide-react";
import { ProfileMedia } from "@/types/enhanced-profile";
import { cn } from "@/lib/utils";

interface ProfileLightboxProps {
  media: ProfileMedia[];
  initialIndex: number;
  open: boolean;
  onClose: () => void;
}

export function ProfileLightbox({ media, initialIndex, open, onClose }: ProfileLightboxProps) {
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const currentMedia = media[currentIndex];

  const handleNext = () => {
    setCurrentIndex((prev) => (prev === media.length - 1 ? 0 : prev + 1));
  };

  const handlePrevious = () => {
    setCurrentIndex((prev) => (prev === 0 ? media.length - 1 : prev - 1));
  };

  const handleDownload = () => {
    if (!currentMedia) return;
    
    // Create an anchor element and trigger download
    const link = document.createElement('a');
    link.href = currentMedia.url;
    link.download = `image-${currentMedia.id}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleShare = async () => {
    if (!currentMedia) return;
    
    // Use Web Share API if available
    if (navigator.share) {
      try {
        await navigator.share({
          title: currentMedia.caption || 'Shared image',
          text: 'Check out this image',
          url: currentMedia.url,
        });
      } catch (error) {
        console.error('Error sharing:', error);
      }
    } else {
      // Fallback to clipboard copy
      navigator.clipboard.writeText(currentMedia.url);
      // You could add a toast notification here
    }
  };

  if (!currentMedia) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[90vw] md:max-w-[80vw] lg:max-w-[1200px] p-0 overflow-hidden bg-black/90">
        <div className="relative h-[90vh] flex flex-col">
          {/* Top Bar */}
          <div className="p-2 flex justify-between items-center text-white bg-black/40 absolute top-0 left-0 right-0 z-10">
            <div className="text-sm">
              {currentIndex + 1} / {media.length}
            </div>
            <div className="flex gap-2">
              <Button 
                variant="ghost" 
                size="icon" 
                className="text-white hover:bg-white/20" 
                onClick={handleDownload}
              >
                <Download className="h-5 w-5" />
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                className="text-white hover:bg-white/20" 
                onClick={handleShare}
              >
                <Share2 className="h-5 w-5" />
              </Button>
              <Button 
                variant="ghost" 
                size="icon" 
                className="text-white hover:bg-white/20" 
                onClick={onClose}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>
          </div>

          {/* Main Image */}
          <div className="flex items-center justify-center h-full overflow-hidden">
            <img
              src={currentMedia.url}
              alt={currentMedia.caption || 'Gallery image'}
              className="max-h-full max-w-full object-contain"
            />
          </div>

          {/* Navigation Controls */}
          <Button
            variant="ghost"
            size="icon"
            className="absolute left-2 top-1/2 transform -translate-y-1/2 text-white bg-black/40 hover:bg-black/60 rounded-full h-10 w-10"
            onClick={handlePrevious}
          >
            <ChevronLeft className="h-6 w-6" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            className="absolute right-2 top-1/2 transform -translate-y-1/2 text-white bg-black/40 hover:bg-black/60 rounded-full h-10 w-10"
            onClick={handleNext}
          >
            <ChevronRight className="h-6 w-6" />
          </Button>

          {/* Caption */}
          {currentMedia.caption && (
            <div className="absolute bottom-0 left-0 right-0 bg-black/60 p-4 text-white">
              <p>{currentMedia.caption}</p>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
