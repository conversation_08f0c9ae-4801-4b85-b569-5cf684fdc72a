import React from 'react';
import { Link } from 'react-router-dom';
import { EnhancedUserProfile } from '@/types/enhanced-profile';
import { Card } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Users, Calendar, MapPin } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProfileStatsProps {
  profile: EnhancedUserProfile;
  className?: string;
  isLoading?: boolean;
}

export function ProfileStats({ profile, className, isLoading = false }: ProfileStatsProps) {
  if (isLoading) {
    return <ProfileStatsSkeleton className={className} />;
  }
  
  // Get stats from profile
  const followerCount = profile.follower_count || 0;
  const followingCount = profile.following_count || 0;
  const activitiesCount = (profile.hosted_activities_count || 0) + (profile.participated_activities_count || 0);
  
  return (
    <div className={cn("grid grid-cols-3 gap-4", className)}>
      {/* Followers Stat */}
      <StatCard
        icon={<Users className="h-4 w-4" />}
        label="Followers"
        value={followerCount.toString()}
        to={`/${profile.username || profile.id}/followers`}
      />
      
      {/* Following Stat */}
      <StatCard
        icon={<Users className="h-4 w-4" />}
        label="Following"
        value={followingCount.toString()}
        to={`/${profile.username || profile.id}/following`}
      />
      
      {/* Activities Stat */}
      <StatCard
        icon={<Calendar className="h-4 w-4" />}
        label="Activities"
        value={activitiesCount.toString()}
        to={`/${profile.username || profile.id}/activities`}
      />
    </div>
  );
}

interface StatCardProps {
  icon: React.ReactNode;
  label: string;
  value: string;
  to?: string;
  onClick?: () => void;
}

function StatCard({ icon, label, value, to, onClick }: StatCardProps) {
  const content = (
    <Card className="flex flex-col items-center justify-center p-3 hover:bg-accent/5 transition-colors">
      <div className="flex items-center gap-1 text-muted-foreground mb-1">
        {icon}
        <span className="text-xs">{label}</span>
      </div>
      <span className="text-xl font-semibold">{value}</span>
    </Card>
  );
  
  if (to) {
    return (
      <Link to={to} className="block">
        {content}
      </Link>
    );
  }
  
  if (onClick) {
    return (
      <button onClick={onClick} className="w-full text-left">
        {content}
      </button>
    );
  }
  
  return content;
}

function ProfileStatsSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("grid grid-cols-3 gap-4", className)}>
      <Skeleton className="h-16" />
      <Skeleton className="h-16" />
      <Skeleton className="h-16" />
    </div>
  );
}
