
import React, { useState } from 'react';
import { Button, ButtonProps } from '@/components/ui/button';
import { MapPin } from 'lucide-react';
import { useLocationMessages } from '@/hooks/use-location-messages';
import { toast } from 'sonner';

interface LocationShareButtonProps extends ButtonProps {
  conversationId: string;
}

export function LocationShareButton({ conversationId, ...props }: LocationShareButtonProps) {
  const [isSharing, setIsSharing] = useState(false);
  const locationMessages = useLocationMessages(conversationId);

  const handleLocationShare = async () => {
    if (!navigator.geolocation) {
      toast.error("Geolocation is not supported by your browser");
      return;
    }

    setIsSharing(true);
    
    try {
      navigator.geolocation.getCurrentPosition(async (position) => {
        const { latitude, longitude } = position.coords;
        // Get address from coordinates (would typically use a geocoding API)
        const label = "My current location"; // Could be replaced with actual address
  
        // Set the location in the hook
        locationMessages.setNewLocation({
          lat: position.coords.latitude,
          lng: position.coords.longitude,
          label
        });
        
        // Set expiration to 24 hours by default
        const expirationTime = new Date();
        expirationTime.setHours(expirationTime.getHours() + 24);
        locationMessages.setExpiration(expirationTime);
        
        // Send the location message
        await locationMessages.insertLocation.mutateAsync();
        
        toast.success("Location shared successfully");
        setIsSharing(false);
      }, (error) => {
        console.error("Error getting location:", error);
        toast.error("Couldn't access your location. Please check your permissions.");
        setIsSharing(false);
      }, {
        enableHighAccuracy: true,
        timeout: 10000
      });
    } catch (error) {
      console.error("Error sharing location:", error);
      toast.error("Error sharing your location");
      setIsSharing(false);
    }
  };
  
  return (
    <Button
      variant="ghost"
      size="icon"
      onClick={handleLocationShare}
      disabled={isSharing || locationMessages.insertLocation.isPending}
      {...props}
    >
      <MapPin className="h-4 w-4" />
      <span className="sr-only">Share location</span>
    </Button>
  );
}
