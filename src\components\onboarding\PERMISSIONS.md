# Permissions System in BuddySurfNow

This document explains how the permissions system works in BuddySurfNow, specifically for location and notification permissions.

## Database Schema

The following fields have been added to the `profiles` table:

- `default_location` (PostgreSQL point): Stores the user's default location coordinates
- `location_permission_granted` (boolean): Tracks whether the user has granted location permission
- `notifications_enabled` (boolean): Tracks whether the user has enabled notifications
- `location_display` (text): Stores a human-readable location name (e.g., "New York, NY")

## Onboarding Flow

The permissions step in the onboarding flow:

1. Asks for location permission
2. Asks for notification permission
3. Saves the user's choices to the database
4. Updates the form state to reflect these choices

## Implementation Details

### Location Permission

When a user grants location permission:

1. The browser's Geolocation API is used to get the current position
2. The coordinates are saved to the `default_location` field in the database
3. The `location_permission_granted` field is set to `true`
4. The form state is updated to reflect this

```typescript
// Get current position and save coordinates
const getCurrentPosition = () => {
  if ('geolocation' in navigator) {
    navigator.geolocation.getCurrentPosition(
      (position) => {
        const coords = {
          x: position.coords.longitude,
          y: position.coords.latitude
        };
        setLocationCoords(coords);
        
        // Update form values
        form.setValue('defaultLocation', coords);
        
        // Save to database if user is available
        if (user?.id) {
          saveLocationToDatabase(user.id, coords);
        }
        
        setLocationStatus('granted');
      },
      (error) => {
        console.error('Error getting location:', error);
        setLocationStatus('denied');
      },
      { enableHighAccuracy: true, timeout: 10000, maximumAge: 60000 }
    );
  }
};
```

### Notification Permission

When a user enables notifications:

1. The browser's Notification API is used to request permission
2. The permission status is saved to the `notifications_enabled` field in the database
3. The form state is updated to reflect this

```typescript
// Request notification permission
const requestNotificationPermission = async () => {
  setNotificationStatus('requesting');
  if ('Notification' in window) {
    try {
      const permission = await Notification.requestPermission();
      const isGranted = permission === 'granted';
      setNotificationStatus(isGranted ? 'granted' : 'denied');
      
      // Update form values
      form.setValue('notificationsEnabled', isGranted);
      
      // Save to database if user is available
      if (user?.id) {
        await saveNotificationSettingsToDatabase(user.id, isGranted);
      }
    } catch (error) {
      console.error('Error requesting notification permission:', error);
      setNotificationStatus('denied');
    }
  } else {
    setNotificationStatus('denied');
  }
};
```

## Using the Permissions

### Location

The user's default location can be used for:

- Setting the initial map view
- Finding nearby activities
- Suggesting local buddies
- Pre-filling location fields in forms

### Notifications

When notifications are enabled, the app can:

- Send notifications for new messages
- Alert users about activity updates
- Notify users about connection requests
- Send reminders for upcoming events

## Checking Permission Status

You can check the permission status using the `useProfile` hook:

```typescript
const { data: profile } = useProfile(userId);

// Check if location permission is granted
if (profile?.location_permission_granted) {
  // Use location features
}

// Check if notifications are enabled
if (profile?.notifications_enabled) {
  // Use notification features
}
```

## Handling Permission Changes

If a user changes their permission settings in the browser, the app should:

1. Detect the change
2. Update the database accordingly
3. Update the UI to reflect the new permission status

This can be implemented using event listeners for permission changes.
