
import { useEffect, useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';

export interface Reaction {
  id: string;
  message_id: string;
  user_id: string;
  emoji: string;
  created_at: string;
}

interface Reactions {
  [messageId: string]: Reaction[];
}

export function useRealtimeReactions(conversationId?: string) {
  const [reactions, setReactions] = useState<Reactions>({});
  const { user } = useAuth();

  const handleNewReaction = useCallback((newReaction: Reaction) => {
    setReactions(prevReactions => {
      const messageId = newReaction.message_id;
      return {
        ...prevReactions,
        [messageId]: [...(prevReactions[messageId] || []), newReaction]
      };
    });
  }, []);

  const handleDeletedReaction = useCallback((deletedReaction: Reaction) => {
    setReactions(prevReactions => {
      const messageId = deletedReaction.message_id;
      return {
        ...prevReactions,
        [messageId]: (prevReactions[messageId] || []).filter(reaction => reaction.id !== deletedReaction.id)
      };
    });
  }, []);

  useEffect(() => {
    if (!conversationId) return;

    const channel = supabase.channel(`reactions:${conversationId}`);

    channel
      .on('postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'message_reactions',
          filter: `conversation_id=eq.${conversationId}`
        },
        (payload) => {
          const newReaction = payload.new as any;
          if (newReaction && newReaction.message_id) {
            handleNewReaction(newReaction);
          }
        }
      )
      .on('postgres_changes',
        {
          event: 'DELETE',
          schema: 'public',
          table: 'message_reactions',
          filter: `conversation_id=eq.${conversationId}`
        },
        (payload) => {
          const deletedReaction = payload.old as any;
          if (deletedReaction && deletedReaction.message_id) {
            handleDeletedReaction(deletedReaction);
          }
        }
      )
      .subscribe();

    return () => {
      channel.unsubscribe();
    };
  }, [conversationId]);

  // Function to add a reaction
  const addReaction = useCallback(async (messageId: string, emoji: string) => {
    if (!conversationId || !user) return;

    try {
      const { data, error } = await supabase
        .from('message_reactions')
        .insert({
          message_id: messageId,
          user_id: user.id,
          emoji: emoji,
          conversation_id: conversationId
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error adding reaction:', error);
      throw error;
    }
  }, [conversationId, user]);

  // Function to remove a reaction
  const removeReaction = useCallback(async (messageId: string, emoji: string) => {
    if (!conversationId || !user) return;

    try {
      const { error } = await supabase
        .from('message_reactions')
        .delete()
        .eq('message_id', messageId)
        .eq('user_id', user.id)
        .eq('emoji', emoji);

      if (error) throw error;
    } catch (error) {
      console.error('Error removing reaction:', error);
      throw error;
    }
  }, [conversationId, user]);

  return {
    reactions,
    addReaction,
    removeReaction,
  };
}
