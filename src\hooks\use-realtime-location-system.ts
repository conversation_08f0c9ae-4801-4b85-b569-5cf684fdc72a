
import { useState, useEffect, useCallback } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useWebSocketManager } from "./use-websocket-manager";
import { UnifiedMapUser } from "@/types/map";
import { useToast } from "@/hooks/use-toast";
import { useLocationServices } from "./use-location-services";
import { useProfile } from "./use-profile";
import { useAuth } from "./use-auth";

/**
 * A hook that manages the user's real-time location system
 * Uses the WebSocketManager for real-time updates and provides
 * methods to update the current user's location
 */
export function useRealtimeLocationSystem() {
  const { users, isConnected, lastUpdate, refreshData } = useWebSocketManager();
  const [lastError, setLastError] = useState<string | null>(null);
  const [isUpdating, setIsUpdating] = useState(false);
  const [lastLocationUpdate, setLastLocationUpdate] = useState<Date | null>(null);
  const { toast } = useToast();
  const { coords: myCoords, getPosition } = useLocationServices();
  const { user } = useAuth();
  const { data: profile } = useProfile(user?.id);

  // Update the user's location in the database
  const updateMyLocation = useCallback(async () => {
    if (isUpdating) return false;

    setIsUpdating(true);
    setLastError(null);

    try {
      // First check if user is authenticated
      if (!user) {
        const errorMessage = "You must be logged in to update your location";
        setLastError(errorMessage);
        toast({
          title: "Authentication required",
          description: errorMessage,
          variant: "destructive",
        });
        return false;
      }

      // Get current position
      const coords = await getPosition();

      // Get user profile data for enhanced location info
      const now = new Date();
      const timestamp = now.toISOString();

      // Update the database with enhanced user information
      const { error } = await supabase
        .from("user_locations")
        .upsert({
          user_id: user.id,
          location: { x: coords.lng, y: coords.lat },
          updated_at: timestamp,
          last_seen_at: timestamp,
          display_name: profile?.display_name || user.email?.split('@')[0] || 'Anonymous',
          avatar_url: profile?.avatar_url || null,
          bio: profile?.bio || null,
          is_anonymous: false
        });

      if (error) {
        throw error;
      }

      // Also update the profile's location if it exists
      if (profile) {
        await supabase
          .from("profiles")
          .update({
            location: { x: coords.lng, y: coords.lat },
            updated_at: timestamp
          })
          .eq('id', user.id);
      }

      // Refresh data to see our own update
      await refreshData();
      setLastLocationUpdate(now);

      toast({
        title: "Location updated",
        description: "Your location has been updated successfully",
      });

      return true;
    } catch (error: any) {
      console.error("Error updating location:", error);
      setLastError(error.message || "Failed to update location");
      toast({
        title: "Location update failed",
        description: error.message || "Failed to update your location",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsUpdating(false);
    }
  }, [isUpdating, getPosition, toast, refreshData, user, profile]);

  // Initialize by trying to get the user's location
  useEffect(() => {
    getPosition().catch(error => {
      console.log("Initial location detection failed:", error.message);
      // Don't show a toast for initial failure, just log it
    });
  }, [getPosition]);

  // Add automatic location updates every 5 minutes
  useEffect(() => {
    if (!user) return;

    const intervalId = setInterval(() => {
      updateMyLocation().catch(error => {
        console.error("Auto location update failed:", error);
      });
    }, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(intervalId);
  }, [user, updateMyLocation]);

  return {
    users,
    myCoords,
    isConnected,
    lastUpdate,
    lastError,
    isUpdating,
    lastLocationUpdate,
    updateMyLocation,
    getCurrentPosition: getPosition
  };
}
