import React from 'react';
import { AppNav } from '@/components/navigation/AppNav';
import { NotificationPopover } from '@/components/notification';
import { cn } from '@/lib/utils';
import { useLocation } from 'react-router-dom';
interface MainLayoutProps {
  children: React.ReactNode;
  title?: string;
  fullWidth?: boolean;
  noContainer?: boolean;
}
export function MainLayout({
  children,
  title,
  fullWidth = false,
  noContainer = false
}: MainLayoutProps) {
  const location = useLocation();
  const isMapPage = location.pathname === '/meetmap';
  const isHomePage = location.pathname === '/';

  // Don't show the title if it's "Home" on the homepage to avoid redundancy
  const shouldShowTitle = !noContainer && title && !(isHomePage && title === "Home");

  return <div className="flex flex-col min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute -top-24 -right-24 w-96 h-96 bg-primary/5 rounded-full blur-3xl pointer-events-none"></div>
      <div className="absolute -bottom-32 -left-32 w-96 h-96 bg-secondary/5 rounded-full blur-3xl pointer-events-none"></div>

      {!isMapPage && <AppNav />}

      <main className="">
        {shouldShowTitle && title}
        {children}
      </main>
    </div>;
}