
import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

export interface Notification {
  id: string;
  type: string;
  title: string;
  content: string;
  source_id?: string;
  source_type?: string;
  is_read: boolean;
  action_url?: string;
  created_at: string;
}

export function useNotifications() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch notifications for the current user
  const { data: notifications, isLoading } = useQuery<Notification[]>({
    queryKey: ['notifications', user?.id],
    queryFn: async () => {
      if (!user) return [];

      const { data, error } = await supabase
        .from('notifications')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) {
        toast({
          title: 'Error fetching notifications',
          description: error.message,
          variant: 'destructive'
        });
        return [];
      }

      return data;
    },
    enabled: !!user
  });

  // Mutation to mark a notification as read
  const markAsRead = useMutation({
    mutationFn: async (notificationId: string) => {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('id', notificationId);

      if (error) {
        toast({
          title: 'Error marking notification as read',
          description: error.message,
          variant: 'destructive'
        });
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications', user?.id] });
    }
  });

  // Mutation to mark all notifications as read
  const markAllAsRead = useMutation({
    mutationFn: async () => {
      const { error } = await supabase
        .from('notifications')
        .update({ is_read: true })
        .eq('user_id', user?.id);

      if (error) {
        toast({
          title: 'Error marking all notifications as read',
          description: error.message,
          variant: 'destructive'
        });
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications', user?.id] });
    }
  });

  // Setup polling for new notifications instead of using real-time subscriptions
  useEffect(() => {
    if (!user) return;
    
    // Poll for new notifications every 15 seconds
    const interval = setInterval(() => {
      queryClient.invalidateQueries({ queryKey: ['notifications', user.id] });
    }, 15000); // 15 seconds
    
    return () => {
      clearInterval(interval);
    };
  }, [user, queryClient]);

  return {
    notifications,
    isLoading,
    markAsRead,
    markAllAsRead,
    unreadCount: notifications?.filter(n => !n.is_read).length || 0
  };
}
