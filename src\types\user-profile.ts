
// Define common user profile interface
export interface UserProfile {
  id: string;
  display_name?: string;
  username?: string;
  avatar_url?: string;
  bio?: string;
  is_verified?: boolean;
  is_admin?: boolean;
  default_location?: {
    x: number;
    y: number;
    address?: string;
  };
  location?: {
    x: number;
    y: number;
    address?: string;
  };
  created_at?: string;
  updated_at?: string;
  last_seen_at?: string;
  birthday?: string;
  gender?: string;
  purposes?: string[];
  vibes?: string[];
  gallery?: string[];
  favorite_locations?: {
    location?: { x: number; y: number };
    name?: string;
    availability?: string;
  }[];
  followers_count?: number;
  following_count?: number;
  location_permission_granted?: boolean;
  notifications_enabled?: boolean;
  interests?: string[];
  languages?: string[];
  website?: string;
  onboarding_completed?: boolean;
  location_display?: string; // Added location_display property
  is_profile_public?: boolean; // Whether the profile is publicly accessible
}

// Define a more specific interface for handling errors
export interface UserWithProfile {
  user_id: string;
  profile: {
    display_name?: string;
    username?: string;
    avatar_url?: string;
    bio?: string;
    is_verified?: boolean;
    location?: { x: number; y: number };
    last_seen_at?: string;
  };
  is_online: boolean;
  distance: number;
  activity_id: string;
  created_at: string;
  updated_at: string;
  role: string;
  status: string;
}
