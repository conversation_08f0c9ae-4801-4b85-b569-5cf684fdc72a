
import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, CardFooter } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/hooks/use-auth';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { getInitials } from '@/utils/get-initials';
import { SendIcon } from 'lucide-react';
import { User } from '@/types/user';

interface ActivityCommentsProps {
  activityId: string;
}

interface Comment {
  id: string;
  content: string;
  created_at: string;
  user: User;
}

export function ActivityComments({ activityId }: ActivityCommentsProps) {
  const { user } = useAuth();
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [comments, setComments] = useState<Comment[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  React.useEffect(() => {
    const timer = setTimeout(() => {
      setComments([
        {
          id: '1',
          content: 'This activity looks great! Looking forward to it.',
          created_at: new Date(Date.now() - 3600000).toISOString(),
          user: {
            id: 'user1',
            display_name: 'Jane Smith',
            avatar_url: undefined,
            username: undefined,
            bio: undefined,
            is_verified: undefined
          }
        },
        {
          id: '2',
          content: 'Does anyone know if there\'s parking nearby?',
          created_at: new Date(Date.now() - 7200000).toISOString(),
          user: {
            id: 'user2',
            display_name: 'Mike Johnson',
            avatar_url: undefined,
            username: undefined,
            bio: undefined,
            is_verified: undefined
          }
        }
      ]);
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, [activityId]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!comment.trim() || !user) return;

    setIsSubmitting(true);

    setTimeout(() => {
      const newComment: Comment = {
        id: `temp-${Date.now()}`,
        content: comment,
        created_at: new Date().toISOString(),
        user: {
          id: user.id,
          avatar_url: user.user_metadata?.avatar_url,
          display_name: user.user_metadata?.display_name,
          username: user.user_metadata?.username,
          bio: user.user_metadata?.bio,
          is_verified: false
        }
      };

      setComments([newComment, ...comments]);
      setComment('');
      setIsSubmitting(false);
    }, 500);
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Comments</CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2, 3].map(i => (
              <div key={i} className="flex gap-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-1/4" />
                  <Skeleton className="h-16 w-full" />
                </div>
              </div>
            ))}
          </div>
        ) : comments.length > 0 ? (
          <div className="space-y-6">
            {comments.map(comment => (
              <div key={comment.id} className="flex gap-3">
                <Avatar>
                  <AvatarImage src={comment.user.avatar_url} />
                  <AvatarFallback>{getInitials(comment.user.display_name || comment.user.username || 'User')}</AvatarFallback>
                </Avatar>
                <div className="space-y-1 flex-1">
                  <div className="flex justify-between">
                    <p className="font-medium text-sm">
                      {comment.user.display_name || comment.user.username || 'Anonymous User'}
                    </p>
                    <span className="text-xs text-muted-foreground">
                      {formatDate(comment.created_at)}
                    </span>
                  </div>
                  <div className="bg-muted/50 p-3 rounded-lg">
                    <p className="text-sm">{comment.content}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-6 text-muted-foreground">
            <p>No comments yet. Be the first to comment!</p>
          </div>
        )}
      </CardContent>
      {user && (
        <CardFooter>
          <form onSubmit={handleSubmit} className="w-full flex gap-2">
            <Textarea
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Write a comment..."
              className="flex-1"
            />
            <Button type="submit" disabled={!comment.trim() || isSubmitting}>
              {isSubmitting ? (
                <div className="h-4 w-4 border-2 border-background border-t-transparent rounded-full animate-spin" />
              ) : (
                <SendIcon className="h-4 w-4" />
              )}
            </Button>
          </form>
        </CardFooter>
      )}
    </Card>
  );
}
