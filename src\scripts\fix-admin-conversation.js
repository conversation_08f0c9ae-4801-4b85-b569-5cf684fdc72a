/**
 * This script ensures that all users are participants in the admin conversation.
 * It can be run to fix any issues with missing participants.
 * 
 * Usage:
 * 1. Run this script with Node.js: node src/scripts/fix-admin-conversation.js
 * 2. It will find or create the admin conversation and ensure all users are participants
 */

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixAdminConversation() {
  try {
    console.log('Starting admin conversation fix...');

    // Find the admin conversation
    console.log('Looking for admin conversation...');
    const { data: existingConversation, error: queryError } = await supabase
      .from('chat_conversations')
      .select('*')
      .or('is_announcement_only.eq.true,is_admin_conversation.eq.true')
      .order('created_at', { ascending: false })
      .limit(1)
      .maybeSingle();

    if (queryError) {
      throw queryError;
    }

    let conversationId;

    if (existingConversation) {
      console.log(`Found existing admin conversation: ${existingConversation.id}`);
      conversationId = existingConversation.id;
    } else {
      console.log('No admin conversation found, creating one...');
      const { data: newConversation, error: createError } = await supabase
        .from('chat_conversations')
        .insert({
          is_announcement_only: true,
          is_admin_conversation: true,
          is_group: true,
          last_message: 'Welcome to BuddySurf! This is the official announcement channel.',
          last_message_at: new Date().toISOString()
        })
        .select()
        .single();

      if (createError) {
        throw createError;
      }

      console.log(`Created new admin conversation: ${newConversation.id}`);
      conversationId = newConversation.id;

      // Add welcome messages
      console.log('Adding welcome messages...');
      await supabase
        .from('messages')
        .insert([
          {
            conversation_id: conversationId,
            sender_id: 'system', // Use a system ID
            recipient_id: 'system', // Use a system ID
            content: 'Welcome to BuddySurf! 👋 This is the official announcement channel where you\'ll receive important updates and information. Stay tuned for exciting news and features!',
            is_read: false,
            is_admin_message: true,
            created_at: new Date().toISOString()
          },
          {
            conversation_id: conversationId,
            sender_id: 'system', // Use a system ID
            recipient_id: 'system', // Use a system ID
            content: 'Here you\'ll receive important announcements about new features, events, and updates. You can also find help and support by clicking the "Contact Support" button in the chat sidebar.',
            is_read: false,
            is_admin_message: true,
            created_at: new Date(Date.now() + 1000).toISOString() // 1 second later
          }
        ]);
    }

    // Get all users
    console.log('Fetching all users...');
    const { data: allUsers, error: usersError } = await supabase
      .from('profiles')
      .select('id');

    if (usersError) {
      throw usersError;
    }

    console.log(`Found ${allUsers.length} users`);

    // Get existing participants
    console.log('Fetching existing participants...');
    const { data: existingParticipants, error: participantsError } = await supabase
      .from('chat_participants')
      .select('user_id')
      .eq('conversation_id', conversationId);

    if (participantsError) {
      throw participantsError;
    }

    console.log(`Found ${existingParticipants.length} existing participants`);

    // Create a set of existing participant user IDs for quick lookup
    const existingParticipantIds = new Set(existingParticipants.map(p => p.user_id));

    // Add users who are not already participants
    const newParticipants = allUsers
      .filter(user => !existingParticipantIds.has(user.id))
      .map(user => ({
        conversation_id: conversationId,
        user_id: user.id,
        joined_at: new Date().toISOString()
      }));

    if (newParticipants.length > 0) {
      console.log(`Adding ${newParticipants.length} new participants...`);

      // Process in batches to avoid request size limits
      const batchSize = 50;
      const batches = [];
      
      for (let i = 0; i < newParticipants.length; i += batchSize) {
        batches.push(newParticipants.slice(i, i + batchSize));
      }
      
      console.log(`Processing ${batches.length} batches of users`);
      
      for (const batch of batches) {
        const { error: insertError } = await supabase
          .from('chat_participants')
          .insert(batch);

        if (insertError) {
          console.error('Error adding batch of participants:', insertError);
          // Continue with next batch
        }
      }

      console.log('New participants added successfully');
    } else {
      console.log('All users are already participants');
    }

    console.log('Admin conversation fix completed successfully!');
  } catch (error) {
    console.error('Error fixing admin conversation:', error);
    process.exit(1);
  }
}

// Run the function
fixAdminConversation();
