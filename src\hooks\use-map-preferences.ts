
import { useMemo } from 'react';
import { useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useState } from 'react';

export type MapStyleId = 'streets' | 'satellite' | 'dark' | 'light';

export interface UserPreferences {
  user_id: string;
  map_style: string;
  mapStyle?: string; // For backward compatibility
  map_zoom_level: number;
  map_show_users: boolean;
  map_show_activities: boolean;
  map_show_services: boolean;
  map_show_markers: boolean;
  map_view_mode: 'satellite' | 'streets' | 'dark' | 'light';
  dark_mode: boolean;
  created_at?: string;
  updated_at?: string;
  // Additional preferences used in MeetMapPage
  enablePitch: boolean;
  enableRotation: boolean;
  showBuildings: boolean;
  saveLocation: boolean;
}

const defaultPreferences: UserPreferences = {
  user_id: '',
  map_style: 'mapbox://styles/mapbox/streets-v12', 
  mapStyle: 'streets-v12',
  map_zoom_level: 12,
  map_show_users: true,
  map_show_activities: true,
  map_show_services: true,
  map_show_markers: true,
  map_view_mode: 'streets',
  dark_mode: false,
  enablePitch: true,
  enableRotation: true,
  showBuildings: true,
  saveLocation: true
};

export function useMapPreferences() {
  const queryClient = useQueryClient();
  const [preferences, setPreferences] = useState<UserPreferences | null>(null);

  // Mock implementation using localStorage instead of DB table
  const { data, isLoading } = useQuery({
    queryKey: ['map-preferences'],
    queryFn: async () => {
      // Use localStorage as a temporary store
      const storedPrefs = localStorage.getItem('map-preferences');
      if (storedPrefs) {
        try {
          return JSON.parse(storedPrefs) as UserPreferences;
        } catch (e) {
          console.error('Failed to parse stored preferences:', e);
        }
      }

      const user = await supabase.auth.getUser();
      const userId = user.data?.user?.id;
      
      if (!userId) return defaultPreferences;
      
      // Create default preferences with this user's ID
      const userPrefs = {
        ...defaultPreferences,
        user_id: userId
      };

      // Store for future use
      localStorage.setItem('map-preferences', JSON.stringify(userPrefs));
      
      return userPrefs;
    }
  });

  // Update preferences
  const updatePreferences = useMutation({
    mutationFn: async (newPreferences: Partial<UserPreferences>) => {
      const user = await supabase.auth.getUser();
      const userId = user.data?.user?.id;
      
      if (!userId) throw new Error('User not authenticated');
      
      // Get current preferences
      const storedPrefs = localStorage.getItem('map-preferences');
      const currentPrefs = storedPrefs 
        ? JSON.parse(storedPrefs) as UserPreferences 
        : { ...defaultPreferences, user_id: userId };
      
      // Update with new values
      const updatedPrefs = {
        ...currentPrefs,
        ...newPreferences,
        updated_at: new Date().toISOString()
      };
      
      // Store updated preferences
      localStorage.setItem('map-preferences', JSON.stringify(updatedPrefs));
      
      return updatedPrefs;
    },
    onSuccess: (data) => {
      queryClient.setQueryData(['map-preferences'], data);
      setPreferences(data);
    }
  });

  // Add a helper function to save preferences
  const savePreferences = useCallback((newPreferences: Partial<UserPreferences>) => {
    return updatePreferences.mutate(newPreferences);
  }, [updatePreferences]);

  return {
    preferences: data || defaultPreferences,
    updatePreferences,
    savePreferences,
    isLoading
  };
}
