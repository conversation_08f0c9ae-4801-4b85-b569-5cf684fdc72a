
import React, { useEffect } from 'react';
import { useActivityQueue, useUpdateQueueStatus } from '@/hooks/use-activity-queue';
import { Button } from "@/components/ui/button";
import { useToast } from '@/components/ui/use-toast';
import { Activity } from '@/types/activity';
import { useAuth } from '@/hooks/use-auth';
import { Progress } from "@/components/ui/progress";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardFooter,
} from "@/components/ui/card";
import {
  Users,
  Clock,
  CheckCircle,
  XCircle,
  DollarSign,
  AlertCircle,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useProfile } from "@/hooks/use-profile";
import { Badge } from "@/components/ui/badge";
import { useQueueNotifications } from '@/hooks/use-queue-notifications';
import { useRecalculatePositions } from '@/hooks/use-recalculate-positions';
import { ActivityQueueParticipants } from './ActivityQueueParticipants';

interface ActivityQueueManagementProps {
  activity: Activity;
  className?: string;
}

export function ActivityQueueManagement({ activity, className = '' }: ActivityQueueManagementProps) {
  // Enable real-time notifications
  useQueueNotifications(activity.id);

  const { data: queueEntries, isLoading, refetch } = useActivityQueue(activity.id);
  const updateStatus = useUpdateQueueStatus();
  const { user } = useAuth();
  const { toast } = useToast();
  const recalculatePositions = useRecalculatePositions();

  const confirmedCount = queueEntries?.filter(entry => entry.status === 'confirmed').length || 0;
  const pendingCount = queueEntries?.filter(entry => entry.status === 'pending').length || 0;
  const progress = activity.max_participants
    ? (confirmedCount / activity.max_participants) * 100
    : 0;

  const isHost = activity.host_id === user?.id;

  // Add effect to auto-refresh queue data
  useEffect(() => {
    const interval = setInterval(() => {
      if (isHost) {
        refetch();
      }
    }, 10000); // Refresh every 10 seconds for hosts

    return () => clearInterval(interval);
  }, [isHost, refetch]);

  const handleStatusUpdate = async (entryId: string, status: 'confirmed' | 'cancelled') => {
    if (!isHost) {
      toast({
        title: "Permission denied",
        description: "Only the activity host can manage the queue",
        variant: "destructive"
      });
      return;
    }

    try {
      await updateStatus.mutateAsync({ entryId, status });

      // Recalculate positions after status change
      await recalculatePositions(activity.id);

      // Show success message
      toast({
        title: status === 'confirmed' ? "Participant confirmed" : "Participant removed",
        description: status === 'confirmed'
          ? "The participant has been successfully confirmed."
          : "The participant has been removed from the activity.",
      });

      // Refresh queue data
      refetch();
    } catch (error) {
      console.error('Error updating queue status:', error);
    }
  };

  // Function to confirm all pending participants up to max capacity
  const confirmAllPending = async () => {
    if (!isHost || !activity.max_participants) return;

    const remainingSpots = activity.max_participants - confirmedCount;
    if (remainingSpots <= 0) {
      toast({
        title: "No spots available",
        description: "There are no spots left to fill.",
        variant: "destructive"
      });
      return;
    }

    const pendingEntries = queueEntries?.filter(entry => entry.status === 'pending') || [];
    const entriesToConfirm = pendingEntries.slice(0, remainingSpots);

    if (entriesToConfirm.length === 0) {
      toast({
        title: "No pending participants",
        description: "There are no pending participants in the queue.",
      });
      return;
    }

    try {
      // Update status for each entry
      for (const entry of entriesToConfirm) {
        await updateStatus.mutateAsync({ entryId: entry.id, status: 'confirmed' });
      }

      // Recalculate positions
      await recalculatePositions(activity.id);

      toast({
        title: "Participants confirmed",
        description: `${entriesToConfirm.length} participants have been confirmed.`,
      });

      // Refresh queue data
      refetch();
    } catch (error) {
      console.error('Error confirming participants:', error);
      toast({
        title: "Error confirming participants",
        description: "An error occurred while confirming participants.",
        variant: "destructive"
      });
    }
  };

  if (isLoading) {
    return <div>Loading queue status...</div>;
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Queue Management
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>{confirmedCount} confirmed</span>
              <span>{activity.max_participants ? `${activity.max_participants - confirmedCount} spots left` : 'Unlimited'}</span>
            </div>
            <Progress value={progress} className="h-2" />
          </div>

          {pendingCount > 0 && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Clock className="h-4 w-4" />
              <span>{pendingCount} in queue</span>
            </div>
          )}

          {isHost && pendingCount > 0 && activity.max_participants && confirmedCount < activity.max_participants && (
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={confirmAllPending}
              disabled={updateStatus.isPending}
            >
              {updateStatus.isPending ? "Processing..." : "Auto-fill Available Spots"}
            </Button>
          )}

          {queueEntries && queueEntries.length > 0 && (
            <ActivityQueueParticipants
              entries={queueEntries}
              isHost={isHost}
              onUpdateStatus={handleStatusUpdate}
              isProcessing={updateStatus.isPending}
            />
          )}
        </div>
      </CardContent>

      {isHost && (
        <CardFooter>
          <p className="text-xs text-muted-foreground">
            As the host, you can manage the queue by confirming or removing participants.
          </p>
        </CardFooter>
      )}
    </Card>
  );
}
