
import React from 'react';
import { useMessageReactions } from '@/hooks/use-message-reactions';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useAuth } from '@/hooks/use-auth';
import { useProfile } from '@/hooks/use-profile';

interface MessageReactionsProps {
  messageId: string;
  conversationId: string;
  align?: 'left' | 'right' | 'center';
}

export function MessageReactions({ messageId, conversationId, align = 'left' }: MessageReactionsProps) {
  const { reactions, isLoading, addReaction, removeReaction, hasUserReacted } = useMessageReactions(messageId, conversationId);
  const { user } = useAuth();
  const { data: userProfile } = useProfile(user?.id);

  if (isLoading || !reactions || reactions.length === 0) {
    return null;
  }

  const justifyContent =
    align === 'right' ? 'justify-end' :
    align === 'center' ? 'justify-center' :
    'justify-start';

  return (
    <div className={`flex flex-wrap gap-1 mt-1 ${justifyContent}`}>
      {reactions.map((reaction, index) => {
        const userHasReacted = hasUserReacted(reaction.emoji);

        return (
          <TooltipProvider key={`${reaction.emoji}-${index}`}>
            <Tooltip>
              <TooltipTrigger asChild>
                <button
                  onClick={() => {
                    if (userHasReacted) {
                      removeReaction.mutate(reaction.emoji);
                    } else {
                      addReaction.mutate(reaction.emoji);
                    }
                  }}
                  className={`
                    rounded-full px-2 py-0.5 text-xs
                    ${userHasReacted
                      ? 'bg-blue-100 text-blue-800 border border-blue-200'
                      : 'bg-gray-100 text-gray-800 hover:bg-gray-200'}
                    transition-colors
                  `}
                >
                  {reaction.emoji} {reaction.count > 1 ? reaction.count : ''}
                </button>
              </TooltipTrigger>
              <TooltipContent>
                <p>
                  {userHasReacted
                    ? 'You reacted with this emoji. Click to remove.'
                    : 'Click to add your reaction'}
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        );
      })}
    </div>
  );
}
