
import { But<PERSON> } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Bell, Loader2, Check } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { useNotifications } from '@/hooks/use-notifications';
import { format } from 'date-fns';
import { useState } from 'react';
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

export function NotificationPopover() {
  const { notifications, markAsRead, markAllAsRead, unreadCount, isLoading } = useNotifications();
  const [isMarkingAllRead, setIsMarkingAllRead] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  const handleNotificationClick = (notificationId: string, actionUrl?: string) => {
    markAsRead.mutate(notificationId);
    if (actionUrl) {
      window.location.href = actionUrl;
    }
  };

  const handleMarkAllAsRead = async () => {
    setIsMarkingAllRead(true);
    await markAllAsRead.mutateAsync();
    setIsMarkingAllRead(false);
  };

  return (
    <Popover open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button size="icon" variant="ghost" className="relative" aria-label="Open notifications">
          <Bell size={20} strokeWidth={1.5} aria-hidden="true" />
          {unreadCount > 0 && (
            <Badge 
              className={cn(
                "absolute -top-2 -right-1 min-w-5 h-5 flex items-center justify-center px-1",
                "animate-in fade-in-50 zoom-in-95 duration-300"
              )}
            >
              {unreadCount > 99 ? "99+" : unreadCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" align="end">
        <div className="flex items-center justify-between gap-4 px-4 py-3 border-b">
          <div className="text-sm font-semibold">Notifications</div>
          {unreadCount > 0 && (
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={handleMarkAllAsRead}
                    disabled={isMarkingAllRead || !unreadCount}
                    className="h-8 text-xs font-medium"
                  >
                    {isMarkingAllRead ? (
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                    ) : (
                      <Check className="h-3.5 w-3.5 mr-1" />
                    )}
                    Mark all as read
                  </Button>
                </TooltipTrigger>
                <TooltipContent side="bottom">
                  <p className="text-xs">Mark all notifications as read</p>
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          )}
        </div>
        
        <ScrollArea className="max-h-[60vh]">
          {isLoading ? (
            <div className="flex justify-center items-center p-6">
              <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            </div>
          ) : notifications && notifications.length > 0 ? (
            notifications.map((notification) => (
              <div
                key={notification.id}
                className={cn(
                  "px-4 py-3 text-sm border-b last:border-0 transition-colors",
                  !notification.is_read 
                    ? 'bg-accent/50 hover:bg-accent/40' 
                    : 'hover:bg-accent/20'
                )}
              >
                <button
                  className="w-full text-left"
                  onClick={() => handleNotificationClick(notification.id, notification.action_url)}
                >
                  <div className="font-medium">{notification.title}</div>
                  <div className="text-sm text-muted-foreground">{notification.content}</div>
                  <div className="text-xs text-muted-foreground/70 mt-1">
                    {format(new Date(notification.created_at), 'MMM d, h:mm a')}
                  </div>
                </button>
              </div>
            ))
          ) : (
            <div className="px-4 py-6 text-center text-sm text-muted-foreground">
              You have no notifications
            </div>
          )}
        </ScrollArea>
      </PopoverContent>
    </Popover>
  );
}
