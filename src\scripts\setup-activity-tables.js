/**
 * This script sets up the activity-related tables in the Supabase database.
 *
 * Usage:
 * 1. Make sure you have the Supabase service key in your .env file
 * 2. Run this script with Node.js: node src/scripts/setup-activity-tables.js
 */

import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import 'dotenv/config';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY; // Use service key for admin operations

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupActivityTables() {
  try {
    console.log('Setting up activity tables...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../../sql/create_activity_tables.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    const { error } = await supabase.sql(sqlContent);

    if (error) {
      console.error('Error setting up activity tables:', error);
      process.exit(1);
    }

    console.log('Activity tables setup completed successfully!');

    // Verify the activities table structure
    const { data, error: tableError } = await supabase
      .from('activities')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error('Error verifying activities table:', tableError);
      process.exit(1);
    }

    console.log('Activities table verified successfully!');

    // Verify the categories table has data
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('*');

    if (categoriesError) {
      console.error('Error verifying categories:', categoriesError);
      process.exit(1);
    }

    console.log(`Categories table has ${categories.length} entries.`);

    // Enable realtime for activities table
    console.log('Enabling realtime for activities table...');

    const { error: realtimeError } = await supabase.sql(`
      BEGIN;
      -- Drop the publication if it exists
      DROP PUBLICATION IF EXISTS supabase_realtime;

      -- Create a new publication for all tables
      CREATE PUBLICATION supabase_realtime FOR TABLE
        activities,
        activity_participants,
        activity_queue,
        profiles,
        messages,
        chat_conversations,
        chat_participants,
        location_messages,
        message_reactions;
      COMMIT;
    `);

    if (realtimeError) {
      console.error('Error enabling realtime:', realtimeError);
      process.exit(1);
    }

    console.log('Realtime enabled for activities table.');
    console.log('Setup completed successfully!');
  } catch (error) {
    console.error('Error setting up activity tables:', error);
    process.exit(1);
  }
}

// Execute the function
setupActivityTables();
