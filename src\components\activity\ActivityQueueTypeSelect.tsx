
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { UseFormReturn } from 'react-hook-form';

interface ActivityQueueTypeSelectProps {
  form: UseFormReturn<any>;
}

export function ActivityQueueTypeSelect({ form }: ActivityQueueTypeSelectProps) {
  return (
    <FormField
      control={form.control}
      name="queueType"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Queue Type</FormLabel>
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select queue type" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="fifo">First Come, First Served</SelectItem>
              <SelectItem value="priority">Priority Based</SelectItem>
            </SelectContent>
          </Select>
          <FormDescription>
            Choose how participants will be queued for your activity
          </FormDescription>
          <FormMessage />
        </FormItem>
      )}
    />
  );
}
