
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Send, Loader2 } from 'lucide-react';
import { useAdminConversation } from '@/hooks/use-buddy-admin-conversation';
import { useAuth } from '@/hooks/use-auth';
import { useProfile } from '@/hooks/use-profile';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

/**
 * This component allows admins to send messages to all users via the Buddy Admin conversation.
 */
export function AdminMessagePanel() {
  const [open, setOpen] = useState(false);
  const [message, setMessage] = useState('');
  const { adminConversation, sendAdminMessage } = useAdminConversation();
  const { user } = useAuth();
  const { data: profile } = useProfile(user?.id);

  // Check if user is admin
  const isAdmin = profile?.is_admin === true;

  // Only show this panel for admin users
  if (!user || !isAdmin) {
    return null;
  }

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!message.trim() || !adminConversation?.id) return;

    try {
      // Update: Just pass the message string as expected by the mutation
      await sendAdminMessage.mutateAsync(message);

      setMessage('');
      setOpen(false);
    } catch (error) {
      console.error('Error sending admin message:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="default" size="sm" className="bg-blue-600 hover:bg-blue-700 text-white">
          Send Admin Announcement
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Send Admin Announcement</DialogTitle>
          <DialogDescription>
            Send an announcement to all users. This will appear in the Buddy Admin conversation for everyone.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSendMessage}>
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <Label htmlFor="message">Message</Label>
              <Textarea
                id="message"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
                placeholder="Enter your announcement message..."
                className="resize-none"
                rows={4}
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              type="submit"
              disabled={!message.trim() || sendAdminMessage.isPending}
            >
              {sendAdminMessage.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Sending...
                </>
              ) : (
                <>
                  <Send className="mr-2 h-4 w-4" />
                  Send as Admin
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
