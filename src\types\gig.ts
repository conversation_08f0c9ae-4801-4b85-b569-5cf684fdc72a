
export interface GigCategory {
  id: string;
  name: string;
  description?: string;
}

export interface Gig {
  id: string;
  title: string;
  description: string;
  short_description: string;
  hourly_rate: number;
  price_starting: number;
  provider_id: string;
  tags: string[];
  delivery_time: number;
  experience_level: 'beginner' | 'intermediate' | 'expert';
  availability: 'available' | 'busy' | 'unavailable';
  created_at: string;
  updated_at: string;
  total_orders: number;
  rating: number;
  profile: {
    id: string;
    display_name?: string;
    username?: string;
    avatar_url?: string;
    bio?: string;
    is_verified?: boolean;
  };
}
