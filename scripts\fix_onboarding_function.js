// Script to fix the check_onboarding_completed function
import fs from 'fs';
import path from 'path';
import { createClient } from '@supabase/supabase-js';
import { fileURLToPath } from 'url';

// Get the current file's directory
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Get Supabase credentials from environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

// Create Supabase client
const supabase = createClient(supabaseUrl, supabaseKey);

async function fixOnboardingFunction() {
  try {
    console.log('Fixing check_onboarding_completed function...');

    // Read the SQL file
    const sqlFilePath = path.join(__dirname, '../sql/fix_check_onboarding_completed.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    // Execute the SQL
    const { error } = await supabase.sql(sqlContent);

    if (error) {
      console.error('Error fixing function:', error);
      process.exit(1);
    }

    console.log('Function fixed successfully!');

    // Verify the function exists
    const { data, error: verifyError } = await supabase
      .rpc('check_onboarding_completed', { user_id: '00000000-0000-0000-0000-000000000000' });

    if (verifyError) {
      console.log('Function exists but returned an error (expected for non-existent user):', verifyError);
    } else {
      console.log('Function executed successfully:', data);
    }

    process.exit(0);
  } catch (error) {
    console.error('Unexpected error:', error);
    process.exit(1);
  }
}

// Run the function
fixOnboardingFunction();
