
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Activity } from '@/types/activity';
import { toUnifiedLocation } from '@/utils/location-normalizer';

export interface ActivityMapFilters {
  category?: string;
  categoryIds?: string[];
  status?: string;
  radius?: number;
  minPrice?: number;
  maxPrice?: number;
  dateRange?: {
    start?: string;
    end?: string;
  };
  priceRange?: {
    min?: number;
    max?: number;
  };
  distance?: number;
}

export function useMapActivities() {
  return useQuery({
    queryKey: ['map-activities'],
    queryFn: async (): Promise<Activity[]> => {
      const { data, error } = await supabase
        .from('activities')
        .select(`
          *,
          category:categories(id, name, icon, type)
        `)
        .eq('status', 'active')
        .order('start_time', { ascending: true });

      if (error) {
        throw error;
      }

      return data.map(activity => {
        const location = toUnifiedLocation(activity.location);

        return {
          id: activity.id,
          title: activity.title,
          description: activity.description || '',
          start_time: activity.start_time,
          end_time: activity.end_time,
          location,
          address: activity.address,
          host_id: activity.host_id,
          is_paid: activity.is_paid || false,
          price: activity.price,
          max_participants: activity.max_participants,
          media_urls: activity.media_urls || [],
          created_at: activity.created_at,
          updated_at: activity.updated_at,
          status: activity.status as 'active' | 'cancelled' | 'completed',
          visibility: activity.visibility as 'public' | 'private' | 'unlisted' || 'public',
          queue_type: activity.queue_type as 'fcfs' | 'priority' | 'fifo' || 'fcfs',
          allow_waitlist: activity.allow_waitlist || false,
          group_chat_id: activity.group_chat_id || '',
          moderation_status: '',
          is_verified: false,
          category: activity.category || undefined,
          current_participants: 0
        } as Activity;
      });
    }
  });
}
