
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FormControl } from "@/components/ui/form";
import { ArrowDownAZ, Star } from 'lucide-react';

interface QueueTypeSelectProps {
  field: any;
}

export function QueueTypeSelect({ field }: QueueTypeSelectProps) {
  return (
    <Select onValueChange={field.onChange} defaultValue={field.value}>
      <FormControl>
        <SelectTrigger>
          <SelectValue placeholder="Select queue type" />
        </SelectTrigger>
      </FormControl>
      <SelectContent>
        <SelectItem value="fifo">
          <div className="flex items-center">
            <ArrowDownAZ className="h-4 w-4 mr-2" />
            <span>First In, First Out</span>
          </div>
        </SelectItem>
        <SelectItem value="priority">
          <div className="flex items-center">
            <Star className="h-4 w-4 mr-2" />
            <span>Priority Queue</span>
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  );
}
