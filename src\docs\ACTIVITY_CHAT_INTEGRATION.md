# Activity-Chat Integration Documentation

This document outlines the integration between the Activity system and the Chat system in BuddySurf, including activity proposal cards, group chats for participants, and location sharing in activity chats.

## 1. Overview

The Activity-Chat Integration consists of three main components:

1. **Activity Proposal Cards**: Rich, interactive cards that can be sent in chat to propose activities to other users
2. **Activity Group Chats**: Automatically created group conversations for all participants of an activity
3. **Location Sharing**: The ability to share precise locations within chat conversations, particularly useful for activity coordination

## 2. Activity Proposal Cards

### 2.1 Purpose

Activity Proposal Cards allow users to:
- Share activities they've created with other users
- Invite others to join their activities
- Provide rich, visual information about the activity
- Allow recipients to accept or decline the invitation directly in chat

### 2.2 Implementation

The implementation consists of:

- `ActivityProposalCard` component: Displays activity details with accept/decline buttons
- `useCreateProposal` hook: Handles creating new activity proposals
- `useRespondToProposal` hook: Handles accepting or declining proposals
- Database tables: `chat_proposals` links proposals to conversations and activities

### 2.3 Features

- **Rich Visual Display**: Shows activity title, description, date, location, price, and host
- **Interactive Map**: Embedded map showing the activity location
- **Accept/Decline Buttons**: Allow recipients to respond directly
- **Status Indicators**: Show whether a proposal is pending, accepted, or declined
- **Expiration Handling**: Properly handles expired or past activities
- **Navigation Options**: Buttons to view details, chat with participants, share, or open in maps

## 3. Activity Group Chats

### 3.1 Purpose

Activity Group Chats provide:
- A dedicated communication channel for all activity participants
- Automatic participant management as users join or leave the activity
- System messages for important activity updates
- Location sharing for coordination

### 3.2 Implementation

The implementation consists of:

- `useActivityGroupChat` hook: Manages the group chat for an activity
- `ActivityGroupChat` component: Displays the group chat UI
- Database integration: Links activities to chat conversations

### 3.3 Features

- **Automatic Creation**: Group chat is created when an activity is created
- **Participant Management**: Users are automatically added to the chat when they join the activity
- **System Messages**: Notifications when users join or leave
- **Location Sharing**: Ability to share precise meeting locations
- **Full Chat Integration**: Option to open in the main chat interface
- **Real-time Updates**: Messages appear instantly for all participants

## 4. Location Sharing in Chats

### 4.1 Purpose

Location sharing allows users to:
- Share precise meeting locations with other users
- Provide context with custom labels
- Set expiration times for shared locations
- View shared locations on an interactive map
- Navigate to shared locations

### 4.2 Implementation

The implementation consists of:

- `LocationShareButton` component: UI for sharing locations
- `LocationMessageCard` component: Displays shared locations in chat
- `useLocationMessages` hook: Manages location messages in a conversation
- Database table: `location_messages` stores shared location data

### 4.3 Features

- **Interactive Maps**: Embedded maps showing the shared location
- **Custom Labels**: Users can add descriptive labels to locations
- **Expiration Times**: Locations automatically expire after a set time
- **Navigation Options**: Open in maps or navigate directly
- **Manual Expiration**: Senders can manually expire shared locations
- **Real-time Updates**: Location messages appear instantly for all participants

## 5. Database Schema

### 5.1 Tables

#### chat_proposals
- `id`: Primary key
- `conversation_id`: Reference to chat_conversations
- `sender_id`: User who sent the proposal
- `recipient_id`: User who received the proposal
- `activity_id`: Reference to activities
- `status`: Status of the proposal (pending, accepted, declined)
- `created_at`: Timestamp
- `expires_at`: Expiration timestamp

#### location_messages
- `id`: Primary key
- `conversation_id`: Reference to chat_conversations
- `sender_id`: User who shared the location
- `sender_name`: Display name of the sender
- `sender_avatar_url`: Avatar URL of the sender
- `lat`: Latitude
- `lng`: Longitude
- `label`: Custom label for the location
- `expiration`: Expiration timestamp
- `is_expired`: Whether the location has expired
- `created_at`: Timestamp

#### messages (additions)
- `is_system_message`: Boolean indicating if it's a system message

#### chat_participants (additions)
- `joined_at`: Timestamp when the user joined the conversation

### 5.2 Relationships

- Activities have a one-to-many relationship with chat_proposals
- Activities have a one-to-one relationship with group chat conversations
- Conversations have a one-to-many relationship with location_messages
- Users have a many-to-many relationship with conversations through chat_participants

## 6. Security Considerations

### 6.1 Row-Level Security

- Users can only view proposals they've sent or received
- Users can only view location messages in conversations they're part of
- Users can only send location messages to conversations they're part of
- Users can only update or expire their own location messages

### 6.2 Data Validation

- Location coordinates are validated before storage
- Expiration times have reasonable limits
- Activity proposals are validated against existing activities
- System messages are properly flagged and secured

## 7. User Experience Flow

### 7.1 Sending an Activity Proposal

1. User navigates to a chat conversation
2. User clicks the "Propose Activity" button
3. User selects an activity from their created activities
4. Proposal card appears in the chat for the recipient
5. Recipient can view details, accept, or decline

### 7.2 Activity Group Chat

1. Activity is created with an associated group chat
2. Host is automatically added as a participant
3. Users who join the activity are automatically added to the chat
4. System messages notify when users join or leave
5. Participants can share locations for coordination
6. Participants can open the full chat interface for more features

### 7.3 Sharing a Location

1. User clicks the location share button in chat
2. User confirms current location or selects a custom location
3. User adds a label and sets expiration time
4. Location card appears in the chat for all participants
5. Recipients can view the location, open in maps, or navigate to it
6. Location automatically expires after the set time

## 8. Future Enhancements

- **Rich Media in Group Chats**: Allow sharing photos and videos
- **Activity Updates in Chat**: Automatically post updates when activity details change
- **Advanced Location Features**: Routes, ETA, and real-time tracking
- **Poll Creation**: Create polls for activity decisions
- **Calendar Integration**: Add activities to calendar from chat
- **Check-in System**: Allow participants to check in when they arrive
