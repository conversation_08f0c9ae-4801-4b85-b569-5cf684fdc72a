/* User markers */
.user-marker {
  cursor: pointer;
  transition: all 0.3s ease;
}

.user-marker:hover {
  transform: scale(1.1);
}

.user-marker:active {
  transform: scale(0.95);
}

.user-marker.selected-user {
  z-index: 20;
}

.user-marker.current-user {
  z-index: 1000;
}

.user-marker.current-user:hover {
  z-index: 1001;
}

.user-marker.current-user .animate-ping {
  animation: ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite;
}

.online-indicator {
  position: absolute;
  right: -2px;
  bottom: -2px;
  width: 12px;
  height: 12px;
  background-color: #10b981; /* Green */
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
  animation: pulse 2s infinite;
}

.marker-tooltip {
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) translateY(calc(100% + 8px));
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s ease;
  z-index: 50;
}

.user-marker:hover .marker-tooltip,
.activity-marker:hover .marker-tooltip {
  opacity: 1;
}

/* Activity markers */
.activity-marker {
  cursor: pointer;
  transition: all 0.3s ease;
}

.activity-marker.new-activity {
  animation: bounce 1s ease infinite;
}

.activity-marker.selected-activity {
  z-index: 10;
  transform: scale(1.2);
}

.price-indicator {
  position: absolute;
  right: -2px;
  top: -2px;
  width: 16px;
  height: 16px;
  background-color: #f59e0b;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  border: 1px solid white;
}

/* Cluster animations */
.mapboxgl-canvas-container .cluster-animation {
  transition: all 0.3s ease-in-out;
}

.mapboxgl-canvas-container .cluster-animation:hover {
  transform: scale(1.1);
}

/* Animations */
@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.8;
  }
  50% {
    transform: scale(1.1);
    opacity: 1;
  }
  100% {
    transform: scale(1);
    opacity: 0.8;
  }
}

@keyframes bounce {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-5px);
  }
}

@keyframes ping {
  75%, 100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

/* Marker animations */
.mapboxgl-marker {
  will-change: transform;
  transition: transform 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.mapboxgl-marker:hover {
  z-index: 100;
}

/* Loading animations for clusters */
.cluster-loading {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top-color: #4f46e5;
  animation: spin 1s infinite linear;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Map layout */
.map-layout {
  display: flex;
  height: 100%;
  width: 100%;
}

.map-container {
  flex: 1;
  position: relative;
}

.map-sidebar {
  height: 100%;
  backdrop-filter: blur(8px);
  border-left: 1px solid rgba(255, 255, 255, 0.1);
}
