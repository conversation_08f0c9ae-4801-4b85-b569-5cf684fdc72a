
import React from 'react';
import { CheckCircle2, <PERSON><PERSON><PERSON><PERSON>, Clock, RefreshCcw } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from 'date-fns';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

interface MapLocationStatusProps {
  isConnected: boolean;
  isLoading: boolean;
  lastUpdate: Date | null;
  onRefresh: () => void;
  className?: string;
}

export function MapLocationStatus({
  isConnected,
  isLoading,
  lastUpdate,
  onRefresh,
  className
}: MapLocationStatusProps) {
  const statusColor = isConnected ? "text-green-500" : "text-red-500";
  const statusIcon = isConnected ? <CheckCircle2 className="h-4 w-4" /> : <XCircle className="h-4 w-4" />;
  
  const formatLastUpdate = () => {
    if (!lastUpdate) return "Never updated";
    return `${formatDistanceToNow(lastUpdate, { addSuffix: true })}`;
  };

  return (
    <div className={cn("flex items-center justify-between p-3 rounded-lg shadow-sm text-sm", className)}>
      <div className="flex items-center space-x-1.5">
        <span className={statusColor}>{statusIcon}</span>
        <span className="font-medium">
          {isConnected ? "Connected" : "Disconnected"}
        </span>
      </div>

      <div className="flex items-center space-x-4">
        <div className="flex items-center text-muted-foreground text-xs">
          <Clock className="h-3.5 w-3.5 mr-1" />
          <span>{formatLastUpdate()}</span>
        </div>

        <TooltipProvider>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="outline" 
                size="sm" 
                className="h-8 w-8 p-0" 
                onClick={onRefresh}
                disabled={isLoading}
              >
                <RefreshCcw className={cn("h-4 w-4", isLoading && "animate-spin")} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Update location</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </div>
  );
}
