
import { useEffect } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Navigate, useLocation } from "react-router-dom";

interface RequireAuthProps {
  children: React.ReactNode;
}

export function RequireAuth({ children }: RequireAuthProps) {
  const { user, isLoading } = useAuth();
  const location = useLocation();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-purple"></div>
      </div>
    );
  }

  if (!user) {
    // Redirect to home page but save where they were trying to go
    return <Navigate to="/" state={{ from: location }} replace />;
  }

  return <>{children}</>;
}
