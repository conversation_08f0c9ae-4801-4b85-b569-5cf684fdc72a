
import React from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>r<PERSON><PERSON><PERSON>, UserPlus, UserMinus, Loader2 } from 'lucide-react';
import { cn } from '@/lib/utils';

interface FollowButtonProps {
  userId: string;
  isFollowing: boolean;
  onToggle: () => void;
  disabled?: boolean;
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

export function FollowButton({
  userId,
  isFollowing,
  onToggle,
  disabled = false,
  size = 'default',
  className
}: FollowButtonProps) {
  return (
    <Button
      variant={isFollowing ? "outline" : "default"}
      size={size}
      onClick={onToggle}
      disabled={disabled}
      className={cn(
        "gap-1",
        isFollowing ? "text-muted-foreground" : "text-white",
        className
      )}
    >
      {disabled ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : isFollowing ? (
        <>
          <UserMinus className="h-4 w-4" />
          <span>Unfollow</span>
        </>
      ) : (
        <>
          <UserPlus className="h-4 w-4" />
          <span>Follow</span>
        </>
      )}
    </Button>
  );
}
