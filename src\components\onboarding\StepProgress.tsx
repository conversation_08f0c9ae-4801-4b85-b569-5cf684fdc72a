import React from 'react';
import { LinearProgressWithLabel } from '@/components/ui/linear-progress';
interface StepProgressProps {
  currentStep: number;
  totalSteps: number;
  onStepClick?: (step: number) => void;
  completedSteps?: Record<number, boolean>;
  requiredSteps?: number[];
}
export function StepProgress({
  currentStep,
  totalSteps,
  onStepClick,
  completedSteps = {},
  requiredSteps = []
}: StepProgressProps) {
  const steps = Array.from({
    length: totalSteps
  }, (_, i) => i + 1);
  const progressPercentage = Math.round(currentStep / totalSteps * 100);
  const handleStepClick = (step: number) => {
    if (onStepClick && (step < currentStep || completedSteps[step])) {
      onStepClick(step);
    }
  };
  return (
    <div className="w-full" data-testid="step-progress">
      <LinearProgressWithLabel
        value={progressPercentage}
        className="mb-2"
        barClassName="bg-gradient-to-r from-primary-purple to-primary-deep-purple"
        labelClassName="text-primary-purple font-semibold"
      />
      <div className="flex justify-center mb-2">
        <div className="text-xs text-gray-500">
          Step {currentStep} of {totalSteps}
        </div>
      </div>
    </div>
  );
}