
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { Activity } from '@/types/activity';
import { toUnifiedLocation } from '@/utils/location-normalizer';

// Re-export Activity type for convenience
export type { Activity } from '@/types/activity';

export interface UseActivitiesOptions {
  limit?: number;
  category?: string;
  location?: { lat: number; lng: number };
  radius?: number;
  status?: 'active' | 'cancelled' | 'completed';
}

export function useActivities(options: UseActivitiesOptions = {}) {
  const {
    limit = 20,
    category,
    location,
    radius = 10,
    status = 'active'
  } = options;

  const activitiesQuery = useQuery({
    queryKey: ['activities', limit, category, location, radius, status],
    queryFn: async (): Promise<Activity[]> => {
      try {
        let query = supabase
          .from('activities')
          .select('*')
          .eq('status', status)
          .order('start_time', { ascending: true })
          .limit(limit);

        if (category) {
          query = query.eq('category_id', category);
        }

        const { data, error } = await query;

        if (error) throw error;

        return (data || []).map((activity: any): Activity => {
          const unifiedLocation = toUnifiedLocation(activity.location);

          return {
            id: activity.id,
            title: activity.title,
            description: activity.description || '',
            start_time: activity.start_time,
            end_time: activity.end_time,
            location: unifiedLocation,
            address: activity.address,
            host_id: activity.host_id,
            host: undefined,
            category: undefined,
            is_paid: activity.is_paid || false,
            price: activity.price,
            max_participants: activity.max_participants,
            current_participants: 0,
            media_urls: activity.media_urls || [],
            created_at: activity.created_at,
            updated_at: activity.updated_at,
            status: activity.status as 'active' | 'cancelled' | 'completed',
            visibility: (activity.visibility as 'public' | 'private' | 'unlisted') || 'public',
            queue_type: (activity.queue_type as 'fcfs' | 'priority' | 'fifo') || 'fifo',
            allow_waitlist: activity.allow_waitlist ?? true,
            group_chat_id: activity.group_chat_id,
            moderation_status: '',
            is_verified: false
          };
        });
      } catch (error) {
        console.error('Error fetching activities:', error);
        throw error;
      }
    }
  });

  return {
    activities: activitiesQuery.data || [],
    isLoading: activitiesQuery.isLoading,
    error: activitiesQuery.error,
    refetch: activitiesQuery.refetch
  };
}

export function useActivity(activityId: string) {
  return useQuery({
    queryKey: ['activity', activityId],
    queryFn: async (): Promise<Activity | null> => {
      if (!activityId) return null;

      try {
        const { data, error } = await supabase
          .from('activities')
          .select('*')
          .eq('id', activityId)
          .maybeSingle();

        if (error) throw error;
        if (!data) return null;

        const unifiedLocation = toUnifiedLocation(data.location);

        return {
          id: data.id,
          title: data.title,
          description: data.description || '',
          start_time: data.start_time,
          end_time: data.end_time,
          location: unifiedLocation,
          address: data.address,
          host_id: data.host_id,
          host: undefined,
          category: undefined,
          is_paid: data.is_paid || false,
          price: data.price,
          max_participants: data.max_participants,
          current_participants: 0,
          media_urls: data.media_urls || [],
          created_at: data.created_at,
          updated_at: data.updated_at,
          status: data.status as 'active' | 'cancelled' | 'completed',
          visibility: (data.visibility as 'public' | 'private' | 'unlisted') || 'public',
          queue_type: (data.queue_type as 'fcfs' | 'priority' | 'fifo') || 'fifo',
          allow_waitlist: data.allow_waitlist ?? true,
          group_chat_id: data.group_chat_id,
          moderation_status: '',
          is_verified: false
        };
      } catch (error) {
        console.error('Error fetching activity:', error);
        throw error;
      }
    },
    enabled: !!activityId
  });
}
