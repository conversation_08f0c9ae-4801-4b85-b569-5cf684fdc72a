
import React from 'react';
import { Activity } from '@/types/activity';
import { MapRightSidebarProps, MapRightSidebarTabValue } from './MapRightSidebarTypes';

const MapRightSidebar: React.FC<MapRightSidebarProps> = ({
  users,
  onUserSelect,
  selectedUser,
  activities = [],
  onActivitySelect,
  activeTab = 'nearby',
  onTabChange = () => {},
  className = ''
}) => {
  return (
    <div className={`w-72 bg-background border-l border-border ${className}`}>
      <div className="p-4 border-b">
        <h2 className="font-medium">Nearby Users</h2>
      </div>
      <div className="overflow-auto h-full">
        {users && users.length > 0 ? (
          users.map(user => (
            <div
              key={user.id}
              className={`p-2 border-b cursor-pointer ${selectedUser?.id === user.id ? 'bg-accent' : 'hover:bg-muted'}`}
              onClick={() => onUserSelect(user)}
            >
              <p>{user.display_name || 'Unknown User'}</p>
              <p className="text-xs text-muted-foreground">
                {user.distance ? `${user.distance.toFixed(1)} miles away` : 'Distance unknown'}
              </p>
            </div>
          ))
        ) : (
          <div className="p-4 text-center text-muted-foreground">
            <p>No users found nearby</p>
          </div>
        )}

        {/* Activities section - will be shown if we have activities and tab is 'activities' */}
        {activities && activities.length > 0 && activeTab === 'activities' && (
          <div>
            <div className="p-4 border-b">
              <h2 className="font-medium">Nearby Activities</h2>
            </div>
            {activities.map(activity => (
              <div
                key={activity.id}
                className="p-2 border-b cursor-pointer hover:bg-muted"
                onClick={() => onActivitySelect && onActivitySelect(activity)}
              >
                <p>{activity.title}</p>
                <p className="text-xs text-muted-foreground truncate">
                  {activity.address || 'Location not specified'}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export type { MapRightSidebarTabValue };  // Use export type for isolated modules
export default MapRightSidebar;
