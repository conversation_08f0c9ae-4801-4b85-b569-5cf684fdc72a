// Admin conversation types

export interface AdminConversation {
  id: string;
  created_at: string;
  updated_at: string;
  is_admin_conversation: boolean;
  is_support?: boolean;
  is_group: boolean;
  is_activity_chat?: boolean;
  is_announcement_only?: boolean;
  activity_id?: string;
  last_message?: string;
  last_message_at?: string;
  archived_at?: string;
  archived_by?: string;
  archive_reason?: string;
  title?: string;
  user_id?: string;
  is_active?: boolean;
}

export interface AdminMessage {
  id: string;
  content: string;
  sender_id: string;
  conversation_id: string;
  created_at: string;
  updated_at: string;
  is_read: boolean;
  is_admin: boolean;
  sender_name: string;
  sender_avatar_url: string | null;
  media_url: string | null;
  attachments: any[];
}
