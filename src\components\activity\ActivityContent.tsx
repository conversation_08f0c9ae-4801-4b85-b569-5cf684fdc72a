
import React from 'react';
import { Activity } from '@/types/activity';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface ActivityContentProps {
  activity: Activity;
}

export function ActivityContent({ activity }: ActivityContentProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle>About this activity</CardTitle>
        <CardDescription>Details and description</CardDescription>
      </CardHeader>
      <CardContent>
        <div className="prose prose-sm max-w-none">
          {activity.description ? (
            <div className="whitespace-pre-wrap">{activity.description}</div>
          ) : (
            <p className="text-muted-foreground">No description provided.</p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
