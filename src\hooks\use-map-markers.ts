
import { useRef, useEffect } from 'react';
import mapboxgl from 'mapbox-gl';
import { UnifiedMapUser } from '@/types/map';
import { createUserMarker } from '@/components/map/MapMarkers/createMapMarker';

export function useMapMarkers(
  map: mapboxgl.Map | null,
  users: UnifiedMapUser[],
  onMarkerClick: (user: UnifiedMapUser) => void
) {
  const markersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});

  useEffect(() => {
    if (!map) return;

    // Remove existing markers
    Object.values(markersRef.current).forEach(marker => marker.remove());
    markersRef.current = {};

    // Add new markers
    users.forEach(user => {
      if (!user.location) return;

      const marker = createUserMarker(
        user,
        map,
        () => onMarkerClick(user)
      );

      if (marker) {
        marker.addTo(map);
        markersRef.current[user.user_id] = marker;
      }
    });

    // Cleanup
    return () => {
      Object.values(markersRef.current).forEach(marker => marker.remove());
      markersRef.current = {};
    };
  }, [map, users, onMarkerClick]);

  return markersRef.current;
}
