
/**
 * Utilities for handling file uploads and profile photo updates
 */
import { supabase } from "@/integrations/supabase/client";

/**
 * Update a user's profile photo URL in the database
 * @param userId - The user ID
 * @param newUrl - The new photo URL
 * @returns A promise that resolves to a success object
 */
export async function updateProfilePhotoUrl(userId: string, newUrl: string) {
  console.log(`Updating user ${userId} with new photo URL: ${newUrl}`);

  try {
    const { error } = await supabase
      .from('profiles')
      .update({ avatar_url: newUrl })
      .eq('id', userId);

    if (error) {
      console.error('Error updating profile photo URL:', error);
      return { success: false, error };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error updating profile photo URL:', error);
    return { success: false, error };
  }
}

/**
 * Convert a data URL to a File object
 * @param dataUrl - The data URL
 * @param filename - The filename to use
 * @returns A File object
 */
function dataURLtoFile(dataUrl: string, filename: string): File {
  const arr = dataUrl.split(',');
  const mime = arr[0].match(/:(.*?);/)?.[1] || 'image/jpeg';
  const bstr = atob(arr[1]);
  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], filename, { type: mime });
}

/**
 * Upload an avatar from a data URL to Supabase storage
 * @param userId - The user ID
 * @param dataUrl - The data URL of the image
 * @returns A promise that resolves to an object with the URL and any error
 */
export async function uploadAvatarFromDataUrl(userId: string, dataUrl: string) {
  console.log(`Uploading avatar for user ${userId}`);

  try {
    // Validate the data URL
    if (!dataUrl.startsWith('data:image/')) {
      return { url: null, error: new Error('Invalid image data URL') };
    }

    // Generate a unique filename
    const fileExt = dataUrl.substring(dataUrl.indexOf('/') + 1, dataUrl.indexOf(';'));
    const filename = `${userId}-${Date.now()}.${fileExt || 'jpg'}`;

    // Convert data URL to File
    const file = dataURLtoFile(dataUrl, filename);
    console.log(`Converted data URL to file: ${filename}, size: ${file.size} bytes`);

    // Upload to Supabase Storage
    // First check if the avatars bucket exists, if not try to use the default public bucket
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();

    if (listError) {
      console.error('Error listing buckets:', listError);
      return { url: null, error: listError };
    }

    // Check which bucket to use
    const bucketName = buckets.find(b => b.name === 'avatars') ? 'avatars' : 'public';
    const filePath = bucketName === 'avatars' ? `public/${filename}` : filename;

    console.log(`Using bucket: ${bucketName}, path: ${filePath}`);

    // Upload the file
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true
      });

    if (error) {
      console.error('Error uploading to storage:', error);
      return { url: null, error };
    }

    // Get the public URL
    const { data: { publicUrl } } = supabase.storage
      .from(bucketName)
      .getPublicUrl(filePath);

    console.log(`Avatar uploaded successfully: ${publicUrl}`);

    // No need to update profile here, that will be done separately
    return { url: publicUrl, error: null };
  } catch (error) {
    console.error('Error uploading avatar:', error);
    return { url: null, error: error instanceof Error ? error : new Error('Unknown error') };
  }
}
