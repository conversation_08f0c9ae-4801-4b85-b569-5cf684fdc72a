
import React from 'react';
import { cn } from "@/lib/utils";
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';

interface ChatListItemProps {
  id: string;
  name: string;
  imageUrl?: string;
  lastMessage?: string;
  timestamp?: string;
  isSelected: boolean;
  onClick: () => void;
}

export function ChatListItem({ id, name, imageUrl, lastMessage, timestamp, isSelected, onClick }: ChatListItemProps) {
  const initials = name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .slice(0, 2);

  return (
    <div
      className={cn(
        "flex items-center gap-3 p-2 rounded-md cursor-pointer hover:bg-accent transition-colors mb-1",
        isSelected && "bg-accent"
      )}
      onClick={onClick}
    >
      <Avatar className="h-9 w-9">
        {imageUrl ? (
          <AvatarImage src={imageUrl} alt={name} />
        ) : (
          <AvatarFallback>{initials}</AvatarFallback>
        )}
      </Avatar>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium leading-none truncate">{name}</p>
        {lastMessage && (
          <p className="text-xs text-muted-foreground truncate mt-1">{lastMessage}</p>
        )}
      </div>
      {timestamp && (
        <div className="text-xs text-muted-foreground whitespace-nowrap">
          {timestamp}
        </div>
      )}
    </div>
  );
}
