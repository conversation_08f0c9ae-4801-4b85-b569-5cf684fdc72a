
import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useUserActivities } from "@/hooks/use-user-activities";
import { ActivityListItem } from "@/components/activity/ActivityListItem";
import { Ta<PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";

export interface ProfileActivitiesProps {
  userId?: string;
  profile?: any; // Add this prop to match usage in ProfilePage.tsx
  isLoading?: boolean;
}

export function ProfileActivities({ userId }: ProfileActivitiesProps) {
  const [activeTab, setActiveTab] = React.useState('hosted');
  
  const options = {
    filter: activeTab as 'hosting' | 'participating' | 'all'
  };

  const { data: activities, isLoading } = useUserActivities(options);
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Activities</CardTitle>
        <CardDescription>Activities you're hosting or participating in</CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="hosted" value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="mb-4">
            <TabsTrigger value="hosted">Hosting</TabsTrigger>
            <TabsTrigger value="participating">Participating</TabsTrigger>
          </TabsList>
          
          <TabsContent value="hosted" className="space-y-4">
            {isLoading ? (
              Array(3).fill(0).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-20" />
                </div>
              ))
            ) : activities && activities.length > 0 ? (
              activities.map(activity => (
                <ActivityListItem key={activity.id} activity={activity} />
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>You aren't hosting any activities yet.</p>
                <p className="text-sm">Create an activity to see it here!</p>
              </div>
            )}
          </TabsContent>
          
          <TabsContent value="participating" className="space-y-4">
            {isLoading ? (
              Array(3).fill(0).map((_, i) => (
                <div key={i} className="space-y-2">
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                  <Skeleton className="h-20" />
                </div>
              ))
            ) : activities && activities.length > 0 ? (
              activities.map(activity => (
                <ActivityListItem key={activity.id} activity={activity} />
              ))
            ) : (
              <div className="text-center py-8 text-gray-500">
                <p>You're not participating in any activities yet.</p>
                <p className="text-sm">Join activities to see them here!</p>
              </div>
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}
