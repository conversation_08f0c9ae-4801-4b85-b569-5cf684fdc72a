
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

interface QueueEntry {
  id: string;
  activity_id: string;
  status: 'pending' | 'confirmed' | 'cancelled';
  created_at: string;
}

serve(async (req) => {
  try {
    // Create a Supabase client with the Auth context of the logged in user
    const supabaseClient = createClient(
      // Supabase API URL - env var exposed by default when deployed
      Deno.env.get('SUPABASE_URL') ?? '',
      // Supabase API ANON KEY - env var exposed by default when deployed
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      // Create client with Auth context of the user that called the function
      {
        global: {
          headers: { Authorization: req.headers.get('Authorization')! },
        },
      }
    );
    
    // Parse the request body
    const { activity_id } = await req.json();
    
    if (!activity_id) {
      return new Response(JSON.stringify({ error: 'Activity ID is required' }), {
        headers: { 'Content-Type': 'application/json' },
        status: 400,
      });
    }
    
    // Get all queue entries for this activity
    const { data: entries, error: fetchError } = await supabaseClient
      .from('activity_queue')
      .select('*')
      .eq('activity_id', activity_id)
      .order('created_at', { ascending: true });
    
    if (fetchError) {
      throw fetchError;
    }
    
    if (!entries || entries.length === 0) {
      return new Response(JSON.stringify({ message: 'No queue entries found' }), {
        headers: { 'Content-Type': 'application/json' },
      });
    }
    
    // Group by status and sort
    const sortedEntries = [...entries].sort((a, b) => {
      // First by status priority
      const statusOrder = { 'confirmed': 1, 'pending': 2, 'cancelled': 3 };
      const statusDiff = statusOrder[a.status as keyof typeof statusOrder] - 
                        statusOrder[b.status as keyof typeof statusOrder];
      
      if (statusDiff !== 0) return statusDiff;
      
      // Then by creation time
      return new Date(a.created_at).getTime() - new Date(b.created_at).getTime();
    });
    
    // Assign position numbers
    const updatedEntries = sortedEntries.map((entry, index) => ({
      id: entry.id,
      position: entry.status !== 'cancelled' ? index + 1 : null
    }));
    
    // Update positions in database
    const { error: updateError } = await supabaseClient
      .from('activity_queue')
      .upsert(updatedEntries);
    
    if (updateError) {
      throw updateError;
    }
    
    return new Response(JSON.stringify({
      message: 'Queue positions updated successfully',
      updated: updatedEntries.length
    }), {
      headers: { 'Content-Type': 'application/json' },
    });
    
  } catch (error) {
    return new Response(JSON.stringify({ error: error.message }), {
      headers: { 'Content-Type': 'application/json' },
      status: 500,
    });
  }
})
