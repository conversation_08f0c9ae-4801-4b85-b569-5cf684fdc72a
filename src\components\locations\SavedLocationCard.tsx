
import React, { useRef, useEffect } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { SavedLocation } from '@/hooks/use-saved-locations';
import { MapPin, Edit, Trash2, Share2, Navigation } from 'lucide-react';
import { format } from 'date-fns';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { useToast } from '@/hooks/use-toast';

interface SavedLocationCardProps {
  location: SavedLocation;
  onNavigate?: (location: SavedLocation) => void;
  onEdit?: (location: SavedLocation) => void;
  onDelete?: (location: SavedLocation) => void;
  onShare?: (location: SavedLocation) => void;
  showMap?: boolean;
}

export function SavedLocationCard({ 
  location, 
  onNavigate,
  onEdit,
  onDelete,
  onShare,
  showMap = false
}: SavedLocationCardProps) {
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const mapRef = useRef<mapboxgl.Map | null>(null);
  const { toast } = useToast();

  // Initialize mini map if showMap is true
  useEffect(() => {
    if (!showMap || !mapContainerRef.current || mapRef.current) return;
    
    const mapboxToken = import.meta.env.VITE_MAPBOX_TOKEN;
    if (!mapboxToken) {
      console.error("Mapbox token is not available");
      return;
    }

    mapboxgl.accessToken = mapboxToken;
    
    try {
      const map = new mapboxgl.Map({
        container: mapContainerRef.current,
        style: 'mapbox://styles/mapbox/streets-v11',
        center: [location.location.x, location.location.y],
        zoom: 13,
        interactive: false // Disable interactivity for preview
      });

      // Add marker
      new mapboxgl.Marker({ color: '#0EA5E9' })
        .setLngLat([location.location.x, location.location.y])
        .addTo(map);

      mapRef.current = map;
    } catch (error) {
      console.error("Error initializing map:", error);
    }

    return () => {
      mapRef.current?.remove();
      mapRef.current = null;
    };
  }, [location.location, showMap]);

  const handleNavigate = () => {
    if (onNavigate) {
      onNavigate(location);
    } else {
      toast({
        title: "Navigation feature",
        description: "This would navigate to the location on the map"
      });
    }
  };

  return (
    <Card className="hover:border-primary transition-colors">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4 text-primary flex-shrink-0" />
              <h3 className="font-medium truncate">{location.name}</h3>
            </div>
            
            {location.description && (
              <p className="text-sm text-muted-foreground mt-1 line-clamp-2">
                {location.description}
              </p>
            )}
            
            <div className="flex items-center gap-2 mt-2 text-xs text-muted-foreground">
              <span>Added {format(new Date(location.created_at), 'MMM d, yyyy')}</span>
              {location.category && (
                <>
                  <span>•</span>
                  <span>{location.category}</span>
                </>
              )}
            </div>
          </div>

          <div className="flex items-center gap-1 ml-4">
            <Button variant="ghost" size="sm" onClick={() => onShare?.(location)}>
              <Share2 className="h-4 w-4" />
              <span className="sr-only">Share</span>
            </Button>
            <Button variant="ghost" size="sm" onClick={() => onEdit?.(location)}>
              <Edit className="h-4 w-4" />
              <span className="sr-only">Edit</span>
            </Button>
            <Button 
              variant="ghost" 
              size="sm" 
              className="text-destructive hover:text-destructive"
              onClick={() => onDelete?.(location)}
            >
              <Trash2 className="h-4 w-4" />
              <span className="sr-only">Delete</span>
            </Button>
          </div>
        </div>

        {showMap && (
          <div className="mt-3">
            <div 
              ref={mapContainerRef} 
              className="w-full h-[120px] rounded-md overflow-hidden border border-border mt-2"
            />
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-2 w-full"
              onClick={handleNavigate}
            >
              <Navigation className="h-3 w-3 mr-1" />
              Navigate to this location
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
