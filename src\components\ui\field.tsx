
import React from "react";
import { cva, type VariantProps } from "class-variance-authority";
import {
  FieldError as Aria<PERSON>ieldError,
  FieldErrorProps as AriaFieldErrorProps,
  composeRenderProps,
  Text,
  Label as AriaLabel,
} from "react-aria-components";

import { cn } from "@/lib/utils";

export const fieldGroupVariants = cva(
  "flex w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background",
  {
    variants: {
      variant: {
        default: "focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2",
        ghost: "border-none bg-transparent focus-within:ring-0 focus-within:ring-offset-0",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

// Updated Label component to use AriaLabel instead of Text
export function Label({ className, ...props }: React.HTMLAttributes<HTMLLabelElement>) {
  return (
    <AriaLabel
      className={cn("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70", className)}
      {...props}
    />
  );
}

export function FieldError(props: AriaFieldErrorProps) {
  return (
    <AriaFieldError
      {...props}
      className={composeRenderProps(
        props.className,
        (className) =>
          cn("text-sm font-medium text-destructive", className)
      )}
    />
  );
}
