
import { supabase } from '@/integrations/supabase/client';

/**
 * Verifies if the onboarding status is complete using multiple approaches
 * for increased reliability.
 *
 * @param userId The user ID to check
 * @returns Promise<boolean> True if onboarding is complete
 */
export async function verifyOnboardingStatus(userId: string): Promise<boolean> {
  try {
    // Check if onboarding_completed flag is set directly in the database
    const { data, error } = await supabase
      .from('profiles')
      .select('onboarding_completed')
      .eq('id', userId)
      .single();

    if (error) {
      console.error('Error checking onboarding status:', error);
      return false;
    }

    // If the flag is explicitly set to true, return true
    if (data?.onboarding_completed === true) {
      return true;
    }

    // As a fallback, use the RPC function to check
    try {
      // Try the new verify_onboarding_status function first
      const { data: verifyData, error: verifyError } = await supabase.rpc(
        'verify_onboarding_status',
        { user_id: userId }
      );

      if (!verifyError && verifyData === true) {
        return true;
      }

      // If that fails, try the old check_onboarding_completed function
      if (verifyError) {
        console.log('Trying fallback function due to error:', verifyError);

        try {
          const { data: rpcData, error: rpcError } = await supabase.rpc(
            'check_onboarding_completed',
            { user_id: userId }
          );

          if (!rpcError && rpcData === true) {
            return true;
          }
        } catch (rpcError) {
          console.error('Error in fallback RPC call:', rpcError);
        }
      }
    } catch (rpcError) {
      console.error('Error in RPC call:', rpcError);
    }

    return false;
  } catch (error) {
    console.error('Error in verifyOnboardingStatus:', error);
    return false;
  }
}

/**
 * Force marks the onboarding as completed for a user
 *
 * @param userId The user ID to mark as completed
 * @returns Promise<boolean> True if successful
 */
export async function forceMarkOnboardingCompleted(userId: string): Promise<boolean> {
  try {
    console.log('Forcing onboarding completed for user:', userId);

    // Use direct update for reliability
    const { error } = await supabase
      .from('profiles')
      .update({
        onboarding_completed: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (error) {
      console.error('Error updating onboarding status:', error);
      return false;
    }

    console.log('Successfully marked onboarding as completed for user:', userId);
    return true;
  } catch (error) {
    console.error('Error in forceMarkOnboardingCompleted:', error);
    return false;
  }
}
