import React, { useState } from 'react';
import { YearFirstDatePicker } from '@/components/ui/year-first-date-picker';
import { MainLayout } from '@/components/layouts/MainLayout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

export default function DatePickerTest() {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined);

  const handleDateChange = (date: Date | undefined) => {
    setSelectedDate(date);
    console.log('Selected date:', date);
  };

  return (
    <MainLayout title="Date Picker Test">
      <div className="container mx-auto py-8 px-4">
        <h1 className="text-2xl font-bold mb-6">Year-First Date Picker Test</h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <Card>
            <CardHeader>
              <CardTitle>Year-First Date Picker</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <YearFirstDatePicker
                value={selectedDate}
                onChange={handleDateChange}
                label="Select your birthday"
                description="This date picker starts with year selection, then month, then date"
                placeholder="Click to select a date"
                minAge={13}
                maxAge={100}
                required={true}
              />

              <div className="mt-4 p-4 bg-gray-50 rounded-md">
                <h3 className="text-sm font-medium mb-2">Selected Date:</h3>
                {selectedDate ? (
                  <p className="text-sm">{selectedDate.toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}</p>
                ) : (
                  <p className="text-sm text-gray-500">No date selected</p>
                )}
              </div>

              <Button
                variant="outline"
                onClick={() => setSelectedDate(undefined)}
                className="mt-2"
              >
                Clear Date
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Instructions</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p>This page demonstrates the new Year-First Date Picker component.</p>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Features:</h3>
                  <ul className="list-disc pl-5 text-sm space-y-1">
                    <li>Starts with year selection</li>
                    <li>Then proceeds to month selection</li>
                    <li>Finally shows date selection</li>
                    <li>Enforces age restrictions (13-100 years old)</li>
                    <li>Provides clear navigation between views</li>
                    <li>Shows selected date in a readable format</li>
                  </ul>
                </div>

                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Usage:</h3>
                  <ol className="list-decimal pl-5 text-sm space-y-1">
                    <li>Click on the date picker input</li>
                    <li>Select a year from the grid (use arrows to navigate decades)</li>
                    <li>Select a month from the grid</li>
                    <li>Select a day from the calendar</li>
                    <li>The selected date will appear below the picker</li>
                  </ol>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  );
}
