-- Add missing columns to chat_conversations table
-- This migration adds the title and activity_id columns that are expected by the activity-chat integration

-- Add title column to chat_conversations if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'chat_conversations' AND column_name = 'title') THEN
        ALTER TABLE chat_conversations ADD COLUMN title TEXT;
    END IF;
END $$;

-- Add activity_id column to chat_conversations if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'chat_conversations' AND column_name = 'activity_id') THEN
        ALTER TABLE chat_conversations ADD COLUMN activity_id UUID REFERENCES activities(id);
    END IF;
END $$;

-- Create index on activity_id for better performance
CREATE INDEX IF NOT EXISTS idx_chat_conversations_activity_id ON chat_conversations(activity_id);

-- Update the Supabase types by regenerating them
-- Note: Run `npx supabase gen types typescript --local` after applying this migration
