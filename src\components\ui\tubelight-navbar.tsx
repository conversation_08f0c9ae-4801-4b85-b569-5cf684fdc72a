
import React, { useEffect, useState } from "react"
import { motion } from "framer-motion"
import { Link, useLocation } from "react-router-dom"
import { LucideIcon } from "lucide-react"
import { cn } from "@/lib/utils"
import { useIsMobile } from "@/hooks/use-mobile"

interface NavItem {
  name: string
  url: string
  icon: LucideIcon
}

interface NavBarProps {
  items: NavItem[]
  className?: string
}

export function NavBar({ items, className }: NavBarProps) {
  const location = useLocation();
  const [activeTab, setActiveTab] = useState("");
  const isMobile = useIsMobile();

  useEffect(() => {
    const currentPath = location.pathname;
    const matchingItem = items.find(item => item.url === currentPath);
    if (matchingItem) {
      setActiveTab(matchingItem.name);
    } else {
      setActiveTab(items[0]?.name || "");
    }
  }, [location, items]);

  return (
    <nav className={cn("flex items-center", className)}>
      <div className="flex items-center gap-1 sm:gap-2 bg-background/5 border border-border backdrop-blur-lg py-1 px-1 rounded-full shadow-lg">
        {items.map((item) => {
          const Icon = item.icon
          const isActive = activeTab === item.name

          return (
            <Link
              key={item.name}
              to={item.url}
              onClick={() => setActiveTab(item.name)}
              className={cn(
                "relative cursor-pointer select-none transition-all duration-200",
                "flex items-center justify-center gap-2",
                "px-3 sm:px-4 py-2 rounded-full",
                "text-sm font-medium",
                "active:scale-95",
                isActive 
                  ? "text-primary bg-muted" 
                  : "text-foreground/70 hover:text-primary hover:bg-muted/50",
              )}
            >
              <Icon 
                className={cn(
                  "flex-shrink-0 transition-all duration-200",
                  isMobile ? "h-5 w-5" : "h-4 w-4"
                )} 
                strokeWidth={2.5} 
              />
              <span className={cn(
                "transition-all duration-200",
                isMobile ? "hidden" : "inline-block"
              )}>
                {item.name}
              </span>
              {isActive && (
                <motion.div
                  layoutId="lamp"
                  className="absolute inset-0 w-full bg-primary/5 rounded-full -z-10"
                  initial={false}
                  transition={{
                    type: "spring",
                    stiffness: 300,
                    damping: 30,
                  }}
                >
                  <div className="absolute -top-2 left-1/2 -translate-x-1/2 w-8 h-1 bg-primary rounded-t-full opacity-75">
                    <div className="absolute w-12 h-6 bg-primary/20 rounded-full blur-md -top-2 -left-2" />
                    <div className="absolute w-8 h-6 bg-primary/20 rounded-full blur-md -top-1" />
                    <div className="absolute w-4 h-4 bg-primary/20 rounded-full blur-sm top-0 left-2" />
                  </div>
                </motion.div>
              )}
            </Link>
          )
        })}
      </div>
    </nav>
  )
}
