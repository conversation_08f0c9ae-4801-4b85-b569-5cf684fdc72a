
import React from 'react';
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Filter } from "lucide-react";

interface MapExploreFiltersProps {
  className?: string;
}

export default function MapExploreFilters({ className }: MapExploreFiltersProps) {
  return (
    <Button variant="outline" size="sm" className={cn("bg-background/80 backdrop-blur-sm", className)}>
      <Filter className="h-4 w-4 mr-2" />
      Filter
    </Button>
  );
}
