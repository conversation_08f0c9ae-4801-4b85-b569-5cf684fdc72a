
import * as z from "zod";

export const activityCreationSchema = z.object({
  // Basic Information
  title: z.string().min(3, "Title must be at least 3 characters").max(100, "Title must be less than 100 characters"),
  description: z.string().min(10, "Description must be at least 10 characters").max(1000, "Description must be less than 1000 characters"),
  category_id: z.string().optional().nullable(),
  
  // Location Information
  location: z.object({
    x: z.number(),
    y: z.number(),
  }),
  address: z.string().optional().nullable(),
  
  // Timing Information
  start_time: z.string(),
  end_time: z.string().optional().nullable(),
  
  // Participation Details
  max_participants: z.number().int().positive().optional().nullable(),
  
  // Payment Information
  is_paid: z.boolean().default(false),
  price: z.number().min(0).optional().nullable(),
  early_bird_price: z.number().min(0).optional().nullable(),
  group_discount_threshold: z.number().int().positive().optional().nullable(),
  group_discount_percentage: z.number().min(0).max(100).optional().nullable(),
  
  // Queue Configuration
  queue_type: z.enum(["fifo", "priority"]).default("fifo"),
  allow_waitlist: z.boolean().default(true),
  
  // Media
  media_urls: z.array(z.string()).optional().default([]),
  
  // Visibility Settings
  visibility: z.enum(["public", "private", "unlisted"]).default("public"),
});

export type ActivityCreationValues = z.infer<typeof activityCreationSchema>;
