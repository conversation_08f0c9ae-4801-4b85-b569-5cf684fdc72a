
# BuddySurf: Developer Documentation

## Architecture Overview

- **Frontend**: React + TypeScript + Tailwind CSS + Shadcn UI
- **Backend**: Supabase (Authentication, Database, Storage, Realtime)
- **Maps**: Mapbox GL JS with 3D buildings and custom styling
- **Payments**: Stripe integration (upcoming)

## Core Components

### Map System

- **MapComponent**: The main map renderer using Mapbox GL
- **MapRightSidebar**: Tabbed sidebar displaying nearby users, activities, and hiring options
- **EnhancedMarkerManager**: Manages user and activity markers on the map
- **MapControls**: Components for controlling map appearance and behavior

### User System

- **Authentication**: Email/password login through Supabase
- **Profiles**: User profiles with avatars, bios, and social connections
- **Location Sharing**: Real-time location updates using Supabase Realtime

### Activity System

- **Activity Creation**: Form for creating new activities
- **Activity Display**: Cards for viewing activity details
- **Queue Management**: System for joining and managing activity participants

## Key Features

### MeetMap: Real-time Locations

- **Location Markers:** All locations on the MeetMap update in real time via Supabase Realtime.
- **Location Sharing:** Users must be authenticated to update/save their own location.
- **Privacy:** Each user can remove or update their own marker at any time.
- **Implementation:** Uses authenticated upserts into `user_locations`.
- **Realtime Updates:** All location changes are broadcast to all connected users in realtime.

### User Profiles

- **Profile Data:** User profiles are stored in the `profiles` table in Supabase.
- **Avatar Storage:** User avatars are stored in the `avatars` storage bucket.
- **Profile Sections:** The profile page includes tabs for About, Gallery, Services, and Meetups.

### Activity Management

- **Creation:** Users can create activities with location, time, and details
- **Discovery:** Activities appear on the map and in the Plans tab
- **Participation:** Users can join activities through the queue system
- **Categories:** Activities are organized by category type

## Integration Points

### Map + Activities Integration

- Activities appear as markers on the map
- Clicking an activity marker shows details
- Activities can be filtered by category, date, etc.
- Activity locations are stored as coordinates (x, y)

### Map + User Integration

- Users appear as markers on the map with avatars
- User locations update in real-time
- Distance between users is calculated dynamically
- Online status is displayed on markers

### Activities + User Integration

- Users can join activities through the queue system
- Activity host information is displayed on activity cards
- Users can chat with activity participants (coming soon)

## Database Structure

### Key Tables

- `profiles`: User profile information
- `user_locations`: Real-time user location data
- `activities`: Activity details including location, time, etc.
- `activity_participants`: Join table for users participating in activities
- `activity_categories`: Categories for activities

## Extending

- Add new pages by creating them in `src/pages` and adding to `App.tsx`
- Use `src/components/navigation/AppSidebar.tsx` for quick nav changes
- Create new components in the appropriate subdirectory of `src/components`

## Best Practices

- Use Tailwind CSS for styling
- Create focused, single-responsibility components
- Use TypeScript interfaces for type safety
- Implement responsive design for all components
- Use Supabase Realtime for real-time features
