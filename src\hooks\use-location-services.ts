
import { useState, useCallback, useEffect } from 'react';
import { useToast } from "@/hooks/use-toast";

interface LocationServiceResult {
  coords: {lng: number, lat: number} | null;
  error: string | null;
  isRequesting: boolean;
  isLoading: boolean; // Added missing property
  getPosition: () => Promise<{lng: number, lat: number}>;
  accuracy: number | null;
  permissionStatus: PermissionState | null;
  lastUpdate: Date | null;
}

export function useLocationServices(): LocationServiceResult {
  const [coords, setCoords] = useState<{lng: number, lat: number} | null>(null);
  const [accuracy, setAccuracy] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isRequesting, setIsRequesting] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [permissionStatus, setPermissionStatus] = useState<PermissionState | null>(null);
  const { toast } = useToast();
  
  // Check permission status on mount
  useEffect(() => {
    const checkPermission = async () => {
      try {
        const status = await navigator.permissions.query({ name: 'geolocation' });
        setPermissionStatus(status.state);
        
        status.addEventListener('change', () => {
          setPermissionStatus(status.state);
          if (status.state === 'granted') {
            toast({
              title: "Location Access Granted",
              description: "Your location will now be shown on the map",
            });
          }
        });
      } catch (e) {
        console.error('Error checking location permission:', e);
      }
    };
    
    checkPermission();
  }, []);
  
  const getPosition = useCallback((): Promise<{lng: number, lat: number}> => {
    return new Promise((resolve, reject) => {
      if (!navigator.geolocation) {
        const errorMessage = "Geolocation is not supported by your browser";
        setError(errorMessage);
        toast({
          title: "Location Error",
          description: errorMessage,
          variant: "destructive"
        });
        reject(new Error(errorMessage));
        return;
      }
      
      setIsRequesting(true);
      setError(null);
      
      navigator.geolocation.getCurrentPosition(
        (position) => {
          const newCoords = {
            lng: position.coords.longitude,
            lat: position.coords.latitude
          };
          setCoords(newCoords);
          setAccuracy(position.coords.accuracy);
          setLastUpdate(new Date());
          setIsRequesting(false);
          resolve(newCoords);
        },
        (error) => {
          let errorMessage = "Failed to get your location";
          switch (error.code) {
            case error.PERMISSION_DENIED:
              errorMessage = "Location permission denied. Please enable location services in your browser settings.";
              break;
            case error.POSITION_UNAVAILABLE:
              errorMessage = "Location information is unavailable. Please try again.";
              break;
            case error.TIMEOUT:
              errorMessage = "Location request timed out. Please check your connection and try again.";
              break;
          }
          setError(errorMessage);
          setIsRequesting(false);
          toast({
            title: "Location Error",
            description: errorMessage,
            variant: "destructive"
          });
          reject(new Error(errorMessage));
        },
        { 
          enableHighAccuracy: true, 
          timeout: 10000, 
          maximumAge: 0 
        }
      );
    });
  }, []);
  
  return {
    coords,
    error,
    isRequesting,
    isLoading: isRequesting, // Add isLoading property
    getPosition,
    accuracy,
    permissionStatus,
    lastUpdate
  };
}
