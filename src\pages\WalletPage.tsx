
import React from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useTransactions } from '@/hooks/use-transactions';
import { TransactionHistory } from '@/components/wallet/TransactionHistory';
import { WalletBalance } from '@/components/wallet/WalletBalance';
import { PaymentMethods } from '@/components/wallet/PaymentMethods';
import { Button } from '@/components/ui/button';
import { UserCircle, CreditCard, ReceiptText, Plus, Loader2 } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { useCustomerPortal } from '@/hooks/use-customer-portal';
import { AddFundsDialog } from '@/components/wallet/AddFundsDialog';

export default function WalletPage() {
  const navigate = useNavigate();
  const { mutate: openCustomerPortal, isPending } = useCustomerPortal();

  const handleOpenCustomerPortal = () => {
    openCustomerPortal();
  };

  return (
    <MainLayout>
      <div className="container py-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-3xl font-bold">Wallet</h1>
          <AddFundsDialog />
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <WalletBalance />
          
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
              <CardDescription>Manage your wallet and payments</CardDescription>
            </CardHeader>
            <CardContent className="grid gap-4">
              <Button variant="outline" className="justify-start" onClick={() => navigate('/profile')}>
                <UserCircle className="mr-2 h-4 w-4" />
                Your Profile
              </Button>
              <Button variant="outline" className="justify-start" onClick={() => navigate('/wallet?tab=payment-methods')}>
                <CreditCard className="mr-2 h-4 w-4" />
                Payment Methods
              </Button>
              <Button variant="outline" className="justify-start" onClick={() => navigate('/wallet?tab=transactions')}>
                <ReceiptText className="mr-2 h-4 w-4" />
                Transaction History
              </Button>
            </CardContent>
          </Card>
        </div>

        <Tabs defaultValue="transactions">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="transactions">Transaction History</TabsTrigger>
            <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
            <TabsTrigger value="subscriptions">Subscription</TabsTrigger>
          </TabsList>
          <TabsContent value="transactions">
            <Card>
              <CardHeader>
                <CardTitle>Transaction History</CardTitle>
                <CardDescription>View your recent transactions and payments</CardDescription>
              </CardHeader>
              <CardContent>
                <TransactionHistory />
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="payment-methods">
            <Card>
              <CardHeader>
                <CardTitle>Payment Methods</CardTitle>
                <CardDescription>Manage your payment methods</CardDescription>
              </CardHeader>
              <CardContent>
                <PaymentMethods />
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="subscriptions">
            <Card>
              <CardHeader>
                <CardTitle>Subscription</CardTitle>
                <CardDescription>Manage your subscription plan</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <p>Manage your subscription plans and payment methods.</p>
                  <Button
                    onClick={handleOpenCustomerPortal}
                    disabled={isPending}
                  >
                    {isPending ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        Loading...
                      </>
                    ) : (
                      'Manage Subscription'
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
