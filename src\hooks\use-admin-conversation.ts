
import { useQuery, useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { AdminConversation, AdminResponse } from '@/types/chat';
import { useProfile } from '@/hooks/use-profile';

export function useAdminConversation(): AdminResponse {
  const { user } = useAuth();
  const { data: profile } = useProfile(user?.id);

  // Check if user is an admin
  const isAdmin = profile?.is_admin === true;

  const query = useQuery({
    queryKey: ['admin-conversation'],
    queryFn: async (): Promise<AdminConversation | null> => {
      if (!user?.id) return null;

      // Find the admin conversation
      const { data, error } = await supabase
        .from('chat_conversations')
        .select('*')
        .eq('is_admin_conversation', true)
        .single();

      if (error) {
        if (error.code === 'PGRST116') {
          return null; // No admin conversation found
        }
        throw error;
      }

      return data as AdminConversation;
    },
    enabled: !!user?.id
  });

  // Add support for sending admin messages
  const sendAdminMessage = useMutation({
    mutationFn: async (message: string): Promise<any> => {
      if (!user?.id || !query.data?.id) {
        throw new Error('Admin conversation not found or user not logged in');
      }

      if (!isAdmin) {
        throw new Error('Only admins can send admin messages');
      }

      const { error, data } = await supabase
        .from('messages')
        .insert({
          conversation_id: query.data.id,
          sender_id: user.id,
          content: message,
          is_admin_message: true,
          message_type: 'text'
        })
        .select();

      if (error) throw error;
      return data;
    }
  });

  return {
    adminConversation: query.data,
    isLoading: query.isLoading,
    isError: query.isError,
    error: query.error,
    refetch: query.refetch,
    isAdmin,
    sendAdminMessage: {
      mutateAsync: sendAdminMessage.mutateAsync,
      isPending: sendAdminMessage.isPending
    }
  };
}
