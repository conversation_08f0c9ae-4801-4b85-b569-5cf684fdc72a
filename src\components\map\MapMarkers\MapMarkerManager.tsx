
import React, { useEffect, useRef, useMemo } from 'react';
import { createRoot } from 'react-dom/client';
import mapboxgl from 'mapbox-gl';
import { UnifiedMapUser } from '@/types/map';
import { Activity } from '@/types/activity';
import { createUserMarker, createActivityMarker } from './createMapMarker';
import { useToast } from '@/hooks/use-toast';

interface MapMarkerManagerProps {
  map: mapboxgl.Map | null;
  users: UnifiedMapUser[];
  activities: Activity[];
  selectedUser: UnifiedMapUser | null;
  selectedActivity: Activity | null;
  recentActivities: Record<string, boolean>;
  onUserClick: (user: UnifiedMapUser) => void;
  onActivityClick: (activity: Activity) => void;
  showUsers: boolean;
  showActivities: boolean;
}

export function MapMarkerManager({
  map,
  users,
  activities,
  selectedUser,
  selectedActivity,
  recentActivities,
  onUserClick,
  onActivityClick,
  showUsers,
  showActivities
}: MapMarkerManagerProps) {
  const userMarkersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});
  const activityMarkersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});
  const { toast } = useToast();

  // Handle user markers
  useEffect(() => {
    if (!map || !showUsers) {
      // Clean up markers when they shouldn't be shown
      if (!showUsers) {
        Object.values(userMarkersRef.current).forEach(marker => marker.remove());
        userMarkersRef.current = {};
      }
      return;
    }

    console.log("Updating user markers:", users.length);

    // Remove markers that don't exist in users anymore
    Object.keys(userMarkersRef.current).forEach(userId => {
      if (!users.find(u => u.user_id === userId)) {
        userMarkersRef.current[userId].remove();
        delete userMarkersRef.current[userId];
      }
    });

    // Add or update markers
    users.forEach(user => {
      if (!user.location) return;

      // Remove existing marker if updating
      if (userMarkersRef.current[user.user_id]) {
        userMarkersRef.current[user.user_id].remove();
      }

      // Create and add new marker
      const marker = createUserMarker(
        user,
        map,
        () => onUserClick(user)
      );

      if (marker) {
        marker.addTo(map);
        userMarkersRef.current[user.user_id] = marker;
      }
    });

    return () => {
      // Clean up on unmount
      Object.values(userMarkersRef.current).forEach(marker => marker.remove());
      userMarkersRef.current = {};
    };
  }, [map, users, showUsers, onUserClick]);

  // Handle activity markers with animations for new and selected activities
  useEffect(() => {
    if (!map || !showActivities) {
      // Clean up markers when they shouldn't be shown
      if (!showActivities) {
        Object.values(activityMarkersRef.current).forEach(marker => marker.remove());
        activityMarkersRef.current = {};
      }
      return;
    }

    console.log("Updating activity markers:", activities.length);

    // Remove markers that don't exist in activities anymore
    Object.keys(activityMarkersRef.current).forEach(activityId => {
      if (!activities.find(a => a.id === activityId)) {
        activityMarkersRef.current[activityId].remove();
        delete activityMarkersRef.current[activityId];
      }
    });

    // Add or update markers
    activities.forEach(activity => {
      if (!activity.location) return;

      // Remove existing marker if updating
      if (activityMarkersRef.current[activity.id]) {
        activityMarkersRef.current[activity.id].remove();
      }

      const isNew = recentActivities[activity.id] || false;
      const isSelected = selectedActivity?.id === activity.id;

      // Create and add new marker
      const marker = createActivityMarker(
        activity,
        map,
        () => onActivityClick(activity),
        isNew,
        isSelected
      );

      if (marker) {
        marker.addTo(map);
        activityMarkersRef.current[activity.id] = marker;

        // If this is a new activity, show a notification
        if (isNew && !activityMarkersRef.current[activity.id]) {
          toast({
            title: "New Activity",
            description: `${activity.title} was added to the map`,
          });
        }
      }
    });

    return () => {
      // Clean up on unmount
      Object.values(activityMarkersRef.current).forEach(marker => marker.remove());
      activityMarkersRef.current = {};
    };
  }, [map, activities, selectedActivity, recentActivities, showActivities, onActivityClick, toast]);

  return null; // Render nothing, as this is just a manager
}
