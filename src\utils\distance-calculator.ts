
/**
 * Calculates the Haversine distance between two geographic coordinates.
 * @param lat1 Latitude of the first point in decimal degrees
 * @param lon1 Longitude of the first point in decimal degrees
 * @param lat2 Latitude of the second point in decimal degrees
 * @param lon2 Longitude of the second point in decimal degrees
 * @returns Distance in miles
 */
export function calculateDistanceInMiles(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const toRadians = (degrees: number) => degrees * (Math.PI / 180);
  
  // Earth's radius in miles
  const earthRadiusMiles = 3958.8;
  
  const latDiffRad = toRadians(lat2 - lat1);
  const lonDiffRad = toRadians(lon2 - lon1);
  
  const a = 
    Math.sin(latDiffRad/2) * Math.sin(latDiffRad/2) +
    Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) * 
    Math.sin(lonDiffRad/2) * Math.sin(lonDiffRad/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = earthRadiusMiles * c;
  
  return distance;
}

/**
 * Formats a distance for display
 * @param distance Distance in miles
 * @returns Formatted distance string
 */
export function formatDistance(distance: number): string {
  if (distance < 0.1) {
    return 'Nearby';
  } else if (distance < 1) {
    // Format to 1 decimal place if less than a mile
    return `${(distance).toFixed(1)} miles`;
  } else {
    // Format to whole number if more than a mile
    return `${Math.round(distance)} miles`;
  }
}

/**
 * Creates a human-readable relative time string
 * @param date Date to format
 * @returns Formatted relative time string
 */
export function formatLastSeen(date: Date | string): string {
  const now = new Date();
  const lastSeen = typeof date === 'string' ? new Date(date) : date;
  const diffInSeconds = Math.floor((now.getTime() - lastSeen.getTime()) / 1000);
  
  if (diffInSeconds < 60) {
    return 'Now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d ago`;
  } else {
    // Format as MM/DD/YY for older dates
    return `${lastSeen.getMonth() + 1}/${lastSeen.getDate()}/${lastSeen.getFullYear().toString().slice(2)}`;
  }
}
