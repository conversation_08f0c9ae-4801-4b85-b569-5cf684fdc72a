
import { useState, useEffect, createContext, useContext, ReactNode } from "react";
import { supabase } from "@/integrations/supabase/client";
import { Session, User } from "@supabase/supabase-js";
import { useToast } from "@/hooks/use-toast";
import { Profile, UserProfile } from "@/hooks/use-profile";

type AuthContextType = {
  session: Session | null;
  user: User | null;
  profile: UserProfile | null;
  signIn: (email: string, password: string) => Promise<{
    error: Error | null;
  }>;
  signUp: (email: string, password: string, userData: {
    first_name?: string;
    last_name?: string;
    username?: string;
  }) => Promise<{
    error: Error | null;
  }>;
  signOut: () => Promise<void>;
  isLoading: boolean;
  refreshProfile: () => Promise<void>; // New function to refresh profile data
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      (event, sessionData) => {
        setSession(sessionData);
        setUser(sessionData?.user ?? null);
        
        if (sessionData?.user) {
          setTimeout(() => {
            fetchProfile(sessionData.user.id);
          }, 0);
        } else {
          setProfile(null);
        }
      }
    );

    supabase.auth.getSession().then(({ data: { session: sessionData } }) => {
      setSession(sessionData);
      setUser(sessionData?.user ?? null);
      
      if (sessionData?.user) {
        fetchProfile(sessionData.user.id);
      }
      
      setIsLoading(false);
    });

    return () => {
      subscription.unsubscribe();
    };
  }, []);

  const fetchProfile = async (userId: string) => {
    try {
      console.log(`Fetching profile for user ${userId}...`);
      
      const { data, error } = await supabase
        .from("profiles")
        .select("*")
        .eq("id", userId)
        .maybeSingle();
      
      if (error) {
        console.error("Error fetching profile:", error);
        return;
      }
      
      if (data) {
        // Log the raw profile data from database
        console.log("Raw profile data from database:", data);
        
        // Handle location data safely with type checking
        let locationData = undefined;
        if (data.location && typeof data.location === 'object') {
          const loc = data.location as any;
          locationData = {
            x: Number(loc.x ?? loc[0] ?? 0),
            y: Number(loc.y ?? loc[1] ?? 0)
          };
        }
        
        // Handle default_location data safely with type checking
        let defaultLocationData = undefined;
        if (data.default_location && typeof data.default_location === 'object') {
          const loc = data.default_location as any;
          defaultLocationData = {
            x: Number(loc.x ?? loc[0] ?? 0),
            y: Number(loc.y ?? loc[1] ?? 0)
          };
        } else {
          defaultLocationData = null; // Explicitly set to null if not available
        }
        
        // Parse favorite_locations from JSON if it exists
        let favoriteLocations = undefined;
        if (data.favorite_locations) {
          try {
            // If it's already an array, use it; otherwise parse it
            favoriteLocations = Array.isArray(data.favorite_locations) 
              ? data.favorite_locations
              : typeof data.favorite_locations === 'string' 
                ? JSON.parse(data.favorite_locations) 
                : data.favorite_locations;
          } catch (e) {
            console.error('Error parsing favorite_locations:', e);
            favoriteLocations = [];
          }
        }
        
        // Create profile object with compatible types
        const formattedProfile: UserProfile = {
          ...data,
          location: locationData,
          default_location: defaultLocationData,
          favorite_locations: favoriteLocations as UserProfile['favorite_locations'],
          onboarding_completed: data.onboarding_completed === true
        };
        
        console.log("Formatted profile with onboarding_completed:", formattedProfile.onboarding_completed);
        
        setProfile(formattedProfile);
      }
    } catch (error) {
      console.error("Error in fetchProfile:", error);
    }
  };

  // New function to manually refresh profile data
  const refreshProfile = async () => {
    if (user) {
      console.log("Manually refreshing profile data...");
      await fetchProfile(user.id);
      console.log("Profile refresh complete");
    } else {
      console.warn("Cannot refresh profile: No logged-in user");
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        toast({
          title: "Login failed",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      toast({
        title: "Login successful",
        description: "Welcome back!",
      });
      return { error: null };
    } catch (error) {
      toast({
        title: "Login failed",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
      return { error: error as Error };
    }
  };

  const signUp = async (
    email: string, 
    password: string,
    userData: {
      first_name?: string;
      last_name?: string;
      username?: string;
    }
  ) => {
    try {
      const { error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      });

      if (error) {
        toast({
          title: "Signup failed",
          description: error.message,
          variant: "destructive",
        });
        return { error };
      }

      toast({
        title: "Signup successful",
        description: "Welcome to MeetSphere! Please check your email to confirm your account.",
      });
      return { error: null };
    } catch (error) {
      toast({
        title: "Signup failed",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
      return { error: error as Error };
    }
  };

  const signOut = async () => {
    await supabase.auth.signOut();
    toast({
      title: "Logged out",
      description: "You have been logged out",
    });
  };

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        profile,
        signIn,
        signUp,
        signOut,
        isLoading,
        refreshProfile,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
