
import React from 'react';
import { Activity } from '@/types/activity';

interface MapCoreProps {
  mapCenter?: { lat: number, lng: number };
  activities: Activity[];
  userLocations: any[];
  activeTab: string;
}

const MapCore: React.FC<MapCoreProps> = ({ 
  mapCenter, 
  activities, 
  userLocations,
  activeTab 
}) => {
  // This is a stub component - your actual MapCore implementation goes here
  return (
    <div className="w-full h-full bg-gray-100 relative">
      <div className="absolute inset-0 flex items-center justify-center">
        <p className="text-gray-500">Map data would render here</p>
        {mapCenter && (
          <p className="text-xs text-gray-400 mt-2">
            Center: {mapCenter.lat.toFixed(4)}, {mapCenter.lng.toFixed(4)}
          </p>
        )}
        <p className="text-xs text-gray-400 mt-2">
          Activities: {activities.length}, Locations: {userLocations.length}
        </p>
      </div>
    </div>
  );
};

export default MapCore;
