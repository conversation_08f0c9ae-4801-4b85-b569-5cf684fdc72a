
import React from 'react';
import { format } from 'date-fns';
import { MapPin, Calendar, Clock, Users, DollarSign } from 'lucide-react';
import { Activity } from '@/types/activity';
import { Card, CardContent, CardFooter, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ActivityJoin } from './ActivityJoin';
import { useProfile } from '@/hooks/use-profile';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from '@/lib/utils';
import { useActivityRouter } from './ActivityRouter';
import { EnhancedActivityCardProps } from './EnhancedActivityCardProps';

export function EnhancedActivityCard({
  activity,
  onViewOnMap,
  className = '',
  onClick,
  variant
}: EnhancedActivityCardProps) {
  const { data: hostProfile } = useProfile(activity.host_id);
  const { openModal } = useActivityRouter();

  // Generate category emoji based on category name or use category icon if available
  const getCategoryEmoji = (): string => {
    if (!activity.category) return '📌';
    return activity.category.icon || '📌';
  };

  // Get appropriate gradient based on activity type
  const getGradient = () => {
    const categoryName = activity.category?.name.toLowerCase();
    if (categoryName === 'sports' || categoryName === 'fitness')
      return 'from-blue-500/10 to-cyan-500/10 border-blue-200/30';
    if (categoryName === 'music')
      return 'from-indigo-500/10 to-purple-500/10 border-purple-200/30';
    if (categoryName === 'social' || categoryName === 'dating')
      return 'from-pink-500/10 to-rose-500/10 border-pink-200/30';
    if (categoryName === 'outdoors')
      return 'from-green-500/10 to-emerald-500/10 border-green-200/30';
    if (categoryName === 'dining')
      return 'from-amber-500/10 to-orange-500/10 border-amber-200/30';
    return 'from-primary/10 to-primary-purple/10 border-primary/20'; // default
  };

  // Handle card click
  const handleCardClick = () => {
    if (onClick) {
      onClick();
    } else {
      openModal(activity);
    }
  };

  return (
    <>
      <Card
        className={cn(
          `overflow-hidden hover:shadow-md transition-all duration-300 bg-gradient-to-br ${getGradient()} backdrop-blur-sm cursor-pointer`,
          className
        )}
        isHoverable
        onClick={handleCardClick}
      >
        <div className="relative">
          <div className="h-28 w-full bg-gradient-to-r from-background/40 to-background/60 relative overflow-hidden">
            {/* Decorative elements */}
            <div className="absolute -right-8 -bottom-8 w-32 h-32 bg-primary/5 rounded-full blur-2xl"></div>
            <div className="absolute -left-4 -top-4 w-24 h-24 bg-primary/5 rounded-full blur-xl"></div>

            <div className="absolute top-4 left-4 flex items-center gap-2">
              <div className="bg-white/70 backdrop-blur-md rounded-full w-12 h-12 flex items-center justify-center text-2xl shadow-sm border border-white/10 transform transition-transform duration-300 hover:scale-105">
                {getCategoryEmoji()}
              </div>
              <Badge variant="outline" className="bg-white/80 backdrop-blur-md shadow-sm font-normal border-white/10">
                {activity.category?.name || 'Activity'}
              </Badge>
              {activity.is_paid && (
                <Badge variant="secondary" className="bg-amber-100/90 backdrop-blur-md text-amber-700 border-amber-200/30 font-normal">
                  <DollarSign className="h-3 w-3 mr-0.5" />
                  {activity.price ? `$${activity.price}` : 'Paid'}
                </Badge>
              )}
            </div>
          </div>
        </div>

        <CardHeader className="px-4 pb-1 pt-3">
          <div className="flex items-start justify-between">
            <div>
              <h3 className="font-semibold text-lg line-clamp-1">{activity.title}</h3>
              {hostProfile && (
                <div className="flex items-center text-sm text-muted-foreground mt-1">
                  <span>Hosted by:</span>
                  <div className="flex items-center ml-2">
                    <Avatar className="h-5 w-5 mr-1 ring-1 ring-primary/10">
                      <AvatarImage src={hostProfile.avatar_url || ''} />
                      <AvatarFallback className="text-xs bg-primary/10">
                        {hostProfile.display_name?.charAt(0) || hostProfile.username?.charAt(0) || 'U'}
                      </AvatarFallback>
                    </Avatar>
                    <span className="font-medium">{hostProfile.display_name || hostProfile.username || 'Unknown'}</span>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent className="px-4 py-2 space-y-2">
          {activity.description && (
            <p className="text-sm text-muted-foreground line-clamp-2">{activity.description}</p>
          )}

          <div className="grid grid-cols-1 gap-1.5 text-sm">
            <div className="flex items-center text-muted-foreground">
              <Calendar className="h-4 w-4 mr-2 text-blue-500 shrink-0" />
              <span>
                {format(new Date(activity.start_time), 'EEE, MMM d, yyyy')}
              </span>
            </div>

            <div className="flex items-center text-muted-foreground">
              <Clock className="h-4 w-4 mr-2 text-purple-500 shrink-0" />
              <span>
                {format(new Date(activity.start_time), 'h:mm a')}
                {activity.end_time && ` - ${format(new Date(activity.end_time), 'h:mm a')}`}
              </span>
            </div>

            {activity.address && (
              <div className="flex items-start text-muted-foreground">
                <MapPin className="h-4 w-4 mr-2 mt-0.5 text-rose-500 shrink-0" />
                <span className="line-clamp-1">{activity.address}</span>
              </div>
            )}

            {activity.max_participants && (
              <div className="flex items-center text-muted-foreground">
                <Users className="h-4 w-4 mr-2 text-green-500 shrink-0" />
                <span>Max {activity.max_participants} participants</span>
              </div>
            )}
          </div>
        </CardContent>

        <CardFooter className="px-4 py-3 border-t border-border/20 bg-card/30">
          <div className="w-full flex gap-2">
            <ActivityJoin
              activity={activity}
              size="sm"
              className="bg-gradient-to-r from-primary to-primary-deep-purple shadow-sm hover:opacity-90 transition-opacity"
              onClick={(e) => e.stopPropagation()}
            />
            {onViewOnMap && (
              <Button
                variant="outline"
                size="sm"
                onClick={(e) => {
                  e.stopPropagation();
                  onViewOnMap();
                }}
                className="backdrop-blur-sm"
              >
                View on Map
              </Button>
            )}
          </div>
        </CardFooter>
      </Card>
    </>
  );
}
