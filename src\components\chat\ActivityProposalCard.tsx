
import React, { useRef, useEffect, useState } from 'react';
import { <PERSON>, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  CalendarIcon, Clock, DollarSign, MapPin, Users,
  Check, X, MessageSquare, Share2, Navigation, ExternalLink
} from 'lucide-react';
import { formatDistanceToNow, format, isPast } from 'date-fns';
import { Activity } from '@/types/activity';
import { cn } from '@/lib/utils';
import { useNavigate } from 'react-router-dom';
import { ChatProposal } from '@/hooks/use-chat-proposals';
import { useToast } from '@/hooks/use-toast';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';

// Make sure to set your Mapbox token in your environment variables
mapboxgl.accessToken = import.meta.env.VITE_MAPBOX_TOKEN;

interface ActivityProposalCardProps {
  activity: Activity;
  proposal?: ChatProposal;
  onAccept?: () => void;
  onDecline?: () => void;
  onChat?: () => void;
  onShare?: () => void;
  className?: string;
  showActions?: boolean;
  showMap?: boolean;
  status?: 'pending' | 'accepted' | 'declined';
}

export function ActivityProposalCard({
  activity,
  proposal,
  onAccept,
  onDecline,
  onChat,
  onShare,
  className,
  showActions = true,
  showMap = true,
  status = 'pending'
}: ActivityProposalCardProps) {
  const navigate = useNavigate();
  const { toast } = useToast();
  const mapContainerRef = useRef<HTMLDivElement>(null);
  const [map, setMap] = useState<mapboxgl.Map | null>(null);

  const handleViewActivity = () => {
    navigate(`/activity/${activity.id}`);
  };

  const handleChatClick = () => {
    if (onChat) {
      onChat();
    } else {
      navigate(`/chat?activity=${activity.id}`);
    }
  };

  const handleShareClick = () => {
    if (onShare) {
      onShare();
    } else {
      // Copy activity link to clipboard
      const activityUrl = `${window.location.origin}/activity/${activity.id}`;
      navigator.clipboard.writeText(activityUrl);
      toast({
        description: "Activity link copied to clipboard"
      });
    }
  };

  const handleOpenInMaps = () => {
    if (activity.location) {
      // Open in Google Maps
      window.open(
        `https://www.google.com/maps/search/?api=1&query=${activity.location.y},${activity.location.x}`,
        '_blank'
      );
    }
  };

  const getStatusBadge = () => {
    switch (status) {
      case 'accepted':
        return <Badge className="bg-green-500">Accepted</Badge>;
      case 'declined':
        return <Badge variant="destructive">Declined</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  const isExpired = activity.end_time ? isPast(new Date(activity.end_time)) : false;

  // Initialize map when component mounts
  useEffect(() => {
    if (!showMap || !mapContainerRef.current || !activity.location) return;

    const newMap = new mapboxgl.Map({
      container: mapContainerRef.current,
      style: 'mapbox://styles/mapbox/streets-v12',
      center: [activity.location.x, activity.location.y],
      zoom: 13,
      interactive: false,
    });

    // Add marker
    new mapboxgl.Marker({ color: '#0ea5e9' })
      .setLngLat([activity.location.x, activity.location.y])
      .addTo(newMap);

    setMap(newMap);

    return () => {
      newMap.remove();
    };
  }, [activity.location, showMap]);

  return (
    <Card className={cn("w-full max-w-md overflow-hidden", className, isExpired ? "opacity-70" : "")}>
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg flex items-center">
              {activity.title}
              {isExpired && <Badge variant="outline" className="ml-2 text-xs">Ended</Badge>}
            </CardTitle>
            <CardDescription>
              {activity.description?.substring(0, 100)}
              {activity.description && activity.description.length > 100 ? '...' : ''}
            </CardDescription>
          </div>
          {getStatusBadge()}
        </div>
      </CardHeader>

      {showMap && activity.location && (
        <div
          ref={mapContainerRef}
          className="h-[120px] w-full cursor-pointer"
          onClick={handleOpenInMaps}
          style={{ filter: isExpired ? 'grayscale(1)' : 'none' }}
        />
      )}

      <CardContent className="pb-2">
        <div className="space-y-2">
          <div className="flex items-center text-sm text-muted-foreground">
            <CalendarIcon className="mr-2 h-4 w-4" />
            {activity.start_time ? (
              <span>
                {format(new Date(activity.start_time), 'PPP')} at {format(new Date(activity.start_time), 'p')}
                {activity.end_time && (
                  <span className="ml-1 text-xs">
                    ({formatDistanceToNow(new Date(activity.start_time), { addSuffix: true })})
                  </span>
                )}
              </span>
            ) : (
              <span>Date not specified</span>
            )}
          </div>

          <div className="flex items-center text-sm text-muted-foreground">
            <MapPin className="mr-2 h-4 w-4" />
            <span className="truncate">{activity.address || 'Location not specified'}</span>
          </div>

          <div className="flex items-center text-sm text-muted-foreground">
            <Users className="mr-2 h-4 w-4" />
            <span>
              {typeof activity.current_participants !== 'undefined' ? activity.current_participants : '0'}/
              {activity.max_participants || 'unlimited'} participants
            </span>
          </div>

          <div className="flex items-center text-sm text-muted-foreground">
            <DollarSign className="mr-2 h-4 w-4" />
            <span>{activity.is_paid ? `$${activity.price}` : 'Free'}</span>
          </div>

          {activity.host_id && (
            <div className="flex items-center mt-2">
              <Avatar className="h-6 w-6 mr-2">
                <AvatarImage src={activity.host?.avatar_url || ''} />
                <AvatarFallback>
                  {activity.host?.display_name?.substring(0, 2) || 'HO'}
                </AvatarFallback>
              </Avatar>
              <span className="text-sm">
                Hosted by {activity.host?.display_name || 'Unknown'}
              </span>
            </div>
          )}
        </div>
      </CardContent>

      {showActions && status === 'pending' && !isExpired && (
        <CardFooter className="flex justify-between pt-2">
          <Button variant="outline" size="sm" onClick={handleViewActivity}>
            View Details
          </Button>
          <div className="space-x-2">
            <Button
              variant="destructive"
              size="sm"
              onClick={onDecline}
            >
              <X className="mr-1 h-4 w-4" />
              Decline
            </Button>
            <Button
              variant="default"
              size="sm"
              onClick={onAccept}
            >
              <Check className="mr-1 h-4 w-4" />
              Accept
            </Button>
          </div>
        </CardFooter>
      )}

      {((!showActions || status !== 'pending') && !isExpired) && (
        <CardFooter className="pt-2 flex flex-wrap gap-2">
          <Button variant="outline" size="sm" onClick={handleViewActivity}>
            View Details
          </Button>

          <Button variant="outline" size="sm" onClick={handleChatClick}>
            <MessageSquare className="mr-1 h-4 w-4" />
            Chat
          </Button>

          <Button variant="outline" size="sm" onClick={handleShareClick}>
            <Share2 className="mr-1 h-4 w-4" />
            Share
          </Button>

          {activity.location && (
            <Button variant="outline" size="sm" onClick={handleOpenInMaps}>
              <Navigation className="mr-1 h-4 w-4" />
              Maps
            </Button>
          )}
        </CardFooter>
      )}

      {isExpired && (
        <CardFooter className="pt-2">
          <Button variant="outline" size="sm" onClick={handleViewActivity} className="w-full">
            View Past Activity
          </Button>
        </CardFooter>
      )}
    </Card>
  );
}
