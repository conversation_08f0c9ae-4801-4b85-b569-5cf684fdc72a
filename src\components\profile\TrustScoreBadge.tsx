
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Shield, ShieldCheck, ShieldAlert, Info } from "lucide-react";

interface TrustScoreBadgeProps {
  score: number;
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
}

export function TrustScoreBadge({ score, size = 'md', showLabel = true }: TrustScoreBadgeProps) {
  // Determine trust level and styling based on score
  const getTrustLevel = () => {
    if (score >= 90) return { label: 'Highly Trusted', color: 'bg-green-100 text-green-800 hover:bg-green-200', icon: <ShieldCheck className={`${size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'} mr-1 text-green-600`} /> };
    if (score >= 70) return { label: 'Trusted', color: 'bg-blue-100 text-blue-800 hover:bg-blue-200', icon: <Shield className={`${size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'} mr-1 text-blue-600`} /> };
    if (score >= 40) return { label: 'Neutral', color: 'bg-amber-100 text-amber-800 hover:bg-amber-200', icon: <Shield className={`${size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'} mr-1 text-amber-600`} /> };
    return { label: 'Unverified', color: 'bg-gray-100 text-gray-800 hover:bg-gray-200', icon: <ShieldAlert className={`${size === 'sm' ? 'h-3 w-3' : size === 'lg' ? 'h-5 w-5' : 'h-4 w-4'} mr-1 text-gray-600`} /> };
  };

  const { label, color, icon } = getTrustLevel();
  
  const badgeClasses = `${color} flex items-center ${size === 'sm' ? 'text-xs px-2 py-0.5' : size === 'lg' ? 'text-sm px-3 py-1.5' : 'text-xs px-2.5 py-1'}`;

  const tooltipContent = (
    <div className="space-y-2 max-w-xs">
      <p className="font-medium">Trust Score: {score}</p>
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full ${score >= 90 ? 'bg-green-500' : score >= 70 ? 'bg-blue-500' : score >= 40 ? 'bg-amber-500' : 'bg-gray-500'}`}
          style={{ width: `${score}%` }}
        />
      </div>
      <p className="text-xs">This score represents the user's activity, verification status, and community reputation.</p>
    </div>
  );

  return (
    <TooltipProvider>
      <Tooltip delayDuration={300}>
        <TooltipTrigger asChild>
          <Badge variant="outline" className={badgeClasses}>
            {icon}
            {showLabel ? label : null}
            {!showLabel && <span className="ml-1">{score}</span>}
            {showLabel && <span className="ml-1 opacity-70">({score})</span>}
          </Badge>
        </TooltipTrigger>
        <TooltipContent side="top" align="center">
          {tooltipContent}
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
