
import React, { useState } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { SavedLocationsList } from "@/components/locations/SavedLocationsList";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function SavedLocationsPage() {
  const [activeView, setActiveView] = useState("list");
  
  return (
    <MainLayout>
      <div className="container mx-auto py-6">
        <h1 className="text-3xl font-bold mb-6">Saved Locations</h1>
        
        <Tabs value={activeView} onValueChange={setActiveView} className="mb-6">
          <TabsList>
            <TabsTrigger value="list">List View</TabsTrigger>
            <TabsTrigger value="map">Map View</TabsTrigger>
          </TabsList>
          
          <TabsContent value="list">
            <SavedLocationsList showMaps={false} />
          </TabsContent>
          
          <TabsContent value="map">
            <SavedLocationsList showMaps={true} />
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}
