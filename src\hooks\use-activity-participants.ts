
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export interface ActivityParticipant {
  id: string;
  activity_id: string;
  user_id: string;
  status: 'pending' | 'confirmed' | 'cancelled';
  payment_id: string | null;
  created_at: string;
  updated_at: string;
}

export function useActivityParticipants(activityId: string | undefined) {
  return useQuery({
    queryKey: ['activity-participants', activityId],
    queryFn: async (): Promise<ActivityParticipant[]> => {
      if (!activityId) return [];

      const { data, error } = await supabase
        .from('activity_participants')
        .select('*')
        .eq('activity_id', activityId)
        .order('created_at', { ascending: true });

      if (error) {
        throw error;
      }

      // Ensure the status is cast to the correct type
      return data.map(participant => ({
        ...participant,
        status: participant.status as 'pending' | 'confirmed' | 'cancelled'
      })) || [];
    },
    enabled: !!activityId
  });
}

export function useJoinActivity() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const mutation = useMutation({
    mutationFn: async ({ 
      activityId, 
      userId = '00000000-0000-0000-0000-000000000000', // Placeholder user ID until auth is set up
      paymentId = null 
    }: { 
      activityId: string; 
      userId?: string; 
      paymentId?: string | null;
    }) => {
      const { data, error } = await supabase
        .from('activity_participants')
        .insert({
          activity_id: activityId,
          user_id: userId,
          status: paymentId ? 'confirmed' : 'pending',
          payment_id: paymentId
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      return data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['activity-participants', variables.activityId] });
      
      toast({
        title: "Success",
        description: variables.paymentId 
          ? "You have joined the activity after payment confirmation"
          : "You have joined the activity queue"
      });
    },
    onError: (error) => {
      toast({
        title: "Error joining activity",
        description: error.message || "Something went wrong. Please try again.",
        variant: "destructive"
      });
    }
  });

  return mutation;
}

export function useActivityParticipantCount(activityId: string | undefined) {
  return useQuery({
    queryKey: ['activity-participant-count', activityId],
    queryFn: async (): Promise<{ confirmed: number; pending: number; total: number }> => {
      if (!activityId) return { confirmed: 0, pending: 0, total: 0 };

      const { data, error } = await supabase
        .from('activity_participants')
        .select('status')
        .eq('activity_id', activityId);

      if (error) {
        throw error;
      }

      const confirmed = data.filter(p => p.status === 'confirmed').length;
      const pending = data.filter(p => p.status === 'pending').length;

      return {
        confirmed,
        pending,
        total: data.length
      };
    },
    enabled: !!activityId
  });
}
