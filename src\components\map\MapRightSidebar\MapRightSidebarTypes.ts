
import { Activity } from '@/types/activity';

export type MapRightSidebarTabValue = 'nearby' | 'plans' | 'favorites' | 'messages' | 'users' | 'activities' | 'explore';

export interface MapRightSidebarProps {
  users: any[];
  onUserSelect: (user: any) => void;
  selectedUser?: any;
  activities?: Activity[];
  onActivitySelect?: (activity: Activity) => void;
  activeTab?: MapRightSidebarTabValue;
  onTabChange?: (value: MapRightSidebarTabValue) => void;
  className?: string;
}
