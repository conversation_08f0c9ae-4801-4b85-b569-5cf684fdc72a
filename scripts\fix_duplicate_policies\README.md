# Fix Duplicate RLS Policies

This directory contains scripts to fix the "Multiple Permissive Policies" warnings in Supabase.

## The Problem

The Supabase linter is detecting multiple permissive Row Level Security (RLS) policies for the same role and action on many tables. This happens when:

1. Multiple policies are created for the same table, role, and action (e.g., SELECT)
2. These duplicate policies are executed for every relevant query, which can impact performance

## Solution

The scripts in this directory will consolidate duplicate policies into a single policy per table and action, improving query performance.

## Files

- `fix_remaining_duplicates.sql`: SQL script that consolidates duplicate policies
- `run_fix_duplicates.js`: Node.js script to run the SQL against your Supabase database
- `create_exec_sql_function.sql`: SQL to create a helper function needed by the Node.js script

## How to Use

### Prerequisites

- Node.js installed
- `.env` file in the project root with Supabase credentials:
  ```
  NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
  SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
  ```

### Steps

1. Make sure you have a backup of your database (or run this in a development environment first)
2. First, run the `create_exec_sql_function.sql` script in the Supabase SQL Editor to create the helper function
3. Install dependencies:
   ```
   cd scripts/fix_duplicate_policies
   npm install
   ```
4. Run the script:
   ```
   node run_fix_duplicates.js
   ```

## How It Works

The script:

1. Identifies tables with duplicate policies from a predefined list
2. For each table and action (SELECT, INSERT, UPDATE, DELETE), it:
   - Checks if multiple policies exist
   - Combines the policy conditions with OR operators
   - Creates a new consolidated policy
   - Drops the old duplicate policies

## After Running

After running the script, check the Supabase dashboard to verify that the "Multiple Permissive Policies" warnings have been resolved.

## Caution

Always test this in a development environment before running it in production. While the script is designed to maintain the same access control logic, it's important to verify that your application still works as expected after consolidating policies.
