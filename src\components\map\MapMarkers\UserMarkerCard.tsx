
import React from 'react';
import { UnifiedMapUser } from '@/types/map';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  BadgeCheck,
  MessageCircle,
  UserPlus,
  MapPin,
  X,
  Clock,
} from "lucide-react";
import { UserAvatar } from '@/components/user/UserAvatar';
import { formatDistanceToNow } from 'date-fns';
import { cn } from '@/lib/utils';

interface UserMarkerCardProps {
  user: UnifiedMapUser;
  distance?: number;
  isCurrentUser: boolean;
  onConnect: () => void;
  onViewProfile: () => void;
  onSendMessage: () => void;
  lastSeen?: string;
}

export function UserMarkerCard({
  user,
  distance,
  isCurrentUser,
  onConnect,
  onViewProfile,
  onSendMessage,
  lastSeen = 'Unknown'
}: UserMarkerCardProps) {
  const formattedDistance = distance 
    ? distance < 1 
      ? `${Math.round(distance * 1000)} meters away` 
      : `${distance.toFixed(1)} km away`
    : null;

  const userBio = user.bio 
    ? user.bio.length > 120 
      ? `${user.bio.substring(0, 120)}...` 
      : user.bio
    : "No bio available";

  const lastSeenText = user.is_online 
    ? 'Online now'
    : lastSeen === 'Unknown' && user.updated_at 
      ? formatDistanceToNow(new Date(user.updated_at), { addSuffix: true })
      : lastSeen;

  return (
    <Card className="absolute bottom-4 left-1/2 transform -translate-x-1/2 translate-y-full w-72 z-50 animate-in fade-in duration-300 bg-white/95 backdrop-blur-sm border-2 border-primary/10">
      <CardHeader className="py-3 px-4 flex flex-row items-center gap-3">
        <UserAvatar 
          user={user} 
          size="lg" 
          showVerified={user.is_verified}
          showStatus={true}
          isOnline={user.is_online}
          className="border-2 border-primary/20"
        />
        <div className="flex-1">
          <div className="flex items-center gap-1">
            <p className="font-medium text-sm truncate max-w-[150px]">
              {user.display_name || user.username || 'User'}
            </p>
            {user.is_verified && <BadgeCheck className="h-4 w-4 text-blue-500 flex-shrink-0" />}
          </div>
          <div className="flex flex-col text-xs text-muted-foreground space-y-0.5">
            <div className="flex items-center gap-1">
              <Clock className="h-3 w-3" />
              <span>{lastSeenText}</span>
            </div>
            {formattedDistance && (
              <div className="flex items-center gap-1">
                <MapPin className="h-3 w-3" />
                <span>{formattedDistance}</span>
              </div>
            )}
          </div>
        </div>
        <Button 
          variant="ghost" 
          size="icon" 
          className="h-8 w-8 hover:bg-primary/5" 
          onClick={onViewProfile}
        >
          <X className="h-4 w-4" />
        </Button>
      </CardHeader>
      
      <CardContent className="px-4 py-2">
        <div className="relative">
          <p className={cn(
            "text-sm leading-relaxed whitespace-pre-wrap break-words",
            !user.bio && "text-muted-foreground italic"
          )}>
            {userBio}
          </p>
          {user.bio && user.bio.length > 120 && (
            <div className="absolute bottom-0 right-0 bg-gradient-to-l from-white/95 to-transparent px-2">
              <Button 
                variant="link" 
                className="text-xs p-0 h-auto" 
                onClick={onViewProfile}
              >
                more
              </Button>
            </div>
          )}
        </div>
      </CardContent>
      
      <CardFooter className="px-4 py-3 flex gap-2">
        {!isCurrentUser && (
          <>
            <Button 
              size="sm" 
              variant="outline" 
              className="flex-1 hover:bg-primary/5" 
              onClick={onSendMessage}
            >
              <MessageCircle className="h-4 w-4 mr-1" />
              Message
            </Button>
            <Button 
              size="sm" 
              className="flex-1 bg-primary hover:bg-primary/90" 
              onClick={onConnect}
            >
              <UserPlus className="h-4 w-4 mr-1" />
              Connect
            </Button>
          </>
        )}
        {isCurrentUser && (
          <Button 
            size="sm" 
            className="w-full bg-primary hover:bg-primary/90" 
            onClick={onViewProfile}
          >
            View Your Profile
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
