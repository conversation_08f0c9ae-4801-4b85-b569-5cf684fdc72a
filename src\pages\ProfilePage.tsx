
import React, { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useNavigate } from 'react-router-dom';
import { MainLayout } from '@/components/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';
import { useProfile } from '@/hooks/use-profile';
import { useUserActivities } from '@/hooks/use-user-activities';
import { useFollowUser } from '@/hooks/use-follow-user';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { format } from 'date-fns';

// Enhanced Profile Components
import { <PERSON>Header } from '@/components/profile/ProfileHeader';
import { ProfileGallery } from '@/components/profile/ProfileGallery';
import { ProfileConnections } from '@/components/profile/ProfileConnections';
import { ProfileAbout } from '@/components/profile/ProfileAbout';
import { ProfileActivities } from '@/components/profile/ProfileActivities';
import { ProfileEditModal } from '@/components/profile/ProfileEditModal';
import { EnhancedUserProfile } from '@/types/enhanced-profile';

export default function ProfilePage() {
  // Get parameters from URL - could be userId or username
  const params = useParams<{ userId?: string; username?: string }>();
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // State
  const [activeTab, setActiveTab] = useState('activities');
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // Determine which parameter to use for profile lookup
  // Order of precedence: userId param, username param, current user's ID
  const profileIdentifier = params.userId || params.username || user?.id;

  // Log the profile identifier being used
  console.log(`ProfilePage: Using profile identifier: ${profileIdentifier}`);
  console.log(`ProfilePage: URL params:`, params);

  // Check if we're on the username route (/:username)
  const isUsernameRoute = !params.userId && params.username;
  console.log(`ProfilePage: Is username route: ${isUsernameRoute}`);

  // Check if we're on the base profile route (/profile) with no params
  const isBaseProfileRoute = !params.userId && !params.username;
  console.log(`ProfilePage: Is base profile route: ${isBaseProfileRoute}`);

  // Get profile data
  const { data: profile, isLoading, error, refetch } = useProfile(profileIdentifier);

  // Get user's activities - only if we have a profile ID
  const { data: activities, isLoading: activitiesLoading } = useUserActivities({
    filter: 'all',
    limit: 10
  });

  // Handle special cases for profile viewing
  useEffect(() => {
    // Case 1: User is not logged in but trying to view a profile
    if (!user && isUsernameRoute) {
      console.log("ProfilePage: Not logged in but viewing a profile by username");
    }

    // Case 2: User is logged in and viewing their own profile by username
    if (user && profile && isUsernameRoute && user.id === profile.id) {
      console.log("ProfilePage: User is viewing their own profile by username");
    }

    // Case 3: User is logged in but profile not found
    if (user && !profile && !isLoading) {
      console.log("ProfilePage: User is logged in but profile not found");
    }

    // Case 4: User is on the base profile route (/profile)
    if (isBaseProfileRoute && user) {
      console.log("ProfilePage: User is on the base profile route, showing their own profile");
    }

    // Direct database check for username if profile not found
    const checkUsernameDirectly = async () => {
      if (isUsernameRoute && params.username && (!profile || (profile as any).notFound) && !isLoading) {
        console.log("ProfilePage: Directly checking if username exists:", params.username);

        try {
          const { data, error } = await supabase
            .from('profiles')
            .select('id, username')
            .ilike('username', params.username)
            .limit(1);

          console.log("ProfilePage: Direct username check result:", { data, error });

          if (data && data.length > 0) {
            console.log("ProfilePage: Username exists in database but profile not found in hook");
          } else {
            console.log("ProfilePage: Username does not exist in database");
          }
        } catch (err) {
          console.error("ProfilePage: Error checking username directly:", err);
        }
      }
    };

    checkUsernameDirectly();
  }, [user, profile, isUsernameRoute, isLoading, params.username]);

  // Follow functionality
  const { isFollowing, follow, unfollow, isLoading: isFollowLoading } = useFollowUser(profile?.id || '');

  // Track follow state locally to update UI immediately
  const [followState, setFollowState] = useState({ isFollowing: false });

  // Update local follow state when the query data changes
  useEffect(() => {
    if (isFollowing !== undefined) {
      setFollowState({ isFollowing });
    }
  }, [isFollowing]);

  // Handle follow toggle
  const toggleFollowHandler = async () => {
    // Optimistically update UI
    setFollowState(prev => ({ isFollowing: !prev.isFollowing }));

    try {
      if (followState.isFollowing) {
        await unfollow();
      } else {
        await follow();
      }
    } catch (error) {
      // Revert on error
      setFollowState(prev => ({ isFollowing: !prev.isFollowing }));
      toast({
        title: 'Error',
        description: 'Failed to update follow status',
        variant: 'destructive',
      });
    }
  };

  // Check if this is the current user's profile
  const isOwnProfile = user?.id === profile?.id;

  // Format the member since date
  const memberSince = profile?.created_at
    ? format(new Date(profile.created_at), 'MMMM yyyy')
    : 'Unknown';

  // Handle sending a message
  const handleMessageUser = () => {
    if (!profile) return;
    navigate(`/chat?user=${profile.id}`);
  };

  if (isLoading) {
    return (
      <MainLayout>
        <div className="container max-w-4xl py-8">
          <div className="flex flex-col md:flex-row gap-6">
            {/* Profile sidebar skeleton */}
            <div className="w-full md:w-1/3">
              <Card>
                <CardHeader className="text-center">
                  <Skeleton className="h-24 w-24 rounded-full mx-auto" />
                  <Skeleton className="h-6 w-32 mx-auto mt-2" />
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Main content skeleton */}
            <div className="flex-1">
              <Skeleton className="h-10 w-full mb-4" />
              <div className="space-y-4">
                <Skeleton className="h-32 w-full" />
                <Skeleton className="h-32 w-full" />
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Check for profile not found or error
  if (error || !profile) {
    console.log("ProfilePage: Profile not available:", { error, profile });

    // Check if it's specifically a "not found" error
    const isNotFound = profile && (profile as any).notFound === true;

    // Check if it's a private profile error
    const isPrivateProfile = profile && (profile as any).isPrivate === true;

    // Determine the appropriate error message
    let errorTitle = "Profile not found";
    let errorMessage = "The user profile you're looking for doesn't exist or you don't have permission to view it.";
    let showLoginButton = false;

    if (isPrivateProfile) {
      console.log("ProfilePage: This is a private profile");
      errorTitle = "Private Profile";
      errorMessage = "This profile is set to private by the user.";
    } else if (isNotFound) {
      console.log("ProfilePage: This is a 'not found' error");
      if (params.username) {
        errorMessage = `No user with the username "${params.username}" was found. Please check the spelling and try again.`;
      }
    } else if (error) {
      console.log("ProfilePage: This is a database error:", error);
      errorTitle = "Error loading profile";
      errorMessage = "There was an error loading this profile. Please try again later.";
    }

    // If user is not logged in and trying to view a profile, suggest logging in
    if (!user && isUsernameRoute && !isPrivateProfile) {
      console.log("ProfilePage: Not logged in and profile not found");
      showLoginButton = true;
      errorMessage = `To view this profile, you may need to log in first. If you don't have an account, you can sign up for free.`;
    }

    return (
      <MainLayout>
        <div className="container max-w-4xl py-8 text-center">
          <h1 className="text-2xl font-bold mb-4">{errorTitle}</h1>
          <p className="text-muted-foreground mb-6">
            {errorMessage}
          </p>
          <div className="flex justify-center gap-4">
            <Button onClick={() => navigate('/')}>Return to Home</Button>

            {showLoginButton && (
              <Button variant="default" onClick={() => navigate('/?login=true')}>
                Log In
              </Button>
            )}

            {params.username && !showLoginButton && (
              <Button variant="outline" onClick={() => window.location.reload()}>
                Try Again
              </Button>
            )}
          </div>
        </div>
      </MainLayout>
    );
  }

  // Cast the profile to EnhancedUserProfile
  const enhancedProfile = profile as EnhancedUserProfile;

  return (
    <MainLayout>
      <div className="container max-w-4xl py-8">
        {/* Profile Header */}
        <ProfileHeader
          profile={enhancedProfile}
          isLoading={isLoading}
          onEditClick={() => setIsEditModalOpen(true)}
          onMessageClick={handleMessageUser}
          onProfileUpdated={() => refetch()}
        />

        {/* Main Content */}
        <div className="mt-6">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="w-full">
              <TabsTrigger value="activities" className="flex-1">
                Activities
              </TabsTrigger>
              <TabsTrigger value="gallery" className="flex-1">
                Gallery
              </TabsTrigger>
              <TabsTrigger value="about" className="flex-1">
                About
              </TabsTrigger>
              <TabsTrigger value="connections" className="flex-1">
                Connections
              </TabsTrigger>
            </TabsList>

            {/* Activities Tab */}
            <TabsContent value="activities" className="mt-4">
              <ProfileActivities
                profile={enhancedProfile}
                isLoading={isLoading}
              />
            </TabsContent>

            {/* Gallery Tab */}
            <TabsContent value="gallery" className="mt-4">
              <ProfileGallery
                profile={enhancedProfile}
                isLoading={isLoading}
                onProfileUpdated={() => refetch()}
              />
            </TabsContent>

            {/* About Tab */}
            <TabsContent value="about" className="mt-4">
              <ProfileAbout
                profile={enhancedProfile}
                isLoading={isLoading}
              />
            </TabsContent>

            {/* Connections Tab */}
            <TabsContent value="connections" className="mt-4">
              <ProfileConnections
                profile={enhancedProfile}
                isLoading={isLoading}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Edit Profile Modal */}
      {isEditModalOpen && (
        <ProfileEditModal
          open={isEditModalOpen}
          onClose={() => setIsEditModalOpen(false)}
          profile={enhancedProfile}
          onProfileUpdated={() => {
            refetch();
            setIsEditModalOpen(false);
          }}
        />
      )}
    </MainLayout>
  );
}
