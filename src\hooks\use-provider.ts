
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { ServiceProvider } from './use-providers';

export interface ProfileData {
  avatar_url?: string;
  display_name?: string;
  username?: string;
  bio?: string; // Bio property explicitly defined in the interface
}

export function useProvider(providerId?: string) {
  const { data, isLoading, error } = useQuery({
    queryKey: ['service-provider', providerId],
    queryFn: async (): Promise<ServiceProvider | null> => {
      if (!providerId) return null;
      
      try {
        // In a real implementation, this would fetch from the database
        // For now, return mock data if the ID matches our example provider
        if (providerId === '1') {
          return {
            id: '1',
            user_id: '1',
            name: 'John <PERSON>',
            description: 'Professional services for all your needs',
            avatar_url: 'https://example.com/avatar.jpg',
            display_name: '<PERSON>',
            username: 'johndo<PERSON>',
            rating: 4.8,
            total_reviews: 24,
            services: [],
            trust_score: 85,
            verification_status: 'verified',
            verification_level_id: '1',
            hourly_rate: 50,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
            // Add the profiles field with the correct interface that includes bio
            profiles: {
              avatar_url: 'https://example.com/avatar.jpg',
              display_name: 'John Doe',
              username: 'johndoe',
              bio: 'Professional service provider with 5+ years of experience.'
            }
          };
        }
        
        // If the ID doesn't match our example, return null
        return null;
      } catch (error) {
        console.error('Error fetching provider:', error);
        return null;
      }
    },
    enabled: !!providerId
  });

  return {
    data,
    isLoading,
    error
  };
}
