
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { ChatMessage } from '@/types/chat';

export function useBuddyAdminConversation() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const { data: adminConversation, isLoading: isLoadingConversation, error: conversationError } = useQuery({
    queryKey: ['admin-conversation', user?.id],
    queryFn: async () => {
      if (!user) return null;

      // Check if admin conversation exists
      const { data: existingConversation } = await supabase
        .from('chat_conversations')
        .select('*')
        .eq('is_admin_conversation', true)
        .maybeSingle();

      if (existingConversation) {
        console.log('[Buddy Admin] Found existing admin conversation:', existingConversation.id);
        // Check if user is participant
        const { data: participant } = await supabase
          .from('chat_participants')
          .select('*')
          .eq('conversation_id', existingConversation.id)
          .eq('user_id', user.id)
          .maybeSingle();

        if (!participant) {
          console.log('[Buddy Admin] User is not a participant, adding user:', user.id);
          // Add user as participant
          await supabase
            .from('chat_participants')
            .insert({
              conversation_id: existingConversation.id,
              user_id: user.id
            });
        } else {
          console.log('[Buddy Admin] User is already a participant:', user.id);
        }

        // Ensure there is at least one welcome message from admin
        const { data: messages } = await supabase
          .from('messages')
          .select('id')
          .eq('conversation_id', existingConversation.id);
        if (!messages || messages.length === 0) {
          console.log('[Buddy Admin] No welcome message found, inserting welcome message.');
          await supabase
            .from('messages')
            .insert({
              conversation_id: existingConversation.id,
              content: "Welcome to BuddySurf! 👋 I'm your Buddy Admin, here to help you get started. Feel free to ask any questions about the platform!",
              sender_id: 'admin',
              is_admin: true
            });
        } else {
          console.log('[Buddy Admin] Welcome message already exists.');
        }

        console.log('[Buddy Admin] Returning admin conversation:', existingConversation.id);
        return existingConversation;
      }

      // Create new admin conversation
      console.log('[Buddy Admin] Creating new admin conversation.');
      const { data: newConversation, error: conversationError } = await supabase
        .from('chat_conversations')
        .insert({
          is_admin_conversation: true,
          is_announcement_only: true,
          is_group: false,
          last_message: 'Welcome to BuddySurf! 👋',
          last_message_at: new Date().toISOString()
        })
        .select()
        .single();

      if (conversationError) throw conversationError;

      console.log('[Buddy Admin] New admin conversation created:', newConversation.id);
      // Add user as participant
      await supabase
        .from('chat_participants')
        .insert({
          conversation_id: newConversation.id,
          user_id: user.id
        });
      console.log('[Buddy Admin] User added as participant:', user.id);

      // Add welcome message
      await supabase
        .from('messages')
        .insert({
          conversation_id: newConversation.id,
          content: "Welcome to BuddySurf! 👋 I'm your Buddy Admin, here to help you get started. Feel free to ask any questions about the platform!",
          sender_id: 'admin',
          is_admin: true
        });
      console.log('[Buddy Admin] Welcome message inserted.');

      return newConversation;
    },
    enabled: !!user
  });

  // Support conversation (same as admin for now)
  const supportConversation = adminConversation;
  const isSupportLoading = isLoadingConversation;

  const {
    data: adminMessages,
    isLoading: isLoadingMessages,
    error: messagesError
  } = useQuery({
    queryKey: ['admin-messages', adminConversation?.id],
    queryFn: async (): Promise<ChatMessage[]> => {
      if (!adminConversation?.id) return [];

      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', adminConversation.id)
        .order('created_at', { ascending: true });

      if (error) throw error;

      // Fetch sender profiles separately to avoid join issues
      const senderIds = [...new Set((data || []).map(msg => msg.sender_id).filter(Boolean))];
      const { data: profiles } = await supabase
        .from('profiles')
        .select('id, display_name, avatar_url, username')
        .in('id', senderIds);

      const profileMap = new Map(profiles?.map(p => [p.id, p]) || []);

      return (data || []).map(msg => ({
        ...msg,
        sender: msg.sender_id ? profileMap.get(msg.sender_id) || {
          id: msg.sender_id,
          display_name: 'Unknown User',
          avatar_url: '',
          username: 'unknown'
        } : null
      })) as ChatMessage[];
    },
    enabled: !!adminConversation?.id
  });

  const sendAdminMessage = useMutation({
    mutationFn: async (message: string) => {
      if (!user || !adminConversation?.id) {
        throw new Error("Missing required information to send message");
      }

      const { data, error } = await supabase
        .from('messages')
        .insert({
          sender_id: user.id,
          conversation_id: adminConversation.id,
          content: message.trim(),
          message_type: 'text',
          is_admin_message: true
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      // Invalidate messages query to refetch
      if (adminConversation && typeof adminConversation.id === 'string') {
        queryClient.invalidateQueries({
          queryKey: ['admin-messages', adminConversation.id]
        });
      }
    },
    onError: (error) => {
      console.error("Error sending message:", error);
    }
  });

  return {
    adminConversation,
    supportConversation,
    isSupportLoading,
    adminMessages: adminMessages || [],
    isLoading: isLoadingConversation || isLoadingMessages,
    isError: !!conversationError || !!messagesError,
    error: conversationError || messagesError,
    sendAdminMessage,
  };
}

// Also export as useAdminConversation for backward compatibility
export const useAdminConversation = useBuddyAdminConversation;
