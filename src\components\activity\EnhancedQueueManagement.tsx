
import React, { useState } from 'react';
import { Activity } from '@/types/activity';
import { useQueueWrapper } from '@/hooks/use-queue-wrapper';
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle, CardFooter, CardDescription } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useAuth } from '@/hooks/use-auth';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ActivityGroupChat } from './ActivityGroupChat';
import { 
  Users, Clock, CheckCircle, XCircle, DollarSign, 
  AlertCircle, Loader2, Filter, ArrowUpDown, RefreshCcw,
  ChevronUp, ChevronDown 
} from "lucide-react";
import { format } from 'date-fns';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

interface EnhancedQueueManagementProps {
  activity: Activity;
  className?: string;
}

export function EnhancedQueueManagement({ activity, className = '' }: EnhancedQueueManagementProps) {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState<string>('queue');
  
  const {
    queueEntries,
    isLoading,
    isProcessing,
    stats,
    analytics,
    bulkUpdateQueueStatus,
    recalculatePositions,
    autoFillAvailableSpots,
    cancelActivity,
    userEntry,
  } = useQueueWrapper(activity.id, activity.host_id, activity.max_participants);
  
  const isHost = activity.host_id === user?.id;
  const activityStats = stats;
  
  const confirmedEntries = queueEntries?.filter(entry => entry.status === 'confirmed') || [];
  const pendingEntries = queueEntries?.filter(entry => entry.status === 'pending') || [];
  const cancelledEntries = queueEntries?.filter(entry => entry.status === 'cancelled') || [];
  
  const handleAutoFill = () => {
    if (!activity.max_participants) return;
    autoFillAvailableSpots();
  };
  
  const handleCancellation = () => {
    if (!isHost) return;
    if (window.confirm('Are you sure you want to cancel this activity? This will notify all participants.')) {
      cancelActivity();
    }
  };
  
  // Helper function to safely access estimatedWaitTime
  const getEstimatedWaitTime = () => {
    return stats?.estimatedWaitTime || null;
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Queue Management</CardTitle>
        </CardHeader>
        <CardContent className="flex justify-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </CardContent>
      </Card>
    );
  }
  
  if (!user) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Activity Queue</CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Authentication Required</AlertTitle>
            <AlertDescription>
              Please sign in to view and manage the activity queue.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }
  
  // Get the estimated wait time safely
  const estimatedWaitTime = getEstimatedWaitTime();
  
  return (
    <Tabs defaultValue="queue" value={activeTab} onValueChange={setActiveTab} className={className}>
      <div className="flex justify-between items-center mb-2">
        <TabsList>
          <TabsTrigger value="queue" className="relative">
            Queue
            {pendingEntries.length > 0 && (
              <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 flex items-center justify-center">
                {pendingEntries.length}
              </Badge>
            )}
          </TabsTrigger>
          <TabsTrigger value="chat">Group Chat</TabsTrigger>
          {isHost && <TabsTrigger value="analytics">Analytics</TabsTrigger>}
        </TabsList>
        
        {isHost && (
          <Button variant="outline" size="sm" onClick={recalculatePositions}>
            <RefreshCcw className="h-3 w-3 mr-1" />
            Refresh
          </Button>
        )}
      </div>
      
      <TabsContent value="queue">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Queue Management
            </CardTitle>
            <CardDescription>
              {isHost 
                ? "Manage participants for your activity" 
                : userEntry 
                  ? `You are ${userEntry.status === 'confirmed' ? 'confirmed' : 'in position ' + userEntry.position + ' in the queue'}`
                  : "Join this activity to participate"
              }
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            <div className="space-y-6">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>{confirmedEntries.length} confirmed</span>
                  <span>
                    {activity.max_participants 
                      ? `${Math.max(0, activity.max_participants - confirmedEntries.length)} spots left` 
                      : 'Unlimited'
                    }
                  </span>
                </div>
                <Progress 
                  value={activity.max_participants 
                    ? (confirmedEntries.length / activity.max_participants) * 100 
                    : 0
                  } 
                  className="h-2" 
                />
                
                <div className="flex flex-wrap gap-2 mt-1 text-xs">
                  <div className="flex items-center gap-1 text-green-600">
                    <CheckCircle className="h-3 w-3" />
                    <span>{confirmedEntries.length} confirmed</span>
                  </div>
                  <div className="flex items-center gap-1 text-amber-600">
                    <Clock className="h-3 w-3" />
                    <span>{pendingEntries.length} pending</span>
                  </div>
                  <div className="flex items-center gap-1 text-red-600">
                    <XCircle className="h-3 w-3" />
                    <span>{cancelledEntries.length} cancelled</span>
                  </div>
                </div>
              </div>
              
              {isHost && activity.max_participants && confirmedEntries.length < activity.max_participants && pendingEntries.length > 0 && (
                <div>
                  <Button 
                    variant="default" 
                    size="sm" 
                    className="w-full" 
                    onClick={handleAutoFill}
                    disabled={isProcessing}
                  >
                    {isProcessing ? "Processing..." : "Auto-fill Available Spots"}
                  </Button>
                </div>
              )}
              
              {isHost && confirmedEntries.length > 0 && (
                <div>
                  <Button 
                    variant="destructive" 
                    size="sm" 
                    className="w-full" 
                    onClick={handleCancellation}
                    disabled={isProcessing}
                  >
                    {isProcessing ? "Processing..." : "Cancel Activity & Notify All"}
                  </Button>
                </div>
              )}
              
              {queueEntries && queueEntries.length > 0 && (
                <div className="space-y-2">
                  <h3 className="text-sm font-medium">Participants</h3>
                  <ScrollArea className="h-[200px]">
                    <div className="space-y-1">
                      {confirmedEntries.map((entry) => (
                        <ParticipantRow 
                          key={entry.id} 
                          entry={entry} 
                          isHost={isHost}
                          onUpdateStatus={(status) => {
                            bulkUpdateQueueStatus([entry], status);
                          }}
                          hostId={activity.host_id}
                        />
                      ))}
                      
                      {pendingEntries.length > 0 && (
                        <>
                          <div className="text-xs font-medium text-amber-600 mt-3 mb-1 flex items-center">
                            <Clock className="h-3 w-3 mr-1" />
                            Pending ({pendingEntries.length})
                          </div>
                          
                          {pendingEntries.map((entry) => (
                            <ParticipantRow 
                              key={entry.id} 
                              entry={entry} 
                              isHost={isHost}
                              onUpdateStatus={(status) => {
                                bulkUpdateQueueStatus([entry], status);
                              }}
                              hostId={activity.host_id}
                            />
                          ))}
                        </>
                      )}
                    </div>
                  </ScrollArea>
                </div>
              )}
              
              {userEntry && userEntry.status === 'pending' && estimatedWaitTime && (
                <Alert variant="default" className="bg-blue-50 border-blue-200">
                  <Clock className="h-4 w-4 text-blue-500" />
                  <AlertTitle>Estimated Wait Time</AlertTitle>
                  <AlertDescription>
                    Based on current activity, your estimated wait is approximately{' '}
                    {estimatedWaitTime < 60 ? 
                      `${Math.ceil(estimatedWaitTime)} minutes` :
                      `${Math.floor(estimatedWaitTime / 60)} hours and ${Math.ceil(estimatedWaitTime % 60)} minutes`
                    }.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          </CardContent>
          
          {isHost && (
            <CardFooter>
              <p className="text-xs text-muted-foreground">
                As the host, you can manage the queue by confirming or removing participants.
              </p>
            </CardFooter>
          )}
        </Card>
      </TabsContent>
      
      <TabsContent value="chat">
        <ActivityGroupChat activity={activity} />
      </TabsContent>
      
      <TabsContent value="analytics">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Queue Analytics
            </CardTitle>
            <CardDescription>
              Statistics and insights about your activity queue
            </CardDescription>
          </CardHeader>
          
          <CardContent>
            {analytics ? (
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="bg-muted p-4 rounded-lg">
                    <div className="text-sm font-medium text-muted-foreground">Acceptance Rate</div>
                    <div className="text-2xl font-bold">{analytics.acceptanceRate.toFixed(1)}%</div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Of processed queue requests
                    </div>
                  </div>
                  
                  <div className="bg-muted p-4 rounded-lg">
                    <div className="text-sm font-medium text-muted-foreground">Average Wait</div>
                    <div className="text-2xl font-bold">
                      {analytics.avgWaitTime < 60 
                        ? `${Math.ceil(analytics.avgWaitTime)}m` 
                        : `${Math.floor(analytics.avgWaitTime / 60)}h ${Math.ceil(analytics.avgWaitTime % 60)}m`
                      }
                    </div>
                    <div className="text-xs text-muted-foreground mt-1">
                      Time from join to confirmation
                    </div>
                  </div>
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Peak Hours</h4>
                  <div className="flex flex-wrap gap-1">
                    {analytics.peakHours.map(hour => (
                      <Badge key={hour} variant="outline">{hour}</Badge>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Times with highest queue activity
                  </p>
                </div>
                
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Popularity Score</h4>
                  <div className="flex items-center gap-1">
                    <Progress value={analytics.popularityScore * 10} className="h-2" />
                    <span className="text-sm font-medium">{analytics.popularityScore}/10</span>
                  </div>
                  <p className="text-xs text-muted-foreground mt-1">
                    Based on queue size and activity compared to similar events
                  </p>
                </div>
              </div>
            ) : (
              <div className="py-8 text-center">
                <p className="text-muted-foreground">Not enough data to display analytics</p>
              </div>
            )}
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}

interface ParticipantRowProps {
  entry: any;
  isHost: boolean;
  onUpdateStatus: (status: 'confirmed' | 'cancelled') => void;
  hostId: string;
}

function ParticipantRow({ entry, isHost, onUpdateStatus, hostId }: ParticipantRowProps) {
  const { data: profile } = useQuery({
    queryKey: ['profile', entry.user_id],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('profiles')
        .select('display_name, username, avatar_url')
        .eq('id', entry.user_id)
        .single();
      
      if (error) throw error;
      return data;
    }
  });
  
  const displayName = profile?.display_name || profile?.username || 'Unknown user';
  const isActivityHost = entry.user_id === hostId;
  
  return (
    <div className="flex items-center justify-between p-2 rounded-md hover:bg-muted/50">
      <div className="flex items-center gap-2">
        <Badge 
          variant="outline" 
          className={`w-6 h-6 rounded-full p-0 flex items-center justify-center
            ${entry.status === 'confirmed' ? 'bg-green-100 text-green-700' : 
              entry.status === 'pending' ? 'bg-amber-100 text-amber-700' : 
              'bg-red-100 text-red-700'}`
          }
        >
          {entry.position || '-'}
        </Badge>
        
        <span className="text-sm font-medium">
          {displayName}
          {isActivityHost && (
            <Badge variant="secondary" className="ml-2 h-5 py-0">Host</Badge>
          )}
        </span>
        
        {entry.payment_id && (
          <HoverCard>
            <HoverCardTrigger>
              <DollarSign className="h-3 w-3 text-green-600" />
            </HoverCardTrigger>
            <HoverCardContent className="w-auto">
              <p className="text-xs">This participant has paid for the activity</p>
            </HoverCardContent>
          </HoverCard>
        )}
      </div>
      
      {isHost && entry.status === 'pending' && (
        <div className="flex gap-1">
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-7 px-2 text-green-600 hover:text-green-700 hover:bg-green-50"
            onClick={() => onUpdateStatus('confirmed')}
          >
            <CheckCircle className="h-4 w-4" />
          </Button>
          
          <Button 
            variant="ghost" 
            size="sm" 
            className="h-7 px-2 text-red-600 hover:text-red-700 hover:bg-red-50"
            onClick={() => onUpdateStatus('cancelled')}
          >
            <XCircle className="h-4 w-4" />
          </Button>
        </div>
      )}
      
      {isHost && entry.status === 'confirmed' && (
        <div className="flex gap-1">
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            Confirmed
          </Badge>
        </div>
      )}
      
      {!isHost && (
        <div className="text-xs text-muted-foreground">
          {format(new Date(entry.created_at), 'MMM d, h:mm a')}
        </div>
      )}
    </div>
  );
}
