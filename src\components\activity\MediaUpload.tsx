
import React, { useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Button } from "@/components/ui/button";
import { ImagePlus, X, Loader2 } from "lucide-react";
import { cn } from "@/lib/utils";
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface MediaUploadProps {
  onUpload: (urls: string[]) => void;
  maxFiles?: number;
  maxSizeInMB?: number;
  className?: string;
}

export function MediaUpload({ 
  onUpload, 
  maxFiles = 5, 
  maxSizeInMB = 5,
  className 
}: MediaUploadProps) {
  const [uploading, setUploading] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<string[]>([]);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const { toast } = useToast();

  const onDrop = React.useCallback(async (acceptedFiles: File[]) => {
    // Check max files limit
    if (uploadedFiles.length + acceptedFiles.length > maxFiles) {
      toast({
        title: "Too many files",
        description: `You can only upload a maximum of ${maxFiles} files.`,
        variant: "destructive"
      });
      return;
    }

    // Check file size limit
    const maxSizeInBytes = maxSizeInMB * 1024 * 1024;
    const oversizedFiles = acceptedFiles.filter(file => file.size > maxSizeInBytes);
    
    if (oversizedFiles.length > 0) {
      toast({
        title: "File too large",
        description: `Some files exceed the ${maxSizeInMB}MB size limit.`,
        variant: "destructive"
      });
      
      // Remove oversized files
      const validFiles = acceptedFiles.filter(file => file.size <= maxSizeInBytes);
      if (validFiles.length === 0) return;
    }

    setUploading(true);
    try {
      // Initialize progress tracking for all files
      const newProgress: Record<string, number> = {};
      acceptedFiles.forEach(file => {
        newProgress[file.name] = 0;
      });
      setUploadProgress(prev => ({ ...prev, ...newProgress }));

      const uploadPromises = acceptedFiles.map(async (file) => {
        const fileExt = file.name.split('.').pop();
        const fileName = `${Math.random().toString(36).substring(2)}.${fileExt}`;
        const filePath = `${fileName}`;

        // Simulate upload progress for better UX
        const progressInterval = setInterval(() => {
          setUploadProgress(prev => {
            if (prev[file.name] >= 95) {
              clearInterval(progressInterval);
              return prev;
            }
            return { 
              ...prev, 
              [file.name]: Math.min(95, prev[file.name] + Math.random() * 10) 
            };
          });
        }, 200);

        const { data, error } = await supabase.storage
          .from('activity-media')
          .upload(filePath, file);

        clearInterval(progressInterval);

        if (error) {
          setUploadProgress(prev => ({ ...prev, [file.name]: 0 }));
          throw error;
        }

        setUploadProgress(prev => ({ ...prev, [file.name]: 100 }));

        const { data: { publicUrl } } = supabase.storage
          .from('activity-media')
          .getPublicUrl(filePath);

        return publicUrl;
      });

      const urls = await Promise.all(uploadPromises);
      setUploadedFiles(prev => [...prev, ...urls]);
      onUpload(urls);
      
      toast({
        title: "Files uploaded successfully",
        description: `${urls.length} ${urls.length === 1 ? 'file' : 'files'} have been uploaded.`,
      });
    } catch (error: any) {
      console.error('Error uploading files:', error);
      toast({
        title: "Upload failed",
        description: error.message || "There was an error uploading your files.",
        variant: "destructive"
      });
    } finally {
      setUploading(false);
      setUploadProgress({});
    }
  }, [uploadedFiles, onUpload, maxFiles, maxSizeInMB, toast]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'image/*': ['.png', '.jpg', '.jpeg', '.gif', '.webp']
    },
    maxFiles,
    disabled: uploading
  });

  const removeFile = (urlToRemove: string) => {
    setUploadedFiles(prev => prev.filter(url => url !== urlToRemove));
    onUpload(uploadedFiles.filter(url => url !== urlToRemove));
  };

  const getTotalProgress = () => {
    if (Object.keys(uploadProgress).length === 0) return 0;
    
    const totalProgress = Object.values(uploadProgress).reduce((sum, progress) => sum + progress, 0);
    return totalProgress / Object.keys(uploadProgress).length;
  };

  return (
    <div className={className}>
      <div
        {...getRootProps()}
        className={cn(
          "border-2 border-dashed rounded-lg p-6 cursor-pointer text-center transition-colors",
          isDragActive ? "border-primary bg-primary/5" : "border-muted-foreground/20",
          uploading ? "opacity-50 pointer-events-none" : "",
          className
        )}
      >
        <input {...getInputProps()} />
        <ImagePlus className="mx-auto h-8 w-8 text-muted-foreground mb-2" />
        <p className="text-sm text-muted-foreground">
          {uploading ? (
            <span className="flex items-center justify-center">
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Uploading... {Math.round(getTotalProgress())}%
            </span>
          ) : (
            `Drag & drop images here, or click to select (max ${maxFiles} files, ${maxSizeInMB}MB each)`
          )}
        </p>
      </div>

      {uploadedFiles.length > 0 && (
        <div className="mt-4 grid grid-cols-2 sm:grid-cols-3 gap-4">
          {uploadedFiles.map((url) => (
            <div key={url} className="relative group">
              <img
                src={url}
                alt="Uploaded media"
                className="w-full h-32 object-cover rounded-lg"
              />
              <Button
                variant="destructive"
                size="icon"
                className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => removeFile(url)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ))}
        </div>
      )}
      
      <div className="text-sm text-muted-foreground mt-2">
        {uploadedFiles.length > 0 && (
          <p>{uploadedFiles.length} of {maxFiles} files uploaded</p>
        )}
      </div>
    </div>
  );
}
