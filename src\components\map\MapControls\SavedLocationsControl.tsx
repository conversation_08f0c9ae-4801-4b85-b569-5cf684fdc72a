
import React, { useState } from 'react';
import { Star, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { SavedLocationModal } from './SavedLocationModal';
import { SavedLocationsList } from '../SavedLocations/SavedLocationsList';

interface SavedLocationsControlProps {
  currentLocation?: { x: number; y: number };
  onLocationSelect?: (location: { x: number; y: number; name: string }) => void;
  className?: string;
}

export function SavedLocationsControl({
  currentLocation,
  onLocationSelect,
  className,
}: SavedLocationsControlProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Card className={`${className} w-full border shadow-sm bg-background`}>
        <CardContent className="p-4">
          <SavedLocationsList 
            onLocationSelected={(location) => {
              if (onLocationSelect && location.location) {
                onLocationSelect({
                  x: location.location.x,
                  y: location.location.y,
                  name: location.name
                });
              }
            }}
          />
          
          {currentLocation && (
            <Button
              variant="outline"
              size="sm"
              className="w-full mt-4 flex items-center justify-center"
              onClick={() => setIsModalOpen(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              Save Current Location
            </Button>
          )}
        </CardContent>
      </Card>

      {currentLocation && (
        <SavedLocationModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          location={currentLocation}
        />
      )}
    </>
  );
}
