
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from './use-auth';

export interface ServiceProvider {
  id: string;
  user_id: string;
  name: string;
  bio?: string;
  avatar_url?: string;
  location?: string;
  tags: string[];
  hourly_rate?: number;
  rating: number;
  total_reviews: number;
  is_verified: boolean;
  verification_status: 'pending' | 'approved' | 'rejected';
  trust_score: number;
  created_at: string;
  updated_at: string;
}

export interface CreateServiceProviderData {
  name: string;
  bio?: string;
  location?: string;
  tags: string[];
  hourly_rate?: number;
}

export function useServiceProviders() {
  return useQuery({
    queryKey: ['service-providers'],
    queryFn: async (): Promise<ServiceProvider[]> => {
      const { data, error } = await supabase
        .from('service_providers')
        .select('*')
        .order('rating', { ascending: false });
      
      if (error) throw error;
      return data || [];
    }
  });
}

export function useCreateServiceProvider() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateServiceProviderData): Promise<ServiceProvider> => {
      if (!user) throw new Error('User not authenticated');

      const { data: provider, error } = await supabase
        .from('service_providers')
        .insert({
          user_id: user.id,
          ...data,
          rating: 0,
          total_reviews: 0,
          is_verified: false,
          verification_status: 'pending',
          trust_score: 0
        })
        .select()
        .single();

      if (error) throw error;
      return provider;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['service-providers'] });
    }
  });
}

export function useUserServiceProvider() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user-service-provider', user?.id],
    queryFn: async (): Promise<ServiceProvider | null> => {
      if (!user) return null;

      const { data, error } = await supabase
        .from('service_providers')
        .select('*')
        .eq('user_id', user.id)
        .maybeSingle();

      if (error) throw error;
      return data;
    },
    enabled: !!user
  });
}
