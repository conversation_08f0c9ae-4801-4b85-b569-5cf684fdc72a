
/**
 * Utility functions for handling PostgreSQL point data
 */

/**
 * Format coordinates as a PostgreSQL point string
 * @param x X coordinate (longitude)
 * @param y Y coordinate (latitude)
 * @returns PostgreSQL point string in format (x,y)
 */
export function formatPoint(x: number, y: number): string {
  // Ensure x and y are valid numbers
  if (isNaN(x) || isNaN(y)) {
    console.error('Invalid coordinates for formatPoint:', { x, y });
    return '(0,0)'; // Default to origin if invalid
  }
  
  return `(${x},${y})`;
}

/**
 * Check if a point object is valid
 * @param point Object with x and y properties
 * @returns Boolean indicating if the point is valid
 */
export function isValidPoint(point: any): boolean {
  if (!point) return false;
  
  // Check if x and y exist and are valid numbers
  return (
    typeof point.x === 'number' && 
    !isNaN(point.x) && 
    typeof point.y === 'number' && 
    !isNaN(point.y)
  );
}

/**
 * Parse a PostgreSQL point string into an object
 * @param pointString PostgreSQL point string in format (x,y)
 * @returns Object with x and y properties, or null if invalid
 */
export function parsePoint(pointString: string): { x: number, y: number } | null {
  if (!pointString) return null;
  
  // Match the pattern (x,y)
  const match = pointString.match(/\((-?\d+\.?\d*),(-?\d+\.?\d*)\)/);
  
  if (match && match.length === 3) {
    const x = parseFloat(match[1]);
    const y = parseFloat(match[2]);
    
    if (!isNaN(x) && !isNaN(y)) {
      return { x, y };
    }
  }
  
  console.error('Failed to parse point string:', pointString);
  return null;
}
