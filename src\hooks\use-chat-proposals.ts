
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface ChatProposal {
  id: string;
  conversation_id: string;
  sender_id: string;
  recipient_id: string;
  activity_id?: string; 
  service_id?: string;
  proposal_status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  expires_at?: string;
  response_at?: string;
  content?: string;
  metadata?: any;
}

export function useChatProposals(conversationId: string | undefined) {
  const queryClient = useQueryClient();

  // This is a mock implementation since we don't have a chat_proposals table
  const query = useQuery({
    queryKey: ['chat-proposals', conversationId],
    queryFn: async (): Promise<ChatProposal[]> => {
      if (!conversationId) return [];

      // Use messages with a specific structure as proposals
      const { data, error } = await supabase
        .from('messages')
        .select('*')
        .eq('conversation_id', conversationId)
        .contains('location_data', { is_proposal: true })
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error fetching chat proposals:', error);
        return [];
      }

      // Map to our proposal format
      return (data || []).map(message => {
        const proposalData = message.location_data as any || {}; // Using location_data to store proposal info
        
        return {
          id: message.id,
          conversation_id: message.conversation_id,
          sender_id: message.sender_id,
          recipient_id: message.recipient_id || '',
          activity_id: proposalData.activity_id || undefined,
          service_id: proposalData.service_id || undefined,
          proposal_status: proposalData.status || 'pending',
          created_at: message.created_at,
          expires_at: proposalData.expires_at,
          response_at: proposalData.response_at,
          content: message.content,
          metadata: proposalData
        };
      });
    },
    enabled: !!conversationId
  });

  const createProposal = useMutation({
    mutationFn: async ({
      activityId,
      serviceId,
      recipientId,
      content
    }: {
      activityId?: string;
      serviceId?: string;
      recipientId: string;
      content?: string;
    }): Promise<ChatProposal> => {
      if (!conversationId) {
        throw new Error('No conversation ID provided');
      }

      const user = await supabase.auth.getUser();
      const senderId = user.data.user?.id;

      if (!senderId) {
        throw new Error('User not authenticated');
      }

      // Create a message with proposal data
      const { data, error } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: senderId,
          recipient_id: recipientId,
          content: content || 'New proposal',
          location_data: {
            is_proposal: true,
            activity_id: activityId,
            service_id: serviceId,
            status: 'pending',
            expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 1 week expiry
          },
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()
        .single();

      if (error) {
        throw error;
      }

      const proposalData = data.location_data as any;

      return {
        id: data.id,
        conversation_id: data.conversation_id,
        sender_id: data.sender_id,
        recipient_id: data.recipient_id || recipientId,
        activity_id: proposalData.activity_id,
        service_id: proposalData.service_id,
        proposal_status: proposalData.status || 'pending',
        created_at: data.created_at,
        expires_at: proposalData.expires_at,
        content: data.content,
        metadata: proposalData
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['chat-proposals', conversationId]
      });
    }
  });

  const updateProposalStatus = useMutation({
    mutationFn: async ({
      proposalId,
      status
    }: {
      proposalId: string;
      status: 'accepted' | 'rejected';
    }): Promise<ChatProposal> => {
      // First get the existing message
      const { data: existingMessage, error: fetchError } = await supabase
        .from('messages')
        .select('*')
        .eq('id', proposalId)
        .single();

      if (fetchError) throw fetchError;

      const locationData = existingMessage.location_data as any || {};

      // Update the message with new status
      const { data, error } = await supabase
        .from('messages')
        .update({
          location_data: {
            ...locationData,
            status,
            response_at: new Date().toISOString()
          },
          updated_at: new Date().toISOString()
        })
        .eq('id', proposalId)
        .select()
        .single();

      if (error) throw error;

      const proposalData = data.location_data as any;

      return {
        id: data.id,
        conversation_id: data.conversation_id,
        sender_id: data.sender_id,
        recipient_id: data.recipient_id || '',
        activity_id: proposalData.activity_id,
        service_id: proposalData.service_id,
        proposal_status: proposalData.status,
        created_at: data.created_at,
        expires_at: proposalData.expires_at,
        response_at: proposalData.response_at,
        content: data.content,
        metadata: proposalData
      };
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ['chat-proposals', conversationId]
      });
    }
  });

  return {
    proposals: query.data || [],
    isLoading: query.isLoading,
    createProposal,
    updateProposalStatus
  };
}
