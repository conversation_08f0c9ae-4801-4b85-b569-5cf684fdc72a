import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import type { UserProfile } from '@/types/user-profile';

export type { UserProfile };

export type Profile = UserProfile;

export function useProfile(userIdOrUsername: string | undefined) {
  return useQuery({
    queryKey: ['profile', userIdOrUsername],
    queryFn: async (): Promise<UserProfile | null> => {
      if (!userIdOrUsername) {
        console.log('useProfile: No userIdOrUsername provided');
        return null;
      }

      console.log(`useProfile: Looking up profile for: ${userIdOrUsername}`);

      // Check if the input is a UUID (user ID) or a username
      const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(userIdOrUsername);
      console.log(`useProfile: Input is ${isUUID ? 'a UUID' : 'a username'}`);

      // Query by ID or username based on the input format
      const query = supabase
        .from('profiles')
        .select('*');

      if (isUUID) {
        query.eq('id', userIdOrUsername);
        console.log(`useProfile: Querying by ID: ${userIdOrUsername}`);
      } else {
        // Try to find the profile by username (case-insensitive)
        // First, try exact match
        console.log(`useProfile: Querying by username (case-insensitive): ${userIdOrUsername}`);

        // Use a more robust approach - first try exact match, then try case-insensitive
        const { data: exactMatch, error: exactMatchError } = await supabase
          .from('profiles')
          .select('id')
          .eq('username', userIdOrUsername)
          .single();

        if (exactMatch) {
          console.log(`useProfile: Found exact username match: ${exactMatch.id}`);
          query.eq('id', exactMatch.id);
        } else {
          console.log(`useProfile: No exact match found, trying case-insensitive match`);
          query.ilike('username', userIdOrUsername);
        }
      }

      const { data, error } = await query.single();

      if (error) {
        // Log detailed error information
        console.error('Error fetching profile:', error);

        // Check if it's a "not found" error
        if (error.code === 'PGRST116') {
          console.log(`useProfile: Profile not found for ${userIdOrUsername}`);
          return { notFound: true } as any; // Special marker for not found
        }

        // Other database errors
        console.log(`useProfile: Database error for ${userIdOrUsername}:`, error.message);
        return null;
      }

      // Check if the profile is private and the user is not logged in
      const { data: session } = await supabase.auth.getSession();
      const currentUserId = session?.session?.user?.id;

      if (!isUUID && data.is_profile_public === false && (!currentUserId || currentUserId !== data.id)) {
        console.log(`useProfile: Profile is private and user is not the owner: ${userIdOrUsername}`);
        return { isPrivate: true } as any; // Special marker for private profile
      }

      // Handle location data safely with type checking
      let locationData = undefined;
      if (data.location && typeof data.location === 'object') {
        const loc = data.location as any;
        locationData = {
          x: Number(loc.x ?? loc[0] ?? 0),
          y: Number(loc.y ?? loc[1] ?? 0)
        };
      }

      // Handle default_location data safely with type checking
      let defaultLocationData = undefined;
      if (data.default_location && typeof data.default_location === 'object') {
        const loc = data.default_location as any;
        defaultLocationData = {
          x: Number(loc.x ?? loc[0] ?? 0),
          y: Number(loc.y ?? loc[1] ?? 0),
          address: loc.address
        };
      }

      // Parse favorite_locations from JSON if it exists
      let favoriteLocations = undefined;
      if (data.favorite_locations) {
        try {
          // If it's already an array, use it; otherwise parse it
          favoriteLocations = Array.isArray(data.favorite_locations)
            ? data.favorite_locations
            : typeof data.favorite_locations === 'string'
              ? JSON.parse(data.favorite_locations)
              : data.favorite_locations;
        } catch (e) {
          console.error('Error parsing favorite_locations:', e);
          favoriteLocations = [];
        }
      }

      // Return the profile with properly typed data
      return {
        ...data,
        location: locationData,
        default_location: defaultLocationData,
        favorite_locations: favoriteLocations as UserProfile['favorite_locations'],
        followers_count: 0, // Default to 0 until follows table is properly set up
        following_count: 0, // Default to 0 until follows table is properly set up
        onboarding_completed: data.onboarding_completed || false
      };
    },
    enabled: !!userIdOrUsername,
    staleTime: 5 * 60 * 1000,
  });
}
