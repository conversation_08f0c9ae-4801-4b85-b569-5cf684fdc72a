
import React, { useState, useEffect } from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from '@/components/ui/form';
import { UseFormReturn } from 'react-hook-form';
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';

interface ActivityLocationPickerProps {
  form: UseFormReturn<any>;
}

export function ActivityLocationPicker({ form }: ActivityLocationPickerProps) {
  const [isMapReady, setIsMapReady] = useState(false);
  
  // Handle location change from direct input
  const handleLocationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    // Parse latitude,longitude from input
    try {
      const [latitude, longitude] = e.target.value.split(',').map(Number);
      if (!isNaN(latitude) && !isNaN(longitude)) {
        const locationValue = { 
          latitude, 
          longitude,
          x: longitude,
          y: latitude 
        };
        
        form.setValue('location', locationValue);
      }
    } catch (error) {
      console.error('Error parsing coordinates:', error);
    }
  };

  // Default location (for demo purposes)
  useEffect(() => {
    if (!form.getValues().location.latitude && !form.getValues().location.longitude) {
      // Set a default location (San Francisco)
      const defaultLocation = { 
        latitude: 37.7749, 
        longitude: -122.4194,
        x: -122.4194,
        y: 37.7749
      };
      
      form.setValue('location', defaultLocation);
    }
  }, [form]);

  // Get the current location value
  const locationValue = form.getValues().location;

  return (
    <div className="space-y-2">
      <Card>
        <CardContent className="p-4">
          {/* Simple input for demo purposes - would be replaced with interactive map */}
          <Input
            type="text"
            placeholder="Latitude,Longitude (e.g. 37.7749,-122.4194)"
            value={locationValue ? `${locationValue.latitude},${locationValue.longitude}` : ''}
            onChange={handleLocationChange}
            className="mb-2"
          />
          <p className="text-sm text-muted-foreground">
            Enter coordinates for the activity location
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
