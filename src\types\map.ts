export interface UserLocation {
  x: number;
  y: number;
}

export interface UnifiedMapUser {
  user_id: string;
  id?: string; // Added back for backward compatibility
  username: string;
  display_name?: string;
  avatar_url?: string;
  location: UserLocation;
  last_active: string;
  bio?: string;
  is_verified?: boolean;
  is_online?: boolean;
  created_at?: string;
  updated_at?: string;
  last_seen_at?: string;
  online_at?: string;
  profile_color?: string;
  interests?: string[];
  distance?: number; // Changed from string to number
}

export interface DistanceParams {
  from: { lat: number; lng: number };
  to: { lat: number; lng: number };
}

export function formatDistance(params: DistanceParams | number): string {
  // If the input is a number (direct distance value)
  if (typeof params === 'number') {
    const distance = params;
    if (distance < 0.1) {
      return 'Very close';
    } else if (distance < 1) {
      return `${Math.round(distance * 1000)} meters away`;
    } else {
      return `${distance.toFixed(1)} km away`;
    }
  } 
  // If the input is a DistanceParams object
  else {
    const { from, to } = params;
    const distance = calculateDistance(from.lat, from.lng, to.lat, to.lng);
    
    if (distance < 0.1) {
      return 'Very close';
    } else if (distance < 1) {
      return `${Math.round(distance * 1000)} meters away`;
    } else {
      return `${distance.toFixed(1)} km away`;
    }
  }
}

// Renamed from calculateHaversineDistance to calculateDistance for consistency
function calculateDistance(
  lat1: number,
  lon1: number,
  lat2: number,
  lon2: number
): number {
  const R = 6371; // Radius of the Earth in km
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  const distance = R * c; // Distance in km
  
  return distance;
}

// Re-add the utility functions that were removed
export const calculateHaversineDistance = calculateDistance;

export function lngLatToXY(lng: number, lat: number): UserLocation {
  return { x: lng, y: lat };
}

export function xyToLngLat(x: number, y: number): [number, number] {
  return [x, y];
}

export function mapPresenceToUnifiedUser(presenceData: any): UnifiedMapUser {
  return {
    id: presenceData.id || presenceData.user_id,
    user_id: presenceData.user_id,
    username: presenceData.username || '',
    display_name: presenceData.display_name,
    avatar_url: presenceData.avatar_url,
    bio: presenceData.bio,
    is_verified: presenceData.is_verified,
    location: presenceData.location || { x: 0, y: 0 },
    last_active: presenceData.last_active || presenceData.updated_at || new Date().toISOString(),
    created_at: presenceData.created_at || new Date().toISOString(),
    updated_at: presenceData.updated_at || new Date().toISOString(),
    is_online: presenceData.is_online || false,
    last_seen_at: presenceData.last_seen_at,
    online_at: presenceData.online_at,
    profile_color: presenceData.profile_color,
    interests: presenceData.interests,
    distance: presenceData.distance,
  };
}

// Add the MapLocation type needed by ActivityCreationModal
export interface MapLocation {
  lat: number;
  lng: number;
  address?: string;
  zoom?: number;
}
