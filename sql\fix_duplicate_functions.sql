-- Fix duplicate check_onboarding_completed functions
-- First, drop any triggers that depend on the functions

-- Drop the trigger that depends on check_onboarding_completed()
DROP TRIGGER IF EXISTS set_onboarding_completed_trigger ON public.profiles;

-- Drop function with UUID parameter (with CASCAD<PERSON> to drop any dependencies)
DROP FUNCTION IF EXISTS public.check_onboarding_completed(UUID) CASCADE;

-- Drop function with text parameter (if it exists)
DROP FUNCTION IF EXISTS public.check_onboarding_completed(text) CASCADE;

-- Drop function with no parameters (if it exists)
DROP FUNCTION IF EXISTS public.check_onboarding_completed() CASCADE;

-- Create a new version of the function that handles text[] arrays correctly
CREATE OR REPLACE FUNCTION public.check_onboarding_completed(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  profile_record RECORD;
  has_vibes BOOLEAN;
BEGIN
  -- Get the profile record
  SELECT * INTO profile_record
  FROM public.profiles
  WHERE id = user_id;

  -- If no profile found, return false
  IF profile_record IS NULL THEN
    RETURN FALSE;
  END IF;

  -- If onboarding_completed is already true, return true
  IF profile_record.onboarding_completed = TRUE THEN
    RETURN TRUE;
  END IF;

  -- Check if vibes array has elements
  has_vibes := profile_record.vibes IS NOT NULL AND
               array_length(profile_record.vibes, 1) > 0;

  -- Check all required fields
  RETURN (
    profile_record.display_name IS NOT NULL AND
    profile_record.avatar_url IS NOT NULL AND
    profile_record.birthday IS NOT NULL AND
    profile_record.gender IS NOT NULL AND
    profile_record.default_location IS NOT NULL AND
    profile_record.location_permission_granted = TRUE AND
    has_vibes
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.check_onboarding_completed(UUID) TO authenticated;

-- Comment explaining the function
COMMENT ON FUNCTION public.check_onboarding_completed(UUID) IS 'Checks if a user has completed onboarding by verifying all required fields are present.';

-- Create a new function with a different name for the client-side code to use
CREATE OR REPLACE FUNCTION public.verify_onboarding_status(user_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN public.check_onboarding_completed(user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.verify_onboarding_status(UUID) TO authenticated;

-- Comment explaining the function
COMMENT ON FUNCTION public.verify_onboarding_status(UUID) IS 'Alias for check_onboarding_completed to avoid function name conflicts.';

-- Recreate the trigger that was dropped
CREATE OR REPLACE FUNCTION public.trigger_set_onboarding_completed()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if all required fields are present
  NEW.onboarding_completed := public.check_onboarding_completed(NEW.id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the trigger
CREATE TRIGGER set_onboarding_completed_trigger
BEFORE INSERT OR UPDATE ON public.profiles
FOR EACH ROW
EXECUTE FUNCTION public.trigger_set_onboarding_completed();
