
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { UseFormReturn } from "react-hook-form";
import { OnboardingFormValues } from "@/types/onboarding";
import { ProfilePhotoUploader } from '@/components/shared/ProfilePhotoUploader';

interface BasicInfoStepProps {
  form: UseFormReturn<OnboardingFormValues>;
  previewUrl: string | null;
  handleThumbnailClick: () => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  user: any;
  // Add these properties to match what OnboardingModal is passing
  onSubmit: (stepData?: Partial<OnboardingFormValues>) => Promise<void>;
  initialValues: Partial<OnboardingFormValues>;
  isLoading: boolean;
}

export function BasicInfoStep({
  form,
  previewUrl,
  handleThumbnailClick,
  fileInputRef,
  handleFileChange,
  user,
  onSubmit,
  initialValues,
  isLoading
}: BasicInfoStepProps) {
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-bold text-gray-800">Create Your Profile</h2>
        <p className="text-sm text-gray-600 mt-1">
          Let's start with the basics <span className="text-red-500">*</span>
        </p>
        <div className="mt-3 inline-flex items-center justify-center px-3 py-1 rounded-full bg-purple-50 border border-purple-100 shadow-sm">
          <span className="text-xs font-medium text-primary-purple">Required Information</span>
        </div>
      </div>

      {/* Profile Photo Upload - Optional if already set */}
      <FormField
        control={form.control}
        name="avatar_url"
        render={({field}) => {
          return (
            <FormItem className="mb-8">
              <div className="text-center mb-2">
                <FormLabel className="text-base font-semibold bg-gradient-to-r from-primary-purple to-primary-deep-purple bg-clip-text text-transparent">
                  Profile Photo <span className="text-red-500">*</span>
                </FormLabel>
                <p className="text-xs text-muted-foreground mt-1">
                  {!!user?.user_metadata?.avatar_url || !!field.value
                    ? "You already have a profile photo. You can keep it or choose a new one."
                    : "Choose a profile picture that represents you"}
                </p>
              </div>
              <FormControl>
                <ProfilePhotoUploader
                  previewUrl={previewUrl || field.value || user?.user_metadata?.avatar_url}
                  onPhotoChange={(file, dataUrl) => {
                    console.log("BasicInfoStep - Photo changed, setting avatar_url to data URL");

                    // Set the form value to the data URL
                    form.setValue('avatar_url', dataUrl, {
                      shouldValidate: true,
                      shouldDirty: true,
                      shouldTouch: true
                    });

                    // Log the current form value to verify it was set
                    console.log("BasicInfoStep - Current avatar_url value:", form.getValues('avatar_url')?.substring(0, 50) + "...");

                    // Trigger validation
                    form.trigger('avatar_url');

                    // Call the parent handler if it exists
                    if (handleFileChange && fileInputRef?.current) {
                      // Create a synthetic event to pass to the parent handler
                      const syntheticEvent = {
                        target: {
                          files: [file]
                        }
                      } as unknown as React.ChangeEvent<HTMLInputElement>;

                      handleFileChange(syntheticEvent);
                      console.log("BasicInfoStep - Called parent handleFileChange");
                    }
                  }}
                  size="lg"
                  required={true}
                  id="profile-photo"
                  name="avatar_url"
                  label="Profile Photo"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          );
        }}
      />

      <div className="py-4">
        <FormField
        control={form.control}
        name="display_name"
        render={({ field }) => (
          <FormItem>
            <FormLabel htmlFor="display-name">Display Name</FormLabel>
            <FormControl>
              <Input id="display-name" name="display_name" placeholder="Your name" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      </div>
    </div>
  );
}
