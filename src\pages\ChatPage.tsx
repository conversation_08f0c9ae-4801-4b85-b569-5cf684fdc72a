
import { useEffect } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { ChatSidebar } from '@/components/chat/ChatSidebar';
import { ChatContainer } from '@/components/chat/ChatContainer';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { Megaphone } from 'lucide-react';
import { useProfile } from '@/hooks/use-profile';
import { useAdminConversation } from '@/hooks/use-buddy-admin-conversation';
import { useQueryClient } from '@tanstack/react-query';

export default function ChatPage() {
  const { adminConversation } = useAdminConversation();
  const { data: profile } = useProfile(undefined);
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const queryClient = useQueryClient();

  // Get conversation ID from URL params
  const conversationId = searchParams.get('conversation') || '';
  const activityId = searchParams.get('activity') || undefined;

  // Check if user is admin
  const isAdmin = profile?.is_admin === true;

  // Set up polling for chat data instead of relying on WebSockets
  useEffect(() => {
    if (conversationId) {
      const intervalId = setInterval(() => {
        // Invalidate queries to refresh data
        queryClient.invalidateQueries({
          queryKey: ['chat-messages', conversationId],
        });
        queryClient.invalidateQueries({
          queryKey: ['chat-conversations'],
        });
      }, 3000); // Poll every 3 seconds

      return () => clearInterval(intervalId);
    }
  }, [searchParams, queryClient, conversationId]);

  // If no conversation is selected and admin conversation exists, select it by default
  useEffect(() => {
    const userId = searchParams.get('user');

    // Only redirect if no conversation parameters are present and admin conversation exists
    if (!conversationId && !activityId && !userId && adminConversation?.id) {
      console.log('No conversation selected, redirecting to admin conversation');
      navigate(`/chat?conversation=${adminConversation.id}`, { replace: true });
    }
  }, [adminConversation, searchParams, navigate, conversationId, activityId]);

  return (
    <MainLayout>
      {/* Admin Message Panel - Only shown to admin users */}
      {isAdmin && adminConversation && (
        <div className="flex justify-end px-4 mb-4 mt-2">
          <div className="bg-blue-50 p-3 rounded-md border border-blue-200 shadow-sm">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium text-blue-800">Admin Panel</h3>
              <div className="text-xs text-green-600 bg-green-50 px-2 py-1 rounded border border-green-200">
                Admin Chat Active
              </div>
            </div>
            <div className="flex items-center gap-2">
              <div className="flex-1">
                <Button
                  variant="default"
                  size="sm"
                  className="bg-blue-600 hover:bg-blue-700 text-white w-full"
                  onClick={() => {
                    if (adminConversation) {
                      navigate(`/chat?conversation=${adminConversation.id}`);
                    }
                  }}
                >
                  <Megaphone className="mr-2 h-4 w-4" />
                  Send Admin Announcement
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="h-[calc(100vh-9rem)] flex overflow-hidden bg-white rounded-lg shadow-md">
        <ChatSidebar />
        <div className="flex-1 flex flex-col">
          {conversationId ? (
            <ChatContainer conversationId={conversationId} activityId={activityId} />
          ) : (
            <div className="flex-1 flex items-center justify-center text-muted-foreground">
              <p>Select a conversation to start messaging</p>
            </div>
          )}
        </div>
      </div>
    </MainLayout>
  );
}
