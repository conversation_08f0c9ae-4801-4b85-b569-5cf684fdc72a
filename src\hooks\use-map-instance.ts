
import { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';

interface MapConfig {
  mapboxToken: string;
  mapStyle?: string;
  enablePitch?: boolean;
  enableRotation?: boolean;
  showBuildings?: boolean;
}

export function useMapInstance({
  mapboxToken,
  mapStyle = 'light-v10',
  enablePitch = true,
  enableRotation = true,
  showBuildings = true,
}: MapConfig) {
  const mapInstanceRef = useRef<mapboxgl.Map | null>(null);
  const containerRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    if (!containerRef.current || !mapboxToken || mapInstanceRef.current) return;

    mapboxgl.accessToken = mapboxToken;
    
    const map = new mapboxgl.Map({
      container: containerRef.current,
      style: `mapbox://styles/mapbox/${mapStyle}`,
      center: [0, 20],
      zoom: 2,
      pitch: enablePitch ? 45 : 0,
      minZoom: 1.5,
      maxZoom: 22,
      attributionControl: false,
      dragRotate: enableRotation,
      touchPitch: enablePitch,
      projection: 'globe',
      antialias: true, // Enable antialiasing for smoother rendering
    });

    map.addControl(new mapboxgl.AttributionControl(), 'bottom-left');
    map.addControl(new mapboxgl.ScaleControl(), 'bottom-right');

    const touchZoomRotate = map.touchZoomRotate;
    if (touchZoomRotate) {
      touchZoomRotate.enable();
      if (!enableRotation) {
        touchZoomRotate.disableRotation();
      }
    }

    map.keyboard.enable();
    map.scrollZoom.setWheelZoomRate(1/300); // Smoother zoom with mouse wheel

    map.on('style.load', () => {
      if (!map) return;

      // Enhanced atmospheric effect with soft mint colors
      map.setFog({
        'color': 'rgb(236, 245, 240)', // Light mint tinted fog
        'high-color': 'rgb(225, 240, 235)', // Subtle green-blue sky color
        'horizon-blend': 0.1, // Smoother transition
        'space-color': 'rgb(220, 240, 235)', // Slightly greener space color
        'star-intensity': 0.05 // Very subtle stars
      });
      
      // Set soft lighting with 45-degree angle
      map.setLight({
        anchor: 'viewport',
        color: 'white',
        intensity: 0.4,
        position: [1.5, 180, 45] // x, y, z position with 45-degree angle
      });

      // Set terrain if available
      try {
        if (map.getSource('mapbox-dem')) {
          map.setTerrain({
            source: 'mapbox-dem',
            exaggeration: 1.5 // Slight exaggeration for better visual effect
          });
        }
      } catch (error) {
        console.log('Could not set terrain, continuing without it');
      }

      if (showBuildings) {
        const layers = map.getStyle().layers;
        let firstSymbolId;
        for (const layer of layers!) {
          if (layer.type === 'symbol') {
            firstSymbolId = layer.id;
            break;
          }
        }
        
        // Enhanced 3D building layer with custom styling
        map.addLayer({
          'id': '3d-buildings',
          'source': 'composite',
          'source-layer': 'building',
          'filter': ['==', 'extrude', 'true'],
          'type': 'fill-extrusion',
          'minzoom': 14,
          'paint': {
            'fill-extrusion-color': [
              'interpolate',
              ['linear'],
              ['get', 'height'],
              0, '#F5F5F0',    // Off-white/ivory base for low buildings
              50, '#F0F0EB',
              100, '#EAEAE5',
              200, '#E5E5E0'   // Slightly darker for taller buildings
            ],
            'fill-extrusion-height': [
              'interpolate', ['linear'], ['zoom'],
              14, 0,
              16, ['get', 'height']
            ],
            'fill-extrusion-base': [
              'interpolate', ['linear'], ['zoom'],
              14, 0,
              16, ['get', 'min_height']
            ],
            'fill-extrusion-opacity': [
              'interpolate', ['linear'], ['zoom'],
              14, 0,
              15, 0.3,
              16, 0.6,
              17, 0.85  // Max 85% opacity for better blend with background
            ],
            'fill-extrusion-ambient-occlusion-intensity': 0.35, // Reduced for softer shadows
            'fill-extrusion-ambient-occlusion-radius': 3
          }
        }, firstSymbolId);
        
        // Add subtle patterns to buildings
        try {
          map.addLayer({
            'id': 'building-patterns',
            'source': 'composite',
            'source-layer': 'building',
            'type': 'fill-extrusion',
            'filter': ['==', 'extrude', 'true'],
            'minzoom': 15.5,
            'paint': {
              'fill-extrusion-opacity': 0.08, // Very subtle pattern
              'fill-extrusion-height': ['get', 'height'],
              'fill-extrusion-base': ['get', 'min_height'],
              'fill-extrusion-vertical-gradient': true
            }
          }, firstSymbolId);
        } catch (error) {
          console.log('Could not add building patterns layer, continuing without it');
        }
        
        // Add ground vegetation layer
        try {
          map.addLayer({
            'id': 'vegetation-layer',
            'type': 'fill',
            'source': 'composite',
            'source-layer': 'landuse',
            'filter': [
              'in', 
              'class', 
              'park', 'wood', 'grass', 'forest', 'garden'
            ],
            'paint': {
              'fill-color': '#E8F5E9', // Light mint green
              'fill-opacity': 0.85 // 85% transparency
            }
          }, 'building-patterns');
        } catch (error) {
          console.log('Could not add vegetation layer, continuing without it');
        }
      }
    });

    mapInstanceRef.current = map;

    return () => {
      map.remove();
      mapInstanceRef.current = null;
    };
  }, [mapboxToken, enablePitch, enableRotation, showBuildings]);

  // Only update style when it changes
  useEffect(() => {
    const map = mapInstanceRef.current;
    if (map && map.isStyleLoaded()) {
      map.setStyle(`mapbox://styles/mapbox/${mapStyle}`);
    }
  }, [mapStyle]);

  return {
    mapInstanceRef,
    containerRef
  };
}
