
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

export interface Gig {
  id: string;
  title: string;
  description: string;
  short_description?: string;
  price_starting: number;
  delivery_time_days: number;
  category_id?: string;
  gig_type: 'service' | 'digital' | 'consultation';
  skill_level: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  requirements_from_buyer?: string;
  gallery_images: string[];
  gallery_videos: string[];
  packages: any[];
  add_ons: any[];
  faq: any[];
  user_id: string;
  provider_id?: string;
  hourly_rate?: number;
  rating: number;
  total_orders: number;
  is_featured: boolean;
  availability: string;
  created_at: string;
  updated_at: string;
  // Add missing properties for GigCard
  average_rating: number;
  total_reviews: number;
  provider?: {
    id: string;
    business_name?: string;
    user?: {
      id: string;
      display_name?: string;
      avatar_url?: string;
      username?: string;
    };
  };
}

interface CreateGigData {
  title: string;
  description: string;
  short_description?: string;
  price_starting: number;
  delivery_time_days: number;
  category_id?: string;
  gig_type: 'service' | 'digital' | 'consultation';
  skill_level: 'beginner' | 'intermediate' | 'advanced';
  tags: string[];
  requirements_from_buyer?: string;
  gallery_images: string[];
  gallery_videos: string[];
  packages: any[];
  add_ons: any[];
  faq: any[];
}

export function useGigs(categoryId?: string, searchQuery?: string) {
  return useQuery({
    queryKey: ['gigs', categoryId, searchQuery],
    queryFn: async (): Promise<Gig[]> => {
      let query = supabase
        .from('gigs')
        .select(`
          *,
          provider:service_providers(
            id,
            name,
            user:profiles!service_providers_user_id_fkey(
              id,
              display_name,
              avatar_url,
              username
            )
          ),
          category:categories(id, name, icon, type)
        `)
        .order('created_at', { ascending: false });

      // Apply category filter
      if (categoryId) {
        query = query.eq('category_id', categoryId);
      }

      // Apply search filter
      if (searchQuery) {
        query = query.or(`title.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`);
      }

      const { data, error } = await query;

      if (error) throw error;

      // Map database fields to interface fields
      return (data || []).map(gig => ({
        ...gig,
        delivery_time_days: gig.delivery_time || 1,
        gig_type: 'service' as const,
        skill_level: gig.experience_level || 'intermediate',
        gallery_images: gig.media_urls || [],
        gallery_videos: [],
        packages: [],
        add_ons: [],
        faq: [],
        average_rating: Number(gig.rating) || 0,
        total_reviews: gig.total_orders || 0
      })) as Gig[];
    }
  });
}

export function useCreateGig() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (gigData: CreateGigData) => {
      if (!user) throw new Error('User not authenticated');

      // First, get the user's service provider ID
      const { data: provider, error: providerError } = await supabase
        .from('service_providers')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (providerError || !provider) {
        throw new Error('You must be a registered service provider to create gigs');
      }

      const { data, error } = await supabase
        .from('gigs')
        .insert({
          user_id: user.id,
          provider_id: provider.id,
          title: gigData.title,
          description: gigData.description,
          short_description: gigData.short_description,
          price_starting: gigData.price_starting,
          delivery_time: gigData.delivery_time_days,
          category_id: gigData.category_id,
          experience_level: gigData.skill_level,
          tags: gigData.tags,
          media_urls: gigData.gallery_images,
          requirements: gigData.requirements_from_buyer,
          what_you_get: gigData.description, // Use description as what_you_get for now
          availability: 'available',
          is_featured: false,
          rating: 0,
          total_orders: 0,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select(`
          *,
          provider:service_providers(
            id,
            name,
            user:profiles!service_providers_user_id_fkey(
              id,
              display_name,
              avatar_url,
              username
            )
          )
        `)
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['gigs'] });
      toast({
        title: 'Gig Created',
        description: 'Your gig has been created successfully!',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error Creating Gig',
        description: error.message || 'Failed to create gig',
        variant: 'destructive'
      });
    }
  });
}
