import { useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

export function useServiceBookingPayment() {
  const { user } = useAuth();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (bookingId: string) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase.functions.invoke('create-checkout', {
        body: { 
          type: 'service_booking', 
          bookingId: bookingId 
        }
      });
      
      if (error) throw error;
      if (!data?.url) throw new Error('No checkout URL returned');
      
      return data.url;
    },
    onSuccess: (url) => {
      window.location.href = url;
    },
    onError: (error: Error) => {
      toast({
        title: 'Payment Failed',
        description: error.message,
        variant: 'destructive'
      });
    }
  });
}
