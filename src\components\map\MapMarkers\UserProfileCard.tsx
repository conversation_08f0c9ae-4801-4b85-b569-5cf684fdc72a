
import React from 'react';
import { UnifiedMapUser, calculateHaversineDistance, formatDistance } from '@/types/map';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardHeader,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  BadgeCheck,
  MessageCircle,
  UserPlus,
  MapPin,
  X,
  Calendar,
  Clock
} from "lucide-react";
import { formatDistanceToNow } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface UserProfileCardProps {
  user: UnifiedMapUser;
  onClose: () => void;
  myLocation?: { x: number; y: number };
  onConnect?: () => void;
  onMessage?: () => void;
}

export default function UserProfileCard({
  user,
  onClose,
  myLocation,
  onConnect,
  onMessage,
}: UserProfileCardProps) {
  const userLocation = user.location || { x: 0, y: 0 };
  const userBio = user.bio || "No bio available";
  const displayName = user.display_name || user.username || `User ${user.user_id?.substring?.(0, 5)}`;
  const avatarUrl = user.avatar_url;
  const isVerified = user.is_verified === true;
  const isOnline = user.is_online;
  
  const distance = React.useMemo(() => {
    if (myLocation && user.location) {
      const dist = calculateHaversineDistance(
        user.location.y,
        user.location.x,
        myLocation.y,
        myLocation.x
      );
      return formatDistance(dist);
    }
    return null;
  }, [myLocation, user.location]);
  
  const lastSeenText = React.useMemo(() => {
    if (isOnline) return 'Online now';
    
    const lastSeen = user.last_seen_at || user.online_at || user.updated_at;
    if (!lastSeen) return 'Unknown';
    
    try {
      return formatDistanceToNow(new Date(lastSeen), { addSuffix: true });
    } catch {
      return 'Unknown';
    }
  }, [isOnline, user.last_seen_at, user.online_at, user.updated_at]);
  
  const profileColor = user.profile_color || generateProfileColor(user.user_id);
  
  return (
    <Card className="w-80 shadow-lg animate-in fade-in zoom-in duration-300 bg-background/95 backdrop-blur-sm">
      <CardHeader className="pb-2 relative">
        <Button
          variant="ghost"
          size="icon"
          className="absolute right-2 top-2 h-7 w-7"
          onClick={onClose}
        >
          <X size={16} />
        </Button>
        
        <div className="flex items-start gap-3">
          <Avatar className="h-16 w-16 border-2" style={{ borderColor: profileColor }}>
            <AvatarImage
              src={avatarUrl || `https://i.pravatar.cc/150?u=${user.user_id}`}
              alt={displayName}
            />
            <AvatarFallback style={{ backgroundColor: profileColor }}>
              {getInitials(displayName)}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1 pt-1">
            <div className="flex items-center gap-1">
              <h3 className="font-semibold text-lg truncate max-w-[160px]">
                {displayName}
              </h3>
              {isVerified && (
                <BadgeCheck className="h-4 w-4 text-blue-500 flex-shrink-0" />
              )}
            </div>
            
            <div className="flex items-center text-sm text-muted-foreground mt-0.5">
              <div className="flex items-center gap-1">
                <Clock className="h-3 w-3" />
                <span>{lastSeenText}</span>
              </div>
            </div>
            
            {distance && (
              <div className="flex items-center text-sm text-muted-foreground mt-0.5">
                <MapPin className="h-3 w-3 mr-1" />
                <span>{distance}</span>
              </div>
            )}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="pb-3">
        <div className="text-sm mt-1">
          <p className="line-clamp-3">{userBio}</p>
        </div>
        
        {user.updated_at && (
          <div className="flex items-center mt-3 text-xs text-muted-foreground">
            <Calendar className="h-3 w-3 mr-1" />
            <span>Joined {formatDistanceToNow(new Date(user.created_at), { addSuffix: true })}</span>
          </div>
        )}
        
        {user.interests && user.interests.length > 0 && (
          <div className="flex flex-wrap gap-1 mt-3">
            {user.interests.map((interest, i) => (
              <Badge variant="outline" key={i} className="text-[10px]">
                {interest}
              </Badge>
            ))}
          </div>
        )}
      </CardContent>
      
      <CardFooter className="flex justify-between pt-0 gap-2">
        <Button
          size="sm"
          variant="outline"
          className="flex items-center gap-1 flex-1"
          onClick={onMessage || (() => {})}
        >
          <MessageCircle className="h-4 w-4" />
          <span>Message</span>
        </Button>
        <Button
          size="sm"
          className="flex items-center gap-1 flex-1"
          onClick={onConnect || (() => {})}
        >
          <UserPlus className="h-4 w-4" />
          <span>Connect</span>
        </Button>
      </CardFooter>
    </Card>
  );
}

function getInitials(name: string): string {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
}

function generateProfileColor(userId: string): string {
  let hash = 0;
  for (let i = 0; i < userId.length; i++) {
    hash = userId.charCodeAt(i) + ((hash << 5) - hash);
  }
  
  const hue = Math.abs(hash) % 360;
  return `hsl(${hue}, 70%, 60%)`;
}
