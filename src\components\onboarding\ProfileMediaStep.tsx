
import React, { useRef, useState, useEffect } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { X, Upload, PartyPopper } from "lucide-react";
import { OnboardingFormValues } from "@/types/onboarding";
import { UserAvatar } from "@/components/user/UserAvatar";
import { useToast } from '@/hooks/use-toast';
import { ProfilePhotoUploader } from '@/components/shared/ProfilePhotoUploader';
interface ProfileMediaStepProps {
  form: UseFormReturn<OnboardingFormValues>;
  previewUrl: string | null;
  handleThumbnailClick: () => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  user: any;
}
export const ProfileMediaStep = ({
  form,
  previewUrl,
  handleThumbnailClick,
  fileInputRef,
  handleFileChange,
  user
}: ProfileMediaStepProps) => {
  const [gallery, setGallery] = useState<string[]>(form.watch("gallery") || []);
  const galleryInputRef = useRef<HTMLInputElement>(null);
  const [showCelebration, setShowCelebration] = useState(false);
  const {
    toast
  } = useToast();
  const handleGalleryClick = () => {
    if (galleryInputRef.current) {
      galleryInputRef.current.click();
    }
  };
  const handleGalleryFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const file = e.target.files[0];
      const reader = new FileReader();
      reader.onloadend = () => {
        const newGallery = [...gallery];
        if (newGallery.length < 6 && reader.result) {
          newGallery.push(reader.result.toString());
          setGallery(newGallery);
          form.setValue("gallery", newGallery);
          setShowCelebration(true);
          toast({
            title: "Photo added!",
            description: gallery.length === 0 ? "Great start to your profile!" : "Your gallery is looking great!"
          });
          setTimeout(() => {
            setShowCelebration(false);
          }, 2000);
        }
      };
      reader.readAsDataURL(file);
    }
  };
  const removeGalleryImage = (index: number) => {
    const newGallery = [...gallery];
    newGallery.splice(index, 1);
    setGallery(newGallery);
    form.setValue("gallery", newGallery);
  };
  const handleMainPhotoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileChange(e);
    if (e.target.files && e.target.files.length > 0) {
      setTimeout(() => {
        setShowCelebration(true);
        toast({
          title: "Perfect photo!",
          description: "Your profile picture looks great!"
        });
        setTimeout(() => {
          setShowCelebration(false);
        }, 2000);
      }, 500);
    }
  };
  return <div className="space-y-6 relative">
      {showCelebration && <div className="absolute inset-0 pointer-events-none z-50 overflow-hidden">
          <div className="absolute top-0 left-1/4 animate-fall-slow">
            <PartyPopper className="h-6 w-6 text-blue-500" />
          </div>
          <div className="absolute top-0 right-1/4 animate-fall-medium">
            <PartyPopper className="h-8 w-8 text-yellow-500" />
          </div>
          <div className="absolute top-0 left-1/2 animate-fall-fast">
            <PartyPopper className="h-5 w-5 text-red-500" />
          </div>
          <style>
            {`
            @keyframes fall-slow {
              0% { transform: translateY(-20px) rotate(0deg); opacity: 1; }
              100% { transform: translateY(500px) rotate(180deg); opacity: 0; }
            }
            @keyframes fall-medium {
              0% { transform: translateY(-20px) rotate(0deg); opacity: 1; }
              100% { transform: translateY(500px) rotate(-180deg); opacity: 0; }
            }
            @keyframes fall-fast {
              0% { transform: translateY(-20px) rotate(0deg); opacity: 1; }
              100% { transform: translateY(500px) rotate(90deg); opacity: 0; }
            }
            .animate-fall-slow {
              animation: fall-slow 2s forwards;
            }
            .animate-fall-medium {
              animation: fall-medium 1.7s forwards;
            }
            .animate-fall-fast {
              animation: fall-fast 1.5s forwards;
            }
            `}
          </style>
        </div>}

      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold">Additional Photos</h2>
        <p className="text-sm text-muted-foreground mt-1">Add more photos to enhance your profile</p>
      </div>

      {/* Profile photo preview */}
      <div className="mb-6">
        <div className="text-center mb-2">
          <h3 className="text-base font-medium">Your Profile Photo</h3>
          <p className="text-xs text-muted-foreground">This is your current profile photo</p>
        </div>
        <div className="flex justify-center">
          <ProfilePhotoUploader
            previewUrl={previewUrl || form.watch('avatar_url')}
            onPhotoChange={(file, dataUrl) => {
              // Create a synthetic event to pass to the parent handler
              if (handleFileChange && fileInputRef.current) {
                const syntheticEvent = {
                  target: {
                    files: [file]
                  }
                } as unknown as React.ChangeEvent<HTMLInputElement>;

                handleFileChange(syntheticEvent);
              }

              // Set the form value
              form.setValue('avatar_url', dataUrl);
            }}
            size="sm"
          />
        </div>
      </div>

      <FormField control={form.control} name="gallery" render={({
      field
    }) => <FormItem className="space-y-3 rounded-2xl bg-gray-100">
            <FormLabel>Photo Gallery</FormLabel>
            <FormControl>
              <div className="grid grid-cols-3 gap-3">
                {gallery.map((img, index) => <div key={index} className="relative aspect-square rounded-md overflow-hidden border border-border group">
                    <img src={img} alt={`Gallery ${index}`} className="w-full h-full object-cover" />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity"></div>
                    <button type="button" onClick={() => removeGalleryImage(index)} className="absolute bottom-2 right-2 bg-black/60 rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                      <X className="h-4 w-4 text-white" />
                    </button>
                  </div>)}

                {gallery.length < 6 && <div onClick={handleGalleryClick} className="aspect-square rounded-md border-2 border-dashed border-border flex flex-col items-center justify-center cursor-pointer hover:bg-muted/50 transition-colors">
                    <Upload className="h-6 w-6 text-muted-foreground" />
                    <span className="text-xs text-muted-foreground mt-1">Add Photo</span>
                  </div>}

                {Array.from({
            length: Math.max(0, 5 - gallery.length)
          }).map((_, index) => <div key={`empty-${index}`} className="aspect-square rounded-md border border-dashed border-border flex items-center justify-center bg-gray-50">
                    <span className="text-xs text-muted-foreground">+</span>
                  </div>)}
              </div>
            </FormControl>
            <input ref={galleryInputRef} type="file" accept="image/*" onChange={handleGalleryFileChange} className="hidden" />
            <div className="text-sm text-center text-muted-foreground">
              Add up to 6 photos that show your interests and personality
            </div>
            <FormMessage />
          </FormItem>} />
    </div>;
};
