-- Activity-Chat Integration SQL Functions
-- This file contains all database functions and triggers needed for activity-chat integration

-- ==========================================
-- 1. AUTO-CREATE ACTIVITY GROUP CHATS
-- ==========================================

-- Function to create or get activity group chat
CREATE OR REPLACE FUNCTION create_activity_group_chat(p_activity_id UUID)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_conversation_id UUID;
  v_activity_title TEXT;
  v_host_id UUID;
BEGIN
  -- Check if group chat already exists
  SELECT group_chat_id INTO v_conversation_id
  FROM activities
  WHERE id = p_activity_id;

  -- If chat already exists, return it
  IF v_conversation_id IS NOT NULL THEN
    RETURN v_conversation_id;
  END IF;

  -- Get activity details
  SELECT title, host_id INTO v_activity_title, v_host_id
  FROM activities
  WHERE id = p_activity_id;

  -- Create new group conversation
  INSERT INTO chat_conversations (
    is_group,
    title,
    activity_id,
    last_message,
    last_message_at,
    created_at,
    updated_at
  ) VALUES (
    true,
    v_activity_title || ' - Group Chat',
    p_activity_id,
    'Activity group chat created',
    NOW(),
    NOW(),
    NOW()
  ) RETURNING id INTO v_conversation_id;

  -- Update activity with group chat ID
  UPDATE activities
  SET group_chat_id = v_conversation_id,
      updated_at = NOW()
  WHERE id = p_activity_id;

  -- Add host as first participant
  INSERT INTO chat_participants (
    conversation_id,
    user_id,
    joined_at,
    role
  ) VALUES (
    v_conversation_id,
    v_host_id,
    NOW(),
    'admin'
  );

  -- Add system message about chat creation
  INSERT INTO messages (
    conversation_id,
    sender_id,
    content,
    message_type,
    is_system_message,
    created_at
  ) VALUES (
    v_conversation_id,
    v_host_id,
    'Welcome to the activity group chat! 🎉',
    'system',
    true,
    NOW()
  );

  RETURN v_conversation_id;
END;
$$;

-- Function to add user to activity chat
CREATE OR REPLACE FUNCTION add_user_to_activity_chat(
  p_activity_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_conversation_id UUID;
  v_user_display_name TEXT;
  v_already_participant BOOLEAN := false;
BEGIN
  -- Get or create the group chat
  SELECT create_activity_group_chat(p_activity_id) INTO v_conversation_id;

  -- Check if user is already a participant
  SELECT EXISTS(
    SELECT 1 FROM chat_participants
    WHERE conversation_id = v_conversation_id
    AND user_id = p_user_id
  ) INTO v_already_participant;

  -- If already a participant, return true
  IF v_already_participant THEN
    RETURN true;
  END IF;

  -- Add user as participant
  INSERT INTO chat_participants (
    conversation_id,
    user_id,
    joined_at,
    role
  ) VALUES (
    v_conversation_id,
    p_user_id,
    NOW(),
    'member'
  );

  -- Get user display name for system message
  SELECT COALESCE(display_name, username, 'Someone') INTO v_user_display_name
  FROM profiles
  WHERE id = p_user_id;

  -- Add system message about user joining
  INSERT INTO messages (
    conversation_id,
    sender_id,
    content,
    message_type,
    is_system_message,
    created_at
  ) VALUES (
    v_conversation_id,
    p_user_id,
    v_user_display_name || ' joined the activity! 👋',
    'system',
    true,
    NOW()
  );

  RETURN true;
END;
$$;

-- Function to remove user from activity chat
CREATE OR REPLACE FUNCTION remove_user_from_activity_chat(
  p_activity_id UUID,
  p_user_id UUID
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_conversation_id UUID;
  v_user_display_name TEXT;
BEGIN
  -- Get the group chat ID
  SELECT group_chat_id INTO v_conversation_id
  FROM activities
  WHERE id = p_activity_id;

  -- If no chat exists, return true
  IF v_conversation_id IS NULL THEN
    RETURN true;
  END IF;

  -- Get user display name for system message
  SELECT COALESCE(display_name, username, 'Someone') INTO v_user_display_name
  FROM profiles
  WHERE id = p_user_id;

  -- Add system message about user leaving
  INSERT INTO messages (
    conversation_id,
    sender_id,
    content,
    message_type,
    is_system_message,
    created_at
  ) VALUES (
    v_conversation_id,
    p_user_id,
    v_user_display_name || ' left the activity 👋',
    'system',
    true,
    NOW()
  );

  -- Remove user from chat participants
  DELETE FROM chat_participants
  WHERE conversation_id = v_conversation_id
  AND user_id = p_user_id;

  RETURN true;
END;
$$;

-- ==========================================
-- 2. TRIGGERS FOR AUTOMATIC CHAT MANAGEMENT
-- ==========================================

-- Trigger function for when users join activities
CREATE OR REPLACE FUNCTION handle_activity_participant_added()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Only add to chat if participant status is confirmed or pending
  IF NEW.status IN ('confirmed', 'pending') THEN
    PERFORM add_user_to_activity_chat(NEW.activity_id, NEW.user_id);
  END IF;

  RETURN NEW;
END;
$$;

-- Trigger function for when users leave activities
CREATE OR REPLACE FUNCTION handle_activity_participant_removed()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Remove from chat when participant is deleted or cancelled
  IF TG_OP = 'DELETE' OR (TG_OP = 'UPDATE' AND NEW.status = 'cancelled') THEN
    PERFORM remove_user_from_activity_chat(
      COALESCE(NEW.activity_id, OLD.activity_id),
      COALESCE(NEW.user_id, OLD.user_id)
    );
  END IF;

  RETURN COALESCE(NEW, OLD);
END;
$$;

-- Create triggers
DROP TRIGGER IF EXISTS trigger_activity_participant_added ON activity_participants;
CREATE TRIGGER trigger_activity_participant_added
  AFTER INSERT OR UPDATE ON activity_participants
  FOR EACH ROW
  EXECUTE FUNCTION handle_activity_participant_added();

DROP TRIGGER IF EXISTS trigger_activity_participant_removed ON activity_participants;
CREATE TRIGGER trigger_activity_participant_removed
  AFTER UPDATE OR DELETE ON activity_participants
  FOR EACH ROW
  EXECUTE FUNCTION handle_activity_participant_removed();

-- ==========================================
-- 3. ACTIVITY NOTIFICATIONS IN CHAT
-- ==========================================

-- Function to send activity update notification to group chat
CREATE OR REPLACE FUNCTION send_activity_update_notification(
  p_activity_id UUID,
  p_update_type TEXT,
  p_message TEXT
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_conversation_id UUID;
  v_host_id UUID;
BEGIN
  -- Get the group chat and host
  SELECT group_chat_id, host_id INTO v_conversation_id, v_host_id
  FROM activities
  WHERE id = p_activity_id;

  -- If no chat exists, return true
  IF v_conversation_id IS NULL THEN
    RETURN true;
  END IF;

  -- Send system message about the update
  INSERT INTO messages (
    conversation_id,
    sender_id,
    content,
    message_type,
    is_system_message,
    metadata,
    created_at
  ) VALUES (
    v_conversation_id,
    v_host_id,
    p_message,
    'system',
    true,
    jsonb_build_object(
      'update_type', p_update_type,
      'activity_id', p_activity_id
    ),
    NOW()
  );

  RETURN true;
END;
$$;

-- Trigger function for activity updates
CREATE OR REPLACE FUNCTION handle_activity_updated()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_message TEXT;
BEGIN
  -- Check what changed and send appropriate notifications

  -- Time change
  IF OLD.start_time != NEW.start_time OR OLD.end_time != NEW.end_time THEN
    v_message := '⏰ Activity time has been updated!';
    PERFORM send_activity_update_notification(NEW.id, 'time_change', v_message);
  END IF;

  -- Location change
  IF OLD.location != NEW.location OR OLD.address != NEW.address THEN
    v_message := '📍 Activity location has been updated!';
    PERFORM send_activity_update_notification(NEW.id, 'location_change', v_message);
  END IF;

  -- Status change
  IF OLD.status != NEW.status THEN
    CASE NEW.status
      WHEN 'cancelled' THEN
        v_message := '❌ This activity has been cancelled.';
      WHEN 'completed' THEN
        v_message := '✅ This activity has been completed!';
      ELSE
        v_message := '📢 Activity status updated to: ' || NEW.status;
    END CASE;
    PERFORM send_activity_update_notification(NEW.id, 'status_change', v_message);
  END IF;

  RETURN NEW;
END;
$$;

-- Create activity update trigger
DROP TRIGGER IF EXISTS trigger_activity_updated ON activities;
CREATE TRIGGER trigger_activity_updated
  AFTER UPDATE ON activities
  FOR EACH ROW
  EXECUTE FUNCTION handle_activity_updated();

-- ==========================================
-- 4. SCHEMA UPDATES FOR INTEGRATION
-- ==========================================

-- Add role column to chat_participants if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'chat_participants' AND column_name = 'role') THEN
        ALTER TABLE chat_participants ADD COLUMN role TEXT DEFAULT 'member';
    END IF;
END $$;

-- Add is_system_message column to messages if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'messages' AND column_name = 'is_system_message') THEN
        ALTER TABLE messages ADD COLUMN is_system_message BOOLEAN DEFAULT false;
    END IF;
END $$;

-- Add metadata column to messages if it doesn't exist
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'messages' AND column_name = 'metadata') THEN
        ALTER TABLE messages ADD COLUMN metadata JSONB;
    END IF;
END $$;

-- Update message_type enum to include new types
DO $$
BEGIN
    -- Check if the type exists and add new values
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'message_type_enum') THEN
        -- Add new enum values if they don't exist
        BEGIN
            ALTER TYPE message_type_enum ADD VALUE IF NOT EXISTS 'activity_proposal';
            ALTER TYPE message_type_enum ADD VALUE IF NOT EXISTS 'activity_share';
            ALTER TYPE message_type_enum ADD VALUE IF NOT EXISTS 'system';
        EXCEPTION
            WHEN duplicate_object THEN NULL;
        END;
    ELSE
        -- Create the enum type if it doesn't exist
        CREATE TYPE message_type_enum AS ENUM (
            'text', 'media', 'location', 'activity_proposal', 'activity_share', 'system'
        );
    END IF;
END $$;

-- ==========================================
-- 5. ACTIVITY PROPOSAL FUNCTIONS
-- ==========================================

-- Function to send activity proposal in chat
CREATE OR REPLACE FUNCTION send_activity_proposal(
  p_conversation_id UUID,
  p_sender_id UUID,
  p_activity_id UUID,
  p_message TEXT DEFAULT 'Check out this activity!'
)
RETURNS UUID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_message_id UUID;
  v_activity_title TEXT;
BEGIN
  -- Get activity title
  SELECT title INTO v_activity_title
  FROM activities
  WHERE id = p_activity_id;

  -- Insert the proposal message
  INSERT INTO messages (
    conversation_id,
    sender_id,
    content,
    message_type,
    metadata,
    created_at
  ) VALUES (
    p_conversation_id,
    p_sender_id,
    p_message,
    'activity_proposal',
    jsonb_build_object(
      'activity_id', p_activity_id,
      'activity_title', v_activity_title,
      'proposal_status', 'pending'
    ),
    NOW()
  ) RETURNING id INTO v_message_id;

  -- Update conversation last message
  UPDATE chat_conversations
  SET last_message = 'Activity proposal: ' || v_activity_title,
      last_message_at = NOW(),
      updated_at = NOW()
  WHERE id = p_conversation_id;

  RETURN v_message_id;
END;
$$;

-- Function to respond to activity proposal
CREATE OR REPLACE FUNCTION respond_to_activity_proposal(
  p_message_id UUID,
  p_user_id UUID,
  p_response TEXT -- 'accepted' or 'declined'
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  v_activity_id UUID;
  v_conversation_id UUID;
  v_user_display_name TEXT;
  v_response_message TEXT;
BEGIN
  -- Get activity ID and conversation ID from the proposal message
  SELECT
    (metadata->>'activity_id')::UUID,
    conversation_id
  INTO v_activity_id, v_conversation_id
  FROM messages
  WHERE id = p_message_id
  AND message_type = 'activity_proposal';

  -- Get user display name
  SELECT COALESCE(display_name, username, 'Someone') INTO v_user_display_name
  FROM profiles
  WHERE id = p_user_id;

  -- Handle the response
  IF p_response = 'accepted' THEN
    -- Add user to activity queue/participants
    INSERT INTO activity_queue (
      activity_id,
      user_id,
      status,
      position,
      created_at
    ) VALUES (
      v_activity_id,
      p_user_id,
      'pending',
      (SELECT COALESCE(MAX(position), 0) + 1 FROM activity_queue WHERE activity_id = v_activity_id),
      NOW()
    ) ON CONFLICT (activity_id, user_id) DO NOTHING;

    v_response_message := v_user_display_name || ' accepted the activity proposal! 🎉';
  ELSE
    v_response_message := v_user_display_name || ' declined the activity proposal.';
  END IF;

  -- Send response message
  INSERT INTO messages (
    conversation_id,
    sender_id,
    content,
    message_type,
    is_system_message,
    metadata,
    created_at
  ) VALUES (
    v_conversation_id,
    p_user_id,
    v_response_message,
    'system',
    true,
    jsonb_build_object(
      'proposal_response', p_response,
      'activity_id', v_activity_id,
      'original_message_id', p_message_id
    ),
    NOW()
  );

  RETURN true;
END;
$$;
