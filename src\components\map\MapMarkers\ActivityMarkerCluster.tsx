
import React, { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import { Activity } from '@/types/activity';
import { createActivityMarker } from './createMapMarker';

interface ActivityMarkerClusterProps {
  map: mapboxgl.Map;
  activities: Activity[];
  sourceId: string;
  onActivityClick: (activity: Activity) => void;
}

export function ActivityMarkerCluster({
  map,
  activities,
  sourceId,
  onActivityClick
}: ActivityMarkerClusterProps) {
  const markersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});

  // Update activity markers whenever activities data changes
  useEffect(() => {
    if (!map) return;

    // Remove existing markers
    Object.values(markersRef.current).forEach(marker => marker.remove());
    markersRef.current = {};

    // Add new markers for each activity
    activities.forEach(activity => {
      if (!activity.location) return;

      const marker = createActivityMarker(
        activity,
        map,
        () => onActivityClick(activity),
        false,
        false
      );

      if (marker) {
        marker.addTo(map);
        markersRef.current[activity.id] = marker;
      }
    });

    // Cleanup on unmount
    return () => {
      Object.values(markersRef.current).forEach(marker => marker.remove());
      markersRef.current = {};
    };
  }, [map, activities, onActivityClick]);

  return null; // This is a non-visual component
}
