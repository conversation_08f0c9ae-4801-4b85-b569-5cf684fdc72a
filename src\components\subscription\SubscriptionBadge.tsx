
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { useSubscription } from "@/hooks/use-subscription";
import { cn } from "@/lib/utils";
import { Sparkles } from "lucide-react";

interface SubscriptionBadgeProps {
  className?: string;
  size?: "sm" | "md" | "lg";
}

export function SubscriptionBadge({ className, size = "md" }: SubscriptionBadgeProps) {
  const { data: subscriptionData, isLoading } = useSubscription();
  
  if (isLoading || !subscriptionData?.active) {
    return null;
  }
  
  const sizeClasses = {
    sm: "text-xs py-0 px-1.5",
    md: "text-xs py-0.5 px-2",
    lg: "text-sm py-1 px-2.5"
  };
  
  const planColors = {
    weekly: "bg-blue-100 text-blue-800 hover:bg-blue-100",
    monthly: "bg-purple-100 text-purple-800 hover:bg-purple-100",
    lifetime: "bg-amber-100 text-amber-800 hover:bg-amber-100"
  };
  
  const planLabels = {
    weekly: "Weekly",
    monthly: "Monthly",
    lifetime: "Lifetime"
  };
  
  const plan = subscriptionData.plan as keyof typeof planColors || "weekly";
  
  return (
    <Badge 
      variant="outline" 
      className={cn(
        "font-normal gap-1 border", 
        sizeClasses[size],
        planColors[plan],
        className
      )}
    >
      <Sparkles className="h-3 w-3" />
      {planLabels[plan as keyof typeof planLabels]} Member
    </Badge>
  );
}
