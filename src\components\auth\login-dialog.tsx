
import * as React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useId, useState } from "react";
import { SignupDialog } from "./signup-dialog";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";

interface LoginDialogProps {
  open?: boolean;
  setOpen?: (open: boolean) => void;
  onSignupClick?: () => void;
}

export function LoginDialog({ open: externalOpen, setOpen: setExternalOpen, onSignupClick }: LoginDialogProps) {
  const id = useId();
  const [isLoginOpen, setIsLoginOpen] = useState(false);
  const [isSignupOpen, setIsSignupOpen] = useState(false);
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  
  const { signIn, user } = useAuth();

  // Handle controlled/uncontrolled state
  const dialogOpen = externalOpen !== undefined ? externalOpen : isLoginOpen;
  const setDialogOpen = setExternalOpen || setIsLoginOpen;

  const handleSignupClick = () => {
    setDialogOpen(false);
    
    if (onSignupClick) {
      onSignupClick();
    } else {
      setIsSignupOpen(true);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrorMessage("");
    
    try {
      const { error } = await signIn(email, password);
      
      if (!error) {
        setDialogOpen(false);
        // Clear fields on success
        setEmail("");
        setPassword("");
      } else {
        setErrorMessage(error.message);
      }
    } catch (error) {
      setErrorMessage("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  React.useEffect(() => {
    if (user) {
      setDialogOpen(false);
    }
  }, [user]);

  return (
    <>
      <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline">Log in</Button>
        </DialogTrigger>
        <DialogContent>
          <div className="flex flex-col items-center gap-2">
            <div
              className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border bg-primary-purple/20"
              aria-hidden="true"
            >
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                width="24" 
                height="24" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                strokeWidth="2" 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                className="text-primary-purple"
              >
                <path d="M18 8a6 6 0 0 0-6-6 6 6 0 0 0-6 6c0 7-3 9-3 9h18s-3-2-3-9"/>
                <path d="M13.73 21a2 2 0 0 1-3.46 0"/>
              </svg>
            </div>
            <DialogHeader>
              <DialogTitle className="sm:text-center">Welcome back</DialogTitle>
              <DialogDescription className="sm:text-center">
                Enter your credentials to login to your account
              </DialogDescription>
            </DialogHeader>
          </div>

          <form className="space-y-5" onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor={`${id}-email`}>Email</Label>
                <Input 
                  id={`${id}-email`} 
                  placeholder="<EMAIL>" 
                  type="email" 
                  required 
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor={`${id}-password`}>Password</Label>
                <Input
                  id={`${id}-password`}
                  placeholder="Enter your password"
                  type="password"
                  required
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                />
              </div>
            </div>
            
            {errorMessage && (
              <div className="bg-red-50 p-2 rounded text-sm text-red-600">
                {errorMessage}
              </div>
            )}
            
            <div className="flex justify-between gap-2">
              <div className="flex items-center gap-2">
                <Checkbox 
                  id={`${id}-remember`} 
                  checked={rememberMe}
                  onCheckedChange={(checked) => setRememberMe(checked === true)}
                />
                <Label htmlFor={`${id}-remember`} className="font-normal text-muted-foreground">
                  Remember me
                </Label>
              </div>
              <a className="text-sm underline hover:no-underline text-primary-purple" href="#">
                Forgot password?
              </a>
            </div>
            
            <Button 
              type="submit" 
              className="w-full bg-primary-purple hover:bg-primary-deep-purple"
              disabled={isLoading}
            >
              {isLoading ? (
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              ) : null}
              Sign in
            </Button>
          </form>

          <div className="flex items-center gap-3 before:h-px before:flex-1 before:bg-border after:h-px after:flex-1 after:bg-border">
            <span className="text-xs text-muted-foreground">Or</span>
          </div>

          <Button variant="outline">Login with Google</Button>
          
          <div className="text-center text-sm">
            Don't have an account?{" "}
            <button 
              className="text-primary-purple font-medium hover:underline"
              onClick={handleSignupClick}
              type="button"
            >
              Sign up
            </button>
          </div>
        </DialogContent>
      </Dialog>
      {!onSignupClick && <SignupDialog open={isSignupOpen} setOpen={setIsSignupOpen} setLoginOpen={setIsLoginOpen} />}
    </>
  );
}
