
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>er, CardHeader } from '@/components/ui/card';
import { UserAvatar } from '@/components/user/UserAvatar';
import { MessageSquare, MapPin, Clock, Users, Calendar, ExternalLink, Verified, Shield } from 'lucide-react';
import { formatDistance, formatLastSeen } from '@/utils/distance-calculator';
import { useNavigate } from 'react-router-dom';
import { FollowButton } from "@/components/user/FollowButton";
import { useFollows } from "@/hooks/use-follows";
import { Badge } from "@/components/ui/badge";
import { formatDistanceToNow, format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useFormattedBio, useFormattedLastSeen } from '@/hooks/use-formatted-bio';

interface UserProfilePreviewProps {
  user: {
    user_id: string;
    display_name?: string;
    username?: string;
    avatar_url?: string;
    bio?: string;
    online_at?: string;
    created_at?: string;
    location?: { x: number; y: number };
    is_verified?: boolean;
    distance?: number;
    interests?: string[];
    profile_color?: string;
  };
  onClose?: () => void;
}

export function UserProfilePreview({ user, onClose }: UserProfilePreviewProps) {
  const navigate = useNavigate();
  const { followers, isLoading } = useFollows(user.user_id);

  const handleStartChat = () => {
    navigate(`/chat?user=${user.user_id}`);
    if (onClose) onClose();
  };

  const handleViewProfile = () => {
    // Use clean URL structure if username is available, otherwise fall back to /profile/id
    navigate(user.username ? `/${user.username}` : `/profile/${user.user_id}`);
    if (onClose) onClose();
  };

  // Use our new formatting hooks
  const formattedBio = useFormattedBio(user.bio);
  const lastSeenFormatted = useFormattedLastSeen(user.online_at);

  // Generate text color based on profile color for better contrast
  const textColor = user.profile_color
    ? getContrastingTextColor(user.profile_color)
    : 'text-white';

  // Format join date in a friendly way
  const joinDate = React.useMemo(() => {
    if (!user.created_at) return null;

    try {
      return format(new Date(user.created_at), 'MMMM yyyy');
    } catch {
      return null;
    }
  }, [user.created_at]);

  return (
    <Card className="w-[340px] shadow-lg overflow-hidden animate-in fade-in slide-in-from-bottom-5 duration-300">
      <CardHeader className="pb-2">
        <div className="flex items-center gap-3">
          <UserAvatar
            user={user}
            size="lg"
            showVerified={true}
          />
          <div className="flex-1 overflow-hidden">
            <div className="flex items-center gap-1.5">
              <h3 className="font-semibold truncate">
                {user.display_name || user.username || 'User'}
              </h3>
              {user.is_verified && (
                <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200 flex items-center gap-1 py-0 h-5">
                  <Shield className="h-3 w-3" />
                  <span className="text-[10px]">Verified</span>
                </Badge>
              )}
            </div>
            {user.username && (
              <p className="text-sm text-muted-foreground truncate">
                @{user.username}
              </p>
            )}
          </div>
          <div>
            <Badge variant="outline" className="text-xs">
              <Users className="h-3 w-3 mr-1" />
              {!isLoading ? followers : "..."}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        {formattedBio ? (
          <div className="text-sm line-clamp-3 relative pr-5">
            {formattedBio}
            <Button
              variant="link"
              size="sm"
              className="absolute bottom-0 right-0 p-0 h-auto text-xs"
              onClick={handleViewProfile}
            >
              more
            </Button>
          </div>
        ) : (
          <p className="text-sm text-muted-foreground italic">No bio available</p>
        )}

        <div className="flex flex-wrap gap-1.5">
          {user.interests?.map((interest, idx) => (
            <Badge
              key={idx}
              variant="secondary"
              className="text-xs py-0 px-2 hover:bg-secondary/80 transition-colors"
            >
              {interest}
            </Badge>
          ))}
        </div>

        <div className="flex flex-col space-y-1.5 text-xs text-muted-foreground">
          {user.online_at && (
            <div className="flex items-center gap-1.5">
              <Clock className="h-3.5 w-3.5" />
              <span>{lastSeenFormatted}</span>
            </div>
          )}

          {user.distance !== undefined && (
            <div className="flex items-center gap-1.5">
              <MapPin className="h-3.5 w-3.5" />
              <span>{formatDistance(user.distance)}</span>
            </div>
          )}

          {joinDate && (
            <div className="flex items-center gap-1.5">
              <Calendar className="h-3.5 w-3.5" />
              <span>Joined {joinDate}</span>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="flex gap-2 pt-0">
        <FollowButton
          userId={user.user_id}
          className="flex-1"
        />
        <Button
          size="sm"
          className="flex-1"
          onClick={handleStartChat}
        >
          <MessageSquare className="h-4 w-4 mr-2" />
          Message
        </Button>
      </CardFooter>
      <div className="px-4 pb-2 flex justify-center">
        <Button
          variant="link"
          size="sm"
          className="text-xs flex items-center gap-1 h-6 hover:underline transition-all"
          onClick={handleViewProfile}
        >
          View full profile
          <ExternalLink className="h-3 w-3" />
        </Button>
      </div>
    </Card>
  );
}

// Helper function to determine if text should be black or white based on background color
function getContrastingTextColor(hexColor: string): string {
  // Remove # if present
  hexColor = hexColor.replace('#', '');

  // Convert to RGB
  const r = parseInt(hexColor.substr(0, 2), 16);
  const g = parseInt(hexColor.substr(2, 2), 16);
  const b = parseInt(hexColor.substr(4, 2), 16);

  // Calculate brightness
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;

  // Return black for bright colors, white for dark
  return brightness > 128 ? 'text-black' : 'text-white';
}
