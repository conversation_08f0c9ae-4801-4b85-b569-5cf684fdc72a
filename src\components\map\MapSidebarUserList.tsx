import React, { useEffect, useState, useCallback } from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { UnifiedMapUser } from "@/types/map";
import { supabase } from "@/integrations/supabase/client";
import { Loader2, Users, MessageSquare, RefreshCw } from "lucide-react";
import { useAuth } from "@/hooks/use-auth";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useToast } from "@/hooks/use-toast";

// Accept array of unified user type
interface MapSidebarUserListProps {
  users: UnifiedMapUser[];
  onUserClick?: (user: UnifiedMapUser) => void;
}

function initials(name: string) {
  const parts = name?.split(" ");
  if (parts?.length === 1) return parts[0]?.substring(0,2).toUpperCase();
  return (
    (parts?.[0]?.charAt(0) ?? "").toUpperCase() +
    (parts?.[1]?.charAt(0) ?? "").toUpperCase()
  );
}

export function MapSidebarUserList({users, onUserClick}: MapSidebarUserListProps) {
  const { user: currentUser } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const [allUsers, setAllUsers] = useState<UnifiedMapUser[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Function to handle user click with fallback
  const handleUserClick = useCallback((user: UnifiedMapUser) => {
    console.log("User clicked:", user);

    if (onUserClick) {
      // First try the provided click handler
      onUserClick(user);
    } else {
      // Fallback: Navigate directly to chat with this user
      navigate(`/chat?user=${user.user_id}`);

      // Show feedback to the user
      toast({
        description: `Opening chat with ${user.display_name || user.username || 'User'}...`,
      });
    }
  }, [onUserClick, navigate, toast]);

  // Function to fetch all users
  const fetchAllUsers = useCallback(async () => {
    if (!currentUser) return;

    setIsLoading(true);
    setError(null);

    try {
      // Fetch all profiles from the database
      const { data, error } = await supabase
        .from("profiles")
        .select("id, username, display_name, avatar_url, bio, created_at, updated_at")
        .neq("id", currentUser.id) // Exclude current user
        .limit(20); // Limit to 20 users for performance

      if (error) {
        console.error("Error fetching all users:", error);
        setError("Failed to load users. Please try again.");
        return;
      }

      if (data) {
        // Convert profiles to UnifiedMapUser format
        const mappedUsers: UnifiedMapUser[] = data.map(profile => ({
          user_id: profile.id,
          id: profile.id,
          username: profile.username || profile.display_name || `User ${profile.id.substring(0, 5)}`,
          display_name: profile.display_name,
          avatar_url: profile.avatar_url,
          bio: profile.bio,
          location: { x: 0, y: 0 }, // Default location
          last_active: profile.updated_at || profile.created_at,
          created_at: profile.created_at,
          updated_at: profile.updated_at,
          is_online: false
        }));

        setAllUsers(mappedUsers);
      }
    } catch (err) {
      console.error("Unexpected error fetching users:", err);
      setError("An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [currentUser]);

  // Fetch all users when the provided users array is empty
  useEffect(() => {
    if (users.length === 0 && currentUser) {
      fetchAllUsers();
    }
  }, [users, currentUser, fetchAllUsers, retryCount]);

  // Determine which users to display
  const displayUsers = users.length > 0 ? users : allUsers;

  return (
    <div className="bg-white/90 overflow-y-auto flex flex-col gap-2">
      {users.length === 0 && isLoading && (
        <div className="text-center text-xs text-muted-foreground py-10 flex flex-col items-center">
          <Loader2 className="h-5 w-5 animate-spin mb-2" />
          <span>Loading users...</span>
        </div>
      )}

      {users.length === 0 && !isLoading && error && (
        <div className="text-center text-xs text-red-500 py-10 flex flex-col items-center">
          <span className="mb-2">{error}</span>
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              setRetryCount(prev => prev + 1);
            }}
            className="flex items-center gap-1"
          >
            <RefreshCw className="h-3 w-3" />
            <span>Retry</span>
          </Button>
        </div>
      )}

      {users.length === 0 && !isLoading && !error && allUsers.length === 0 && (
        <div className="text-center text-xs text-muted-foreground py-10 flex flex-col items-center">
          <Users className="h-5 w-5 mb-2 opacity-50" />
          <span>No users found.</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => {
              setRetryCount(prev => prev + 1);
            }}
            className="mt-2 text-xs"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            <span>Refresh</span>
          </Button>
        </div>
      )}

      {displayUsers.map(u => {
        const username = u.display_name || u.username || `User ${u.user_id?.substring?.(0,5)}` || "Buddy";
        const avatarUrl = u.avatar_url || `https://i.pravatar.cc/150?u=${u.user_id}`;
        const online = u.is_online || false;
        const bio = u.bio || "";
        const distance = u.distance || "";

        return (
          <div
            key={u.user_id}
            className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-blue-50 transition cursor-pointer group"
            onClick={() => handleUserClick(u)}
          >
            <Avatar className="w-12 h-12 ring-2 ring-blue-400 ring-offset-1 shrink-0">
              <AvatarImage src={avatarUrl} alt={username} />
              <AvatarFallback>
                {initials(username)}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 min-w-0">
              <div className="font-semibold truncate flex items-center gap-1">
                {username}
                {online && (
                  <span className="inline-block w-2 h-2 bg-green-500 rounded-full ml-1" title="Online" />
                )}
              </div>
              <div className="text-xs text-muted-foreground line-clamp-2 mt-0.5">
                {bio ? bio : "No bio available"}
              </div>
              {distance && <div className="text-xs text-primary mt-0.5">{distance}</div>}
            </div>
            <Button
              size="sm"
              variant="ghost"
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              disabled={currentUser?.id === u.user_id}
              asChild
            >
              <span>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span>
                        <Button
                          size="sm"
                          variant="ghost"
                          className="opacity-0 group-hover:opacity-100 transition-opacity"
                          disabled={currentUser?.id === u.user_id}
                          onClick={(e) => {
                            e.stopPropagation();
                            if (currentUser?.id !== u.user_id) {
                              navigate(`/chat?user=${u.user_id}`);
                            }
                          }}
                          tabIndex={currentUser?.id === u.user_id ? -1 : 0}
                          aria-disabled={currentUser?.id === u.user_id}
                        >
                          <MessageSquare className="h-4 w-4" />
                        </Button>
                      </span>
                    </TooltipTrigger>
                    {currentUser?.id === u.user_id && (
                      <TooltipContent>You cannot chat with yourself.</TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              </span>
            </Button>
          </div>
        );
      })}
    </div>
  );
}
