
import React, { useContext } from 'react';
import { Button } from "@/components/ui/button";
import { HelpCircle, Loader2 } from "lucide-react";
import { useBuddyAdminConversation } from '@/hooks/use-buddy-admin-conversation';
import { useNavigate, UNSAFE_NavigationContext } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';

interface SupportChatButtonProps {
  variant?: 'default' | 'outline' | 'ghost' | 'link';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

export function SupportChatButton({ 
  variant = 'outline', 
  size = 'default',
  className = ''
}: SupportChatButtonProps) {
  const { supportConversation, isSupportLoading } = useBuddyAdminConversation();
  // Check if we're inside a Router context before using useNavigate
  const hasRouter = useContext(UNSAFE_NavigationContext) !== undefined;
  const navigate = hasRouter ? useNavigate() : null;
  const { toast } = useToast();

  const handleClick = async () => {
    if (supportConversation) {
      if (navigate) {
        navigate(`/chat?conversation=${supportConversation.id}`);
      } else {
        // Fallback if no router context
        window.location.href = `/chat?conversation=${supportConversation.id}`;
      }
    } else if (!isSupportLoading) {
      toast({
        title: 'Support chat unavailable',
        description: 'Please try again in a moment.',
        variant: 'destructive'
      });
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={isSupportLoading}
      className={`${className} ${variant === 'default' ? 'bg-green-600 hover:bg-green-700 text-white' : ''}`}
    >
      {isSupportLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
          Loading...
        </>
      ) : (
        <>
          <HelpCircle className="mr-2 h-4 w-4" />
          Contact Support
        </>
      )}
    </Button>
  );
}
