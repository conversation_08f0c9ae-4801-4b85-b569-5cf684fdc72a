import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ActivityCreationValues } from '@/schemas/activity-creation-schema';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Image, X, Upload, Check } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ActivityMediaStepProps {
  form: UseFormReturn<ActivityCreationValues>;
}

export function ActivityMediaStep({ form }: ActivityMediaStepProps) {
  const { toast } = useToast();
  const [uploading, setUploading] = useState(false);
  const [previewUrls, setPreviewUrls] = useState<string[]>([]);

  // Get current media URLs from form
  const mediaUrls = form.watch('media_urls') || [];

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) return;

    setUploading(true);

    try {
      // Create preview URLs for the selected files
      const newPreviewUrls = Array.from(files).map(file => URL.createObjectURL(file));
      setPreviewUrls([...previewUrls, ...newPreviewUrls]);

      // In a real implementation, you would upload the files to your storage service here
      // For now, we'll just simulate a successful upload
      setTimeout(() => {
        // Update the form with the new media URLs
        const newMediaUrls = [...mediaUrls, ...newPreviewUrls];
        form.setValue('media_urls', newMediaUrls);

        toast({
          title: 'Media uploaded',
          description: 'Your media has been uploaded successfully',
        });

        setUploading(false);
      }, 1500);
    } catch (error) {
      console.error('Error uploading media:', error);
      toast({
        title: 'Upload failed',
        description: 'There was an error uploading your media',
        variant: 'destructive',
      });
      setUploading(false);
    }
  };

  const removeMedia = (index: number) => {
    const newMediaUrls = [...mediaUrls];
    newMediaUrls.splice(index, 1);
    form.setValue('media_urls', newMediaUrls);

    // Also remove from preview URLs if it exists there
    if (index < previewUrls.length) {
      const newPreviewUrls = [...previewUrls];
      URL.revokeObjectURL(newPreviewUrls[index]);
      newPreviewUrls.splice(index, 1);
      setPreviewUrls(newPreviewUrls);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-xl font-medium text-center text-primary-purple">Activity Media</h3>
        <p className="text-sm text-muted-foreground text-center">
          Add photos to showcase your activity (optional)
        </p>
      </div>

      <FormField
        control={form.control}
        name="media_urls"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Media</FormLabel>
            <FormControl>
              <div className="space-y-4">
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                  {mediaUrls.map((url, index) => (
                    <Card key={index} className="relative overflow-hidden group">
                      <CardContent className="p-0">
                        <img
                          src={url}
                          alt={`Activity media ${index + 1}`}
                          className="w-full h-32 object-cover"
                        />
                        <Button
                          variant="destructive"
                          size="icon"
                          className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => removeMedia(index)}
                        >
                          <X className="h-4 w-4" />
                        </Button>
                      </CardContent>
                    </Card>
                  ))}

                  <Card className="border-dashed">
                    <CardContent className="p-0">
                      <label className="flex flex-col items-center justify-center w-full h-32 cursor-pointer bg-muted/50 hover:bg-muted transition-colors">
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <Upload className="h-6 w-6 mb-2 text-muted-foreground" />
                          <p className="text-sm text-muted-foreground">
                            {uploading ? 'Uploading...' : 'Click to upload'}
                          </p>
                        </div>
                        <input
                          type="file"
                          className="hidden"
                          accept="image/*"
                          multiple
                          onChange={handleFileChange}
                          disabled={uploading}
                        />
                      </label>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </FormControl>
            <FormDescription>
              Upload images to showcase your activity (optional)
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <div className="space-y-2 pt-4">
        <h3 className="text-xl font-medium text-center text-primary-purple">Review & Submit</h3>
        <p className="text-sm text-muted-foreground text-center">
          Review your activity details before submitting
        </p>

        <div className="rounded-lg border border-gray-200 p-4 space-y-2 bg-gray-50">
          <div className="flex items-center text-sm">
            <Check className="h-4 w-4 mr-2 text-green-500" />
            <span>Basic information completed</span>
          </div>
          <div className="flex items-center text-sm">
            <Check className="h-4 w-4 mr-2 text-green-500" />
            <span>Location selected</span>
          </div>
          <div className="flex items-center text-sm">
            <Check className="h-4 w-4 mr-2 text-green-500" />
            <span>Activity details provided</span>
          </div>
          <div className="flex items-center text-sm">
            <Check className="h-4 w-4 mr-2 text-green-500" />
            <span>Ready to create your activity!</span>
          </div>
        </div>
      </div>
    </div>
  );
}
