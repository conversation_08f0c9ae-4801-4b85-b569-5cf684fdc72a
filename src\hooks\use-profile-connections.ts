
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { ProfileConnection } from '@/types/enhanced-profile';

type ConnectionType = 'followers' | 'following';

export function useProfileConnections(
  profileId?: string, 
  type: ConnectionType = 'followers',
  limit: number = 10,
  offset: number = 0
) {
  return useQuery({
    queryKey: ['profileConnections', profileId, type, limit, offset],
    queryFn: async () => {
      if (!profileId) return { connections: [], count: 0 };

      let query;
      let countQuery;
      
      if (type === 'followers') {
        // Get users who follow this profile
        query = supabase
          .from('follows')
          .select(`
            id,
            follower_id,
            following_id,
            created_at,
            follower:profiles!follower_id(id, username, display_name, avatar_url, is_verified)
          `)
          .eq('following_id', profileId)
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1);
          
        countQuery = supabase
          .from('follows')
          .select('id', { count: 'exact', head: true })
          .eq('following_id', profileId);
      } else {
        // Get users who this profile follows
        query = supabase
          .from('follows')
          .select(`
            id,
            follower_id,
            following_id,
            created_at,
            following:profiles!following_id(id, username, display_name, avatar_url, is_verified)
          `)
          .eq('follower_id', profileId)
          .order('created_at', { ascending: false })
          .range(offset, offset + limit - 1);
          
        countQuery = supabase
          .from('follows')
          .select('id', { count: 'exact', head: true })
          .eq('follower_id', profileId);
      }

      const [{ data, error }, { count, error: countError }] = await Promise.all([
        query,
        countQuery
      ]);

      if (error) {
        console.error(`Error fetching ${type}:`, error);
        return { connections: [], count: 0 };
      }

      if (countError) {
        console.error(`Error counting ${type}:`, countError);
      }

      // Transform data to match ProfileConnection interface
      const connections: ProfileConnection[] = data.map(item => {
        if (type === 'followers') {
          const follower = item.follower;
          return {
            id: item.id,
            follower_id: item.follower_id,
            following_id: item.following_id,
            created_at: item.created_at,
            follower_username: follower?.username || '',
            follower_display_name: follower?.display_name || '',
            follower_avatar_url: follower?.avatar_url,
            follower_is_verified: follower?.is_verified || false,
            following_username: '', // Will be filled in later if needed
            following_display_name: '',
            following_avatar_url: undefined,
            following_is_verified: false,
            is_mutual: false // Will be computed later
          };
        } else {
          const following = item.following;
          return {
            id: item.id,
            follower_id: item.follower_id,
            following_id: item.following_id,
            created_at: item.created_at,
            follower_username: '', // Will be filled in later if needed
            follower_display_name: '',
            follower_avatar_url: undefined,
            follower_is_verified: false,
            following_username: following?.username || '',
            following_display_name: following?.display_name || '',
            following_avatar_url: following?.avatar_url,
            following_is_verified: following?.is_verified || false,
            is_mutual: false // Will be computed later
          };
        }
      });

      // Check for mutual follows if there are connections
      if (connections.length > 0) {
        // Prepare the IDs to check based on connection type
        const idsToCheck = type === 'followers' 
          ? connections.map(conn => conn.follower_id)
          : connections.map(conn => conn.following_id);
        
        if (idsToCheck.length > 0) {
          // For followers, check if the profile follows them back
          // For following, check if they follow the profile back
          const { data: mutualData, error: mutualError } = await supabase
            .from('follows')
            .select('follower_id, following_id')
            .eq(type === 'followers' ? 'follower_id' : 'following_id', profileId)
            .in(type === 'followers' ? 'following_id' : 'follower_id', idsToCheck);

          if (!mutualError && mutualData) {
            const mutualIds = new Set(
              mutualData.map(m => type === 'followers' ? m.following_id : m.follower_id)
            );
            
            // Update is_mutual flag in connections
            connections.forEach(conn => {
              const idToCheck = type === 'followers' ? conn.follower_id : conn.following_id;
              conn.is_mutual = mutualIds.has(idToCheck);
            });
          }
        }
      }

      return { connections, count: count || 0 };
    },
    enabled: !!profileId,
  });
}
