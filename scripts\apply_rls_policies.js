// <PERSON>ript to apply improved RLS policies to Supabase
const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// Load environment variables
require('dotenv').config();

// Create Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Error: Missing Supabase URL or service key in environment variables.');
  console.error('Please ensure VITE_SUPABASE_URL and SUPABASE_SERVICE_KEY are set.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function applyRLSPolicies() {
  try {
    console.log('Reading SQL file...');
    const sqlFilePath = path.join(__dirname, '..', 'sql', 'improved_rls_policies.sql');
    const sqlContent = fs.readFileSync(sqlFilePath, 'utf8');

    console.log('Applying RLS policies...');
    const { data, error } = await supabase.rpc('exec_sql', { sql: sqlContent });

    if (error) {
      console.error('Error applying RLS policies:', error);
      return;
    }

    console.log('RLS policies applied successfully!');
    console.log('Summary:');
    console.log('- Updated Activities table policies');
    console.log('- Updated Activity Queue policies');
    console.log('- Updated Chat Conversations policies');
    console.log('- Updated Chat Messages policies');
    console.log('- Updated Profiles policies');
    console.log('- Updated User Locations policies');
    console.log('- Granted admin <NAME_EMAIL>');
  } catch (error) {
    console.error('Error:', error);
  }
}

// Run the function
applyRLSPolicies();
