
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useEffect } from 'react';

export interface ConversationParticipant {
  user_id: string;
  conversation_id: string;
  joined_at: string;
  last_read_at?: string | null;
}

// Extended conversation interface with the properties needed by ChatSidebar
export interface Conversation {
  id: string;
  activity_id?: string | null;
  last_message: string | null;
  last_message_at: string | null;
  is_group: boolean;
  is_announcement_only?: boolean;
  is_admin_conversation?: boolean;
  is_support?: boolean;
  created_at: string;
  updated_at: string;
  participants?: ConversationParticipant[];
  other_participant?: {
    display_name?: string;
    username?: string;
    avatar_url?: string;
  };
  unread_count?: number;
}

export function useChatConversations() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  // Query to get or create admin conversation
  const { data: adminConversation, isLoading: adminLoading } = useQuery({
    queryKey: ['admin-conversation', user?.id],
    queryFn: async () => {
      if (!user) return null;

      // Check if admin conversation exists
      const { data: existingConversation } = await supabase
        .from('chat_conversations')
        .select('*')
        .eq('is_admin_conversation', true)
        .single();

      if (existingConversation) {
        console.log('[Buddy Admin] Found existing admin conversation:', existingConversation.id);
        // Check if user is participant
        const { data: participant } = await supabase
          .from('chat_participants')
          .select('*')
          .eq('conversation_id', existingConversation.id)
          .eq('user_id', user.id)
          .single();

        if (!participant) {
          console.log('[Buddy Admin] User is not a participant, adding user:', user.id);
          // Add user as participant
          await supabase
            .from('chat_participants')
            .insert({
              conversation_id: existingConversation.id,
              user_id: user.id
            });
        } else {
          console.log('[Buddy Admin] User is already a participant:', user.id);
        }

        // Ensure there is at least one welcome message from admin
        const { data: messages } = await supabase
          .from('messages')
          .select('id')
          .eq('conversation_id', existingConversation.id);
        if (!messages || messages.length === 0) {
          console.log('[Buddy Admin] No welcome message found, inserting welcome message.');
          await supabase
            .from('messages')
            .insert({
              conversation_id: existingConversation.id,
              content: "Welcome to BuddySurf! 👋 I'm your Buddy Admin, here to help you get started. Feel free to ask any questions about the platform!",
              sender_id: 'admin',
              is_admin: true
            });
        } else {
          console.log('[Buddy Admin] Welcome message already exists.');
        }

        console.log('[Buddy Admin] Returning admin conversation:', existingConversation.id);
        return existingConversation;
      }

      // Create new admin conversation
      console.log('[Buddy Admin] Creating new admin conversation.');
      const { data: newConversation, error: conversationError } = await supabase
        .from('chat_conversations')
        .insert({
          is_admin_conversation: true,
          is_announcement_only: true,
          is_group: false,
          last_message: 'Welcome to BuddySurf! 👋',
          last_message_at: new Date().toISOString()
        })
        .select()
        .single();

      if (conversationError) throw conversationError;

      console.log('[Buddy Admin] New admin conversation created:', newConversation.id);
      // Add user as participant
      await supabase
        .from('chat_participants')
        .insert({
          conversation_id: newConversation.id,
          user_id: user.id
        });
      console.log('[Buddy Admin] User added as participant:', user.id);

      // Add welcome message
      await supabase
        .from('messages')
        .insert({
          conversation_id: newConversation.id,
          content: "Welcome to BuddySurf! 👋 I'm your Buddy Admin, here to help you get started. Feel free to ask any questions about the platform!",
          sender_id: 'admin',
          is_admin: true
        });
      console.log('[Buddy Admin] Welcome message inserted.');

      return newConversation;
    },
    enabled: !!user
  });

  // Query to get all conversations
  const { data: conversations, isLoading, error, refetch } = useQuery({
    queryKey: ['conversations', user?.id],
    queryFn: async (): Promise<Conversation[]> => {
      if (!user) return [];

      // Get conversations where the user is a participant
      try {
        console.log('Fetching conversations for user:', user.id);

        // First get all conversations
        const { data: allConversationsData, error: allConversationsError } = await supabase
          .from('chat_conversations')
          .select('*')
          .order('updated_at', { ascending: false });

        if (allConversationsError) {
          console.error('Error fetching all conversations:', allConversationsError);
          return [];
        }

        // Then get all participants for the current user
        const { data: userParticipations, error: participationsError } = await supabase
          .from('chat_participants')
          .select('conversation_id')
          .eq('user_id', user.id);

        if (participationsError) {
          console.error('Error fetching user participations:', participationsError);
          return [];
        }

        // Filter conversations to only include those where the user is a participant
        const conversationIds = userParticipations.map(p => p.conversation_id);
        let conversationsData = allConversationsData.filter(
          conversation => conversationIds.includes(conversation.id)
        );

        // Add admin conversation if it exists and not already included
        if (adminConversation && !conversationsData.find(c => c.id === adminConversation.id)) {
          conversationsData = [adminConversation, ...conversationsData];
        }

        // For each conversation, get all participants
        const conversationsWithParticipants = await Promise.all(
          conversationsData.map(async (conversation) => {
            try {
              const { data: participants } = await supabase
                .from('chat_participants')
                .select('*')
                .eq('conversation_id', conversation.id);

              // For direct conversations, find the other participant
              let otherParticipant = null;
              if (!conversation.is_group && participants && participants.length === 2) {
                // Find the participant that isn't the current user
                const otherParticipantId = participants.find(p => p.user_id !== user.id)?.user_id;
                
                if (otherParticipantId) {
                  // Get the other participant's profile
                  const { data: profile } = await supabase
                    .from('profiles')
                    .select('display_name, username, avatar_url')
                    .eq('id', otherParticipantId)
                    .single();
                    
                  otherParticipant = profile;
                }
              }

              // Get unread count for this conversation
              const { count: unreadCount } = await supabase
                .from('messages')
                .select('id', { count: 'exact' })
                .eq('conversation_id', conversation.id)
                .eq('is_read', false)
                .neq('sender_id', user.id);

              return {
                ...conversation,
                participants: participants || [],
                other_participant: otherParticipant,
                unread_count: unreadCount || 0
              };
            } catch (error) {
              console.error(`Error fetching participants for conversation ${conversation.id}:`, error);
              return {
                ...conversation,
                participants: [],
                other_participant: null,
                unread_count: 0
              };
            }
          })
        );

        return conversationsWithParticipants;
      } catch (error) {
        console.error('Error processing conversations:', error);
        return [];
      }
    },
    enabled: !!user
  });

  // Mutation to create a new conversation
  const createConversation = useMutation({
    mutationFn: async ({ 
      participantIds, 
      isGroup = false,
      initialMessage = 'New conversation started'
    }: { 
      participantIds: string[]; 
      isGroup?: boolean;
      initialMessage?: string;
    }) => {
      if (!user) throw new Error('User must be authenticated');
      
      // Ensure the current user is included in participants
      if (!participantIds.includes(user.id)) {
        participantIds = [user.id, ...participantIds];
      }
      
      // Create the conversation
      const { data: conversation, error } = await supabase
        .from('chat_conversations')
        .insert({
          is_group: isGroup,
          last_message: initialMessage,
          last_message_at: new Date().toISOString()
        })
        .select()
        .single();
        
      if (error) throw error;
      
      // Add all participants
      const participantsToInsert = participantIds.map(id => ({
        conversation_id: conversation.id,
        user_id: id,
        joined_at: new Date().toISOString()
      }));
      
      const { error: participantsError } = await supabase
        .from('chat_participants')
        .insert(participantsToInsert);
        
      if (participantsError) throw participantsError;
      
      // Add initial message
      const { error: messageError } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversation.id,
          sender_id: user.id,
          content: initialMessage,
          created_at: new Date().toISOString()
        });
        
      if (messageError) throw messageError;
      
      return conversation;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['conversations'] });
    }
  });

  // Subscribe to real-time updates with proper browser WebSocket handling
  useEffect(() => {
    if (!user) return;

    // Use a more conservative approach for real-time subscriptions to avoid WebSocket issues
    let timeoutId: NodeJS.Timeout;
    
    const setupSubscription = () => {
      try {
        const channel = supabase
          .channel('chat-updates', {
            config: {
              broadcast: { self: false },
              presence: { key: 'user-presence' }
            }
          })
          .on(
            'postgres_changes',
            {
              event: '*',
              schema: 'public',
              table: 'chat_conversations',
            },
            () => {
              // Debounce the invalidation to prevent excessive requests
              clearTimeout(timeoutId);
              timeoutId = setTimeout(() => {
                queryClient.invalidateQueries({ queryKey: ['chat-conversations'] });
              }, 1000);
            }
          )
          .subscribe((status) => {
            console.log('Chat conversations subscription status:', status);
          });

        return () => {
          clearTimeout(timeoutId);
          supabase.removeChannel(channel);
        };
      } catch (error) {
        console.error('Error setting up real-time subscription:', error);
        // Fallback to polling if real-time fails
        const pollInterval = setInterval(() => {
          queryClient.invalidateQueries({ queryKey: ['conversations'] });
        }, 30000); // Poll every 30 seconds

        return () => clearInterval(pollInterval);
      }
    };

    const cleanup = setupSubscription();
    return cleanup;
  }, [user, queryClient]);

  return {
    conversations: conversations || [],
    adminConversation,
    isLoading: isLoading || adminLoading,
    error,
    refetch,
    createConversation
  };
}
