// This script sets up all the necessary tables and infrastructure for real-time features
// Run with: node src/scripts/setup-realtime-features.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY; // Use service key for admin operations

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupRealtimeFeatures() {
  try {
    console.log('Setting up real-time features...');
    
    // 1. Create message_reactions table
    console.log('Setting up message_reactions table...');
    
    const { error: reactionsError } = await supabase.sql(`
      CREATE TABLE IF NOT EXISTS public.message_reactions (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        message_id UUID NOT NULL REFERENCES public.messages(id) ON DELETE CASCADE,
        conversation_id UUID NOT NULL REFERENCES public.chat_conversations(id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        emoji TEXT NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(message_id, user_id, emoji)
      );
      
      -- Add indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_message_reactions_message_id ON public.message_reactions(message_id);
      CREATE INDEX IF NOT EXISTS idx_message_reactions_user_id ON public.message_reactions(user_id);
      CREATE INDEX IF NOT EXISTS idx_message_reactions_conversation_id ON public.message_reactions(conversation_id);
      
      -- Enable Row Level Security
      ALTER TABLE public.message_reactions ENABLE ROW LEVEL SECURITY;
      
      -- Create RLS policies
      DROP POLICY IF EXISTS "Users can view message reactions" ON public.message_reactions;
      CREATE POLICY "Users can view message reactions"
        ON public.message_reactions
        FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM public.chat_participants
            WHERE chat_participants.conversation_id = message_reactions.conversation_id
            AND chat_participants.user_id = auth.uid()
          )
        );
      
      DROP POLICY IF EXISTS "Users can add their own reactions" ON public.message_reactions;
      CREATE POLICY "Users can add their own reactions"
        ON public.message_reactions
        FOR INSERT
        WITH CHECK (
          user_id = auth.uid() AND
          EXISTS (
            SELECT 1 FROM public.chat_participants
            WHERE chat_participants.conversation_id = message_reactions.conversation_id
            AND chat_participants.user_id = auth.uid()
          )
        );
      
      DROP POLICY IF EXISTS "Users can delete their own reactions" ON public.message_reactions;
      CREATE POLICY "Users can delete their own reactions"
        ON public.message_reactions
        FOR DELETE
        USING (user_id = auth.uid());
    `);
    
    if (reactionsError) {
      console.error('Error creating message_reactions table:', reactionsError);
      process.exit(1);
    }
    
    console.log('message_reactions table created successfully.');
    
    // 2. Create typing_status table
    console.log('Setting up typing_status table...');
    
    const { error: typingError } = await supabase.sql(`
      CREATE TABLE IF NOT EXISTS public.typing_status (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        conversation_id UUID NOT NULL REFERENCES public.chat_conversations(id) ON DELETE CASCADE,
        user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
        is_typing BOOLEAN NOT NULL DEFAULT TRUE,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        UNIQUE(conversation_id, user_id)
      );
      
      -- Add indexes for better performance
      CREATE INDEX IF NOT EXISTS idx_typing_status_conversation_id ON public.typing_status(conversation_id);
      CREATE INDEX IF NOT EXISTS idx_typing_status_user_id ON public.typing_status(user_id);
      
      -- Enable Row Level Security
      ALTER TABLE public.typing_status ENABLE ROW LEVEL SECURITY;
      
      -- Create RLS policies
      DROP POLICY IF EXISTS "Users can view typing status" ON public.typing_status;
      CREATE POLICY "Users can view typing status"
        ON public.typing_status
        FOR SELECT
        USING (
          EXISTS (
            SELECT 1 FROM public.chat_participants
            WHERE chat_participants.conversation_id = typing_status.conversation_id
            AND chat_participants.user_id = auth.uid()
          )
        );
      
      DROP POLICY IF EXISTS "Users can insert their own typing status" ON public.typing_status;
      CREATE POLICY "Users can insert their own typing status"
        ON public.typing_status
        FOR INSERT
        WITH CHECK (user_id = auth.uid());
      
      DROP POLICY IF EXISTS "Users can update their own typing status" ON public.typing_status;
      CREATE POLICY "Users can update their own typing status"
        ON public.typing_status
        FOR UPDATE
        USING (user_id = auth.uid());
      
      DROP POLICY IF EXISTS "Users can delete their own typing status" ON public.typing_status;
      CREATE POLICY "Users can delete their own typing status"
        ON public.typing_status
        FOR DELETE
        USING (user_id = auth.uid());
    `);
    
    if (typingError) {
      console.error('Error creating typing_status table:', typingError);
      process.exit(1);
    }
    
    console.log('typing_status table created successfully.');
    
    // 3. Add last_read_at column to chat_participants table
    console.log('Adding last_read_at column to chat_participants table...');
    
    const { error: alterError } = await supabase.sql(`
      ALTER TABLE public.chat_participants 
      ADD COLUMN IF NOT EXISTS last_read_at TIMESTAMP WITH TIME ZONE;
    `);
    
    if (alterError) {
      console.error('Error adding last_read_at column:', alterError);
      process.exit(1);
    }
    
    console.log('last_read_at column added successfully.');
    
    // 4. Enable realtime for the new tables
    console.log('Enabling realtime for new tables...');
    
    const { error: realtimeError } = await supabase.sql(`
      BEGIN;
      -- Check if the tables are already in the publication
      DO $$
      DECLARE
        message_reactions_exists BOOLEAN;
        typing_status_exists BOOLEAN;
      BEGIN
        SELECT EXISTS (
          SELECT 1
          FROM pg_publication_tables
          WHERE pubname = 'supabase_realtime'
          AND schemaname = 'public'
          AND tablename = 'message_reactions'
        ) INTO message_reactions_exists;
        
        SELECT EXISTS (
          SELECT 1
          FROM pg_publication_tables
          WHERE pubname = 'supabase_realtime'
          AND schemaname = 'public'
          AND tablename = 'typing_status'
        ) INTO typing_status_exists;
        
        IF NOT message_reactions_exists THEN
          -- Add the table to the publication
          ALTER PUBLICATION supabase_realtime ADD TABLE public.message_reactions;
        END IF;
        
        IF NOT typing_status_exists THEN
          -- Add the table to the publication
          ALTER PUBLICATION supabase_realtime ADD TABLE public.typing_status;
        END IF;
      END $$;
      COMMIT;
    `);
    
    if (realtimeError) {
      console.error('Error enabling realtime:', realtimeError);
      process.exit(1);
    }
    
    console.log('Realtime enabled for new tables.');
    console.log('Setup completed successfully!');
  } catch (error) {
    console.error('Error setting up real-time features:', error);
    process.exit(1);
  }
}

setupRealtimeFeatures();
