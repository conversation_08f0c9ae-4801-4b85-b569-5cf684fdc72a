
import React, { useEffect, useState } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { useParams, useNavigate } from 'react-router-dom';
import { useFollows, FollowUser } from '@/hooks/use-follows';
import { UserAvatar } from "@/components/user/UserAvatar";
import { FollowButton } from "@/components/user/FollowButton";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { ArrowLeft, Search, UserPlus, X, Users } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { useAuth } from '@/hooks/use-auth';
import { useProfile } from '@/hooks/use-profile';
import { useToast } from '@/hooks/use-toast';
import { supabase } from '@/integrations/supabase/client';

export default function NetworkPage() {
  const { type = 'followers', userId } = useParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const { data: targetProfile, isLoading: profileLoading } = useProfile(userId);
  const [searchQuery, setSearchQuery] = useState("");
  const [fullFollowersList, setFullFollowersList] = useState<FollowUser[]>([]);
  const [fullFollowingList, setFullFollowingList] = useState<FollowUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { followers, following, isLoading: basicFollowsLoading } = useFollows(userId);

  const isCurrentUserProfile = user?.id === userId;

  useEffect(() => {
    if (!userId || !type) return;

    const fetchAllConnections = async () => {
      setIsLoading(true);

      try {
        if (type === 'followers') {
          // Get all followers (instead of just using the join)
          const { data: followersData, error } = await supabase
            .from('follows')
            .select('follower_id')
            .eq('following_id', userId);

          if (error) throw error;

          if (followersData && followersData.length > 0) {
            // Get profile data for each follower
            const followerIds = followersData.map(item => item.follower_id);
            const { data: followerProfiles, error: profilesError } = await supabase
              .from('profiles')
              .select('id, username, avatar_url, display_name')
              .in('id', followerIds);

            if (profilesError) throw profilesError;

            if (followerProfiles) {
              setFullFollowersList(followerProfiles);
            }
          } else {
            setFullFollowersList([]);
          }
        } else {
          // Get all following (instead of just using the join)
          const { data: followingData, error } = await supabase
            .from('follows')
            .select('following_id')
            .eq('follower_id', userId);

          if (error) throw error;

          if (followingData && followingData.length > 0) {
            // Get profile data for each following
            const followingIds = followingData.map(item => item.following_id);
            const { data: followingProfiles, error: profilesError } = await supabase
              .from('profiles')
              .select('id, username, avatar_url, display_name')
              .in('id', followingIds);

            if (profilesError) throw profilesError;

            if (followingProfiles) {
              setFullFollowingList(followingProfiles);
            }
          } else {
            setFullFollowingList([]);
          }
        }
      } catch (error: any) {
        console.error('Error fetching network data:', error);
        toast({
          title: 'Error loading connections',
          description: error?.message || 'Failed to load network connections',
          variant: 'destructive'
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchAllConnections();
  }, [userId, type, toast]);

  const filteredFollowers = fullFollowersList.filter(user =>
    (user.display_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
     user.username.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const filteredFollowing = fullFollowingList.filter(user =>
    (user.display_name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
     user.username.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  const handleUserClick = (username: string, id: string) => {
    // Use clean URL structure if username is available, otherwise fall back to /profile/id
    navigate(username ? `/${username}` : `/profile/${id}`);
  };

  const currentTab = type === 'followers' ? 'followers' : 'following';

  const handleTabChange = (value: string) => {
    navigate(`/network/${value}/${userId}`);
  };

  const handleFollowChange = () => {
    // Refresh the lists when follow status changes
    if (userId) {
      navigate(0); // Simple way to refresh - consider a more elegant solution in production
    }
  };

  if (profileLoading || basicFollowsLoading) {
    return (
      <MainLayout title="Network">
        <div className="max-w-2xl mx-auto px-4">
          <div className="flex items-center mb-6">
            <Button variant="ghost" size="icon" onClick={() => navigate(-1)} className="mr-2">
              <ArrowLeft className="h-5 w-5" />
            </Button>
            <Skeleton className="h-8 w-48" />
          </div>
          <LoadingSkeleton />
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title={`${currentTab === 'followers' ? 'Followers' : 'Following'}`}>
      <div className="max-w-2xl mx-auto px-4">
        <div className="flex items-center mb-6">
          <Button variant="ghost" size="icon" onClick={() => navigate(-1)} className="mr-2">
            <ArrowLeft className="h-5 w-5" />
          </Button>
          <div className="flex-1">
            <h1 className="text-2xl font-bold">
              {isCurrentUserProfile ? 'Your' : `${targetProfile?.display_name || targetProfile?.username || 'User'}'s`} Network
            </h1>
            <div className="flex items-center text-muted-foreground text-sm mt-1">
              <Users className="h-4 w-4 mr-1" />
              <span>{followers} followers · {following} following</span>
            </div>
          </div>
        </div>

        <Tabs value={currentTab} onValueChange={handleTabChange} className="w-full">
          <TabsList className="mb-4 w-full">
            <TabsTrigger value="followers" className="flex-1">
              Followers
              {fullFollowersList.length > 0 && (
                <Badge variant="secondary" className="ml-2">{fullFollowersList.length}</Badge>
              )}
            </TabsTrigger>
            <TabsTrigger value="following" className="flex-1">
              Following
              {fullFollowingList.length > 0 && (
                <Badge variant="secondary" className="ml-2">{fullFollowingList.length}</Badge>
              )}
            </TabsTrigger>
          </TabsList>

          <div className="mb-4 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
            <Input
              placeholder="Search connections..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 pr-10"
            />
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            )}
          </div>

          <TabsContent value="followers">
            {isLoading ? (
              <LoadingSkeleton />
            ) : filteredFollowers.length > 0 ? (
              <div className="space-y-3">
                {filteredFollowers.map((follower) => (
                  <div key={follower.id} className="flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg">
                    <div
                      className="flex items-center gap-3 flex-1 cursor-pointer"
                      onClick={() => handleUserClick(follower.username || '', follower.id)}
                    >
                      <UserAvatar user={follower} size="md" />
                      <div>
                        <p className="font-medium">{follower.display_name || follower.username}</p>
                        {follower.username && (
                          <p className="text-sm text-gray-500">@{follower.username}</p>
                        )}
                      </div>
                    </div>
                    <FollowButton
                      userId={follower.id}
                      className="ml-2"
                      onFollowChange={handleFollowChange}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                {searchQuery ? (
                  <>
                    <p className="font-medium text-gray-700">No results found</p>
                    <p className="mt-1">Try searching with a different term</p>
                  </>
                ) : (
                  <>
                    <p className="font-medium text-gray-700">No followers yet</p>
                    <p className="mt-1">Connect with other users to grow your network!</p>
                  </>
                )}
              </div>
            )}
          </TabsContent>

          <TabsContent value="following">
            {isLoading ? (
              <LoadingSkeleton />
            ) : filteredFollowing.length > 0 ? (
              <div className="space-y-3">
                {filteredFollowing.map((following) => (
                  <div key={following.id} className="flex items-center justify-between p-3 bg-gray-50 hover:bg-gray-100 transition-colors rounded-lg">
                    <div
                      className="flex items-center gap-3 flex-1 cursor-pointer"
                      onClick={() => handleUserClick(following.username || '', following.id)}
                    >
                      <UserAvatar user={following} size="md" />
                      <div>
                        <p className="font-medium">{following.display_name || following.username}</p>
                        {following.username && (
                          <p className="text-sm text-gray-500">@{following.username}</p>
                        )}
                      </div>
                    </div>
                    <FollowButton
                      userId={following.id}
                      className="ml-2"
                      onFollowChange={handleFollowChange}
                    />
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                {searchQuery ? (
                  <>
                    <p className="font-medium text-gray-700">No results found</p>
                    <p className="mt-1">Try searching with a different term</p>
                  </>
                ) : (
                  <>
                    <p className="font-medium text-gray-700">Not following anyone yet</p>
                    <p className="mt-1 mb-4">Find and follow users to see their content in your feed!</p>
                    <Button
                      variant="outline"
                      onClick={() => navigate('/discover')}
                      size="sm"
                    >
                      <UserPlus className="mr-2 h-4 w-4" />
                      Find Users
                    </Button>
                  </>
                )}
              </div>
            )}
          </TabsContent>
        </Tabs>
      </div>
    </MainLayout>
  );
}

const LoadingSkeleton = () => (
  <div className="space-y-4">
    {[1, 2, 3, 4, 5].map((i) => (
      <div key={i} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
        <div className="flex items-center gap-3 flex-1">
          <Skeleton className="h-10 w-10 rounded-full" />
          <div>
            <Skeleton className="h-4 w-24 mb-1" />
            <Skeleton className="h-3 w-16" />
          </div>
        </div>
        <Skeleton className="h-9 w-20" />
      </div>
    ))}
  </div>
);
