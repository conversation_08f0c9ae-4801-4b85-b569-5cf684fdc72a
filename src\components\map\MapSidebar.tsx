
import React, { useState } from 'react';
import { MapPin, Activity, Briefcase } from "lucide-react";
import { cn } from "@/lib/utils";

interface MapSidebarProps {
  className?: string;
  onCategorySelect?: (category: string) => void;
}

const MapSidebar = ({ className, onCategorySelect }: MapSidebarProps) => {
  const [activeCategory, setActiveCategory] = useState('nearby');
  
  const categories = [
    {
      label: "Nearby",
      value: "nearby",
      icon: <MapPin className="text-primary h-5 w-5 flex-shrink-0" />,
      count: 24
    },
    {
      label: "Activity",
      value: "activity",
      icon: <Activity className="text-accent-orange h-5 w-5 flex-shrink-0" />,
      count: 8
    },
    {
      label: "Hire",
      value: "hire",
      icon: <Briefcase className="text-secondary h-5 w-5 flex-shrink-0" />,
      count: 15
    },
  ];

  const handleCategoryClick = (value: string) => {
    setActiveCategory(value);
    if (onCategorySelect) {
      onCategorySelect(value);
    }
  };

  return (
    <div className={cn("w-64 p-4 flex flex-col gap-2", className)}>
      <div className="flex items-center justify-center py-4">
        <h2 className="text-xl font-semibold text-primary">BuddySurf</h2>
      </div>
      
      <div className="mt-6 flex flex-col gap-2">
        {categories.map(({ label, value, icon, count }) => (
          <button
            key={value}
            onClick={() => handleCategoryClick(value)}
            className={cn(
              "flex items-center justify-between px-4 py-3 rounded-lg transition-colors",
              "hover:bg-accent",
              activeCategory === value ? "bg-accent text-accent-foreground" : "text-foreground/60"
            )}
          >
            <div className="flex items-center gap-3">
              {icon}
              <span className="font-medium">{label}</span>
            </div>
            {count > 0 && (
              <span className="bg-primary/10 text-primary px-2 py-0.5 rounded-full text-xs font-medium">
                {count}
              </span>
            )}
          </button>
        ))}
      </div>
      
      <div className="mt-auto p-4 text-sm text-muted-foreground">
        <p className="opacity-70">BuddySurf</p>
        <p className="opacity-70">Find buddies near you</p>
      </div>
    </div>
  );
};

export default MapSidebar;
