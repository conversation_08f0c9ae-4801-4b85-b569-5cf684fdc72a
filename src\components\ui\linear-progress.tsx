import * as React from 'react';
import { cn } from "@/lib/utils";

interface LinearProgressProps {
  value: number;
  className?: string;
  barClassName?: string;
  labelClassName?: string;
}

export function LinearProgressWithLabel({
  value,
  className,
  barClassName,
  labelClassName
}: LinearProgressProps) {
  return (
    <div className={cn("w-full", className)}>
      <div className="flex justify-between items-center mb-1.5">
        <div className="h-2 bg-gray-100 rounded-full flex-grow relative overflow-hidden shadow-inner">
          <div
            className={cn("h-full bg-primary-purple transition-all duration-500 ease-out rounded-full", barClassName)}
            style={{ width: `${Math.min(100, Math.max(0, value))}%` }}
          />
        </div>
        <div className={cn("ml-3 text-xs font-medium text-primary-purple", labelClassName)}>
          {`${Math.round(value)}%`}
        </div>
      </div>
    </div>
  );
}
