
import { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { supabase } from '@/integrations/supabase/client';
import { User } from '@/types/user';
import { ActivityQueueEntry, QueueManagementStats, QueueAnalytics } from '@/types/activity';

interface JoinQueueOptions {
  status?: 'pending' | 'confirmed' | 'waitlisted';
}

export function useQueueWrapper(activityId: string | undefined, hostId?: string, maxParticipants?: number) {
  const { user } = useAuth();
  const [queueStats, setQueueStats] = useState<QueueManagementStats | null>(null);
  const [queueAnalytics, setQueueAnalytics] = useState<QueueAnalytics | null>(null);
  const [isLoading, setLoading] = useState(false);
  const [isJoining, setJoining] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [queueEntries, setQueueEntries] = useState<ActivityQueueEntry[]>([]);
  const [refetchCounter, setRefetchCounter] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [userEntry, setUserEntry] = useState<ActivityQueueEntry | null>(null);
  const [stats, setStats] = useState<QueueManagementStats | null>(null);
  const [analytics, setAnalytics] = useState<QueueAnalytics | null>(null);

  const refetchQueue = () => {
    setRefetchCounter(prev => prev + 1);
  };

  useEffect(() => {
    if (!activityId) {
      console.warn('Activity ID is undefined, skipping queue stats fetch.');
      return;
    }

    const fetchQueueStats = async () => {
      setLoading(true);
      setError(null);

      try {
        const { data: stats, error: statsError } = await supabase.functions.invoke('get_queue_management_stats', {
          body: { activity_id: activityId }
        });

        if (statsError) {
          console.error('Error fetching queue stats:', statsError);
          setError('Failed to fetch queue stats');
          return;
        }

        if (stats) {
          const statsWithCapacity = {
            ...stats,
            capacityRemaining: Math.max(0, (maxParticipants || 50) - (stats.confirmed || 0)),
            entries: []
          };
          setQueueStats(statsWithCapacity);
          setStats(statsWithCapacity);
        }

        const { data: analytics, error: analyticsError } = await supabase.functions.invoke('get_queue_analytics', {
          body: { activity_id: activityId }
        });

        if (analyticsError) {
          console.error('Error fetching queue analytics:', analyticsError);
          setError('Failed to fetch queue analytics');
          return;
        }

        if (analytics) {
          setQueueAnalytics(analytics);
          setAnalytics(analytics);
        }

        const { data: entries, error: entriesError } = await supabase
          .from('activity_queue')
          .select(`
            *,
            user:user_id (
              id,
              display_name,
              username,
              avatar_url
            )
          `)
          .eq('activity_id', activityId)
          .order('position', { ascending: true });

        if (entriesError) {
          console.error('Error fetching queue entries:', entriesError);
          setError('Failed to fetch queue entries');
          return;
        }

        if (entries) {
          // Create a safe validation function for the user object
          const validateUser = (userObj: any): User => {
            if (!userObj || typeof userObj === 'string' || userObj.error) {
              // Return a default user if the user data is invalid
              return {
                id: 'unknown',
                display_name: 'Unknown User',
                username: '',
                avatar_url: '',
                bio: '',
                is_verified: false
              };
            }
            return userObj as User;
          };

          // Ensure the status is one of the allowed values and handle user data
          const validEntries = entries.map(entry => ({
            ...entry,
            status: validateStatus(entry.status),
            user: validateUser(entry.user)
          })) as ActivityQueueEntry[];

          setQueueEntries(validEntries);
          
          // Find the current user's entry
          if (user) {
            const currentUserEntry = validEntries.find(entry => entry.user_id === user.id);
            if (currentUserEntry) {
              setUserEntry(currentUserEntry);
            } else {
              setUserEntry(null);
            }
          }
        }
      } catch (err) {
        console.error('Unexpected error:', err);
        setError('An unexpected error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchQueueStats();
  }, [activityId, refetchCounter, user, maxParticipants]);

  // Helper function to validate status
  const validateStatus = (status: string): 'confirmed' | 'pending' | 'waitlisted' | 'cancelled' => {
    if (status === 'confirmed' || status === 'pending' || status === 'waitlisted' || status === 'cancelled') {
      return status;
    }
    return 'pending'; // Default to pending if invalid
  };

  const joinQueue = async (options?: JoinQueueOptions) => {
    if (!user?.id || !activityId) {
      setError('You must be logged in to join the queue');
      return { success: false, error: 'Not logged in' };
    }
    
    try {
      setIsProcessing(true);
      setJoining(true);
      
      // Instead of calling a stored procedure directly, let's use a different approach
      // First check if user is already in queue
      const { data: existingEntry } = await supabase
        .from('activity_queue')
        .select('*')
        .eq('activity_id', activityId)
        .eq('user_id', user.id)
        .single();
      
      if (existingEntry) {
        setError('You are already in the queue for this activity');
        return { success: false, error: 'Already in queue' };
      }
      
      // Get current max position
      const { data: positionData } = await supabase
        .from('activity_queue')
        .select('position')
        .eq('activity_id', activityId)
        .order('position', { ascending: false })
        .limit(1)
        .single();
      
      const nextPosition = positionData ? positionData.position + 1 : 1;
      
      // Add user to queue
      const { data, error: insertError } = await supabase
        .from('activity_queue')
        .insert({
          activity_id: activityId,
          user_id: user.id,
          position: nextPosition,
          status: options?.status || 'pending'
        })
        .select();
      
      if (insertError) throw insertError;
      
      // Manually update our local state
      void refetchQueue();
      return { success: true, data };
    } catch (error) {
      console.error('Error joining queue:', error);
      setError('Failed to join queue');
      return { success: false, error };
    } finally {
      setIsProcessing(false);
      setJoining(false);
    }
  };

  const leaveQueue = async () => {
    if (!user?.id || !activityId) {
      setError('You must be logged in to leave the queue');
      return { success: false, error: 'Not logged in' };
    }
    
    if (!userEntry) {
      setError('You are not in this activity queue');
      return { success: false, error: 'Not in queue' };
    }

    try {
      setIsProcessing(true);
      setLoading(true);
      setError(null);

      const { error: updateError } = await supabase
        .from('activity_queue')
        .update({ status: 'cancelled', updated_at: new Date().toISOString() })
        .eq('id', userEntry.id);

      if (updateError) {
        console.error('Error leaving queue:', updateError);
        setError('Failed to leave queue');
        return { success: false, error: updateError };
      }

      setSuccess('Successfully left the queue');
      setUserEntry(null);
      void refetchQueue();
      return { success: true };
    } catch (error) {
      console.error('Unexpected error:', error);
      setError('An unexpected error occurred');
      return { success: false, error };
    } finally {
      setIsProcessing(false);
      setLoading(false);
    }
  };

  const updateStatus = async (entryId: string, newStatus: 'confirmed' | 'pending' | 'waitlisted' | 'cancelled') => {
    try {
      setIsProcessing(true);

      const { error } = await supabase
        .from('activity_queue')
        .update({ 
          status: newStatus,
          updated_at: new Date().toISOString()
        })
        .eq('id', entryId);

      if (error) throw error;

      void refetchQueue();
      return { success: true };
    } catch (error) {
      console.error('Error updating status:', error);
      return { success: false, error };
    } finally {
      setIsProcessing(false);
    }
  };

  const bulkUpdateQueueStatus = async (entries: ActivityQueueEntry[], status: 'confirmed' | 'cancelled') => {
    try {
      setIsProcessing(true);
      
      const entryIds = entries.map(entry => entry.id);
      
      const { error } = await supabase
        .from('activity_queue')
        .update({ 
          status,
          updated_at: new Date().toISOString()
        })
        .in('id', entryIds);
      
      if (error) throw error;
      
      void refetchQueue();
      return { success: true };
    } catch (error) {
      console.error('Error updating status for multiple entries:', error);
      return { success: false, error };
    } finally {
      setIsProcessing(false);
    }
  };

  const recalculatePositions = async () => {
    try {
      setIsProcessing(true);

      const { error } = await supabase.rpc('recalculate_queue_positions', {
        p_activity_id: activityId
      });

      if (error) throw error;

      void refetchQueue();
      return { success: true };
    } catch (error) {
      console.error('Error recalculating positions:', error);
      return { success: false, error };
    } finally {
      setIsProcessing(false);
    }
  };

  // Function to automatically fill available spots
  const autoFillAvailableSpots = async () => {
    try {
      setIsProcessing(true);
      
      if (!queueStats?.capacityRemaining) throw new Error('Queue stats not loaded');
      
      const availableSpots = queueStats.capacityRemaining || 0;
      if (availableSpots <= 0) return { success: true, message: 'No available spots' };
      
      const waitlistedEntries = queueEntries
        .filter(entry => entry.status === 'waitlisted')
        .slice(0, availableSpots);
      
      if (waitlistedEntries.length === 0) {
        return { success: true, message: 'No waitlisted entries' };
      }
      
      await bulkUpdateQueueStatus(waitlistedEntries, 'confirmed');
      
      return { success: true };
    } catch (error) {
      console.error('Error filling available spots:', error);
      return { success: false, error };
    } finally {
      setIsProcessing(false);
    }
  };

  // Function to cancel the entire activity
  const cancelActivity = async () => {
    if (!activityId) return { success: false, error: 'No activity ID' };
    
    try {
      setIsProcessing(true);

      // First cancel all queue entries
      const { error: queueError } = await supabase
        .from('activity_queue')
        .update({ status: 'cancelled' })
        .eq('activity_id', activityId);
      
      if (queueError) throw queueError;
      
      // Then update activity status
      const { error: activityError } = await supabase
        .from('activities')
        .update({ status: 'cancelled' })
        .eq('id', activityId);
      
      if (activityError) throw activityError;
      
      void refetchQueue();
      return { success: true };
    } catch (error) {
      console.error('Error cancelling activity:', error);
      return { success: false, error };
    } finally {
      setIsProcessing(false);
    }
  };

  return {
    queueStats,
    queueAnalytics,
    queueEntries,
    isLoading,
    isJoining,
    error,
    success,
    joinQueue,
    leaveQueue,
    updateStatus,
    bulkUpdateQueueStatus,
    recalculatePositions,
    autoFillAvailableSpots,
    cancelActivity,
    refetchQueue,
    isProcessing,
    userEntry,
    stats,
    analytics
  };
}
