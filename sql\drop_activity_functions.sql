-- <PERSON><PERSON> script to drop activity-related functions with their specific signatures

DROP FUNCTION IF EXISTS auto_fill_activity_queue(p_activity_id uuid, p_max_participants integer) CASCADE;
DROP FUNCTION IF EXISTS verify_activity(p_activity_id uuid, p_notes text) CASCADE;
DROP FUNCTION IF EXISTS get_admin_activity_log(p_limit integer, p_offset integer) CASCADE;
DROP FUNCTION IF EXISTS moderate_activity(p_activity_id uuid, p_status text, p_notes text, p_is_hidden boolean) CASCADE;
DROP FUNCTION IF EXISTS track_activity_view(p_activity_id uuid, p_user_id uuid, p_source text, p_session_id text, p_device_type text, p_ip_address text) CASCADE;
DROP FUNCTION IF EXISTS find_activity_geofences_containing_point(point_lng double precision, point_lat double precision) CASCADE;
DROP FUNCTION IF EXISTS rate_activity(p_activity_id uuid, p_user_id uuid, p_rating integer, p_comment text) CASCADE;
DROP FUNCTION IF EXISTS assign_activity_moderation(p_queue_id uuid, p_moderator_id uuid) CASCADE;
DROP FUNCTION IF EXISTS report_activity_safety_incident(p_activity_id uuid, p_incident_type text, p_description text, p_severity text, p_location_description text, p_incident_time timestamp with time zone) CASCADE;
DROP FUNCTION IF EXISTS get_activity_queue_stats(p_activity_id uuid, p_max_participants integer) CASCADE;
DROP FUNCTION IF EXISTS revoke_activity_verification(p_activity_id uuid, p_reason text, p_notes text) CASCADE;
DROP FUNCTION IF EXISTS get_activity_analytics(p_activity_id uuid) CASCADE;
DROP FUNCTION IF EXISTS add_activity_to_moderation_queue(p_activity_id uuid, p_reason text, p_priority text) CASCADE;
DROP FUNCTION IF EXISTS create_activity_from_template(p_template_id uuid, p_user_id uuid, p_start_time timestamp with time zone, p_end_time timestamp with time zone, p_location point, p_address text, p_title text, p_description text) CASCADE;
DROP FUNCTION IF EXISTS process_activity_refund(p_activity_id uuid, p_user_id uuid, p_amount numeric, p_reason text, p_percentage integer) CASCADE;
DROP FUNCTION IF EXISTS process_activity_cancellation_refunds(p_activity_id uuid, p_reason text, p_refund_percentage integer) CASCADE;
DROP FUNCTION IF EXISTS auto_flag_activity(p_activity_id uuid, p_content text) CASCADE;
