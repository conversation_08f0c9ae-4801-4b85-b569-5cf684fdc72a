
import { useMemo } from 'react';
import { Location } from '@/types/activity';

export function useLocationFormatter() {
  const formatLocation = useMemo(() => {
    return (location: Location | null | undefined): Location | null => {
      if (!location) return null;

      // If location has x and y, convert to lng/lat format
      if ('x' in location && 'y' in location && location.x !== undefined && location.y !== undefined) {
        return {
          x: location.x,
          y: location.y,
          lng: location.x,
          lat: location.y,
          longitude: location.x,
          latitude: location.y,
          coordinates: [location.x, location.y],
          address: location.address
        };
      }

      // If location has lng/lat, keep as is but also add x/y
      if ('lng' in location && 'lat' in location && location.lng !== undefined && location.lat !== undefined) {
        return {
          x: location.lng,
          y: location.lat,
          lng: location.lng,
          lat: location.lat,
          longitude: location.lng,
          latitude: location.lat,
          coordinates: [location.lng, location.lat],
          address: location.address
        };
      }

      // If location has longitude/latitude, convert to standard format
      if ('longitude' in location && 'latitude' in location && location.longitude !== undefined && location.latitude !== undefined) {
        return {
          x: location.longitude,
          y: location.latitude,
          lng: location.longitude,
          lat: location.latitude,
          longitude: location.longitude,
          latitude: location.latitude,
          coordinates: [location.longitude, location.latitude],
          address: location.address
        };
      }

      // If location has coordinates array, use that
      if ('coordinates' in location && Array.isArray(location.coordinates) && location.coordinates.length >= 2) {
        return {
          x: location.coordinates[0],
          y: location.coordinates[1],
          lng: location.coordinates[0],
          lat: location.coordinates[1],
          longitude: location.coordinates[0],
          latitude: location.coordinates[1],
          coordinates: location.coordinates,
          address: location.address
        };
      }

      return location;
    };
  }, []);

  const getDisplayAddress = useMemo(() => {
    return (location: Location | null | undefined): string => {
      if (!location) return 'Location not specified';

      if (location.address) {
        return location.address;
      }

      const lng = location.longitude || location.lng || location.x;
      const lat = location.latitude || location.lat || location.y;

      if (lng !== undefined && lat !== undefined) {
        return `${lat.toFixed(4)}, ${lng.toFixed(4)}`;
      }

      return 'Location not specified';
    };
  }, []);

  const normalizeUserLocation = useMemo(() => {
    return (location: any): any => {
      if (!location) {
        return { x: 0, y: 0, latitude: 0, longitude: 0, lat: 0, lng: 0 };
      }
      
      const formatted = formatLocation(location);
      if (!formatted) {
        return { x: 0, y: 0, latitude: 0, longitude: 0, lat: 0, lng: 0 };
      }

      return {
        ...location,
        location: formatted,
        user_id: location.user_id || location.id,
        id: location.id || location.user_id,
        username: location.username || location.display_name,
        display_name: location.display_name || location.username,
        avatar_url: location.avatar_url,
        bio: location.bio,
        is_online: location.is_online || true,
        created_at: location.created_at || new Date().toISOString(),
        updated_at: location.updated_at || new Date().toISOString()
      };
    };
  }, [formatLocation]);

  return {
    formatLocation,
    getDisplayAddress,
    normalizeUserLocation
  };
}
