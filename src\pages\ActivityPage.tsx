import React, { useState, useEffect } from 'react';
import { MainLayout } from '@/components/layouts/MainLayout';
import { useActivity } from '@/hooks/use-activities';
import { useParams, Link } from 'react-router-dom';
import { useAuth } from '@/hooks/use-auth';
import { 
  Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle 
} from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { ActivityHeader } from '@/components/activity/ActivityHeader';
import { ActivityContent } from '@/components/activity/ActivityContent';
import { ActivityMap } from '@/components/activity/ActivityMap';
import { ActivityParticipants } from '@/components/activity/ActivityParticipants';
import { ActivityGroupChat } from '@/components/activity/ActivityGroupChat';
import { ActivityQueueSystem } from '@/components/activity/ActivityQueueSystem';
import { HeartIcon, CalendarIcon, MessageSquareIcon, XIcon } from "lucide-react";
import { ActivityComments } from '@/components/activity/ActivityComments';
import { Button } from '@/components/ui/button';
import { useQueueWrapper } from '@/hooks/use-queue-wrapper';
import { useToast } from '@/hooks/use-toast';
import { useEmergencyContact } from '@/hooks/use-emergency-contact';
import { EmergencyContactButton } from '@/components/activity/EmergencyContactButton';
import { ShareActivityButton } from '@/components/activity/ShareActivityButton';
import { ActivityQueueEntry } from '@/types/activity';

export default function ActivityPage() {
  const { activityId } = useParams<{ activityId: string }>();
  const activityResult = useActivity(activityId || '');
  const { data: activity, isLoading, error } = activityResult;
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('details');
  const { toast } = useToast();
  const { setEmergencyContact } = useEmergencyContact();
  
  const queueManager = useQueueWrapper(activityId);
  
  // Extract properties needed by components
  const { 
    queueEntries,
    queueStats,
    isLoading: isQueueLoading,
    userEntry,
    leaveQueue,
    isProcessing,
    bulkUpdateQueueStatus,
    updateStatus
  } = queueManager;
  
  // Set emergency contact for the activity when it loads
  useEffect(() => {
    if (activity) {
      setEmergencyContact({
        title: activity.title,
        location: activity.address || 'Unknown location',
        startTime: activity.start_time,
        endTime: activity.end_time,
        hostName: activity.host?.display_name || 'Unknown host'
      });
    }
  }, [activity, setEmergencyContact]);

  // Handle error state
  if (error) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center p-8">
          <XIcon className="h-12 w-12 text-red-500 mb-4" />
          <h1 className="text-2xl font-bold mb-2">Activity Not Found</h1>
          <p className="text-muted-foreground mb-4">
            This activity may have been removed or you don't have access to it.
          </p>
          <Button asChild>
            <Link to="/activities">Browse Activities</Link>
          </Button>
        </div>
      </MainLayout>
    );
  }

  // Handle loading state
  if (isLoading || !activity) {
    return (
      <MainLayout>
        <div className="container mx-auto p-4">
          <div className="animate-pulse">
            <div className="h-8 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-80 bg-gray-200 rounded mb-4"></div>
            <div className="h-40 bg-gray-200 rounded mb-4"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </div>
      </MainLayout>
    );
  }

  // Calculate if user can join the activity
  const isUserHost = user?.id === activity.host_id;
  const isParticipant = queueEntries?.some(entry => 
    entry.user_id === user?.id && 
    (entry.status === 'confirmed' || entry.status === 'pending')
  );
  const canJoin = user && !isUserHost && !isParticipant;

  // Create a wrapper for bulkUpdateQueueStatus to match expected signature
  const handleBulkStatusUpdate = (status: 'confirmed' | 'cancelled') => {
    // Get all entries and update their status
    const entries = queueEntries || [];
    if (entries.length > 0) {
      bulkUpdateQueueStatus(entries, status);
    }
  };

  return (
    <MainLayout>
      <div className="container mx-auto p-4 mb-4">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left column: Activity details */}
          <div className="lg:col-span-2 space-y-6">
            <ActivityHeader activity={activity} canJoin={canJoin} />
            
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="details">
                  <CalendarIcon className="h-4 w-4 mr-2" />
                  Details
                </TabsTrigger>
                <TabsTrigger value="participants">
                  <Users className="h-4 w-4 mr-2" />
                  Participants
                </TabsTrigger>
                <TabsTrigger value="comments">
                  <HeartIcon className="h-4 w-4 mr-2" />
                  Comments
                </TabsTrigger>
              </TabsList>
              
              <TabsContent value="details" className="space-y-4">
                <ActivityContent activity={activity} />
                <ActivityMap activity={activity} />
              </TabsContent>
              
              <TabsContent value="participants" className="space-y-4">
                <Card>
                  <CardHeader>
                    <CardTitle>Activity Participants</CardTitle>
                    <CardDescription>
                      People who have joined this activity
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ActivityParticipants 
                      activityId={activity.id} 
                      isHost={isUserHost} 
                      queueStats={queueStats ? {
                        confirmed: queueStats.confirmed,
                        pending: queueStats.pending,
                        waitlisted: queueStats.waitlisted,
                        total: queueStats.total,
                        entries: queueStats.entries || []
                      } : undefined}
                      queueEntries={queueEntries}
                      onUpdateStatus={updateStatus}
                      onBulkUpdate={handleBulkStatusUpdate}
                    />
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="comments" className="space-y-4">
                <ActivityComments activityId={activity.id} />
              </TabsContent>
            </Tabs>
          </div>
          
          {/* Right column: Sidebar content */}
          <div className="space-y-6">
            {/* Chat component */}
            <ActivityGroupChat activity={activity} />
            
            {/* Queue system */}
            <ActivityQueueSystem activity={activity} />
            
            {/* Host info */}
            <Card>
              <CardHeader>
                <CardTitle>Activity Host</CardTitle>
              </CardHeader>
              <CardContent>
                {activity.host ? (
                  <div className="flex items-center space-x-4">
                    <div className="h-12 w-12 rounded-full bg-muted flex items-center justify-center overflow-hidden">
                      {activity.host.avatar_url ? (
                        <img 
                          src={activity.host.avatar_url} 
                          alt={activity.host.display_name || "Host"} 
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <UserIcon className="h-6 w-6 text-muted-foreground" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium">
                        {activity.host.display_name || activity.host.username || "Unknown Host"}
                      </p>
                      <Button variant="link" className="p-0" asChild>
                        <Link to={`/profile/${activity.host.id}`}>
                          View Profile
                        </Link>
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div className="text-muted-foreground">Host information unavailable</div>
                )}
              </CardContent>
            </Card>
            
            {/* Action buttons */}
            <div className="flex flex-col space-y-2">
              <ShareActivityButton activity={activity} />
              <EmergencyContactButton />
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

// Import missing components
import { UserIcon, Users } from "lucide-react";
