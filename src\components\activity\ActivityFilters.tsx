
import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Slider } from "@/components/ui/slider";
import { Toggle } from "@/components/ui/toggle";
import { Badge } from "@/components/ui/badge";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Separator } from "@/components/ui/separator";
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { CalendarIcon, Filter, MapPin, DollarSign, Clock, X } from "lucide-react";
import { format, addDays } from "date-fns";
import { ActivityFilters as FilterOptions } from '@/hooks/use-filtered-activities';
import { cn } from '@/lib/utils';
import { useActivityCategories } from '@/hooks/use-activity-categories';

interface ActivityFiltersProps {
  filters: FilterOptions;
  onFilterChange: (filters: FilterOptions) => void;
  userLocation?: { x: number; y: number } | null;
  className?: string;
}

export function ActivityFiltersComponent({
  filters,
  onFilterChange,
  userLocation,
  className
}: ActivityFiltersProps) {
  const { data: categories, isLoading: categoriesLoading } = useActivityCategories();
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<FilterOptions>(filters);
  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([
    filters.dateRange?.[0] || null,
    filters.dateRange?.[1] || null
  ]);
  
  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.selectedCategories.length > 0) count++;
    if (!filters.showPaidActivities || !filters.showFreeActivities) count++;
    if (filters.showTodayOnly) count++;
    if (filters.showUpcomingOnly) count++;
    if (filters.showNearby) count++;
    if (filters.dateRange && (filters.dateRange[0] || filters.dateRange[1])) count++;
    if (filters.searchText) count++;
    return count;
  };
  
  const activeFilterCount = getActiveFilterCount();
  
  const handleFilterChange = (key: keyof FilterOptions, value: any) => {
    setLocalFilters(prev => ({ ...prev, [key]: value }));
  };
  
  const applyFilters = () => {
    onFilterChange({
      ...localFilters,
      dateRange: dateRange
    });
    setIsFilterOpen(false);
  };
  
  const resetFilters = () => {
    const resetFilters: FilterOptions = {
      selectedCategories: [],
      showPaidActivities: true,
      showFreeActivities: true,
      showTodayOnly: false,
      showUpcomingOnly: false,
      showNearby: false,
      radiusInKm: 5,
      searchText: '',
      dateRange: [null, null],
      showPopular: false,
    };
    
    setLocalFilters(resetFilters);
    setDateRange([null, null]);
    onFilterChange(resetFilters);
    setIsFilterOpen(false);
  };
  
  const toggleCategoryFilter = (categoryId: string) => {
    const categories = localFilters.selectedCategories.includes(categoryId)
      ? localFilters.selectedCategories.filter(id => id !== categoryId)
      : [...localFilters.selectedCategories, categoryId];
    
    handleFilterChange('selectedCategories', categories);
  };
  
  return (
    <div className={className}>
      <div className="flex flex-wrap items-center gap-2">
        <Sheet open={isFilterOpen} onOpenChange={setIsFilterOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" size="sm" className="relative">
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {activeFilterCount > 0 && (
                <Badge className="h-5 w-5 p-0 flex items-center justify-center absolute -top-2 -right-2">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </SheetTrigger>
          <SheetContent side="right" className="w-[300px] sm:w-[400px] overflow-y-auto">
            <SheetHeader>
              <SheetTitle>Filter Activities</SheetTitle>
              <SheetDescription>
                Customize your activity search
              </SheetDescription>
            </SheetHeader>
            
            <div className="mt-4 space-y-6">
              <div className="space-y-2">
                <Label htmlFor="search">Search</Label>
                <Input 
                  id="search"
                  placeholder="Search activities..." 
                  value={localFilters.searchText || ''}
                  onChange={(e) => handleFilterChange('searchText', e.target.value)}
                />
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <Label>Categories</Label>
                <div className="flex flex-wrap gap-2 mt-2">
                  {categoriesLoading ? (
                    <div className="text-sm text-muted-foreground">Loading categories...</div>
                  ) : categories && categories.length > 0 ? (
                    categories.map((category) => (
                      <Badge 
                        key={category.id}
                        variant={localFilters.selectedCategories.includes(category.id) ? "default" : "outline"}
                        className="cursor-pointer"
                        onClick={() => toggleCategoryFilter(category.id)}
                      >
                        {category.name}
                      </Badge>
                    ))
                  ) : (
                    <div className="text-sm text-muted-foreground">No categories available</div>
                  )}
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <Label>Price</Label>
                <div className="flex gap-2 mt-2">
                  <Toggle
                    pressed={localFilters.showFreeActivities}
                    onPressedChange={(pressed) => handleFilterChange('showFreeActivities', pressed)}
                    variant="outline"
                    className={cn(
                      "flex-1",
                      localFilters.showFreeActivities && "border-primary"
                    )}
                  >
                    Free
                  </Toggle>
                  <Toggle
                    pressed={localFilters.showPaidActivities}
                    onPressedChange={(pressed) => handleFilterChange('showPaidActivities', pressed)}
                    variant="outline"
                    className={cn(
                      "flex-1",
                      localFilters.showPaidActivities && "border-primary"
                    )}
                  >
                    <DollarSign className="h-4 w-4 mr-1" />
                    Paid
                  </Toggle>
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <Label>Time</Label>
                <div className="flex gap-2 mt-2">
                  <Toggle
                    pressed={localFilters.showTodayOnly}
                    onPressedChange={(pressed) => handleFilterChange('showTodayOnly', pressed)}
                    variant="outline"
                    className={cn(
                      "flex-1",
                      localFilters.showTodayOnly && "border-primary"
                    )}
                  >
                    Today
                  </Toggle>
                  <Toggle
                    pressed={localFilters.showUpcomingOnly}
                    onPressedChange={(pressed) => handleFilterChange('showUpcomingOnly', pressed)}
                    variant="outline"
                    className={cn(
                      "flex-1",
                      localFilters.showUpcomingOnly && "border-primary"
                    )}
                  >
                    <Clock className="h-4 w-4 mr-1" />
                    Upcoming
                  </Toggle>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label>Date Range</Label>
                <div className="flex flex-col gap-2 mt-2">
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start text-left font-normal w-full",
                          dateRange[0] && "text-primary"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateRange[0] ? format(dateRange[0], "PPP") : "Start date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={dateRange[0] || undefined}
                        onSelect={(date) => setDateRange([date, dateRange[1]])}
                        disabled={(date) => date < new Date()}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant="outline"
                        className={cn(
                          "justify-start text-left font-normal w-full",
                          dateRange[1] && "text-primary"
                        )}
                      >
                        <CalendarIcon className="mr-2 h-4 w-4" />
                        {dateRange[1] ? format(dateRange[1], "PPP") : "End date"}
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={dateRange[1] || undefined}
                        onSelect={(date) => setDateRange([dateRange[0], date])}
                        disabled={(date) => (dateRange[0] ? date < dateRange[0] : date < new Date())}
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                  
                  {(dateRange[0] || dateRange[1]) && (
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => setDateRange([null, null])}
                      className="w-full"
                    >
                      <X className="h-4 w-4 mr-1" />
                      Clear dates
                    </Button>
                  )}
                </div>
              </div>
              
              <Separator />
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="nearby">Nearby Activities</Label>
                  <Toggle
                    id="nearby"
                    pressed={localFilters.showNearby}
                    onPressedChange={(pressed) => {
                      if (!userLocation && pressed) {
                        return;
                      }
                      handleFilterChange('showNearby', pressed);
                    }}
                    disabled={!userLocation}
                    className={cn(
                      "data-[state=on]:bg-primary",
                      !userLocation && "cursor-not-allowed opacity-50"
                    )}
                  >
                    <MapPin className="h-4 w-4" />
                  </Toggle>
                </div>
                
                {!userLocation && (
                  <p className="text-xs text-muted-foreground">
                    Enable location services to use this filter
                  </p>
                )}
                
                {localFilters.showNearby && userLocation && (
                  <div className="space-y-2 mt-2">
                    <div className="flex justify-between items-center">
                      <Label htmlFor="radius">Distance (km): {localFilters.radiusInKm}</Label>
                    </div>
                    <Slider
                      id="radius"
                      defaultValue={[localFilters.radiusInKm || 5]}
                      min={1}
                      max={50}
                      step={1}
                      onValueChange={(value) => handleFilterChange('radiusInKm', value[0])}
                    />
                  </div>
                )}
              </div>
              
              <div className="flex gap-2 pt-4">
                <Button variant="outline" className="flex-1" onClick={resetFilters}>
                  Reset
                </Button>
                <Button className="flex-1" onClick={applyFilters}>
                  Apply Filters
                </Button>
              </div>
            </div>
          </SheetContent>
        </Sheet>
        
        <div className="flex gap-1 overflow-x-auto pb-1 scrollbar-thin">
          <Toggle
            pressed={filters.showTodayOnly}
            onPressedChange={(pressed) => {
              onFilterChange({ ...filters, showTodayOnly: pressed });
            }}
            size="sm"
            variant="outline"
          >
            Today
          </Toggle>
          
          <Toggle
            pressed={filters.showFreeActivities && !filters.showPaidActivities}
            onPressedChange={(pressed) => {
              onFilterChange({ 
                ...filters, 
                showFreeActivities: pressed, 
                showPaidActivities: !pressed 
              });
            }}
            size="sm"
            variant="outline"
          >
            Free
          </Toggle>
          
          <Toggle
            pressed={filters.showPaidActivities && !filters.showFreeActivities}
            onPressedChange={(pressed) => {
              onFilterChange({ 
                ...filters, 
                showPaidActivities: pressed, 
                showFreeActivities: !pressed 
              });
            }}
            size="sm"
            variant="outline"
          >
            Paid
          </Toggle>
          
          {userLocation && (
            <Toggle
              pressed={filters.showNearby}
              onPressedChange={(pressed) => {
                onFilterChange({ 
                  ...filters, 
                  showNearby: pressed,
                  radiusInKm: pressed ? (filters.radiusInKm || 5) : undefined,
                  userLocation: pressed ? userLocation : undefined
                });
              }}
              size="sm"
              variant="outline"
            >
              Nearby
            </Toggle>
          )}
        </div>
        
        {activeFilterCount > 0 && (
          <div className="flex gap-1 overflow-x-auto scrollbar-thin">
            {filters.selectedCategories.length > 0 && categories && (
              categories
                .filter(cat => filters.selectedCategories.includes(cat.id))
                .map(cat => (
                  <Badge key={cat.id} variant="secondary" className="flex items-center gap-1">
                    {cat.name}
                    <X 
                      className="h-3 w-3 cursor-pointer" 
                      onClick={() => {
                        const newCategories = filters.selectedCategories.filter(id => id !== cat.id);
                        onFilterChange({...filters, selectedCategories: newCategories});
                      }}
                    />
                  </Badge>
                ))
            )}
            
            {filters.searchText && (
              <Badge variant="secondary" className="flex items-center gap-1">
                "{filters.searchText}"
                <X 
                  className="h-3 w-3 cursor-pointer" 
                  onClick={() => onFilterChange({...filters, searchText: ''})}
                />
              </Badge>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export { ActivityFiltersComponent as ActivityFilters };
