import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { UserAvatar } from "@/components/user/UserAvatar";
import { Badge } from "@/components/ui/badge";
import { FollowButton } from "@/components/user/FollowButton";
import { useSuggestedUsers, SuggestedUser } from "@/hooks/use-suggested-users";
import { useNavigate } from "react-router-dom";
import { RefreshCw, Users, UserPlus, MapPin, Sparkles } from "lucide-react";
import { formatDistance } from "@/utils/distance-calculator";

interface UserSuggestionsProps {
  limit?: number;
  className?: string;
  onFollowChange?: () => void;
}

export function UserSuggestions({ 
  limit = 6, 
  className = "",
  onFollowChange
}: UserSuggestionsProps) {
  const navigate = useNavigate();
  const { data: suggestedUsers, isLoading, refetch } = useSuggestedUsers(limit);
  
  const handleUserClick = (username: string, id: string) => {
    // Use clean URL structure if username is available, otherwise fall back to /profile/id
    navigate(username ? `/${username}` : `/profile/${id}`);
  };
  
  const handleRefresh = () => {
    refetch();
  };
  
  if (isLoading) {
    return <UserSuggestionsSkeleton />;
  }
  
  if (!suggestedUsers || suggestedUsers.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="text-lg flex items-center">
            <Users className="h-5 w-5 mr-2" />
            People to Follow
          </CardTitle>
          <CardDescription>
            Find people to connect with
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6 text-muted-foreground">
            <p>No suggestions available right now</p>
            <Button 
              variant="outline" 
              size="sm" 
              className="mt-4"
              onClick={handleRefresh}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh Suggestions
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex justify-between items-center">
          <CardTitle className="text-lg flex items-center">
            <Sparkles className="h-5 w-5 mr-2 text-primary-purple" />
            Suggested for You
          </CardTitle>
          <Button 
            variant="ghost" 
            size="sm" 
            onClick={handleRefresh}
            className="h-8 w-8 p-0"
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
        <CardDescription>
          People you might want to follow
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {suggestedUsers.map((user) => (
            <SuggestedUserCard 
              key={user.id} 
              user={user} 
              onClick={() => handleUserClick(user.username || '', user.id)}
              onFollowChange={onFollowChange}
            />
          ))}
        </div>
        <div className="mt-4 text-center">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => navigate('/network')}
            className="w-full"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Find More People
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}

interface SuggestedUserCardProps {
  user: SuggestedUser;
  onClick: () => void;
  onFollowChange?: () => void;
}

function SuggestedUserCard({ user, onClick, onFollowChange }: SuggestedUserCardProps) {
  return (
    <div className="flex items-start p-3 rounded-lg border hover:bg-accent/5 transition-colors">
      <div className="flex-1 min-w-0 cursor-pointer" onClick={onClick}>
        <div className="flex items-center">
          <UserAvatar user={user} size="md" className="mr-3" />
          <div className="flex-1 min-w-0">
            <p className="font-medium truncate">{user.display_name || user.username}</p>
            {user.username && (
              <p className="text-xs text-muted-foreground truncate">@{user.username}</p>
            )}
            {user.reason && (
              <Badge variant="outline" className="mt-1 text-xs bg-primary-purple/10 text-primary-purple border-primary-purple/20">
                {user.reason}
              </Badge>
            )}
            {user.distance !== undefined && (
              <div className="flex items-center mt-1 text-xs text-muted-foreground">
                <MapPin className="h-3 w-3 mr-1" />
                {formatDistance(user.distance)}
              </div>
            )}
          </div>
        </div>
      </div>
      <FollowButton 
        userId={user.id} 
        size="sm"
        onFollowChange={onFollowChange}
      />
    </div>
  );
}

function UserSuggestionsSkeleton() {
  return (
    <Card>
      <CardHeader>
        <Skeleton className="h-6 w-48" />
        <Skeleton className="h-4 w-64 mt-1" />
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {Array(4).fill(0).map((_, i) => (
            <div key={i} className="flex items-center p-3 rounded-lg border">
              <Skeleton className="h-10 w-10 rounded-full mr-3" />
              <div className="flex-1">
                <Skeleton className="h-5 w-24 mb-1" />
                <Skeleton className="h-3 w-16" />
                <Skeleton className="h-4 w-20 mt-2" />
              </div>
              <Skeleton className="h-8 w-16 rounded-md" />
            </div>
          ))}
        </div>
        <Skeleton className="h-9 w-full mt-4 rounded-md" />
      </CardContent>
    </Card>
  );
}
