
import React, { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import { UnifiedMapUser } from '@/types/map';
import { Activity } from '@/types/activity';
import { ActivityMarker, createActivityMarker } from './ActivityMarker';
import { EnhancedUserMarker, createUserMarker } from './EnhancedUserMarker';

interface EnhancedMarkerManagerProps {
  map: mapboxgl.Map;
  users: UnifiedMapUser[];
  activities: Activity[];
  selectedUser: UnifiedMapUser | null;
  selectedActivity: Activity | null;
  onUserClick: (user: UnifiedMapUser) => void;
  onActivityClick: (activity: Activity) => void;
  showUsers?: boolean;
  showActivities?: boolean;
}

export function EnhancedMarkerManager({
  map,
  users,
  activities,
  selectedUser,
  selectedActivity,
  onUserClick,
  onActivityClick,
  showUsers = true,
  showActivities = true
}: EnhancedMarkerManagerProps) {
  const userMarkersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});
  const activityMarkersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});

  // Handle users markers
  useEffect(() => {
    if (!map || !showUsers) return;

    // Clear existing user markers that are no longer present
    Object.keys(userMarkersRef.current).forEach(userId => {
      const userExists = users.some(user => user.user_id === userId);
      if (!userExists) {
        userMarkersRef.current[userId].remove();
        delete userMarkersRef.current[userId];
      }
    });

    // Add or update user markers
    users.forEach(user => {
      if (!user.location) return;

      const isSelected = selectedUser?.user_id === user.user_id;

      // Update existing marker
      if (userMarkersRef.current[user.user_id]) {
        userMarkersRef.current[user.user_id].setLngLat([user.location.x, user.location.y]);

        // Update marker element
        const markerEl = userMarkersRef.current[user.user_id].getElement();
        if (markerEl) {
          const userMarkerEl = createUserMarker(user, isSelected);
          if (userMarkerEl) {
            markerEl.innerHTML = userMarkerEl.innerHTML;
          }
        }
      }
      // Create new marker
      else {
        const el = createUserMarker(user, isSelected);

        if (el) {
          const marker = new mapboxgl.Marker({
            element: el,
            anchor: 'bottom'
          })
            .setLngLat([user.location.x, user.location.y])
            .addTo(map);

          el.addEventListener('click', () => onUserClick(user));
          userMarkersRef.current[user.user_id] = marker;
        }
      }
    });

    return () => {
      Object.values(userMarkersRef.current).forEach(marker => marker.remove());
      userMarkersRef.current = {};
    };
  }, [map, users, selectedUser, onUserClick, showUsers]);

  // Handle activity markers
  useEffect(() => {
    if (!map || !showActivities) return;

    // Clear existing activity markers
    Object.keys(activityMarkersRef.current).forEach(activityId => {
      const activityExists = activities.some(activity => activity.id === activityId);
      if (!activityExists) {
        activityMarkersRef.current[activityId].remove();
        delete activityMarkersRef.current[activityId];
      }
    });

    // Add or update activity markers
    activities.forEach(activity => {
      if (!activity.location) return;

      const isSelected = selectedActivity?.id === activity.id;

      // Update existing marker
      if (activityMarkersRef.current[activity.id]) {
        activityMarkersRef.current[activity.id].setLngLat([activity.location.x, activity.location.y]);

        // Update marker element
        const markerEl = activityMarkersRef.current[activity.id].getElement();
        if (markerEl) {
          const activityMarkerElement = createActivityMarker(activity, () => onActivityClick(activity), isSelected);
          if (activityMarkerElement) {
            markerEl.innerHTML = activityMarkerElement.innerHTML;
          }
        }
      }
      // Create new marker
      else {
        const el = createActivityMarker(activity, () => onActivityClick(activity), isSelected);

        if (el) {
          const marker = new mapboxgl.Marker({
            element: el,
            anchor: 'bottom'
          })
            .setLngLat([activity.location.x, activity.location.y])
            .addTo(map);

          activityMarkersRef.current[activity.id] = marker;
        }
      }
    });

    return () => {
      Object.values(activityMarkersRef.current).forEach(marker => marker.remove());
      activityMarkersRef.current = {};
    };
  }, [map, activities, selectedActivity, onActivityClick, showActivities]);

  return null;
}
