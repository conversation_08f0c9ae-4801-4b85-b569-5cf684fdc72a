
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, MapPin, Plus, Star } from 'lucide-react';
import { SavedLocation, useSavedLocations } from '@/hooks/use-saved-locations';
import { SavedLocationModal } from './SavedLocationModal';
import { useToast } from '@/hooks/use-toast';

interface SavedLocationsPanelProps {
  currentLocation?: { x: number; y: number };
  onLocationSelect?: (location: { x: number; y: number; name: string }) => void;
  className?: string;
}

export function SavedLocationsPanel({
  currentLocation,
  onLocationSelect,
  className,
}: SavedLocationsPanelProps) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const { savedLocations, isLoading } = useSavedLocations();
  const { toast } = useToast();

  const handleLocationSelect = (location: SavedLocation) => {
    if (onLocationSelect && location.location) {
      onLocationSelect({
        x: location.location.x,
        y: location.location.y,
        name: location.name
      });
      
      toast({
        title: "Location selected",
        description: `Navigating to ${location.name}`
      });
    }
  };

  return (
    <div className={className}>
      <Card className="shadow-md bg-background/95 backdrop-blur-sm">
        <CardHeader className="py-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm font-medium">Saved Locations</CardTitle>
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              disabled={!currentLocation}
              onClick={() => setIsModalOpen(true)}
            >
              <Plus className="h-4 w-4" />
              <span className="sr-only">Save current location</span>
            </Button>
          </div>
        </CardHeader>
        <CardContent className="py-2">
          {isLoading ? (
            <div className="flex justify-center py-4">
              <Loader2 className="h-5 w-5 animate-spin text-muted-foreground" />
            </div>
          ) : savedLocations.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-6 text-center">
              <MapPin className="h-8 w-8 text-muted-foreground opacity-50" />
              <p className="mt-2 text-sm font-medium">No saved locations</p>
              <p className="text-xs text-muted-foreground">
                Save your favorite places for quick access
              </p>
            </div>
          ) : (
            <ScrollArea className="h-[180px] pr-3">
              {savedLocations.map((location) => (
                <div
                  key={location.id}
                  className="group mb-2 flex cursor-pointer items-center justify-between rounded-md border border-border p-2 hover:border-primary transition-colors"
                  onClick={() => handleLocationSelect(location)}
                >
                  <div className="flex items-center gap-2">
                    <Star className="h-4 w-4 text-muted-foreground group-hover:text-primary" />
                    <div>
                      <p className="text-sm font-medium">{location.name}</p>
                      {location.description && (
                        <p className="text-xs text-muted-foreground line-clamp-1">
                          {location.description}
                        </p>
                      )}
                    </div>
                  </div>
                  {location.category && (
                    <span className="text-xs bg-primary/10 text-primary px-2 py-0.5 rounded-full">
                      {location.category}
                    </span>
                  )}
                </div>
              ))}
            </ScrollArea>
          )}
        </CardContent>
      </Card>

      {currentLocation && (
        <SavedLocationModal
          isOpen={isModalOpen}
          onClose={() => setIsModalOpen(false)}
          location={currentLocation}
        />
      )}
    </div>
  );
}
