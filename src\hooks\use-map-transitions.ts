
import { useCallback } from 'react';
import mapboxgl from 'mapbox-gl';

interface TransitionOptions {
  center?: [number, number];
  zoom?: number;
  pitch?: number;
  bearing?: number;
  duration?: number;
  padding?: mapboxgl.PaddingOptions;
}

export function useMapTransitions() {
  // Standard flyTo with eased animation
  const flyToLocation = useCallback((
    map: mapboxgl.Map, 
    options: TransitionOptions
  ) => {
    map.flyTo({
      center: options.center,
      zoom: options.zoom,
      pitch: options.pitch,
      bearing: options.bearing,
      duration: options.duration || 2000,
      padding: options.padding,
      essential: true,
    });
  }, []);

  // More gentle transition
  const easeTo = useCallback((
    map: mapboxgl.Map, 
    options: TransitionOptions
  ) => {
    map.easeTo({
      center: options.center,
      zoom: options.zoom,
      pitch: options.pitch,
      bearing: options.bearing,
      duration: options.duration || 1000,
      padding: options.padding,
      essential: true,
    });
  }, []);

  // Enhanced cinematic pan with more dramatic movement
  const cinematicPan = useCallback((
    map: mapboxgl.Map, 
    options: TransitionOptions
  ) => {
    // Start with a slight zoom out effect
    if (map.getZoom() > 10) {
      map.easeTo({
        zoom: map.getZoom() * 0.95,
        pitch: options.pitch ? options.pitch * 0.8 : undefined,
        duration: 800,
        essential: true,
      });
    }

    // Wait a moment then fly to destination
    setTimeout(() => {
      map.flyTo({
        center: options.center,
        zoom: options.zoom,
        pitch: options.pitch,
        bearing: options.bearing || (Math.random() * 60 - 30), // Add some randomness to the bearing for visual interest
        duration: options.duration || 2500,
        padding: options.padding,
        essential: true,
      });
    }, 300);
  }, []);

  return {
    flyToLocation,
    easeTo,
    cinematicPan
  };
}
