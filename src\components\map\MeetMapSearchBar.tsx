
import React, { useState, useEffect } from "react";
import { Search, X, Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { MapUserPresence } from "@/hooks/useMapPresence";
import { useToast } from "@/hooks/use-toast";

interface MeetMapSearchBarProps {
  onUserSelect?: (user: MapUserPresence) => void;
  onLocationSelect?: (location: { x: number, y: number, name: string }) => void;
}

export function MeetMapSearchBar({ onUserSelect, onLocationSelect }: MeetMapSearchBarProps) {
  const [query, setQuery] = useState("");
  const [results, setResults] = useState<any[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const { toast } = useToast();

  // Search effect
  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      setShowResults(false);
      return;
    }

    const timer = setTimeout(() => {
      handleSearch(query);
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  const handleSearch = async (searchQuery: string) => {
    setIsSearching(true);
    try {
      // Search for users
      const { data: userData, error: userError } = await supabase
        .from('profiles')
        .select('id, username, display_name, bio, avatar_url')
        .or(`username.ilike.%${searchQuery}%,display_name.ilike.%${searchQuery}%,bio.ilike.%${searchQuery}%`)
        .limit(5);

      if (userError) throw userError;

      // For now we'll use a simple location search, but this could be enhanced with a geocoding API
      // Mock location search results based on query (in a real app, you'd use Mapbox Geocoding API)
      const locationResults = [
        { name: `${searchQuery} Park`, x: -118.243683, y: 34.052235 },
        { name: `${searchQuery} Square`, x: -118.353683, y: 34.152235 },
        { name: `${searchQuery} Beach`, x: -118.443683, y: 34.052235 },
      ].slice(0, 2); // Just take a couple of mock results

      // Format location results
      const formattedLocations = locationResults.map(location => ({
        type: 'location',
        name: location.name,
        location: { x: location.x, y: location.y }
      }));

      // Format user results
      const formattedUsers = userData ? userData.map(user => ({
        type: 'user',
        user_id: user.id,
        display_name: user.display_name,
        username: user.username,
        bio: user.bio,
        avatar_url: user.avatar_url
      })) : [];

      const combinedResults = [...formattedUsers, ...formattedLocations];
      setResults(combinedResults);
      setShowResults(combinedResults.length > 0);

      if (combinedResults.length === 0) {
        toast({
          title: "No results found",
          description: `No users or locations found for "${searchQuery}"`,
        });
      }
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search failed",
        description: "Unable to complete the search. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSearching(false);
    }
  };

  const handleResultClick = (result: any) => {
    if (result.type === 'user' && onUserSelect) {
      onUserSelect({
        user_id: result.user_id,
        display_name: result.display_name,
        username: result.username,
        bio: result.bio,
        avatar_url: result.avatar_url,
        is_online: true,
        updated_at: new Date().toISOString(),
        location: { x: 0, y: 0 } // This will be updated if we have location data
      });
    } else if (result.type === 'location' && onLocationSelect) {
      onLocationSelect({
        x: result.location.x,
        y: result.location.y,
        name: result.name
      });
    }
    setShowResults(false);
    setQuery("");
  };

  return (
    <div className="absolute top-6 left-1/2 -translate-x-1/2 z-40 w-[400px] max-w-[98vw]">
      <div className="relative">
        <div className="flex items-center rounded-2xl border border-gray-200 bg-white/90 shadow-xl w-full">
          <Search className="ml-4 h-5 w-5 text-gray-400" />
          <input
            type="text"
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            placeholder="Search locations or users..."
            className="w-full py-3 px-3 rounded-2xl bg-transparent outline-none focus:ring-2 focus:ring-blue-400 transition text-gray-700 text-lg placeholder-gray-400"
            onFocus={() => query.trim() && setShowResults(true)}
            onBlur={() => setTimeout(() => setShowResults(false), 200)}
          />
          {isSearching ? (
            <Loader2 className="mr-4 h-5 w-5 animate-spin text-gray-400" />
          ) : query ? (
            <button 
              onClick={() => { setQuery(""); setResults([]); }}
              className="mr-4 rounded-full hover:bg-gray-100 p-1"
            >
              <X className="h-5 w-5 text-gray-400" />
            </button>
          ) : null}
        </div>

        {showResults && (
          <div className="absolute top-full left-0 w-full mt-2 bg-white rounded-xl shadow-lg border border-gray-200 max-h-[300px] overflow-y-auto z-50">
            {results.map((result, index) => (
              <div 
                key={index}
                className="flex items-center p-3 hover:bg-gray-50 cursor-pointer border-b border-gray-100 last:border-none"
                onClick={() => handleResultClick(result)}
              >
                {result.type === 'user' ? (
                  <>
                    <div className="h-10 w-10 rounded-full bg-blue-100 overflow-hidden mr-3 flex-shrink-0">
                      {result.avatar_url ? (
                        <img 
                          src={result.avatar_url} 
                          alt={result.display_name || result.username} 
                          className="h-full w-full object-cover"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center font-semibold text-blue-500">
                          {(result.display_name?.[0] || result.username?.[0] || 'U').toUpperCase()}
                        </div>
                      )}
                    </div>
                    <div className="flex-1">
                      <p className="font-medium">{result.display_name || result.username}</p>
                      <p className="text-sm text-gray-500 truncate">{result.bio || 'No bio'}</p>
                    </div>
                  </>
                ) : (
                  <>
                    <div className="h-10 w-10 rounded-full bg-blue-100 overflow-hidden mr-3 flex-shrink-0 flex items-center justify-center">
                      <Search className="h-5 w-5 text-blue-500" />
                    </div>
                    <div>
                      <p className="font-medium">{result.name}</p>
                      <p className="text-sm text-gray-500">Location</p>
                    </div>
                  </>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
