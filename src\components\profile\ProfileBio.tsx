
import React from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { EnhancedUserProfile, SocialLink } from "@/types/enhanced-profile";
import { useFormattedBio } from "@/hooks/use-formatted-bio";
import { format } from 'date-fns';
import { MapPin, Calendar, Globe, Link as LinkIcon, Mail, Cake, User } from 'lucide-react';
import { cn } from "@/lib/utils";

interface ProfileAboutProps {
  profile: EnhancedUserProfile;
  isLoading?: boolean;
  className?: string;
}

export function ProfileAbout({ profile, isLoading = false, className }: ProfileAboutProps) {
  const formattedBio = useFormattedBio(profile?.bio);

  // Format birthday if available
  const formattedBirthday = profile?.birthday
    ? format(new Date(profile.birthday), 'MMMM d, yyyy')
    : null;

  // Get social links
  const socialLinks = profile?.social_links || [];

  // Get vibes/interests
  const vibes = profile?.vibes || [];
  const interests = profile?.interests || [];
  const allTags = [...vibes, ...interests].filter(Boolean);

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>About</CardTitle>
        <CardDescription>Personal information and interests</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Bio Section */}
        <div>
          <h3 className="text-sm font-medium text-muted-foreground mb-2">Bio</h3>
          <div className="text-gray-700">
            {formattedBio || "No bio information provided yet."}
          </div>
        </div>

        <Separator />

        {/* Personal Info Section */}
        <div>
          <h3 className="text-sm font-medium text-muted-foreground mb-3">Personal Info</h3>
          <div className="space-y-2">
            {profile?.gender && (
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-muted-foreground" />
                <span>{profile.gender}</span>
              </div>
            )}

            {formattedBirthday && (
              <div className="flex items-center gap-2">
                <Cake className="h-4 w-4 text-muted-foreground" />
                <span>{formattedBirthday}</span>
              </div>
            )}

            {profile?.location_display && (
              <div className="flex items-center gap-2">
                <MapPin className="h-4 w-4 text-muted-foreground" />
                <span>{profile.location_display}</span>
              </div>
            )}
          </div>
        </div>

        {/* Social Links Section */}
        {socialLinks.length > 0 && (
          <>
            <Separator />
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-3">Social Links</h3>
              <div className="space-y-2">
                {socialLinks.map((link, index) => (
                  <SocialLinkItem key={index} link={link} />
                ))}
              </div>
            </div>
          </>
        )}

        {/* Interests/Vibes Section */}
        {allTags.length > 0 && (
          <>
            <Separator />
            <div>
              <h3 className="text-sm font-medium text-muted-foreground mb-3">Interests & Vibes</h3>
              <div className="flex flex-wrap gap-2">
                {allTags.map((tag, index) => (
                  <Badge key={index} variant="outline" className="bg-primary-purple/5">
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}

interface SocialLinkItemProps {
  link: SocialLink;
}

function SocialLinkItem({ link }: SocialLinkItemProps) {
  // Get icon based on platform
  const getIcon = () => {
    const platform = link.platform.toLowerCase();

    if (platform.includes('instagram')) return '📸';
    if (platform.includes('twitter') || platform.includes('x.com')) return '🐦';
    if (platform.includes('facebook')) return '👤';
    if (platform.includes('linkedin')) return '💼';
    if (platform.includes('github')) return '💻';
    if (platform.includes('youtube')) return '📺';
    if (platform.includes('tiktok')) return '🎵';
    if (platform.includes('snapchat')) return '👻';
    if (platform.includes('website')) return '🌐';

    return '🔗';
  };

  return (
    <a
      href={link.url}
      target="_blank"
      rel="noopener noreferrer"
      className="flex items-center gap-2 text-primary-blue hover:underline"
    >
      <span>{getIcon()}</span>
      <span>{link.platform}</span>
      {link.username && <span className="text-muted-foreground">({link.username})</span>}
    </a>
  );
}
