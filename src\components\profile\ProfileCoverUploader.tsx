
import React, { useRef, useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Camera, ImageIcon, Loader2, X } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';

interface ProfileCoverUploaderProps {
  previewUrl: string | null;
  onPhotoChange: (file: File, dataUrl: string) => void;
  onClose: () => void;
  isUploading?: boolean;
}

export function ProfileCoverUploader({
  previewUrl,
  onPhotoChange,
  onClose,
  isUploading = false
}: ProfileCoverUploaderProps) {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [localPreviewUrl, setLocalPreviewUrl] = useState<string | null>(previewUrl);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const { toast } = useToast();
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast({
        title: "File too large",
        description: "Please select an image under 5MB.",
        variant: "destructive"
      });
      return;
    }
    
    // Check file type
    if (!file.type.startsWith('image/')) {
      toast({
        title: "Invalid file type",
        description: "Please select an image file.",
        variant: "destructive"
      });
      return;
    }
    
    // Create preview
    const reader = new FileReader();
    reader.onloadend = () => {
      setLocalPreviewUrl(reader.result as string);
    };
    reader.readAsDataURL(file);
    
    setSelectedFile(file);
  };
  
  const handleSelectClick = () => {
    fileInputRef.current?.click();
  };
  
  const handleRemove = () => {
    setLocalPreviewUrl(null);
    setSelectedFile(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };
  
  const handleSave = () => {
    if (selectedFile && localPreviewUrl) {
      onPhotoChange(selectedFile, localPreviewUrl);
    } else {
      onClose();
    }
  };
  
  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Cover Photo</DialogTitle>
          <DialogDescription>
            Upload a cover photo for your profile. This will appear at the top of your profile page.
          </DialogDescription>
        </DialogHeader>
        
        <div className="my-4">
          {localPreviewUrl ? (
            <div className="relative">
              <img 
                src={localPreviewUrl} 
                alt="Cover preview" 
                className="w-full h-48 object-cover rounded-md"
              />
              <Button
                variant="outline"
                size="icon"
                className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                onClick={handleRemove}
                disabled={isUploading}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          ) : (
            <div 
              className="w-full h-48 bg-muted/50 rounded-md flex flex-col items-center justify-center cursor-pointer hover:bg-muted/70 transition-colors"
              onClick={handleSelectClick}
            >
              <ImageIcon className="h-10 w-10 text-muted-foreground mb-2" />
              <p className="text-muted-foreground">Click to select a cover photo</p>
              <p className="text-xs text-muted-foreground mt-1">Recommended size: 1500 x 500 pixels</p>
            </div>
          )}
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="hidden"
          />
        </div>
        
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isUploading}>
            Cancel
          </Button>
          {localPreviewUrl ? (
            <Button onClick={handleSave} disabled={isUploading}>
              {isUploading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Camera className="h-4 w-4 mr-2" />
                  Save Cover Photo
                </>
              )}
            </Button>
          ) : (
            <Button onClick={handleSelectClick} disabled={isUploading}>
              <Camera className="h-4 w-4 mr-2" />
              Select Photo
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
