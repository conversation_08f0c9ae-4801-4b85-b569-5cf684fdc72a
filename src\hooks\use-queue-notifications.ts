
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';

// This hook manages polling for activity queues instead of real-time notifications
export function useQueueNotifications(activityId: string | undefined) {
  const queryClient = useQueryClient();

  // Poll for queue updates
  useEffect(() => {
    if (!activityId) return;

    // Invalidate queue data periodically
    const interval = setInterval(() => {
      queryClient.invalidateQueries({ queryKey: ['activity-queue', activityId] });
    }, 10000); // Poll every 10 seconds

    return () => {
      clearInterval(interval);
    };
  }, [activityId, queryClient]);

  return { enabled: !!activityId };
}

export default useQueueNotifications;
