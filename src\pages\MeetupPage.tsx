
import React from 'react';
import { useNavigate } from 'react-router-dom';
import { MainLayout } from '@/components/layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { AlertCircle, Home, MapPin, Plus } from 'lucide-react';

function MeetupPage() {
  const navigate = useNavigate();

  return (
    <MainLayout title="Meetups">
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Discover Activities</h1>
              <p className="text-gray-600 mt-2">Find and join activities around you</p>
            </div>
            <Button onClick={() => navigate('/activity/create')} className="bg-gradient-to-r from-blue-500 to-indigo-600 text-white hover:from-blue-600 hover:to-indigo-700">
              <Plus className="h-4 w-4 mr-2" />
              Create Activity
            </Button>
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-12">
        <Card className="max-w-3xl mx-auto">
          <CardContent className="p-8 text-center">
            <div className="bg-red-50 p-4 rounded-full inline-flex items-center justify-center mb-6">
              <AlertCircle className="h-12 w-12 text-red-500" />
            </div>
            <h2 className="text-2xl font-bold mb-4">Activity Feature Unavailable</h2>
            <p className="text-gray-600 mb-6 max-w-md mx-auto">
              The activities feature is currently unavailable. The necessary database tables may have been removed or not yet configured.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button variant="outline" onClick={() => navigate('/')}>
                <Home className="h-4 w-4 mr-2" />
                Return Home
              </Button>
              <Button onClick={() => navigate('/meetmap')}>
                <MapPin className="h-4 w-4 mr-2" />
                Explore Map
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
}

export default MeetupPage;
