import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Map } from 'lucide-react';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { MapStylePreview } from '@/components/map/MapStylePreview';
import { ScrollArea } from '@/components/ui/scroll-area';

const MAP_STYLES = [
  { value: 'streets-v11', label: 'Streets' },
  { value: 'light-v11', label: 'Light' },
  { value: 'dark-v11', label: 'Dark' },
  { value: 'satellite-streets-v12', label: 'Satellite' },
  { value: 'outdoors-v12', label: 'Outdoors' },
  { value: 'custom-mint', label: 'Custom Mint' },
];

interface MapStyleSelectorProps {
  currentStyle: string;
  onChange: (style: string) => void;
  className?: string;
  showPreviews?: boolean;
}

export function MapStyleSelector({ 
  currentStyle, 
  onChange, 
  className,
  showPreviews = false
}: MapStyleSelectorProps) {
  const [open, setOpen] = useState(false);
  
  const currentStyleLabel = MAP_STYLES.find(style => style.value === currentStyle)?.label || 'Custom';
  
  const { data: mapboxToken } = useQuery({
    queryKey: ['mapbox-token'],
    queryFn: async () => {
      try {
        const { data, error } = await supabase.functions.invoke('get-mapbox-token');
        if (error) throw error;
        return data.token as string;
      } catch (err) {
        console.error('Failed to fetch Mapbox token:', err);
        return null;
      }
    },
  });
  
  if (showPreviews) {
    return (
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button 
            variant="outline" 
            size="sm"
            className={cn(
              "flex items-center gap-1.5 bg-background/95 backdrop-blur-sm",
              className
            )}
          >
            <Map className="h-4 w-4" />
            <span>{currentStyleLabel}</span>
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-3" align="end">
          <h3 className="font-medium mb-2 text-sm">Map Style</h3>
          <ScrollArea className="h-72">
            <div className="grid grid-cols-1 gap-2">
              {MAP_STYLES.map((style) => (
                <div key={style.value} className="space-y-1">
                  <MapStylePreview
                    mapboxToken={mapboxToken}
                    styleUrl={`mapbox://styles/mapbox/${style.value}`}
                    onClick={() => {
                      onChange(style.value);
                      setOpen(false);
                    }}
                    className={cn({
                      'ring-2 ring-primary': currentStyle === style.value
                    })}
                  />
                  <p className="text-xs font-medium text-center">{style.label}</p>
                </div>
              ))}
            </div>
          </ScrollArea>
        </PopoverContent>
      </Popover>
    );
  }
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          size="sm"
          className={cn(
            "flex items-center gap-1.5 bg-background/95 backdrop-blur-sm",
            className
          )}
        >
          <Map className="h-4 w-4" />
          <span>{currentStyleLabel}</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuRadioGroup value={currentStyle} onValueChange={onChange}>
          {MAP_STYLES.map((style) => (
            <DropdownMenuRadioItem key={style.value} value={style.value}>
              {style.label}
            </DropdownMenuRadioItem>
          ))}
        </DropdownMenuRadioGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
