
import { useLocation, <PERSON> } from "react-router-dom";
import { useEffect, useState } from "react";
import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Home, Map, Activity, MessageCircle, ArrowRight } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

const NotFound = () => {
  const location = useLocation();
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  // Show toast for successful profile creation if coming from onboarding
  useEffect(() => {
    const referrer = document.referrer;
    if (referrer && referrer.includes('/onboarding')) {
      toast({
        title: "Profile created!",
        description: "Your profile has been set up successfully.",
      });
    }
  }, [toast]);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      toast({
        title: "Search not implemented",
        description: "This is a placeholder for search functionality.",
      });
    }
  };

  const popularLinks = [
    { name: "Home", path: "/", icon: <Home className="h-4 w-4" /> },
    { name: "MeetMap", path: "/meetmap", icon: <Map className="h-4 w-4" /> },
    { name: "Activities", path: "/activity", icon: <Activity className="h-4 w-4" /> },
    { name: "Chat", path: "/chat", icon: <MessageCircle className="h-4 w-4" /> },
  ];

  return (
    <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 p-4">
      <div className="max-w-md w-full bg-white rounded-2xl shadow-xl overflow-hidden">
        <div className="bg-gradient-to-r from-primary to-secondary p-8 text-white text-center">
          <h1 className="text-8xl font-extrabold mb-2">404</h1>
          <div className="h-1 w-16 bg-white/50 mx-auto mb-4 rounded-full"></div>
          <p className="text-xl font-medium">Page Not Found</p>
        </div>

        <div className="p-8">
          <p className="text-gray-600 mb-6 text-center">
            We couldn't find the page you're looking for. It might have been moved or doesn't exist.
          </p>

          <form onSubmit={handleSearch} className="mb-6">
            <div className="flex gap-2">
              <Input
                type="text"
                placeholder="Search for content..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="flex-1"
              />
              <Button type="submit" size="icon">
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </form>

          <div className="mb-6">
            <h3 className="text-sm font-medium text-gray-500 mb-3">Popular Links</h3>
            <div className="grid grid-cols-2 gap-2">
              {popularLinks.map((link) => (
                <Link
                  key={link.path}
                  to={link.path}
                  className="flex items-center gap-2 p-3 rounded-md hover:bg-gray-50 transition-colors border border-gray-100"
                >
                  <div className="p-1.5 rounded-full bg-primary/10 text-primary">
                    {link.icon}
                  </div>
                  <span className="text-sm font-medium">{link.name}</span>
                </Link>
              ))}
            </div>
          </div>

          <Button asChild className="w-full">
            <Link to="/" className="flex items-center justify-center gap-2">
              Return to Home
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>

      {/* Decorative elements */}
      <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/5 rounded-full blur-3xl -z-10"></div>
      <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-secondary/5 rounded-full blur-3xl -z-10"></div>
    </div>
  );
};

export default NotFound;
