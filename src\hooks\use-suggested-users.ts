
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { UserProfile } from '@/types/user-profile';

export interface SuggestedUser extends UserProfile {
  reason?: string;
  mutual_connections?: number;
  distance?: number;
  common_interests?: string[];
}

export function useSuggestedUsers(limit: number = 6) {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['suggested-users', user?.id, limit],
    queryFn: async (): Promise<SuggestedUser[]> => {
      if (!user) return [];

      try {
        // Get the current user's profile to check interests and location
        const { data: currentUserProfile, error: profileError } = await supabase
          .from('profiles')
          .select('vibes, interests, default_location, location')
          .eq('id', user.id)
          .single();

        if (profileError) {
          console.error('Error fetching current user profile:', profileError);
          return [];
        }

        // Get users the current user is already following
        const { data: followingData, error: followingError } = await supabase
          .from('follows')
          .select('following_id')
          .eq('follower_id', user.id);

        if (followingError) {
          console.error('Error fetching following data:', followingError);
          return [];
        }

        // Create an array of user IDs the current user is already following
        const followingIds = followingData.map(f => f.following_id);
        // Add the current user's ID to exclude from suggestions
        followingIds.push(user.id);

        // Fetch users not already followed, with complete profiles
        const { data: suggestedUsers, error: suggestedError } = await supabase
          .from('profiles')
          .select('*')
          .not('id', 'in', `(${followingIds.join(',')})`)
          .eq('onboarding_completed', true)
          .order('created_at', { ascending: false })
          .limit(limit * 2); // Fetch more than needed to allow for filtering

        if (suggestedError) {
          console.error('Error fetching suggested users:', suggestedError);
          return [];
        }

        // Process and enhance the suggested users with reasons
        const enhancedUsers: SuggestedUser[] = suggestedUsers.map(profile => {
          // Ensure default_location has proper structure
          const defaultLocation = profile.default_location ? {
            x: typeof profile.default_location === 'object' && 'x' in profile.default_location 
              ? Number(profile.default_location.x) 
              : 0,
            y: typeof profile.default_location === 'object' && 'y' in profile.default_location 
              ? Number(profile.default_location.y) 
              : 0,
            address: typeof profile.default_location === 'object' && 'address' in profile.default_location 
              ? String(profile.default_location.address || '') 
              : undefined
          } : { x: 0, y: 0 };
          
          // Ensure location has proper structure - similar to default_location
          const location = profile.location ? {
            x: typeof profile.location === 'object' && 'x' in profile.location 
              ? Number(profile.location.x) 
              : 0,
            y: typeof profile.location === 'object' && 'y' in profile.location 
              ? Number(profile.location.y) 
              : 0,
            address: typeof profile.location === 'object' && 'address' in profile.location 
              ? String(profile.location.address || '') 
              : undefined
          } : { x: 0, y: 0 };
          
          // Process favorite_locations to ensure it matches the expected type
          let favoriteLocations: { location?: { x: number; y: number }; name?: string; availability?: string; }[] = [];
          
          if (profile.favorite_locations) {
            // Check if it's an array
            if (Array.isArray(profile.favorite_locations)) {
              favoriteLocations = profile.favorite_locations.map(loc => {
                if (typeof loc === 'object' && loc !== null) {
                  // Now let's safely access properties with type checking
                  const locObj = loc as Record<string, any>;
                  
                  // Handle the location object with additional type checking
                  const locationObj = locObj.location && typeof locObj.location === 'object' 
                    ? {
                        x: Number(locObj.location.x || 0),
                        y: Number(locObj.location.y || 0)
                      }
                    : undefined;
                  
                  return {
                    location: locationObj,
                    name: typeof locObj.name === 'string' ? locObj.name : undefined,
                    availability: typeof locObj.availability === 'string' ? locObj.availability : undefined
                  };
                }
                return { location: { x: 0, y: 0 } };
              });
            } else if (typeof profile.favorite_locations === 'object' && profile.favorite_locations !== null) {
              // If it's a JSON object but not an array, convert it to array with one item
              favoriteLocations = [{ 
                location: { x: 0, y: 0 },
                name: "Unknown location",
                availability: "unknown"
              }];
            }
          }

          const user: SuggestedUser = {
            ...profile,
            default_location: defaultLocation,
            location: location,
            favorite_locations: favoriteLocations,
            reason: 'Suggested for you'
          };

          // Check for common interests/vibes
          const userVibes = profile.vibes || [];
          const currentUserVibes = currentUserProfile.vibes || [];
          const commonVibes = userVibes.filter(vibe =>
            currentUserVibes.includes(vibe)
          );

          if (commonVibes.length > 0) {
            user.reason = 'Similar interests';
            user.common_interests = commonVibes;
          }

          // Calculate distance if both users have locations
          if (profile.default_location && currentUserProfile.default_location) {
            // Ensure we have valid location objects with numeric x and y properties
            const profileLocation = {
              x: typeof profile.default_location === 'object' && 'x' in profile.default_location 
                ? Number(profile.default_location.x) 
                : 0,
              y: typeof profile.default_location === 'object' && 'y' in profile.default_location 
                ? Number(profile.default_location.y) 
                : 0
            };
            
            const currentUserLocation = {
              x: typeof currentUserProfile.default_location === 'object' && 'x' in currentUserProfile.default_location 
                ? Number(currentUserProfile.default_location.x) 
                : 0,
              y: typeof currentUserProfile.default_location === 'object' && 'y' in currentUserProfile.default_location 
                ? Number(currentUserProfile.default_location.y) 
                : 0
            };

            // Use Haversine formula for accurate distance calculation
            const lat1 = profileLocation.y;
            const lon1 = profileLocation.x;
            const lat2 = currentUserLocation.y;
            const lon2 = currentUserLocation.x;

            // Earth's radius in kilometers
            const earthRadiusKm = 6371;

            // Convert coordinates to radians
            const toRadians = (degrees: number) => degrees * (Math.PI / 180);
            const latDiffRad = toRadians(lat2 - lat1);
            const lonDiffRad = toRadians(lon2 - lon1);

            const a =
              Math.sin(latDiffRad/2) * Math.sin(latDiffRad/2) +
              Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
              Math.sin(lonDiffRad/2) * Math.sin(lonDiffRad/2);

            const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
            const distanceKm = earthRadiusKm * c;

            // Convert to miles for consistency with the rest of the app
            user.distance = distanceKm * 0.621371;

            if (distanceKm < 10) {
              user.reason = 'Nearby';
            }
          }

          return user;
        });

        // Sort by relevance (common interests first, then nearby, then recent)
        enhancedUsers.sort((a, b) => {
          // Common interests have highest priority
          if (a.common_interests?.length && b.common_interests?.length) {
            return b.common_interests.length - a.common_interests.length;
          } else if (a.common_interests?.length) {
            return -1;
          } else if (b.common_interests?.length) {
            return 1;
          }

          // Nearby users have second priority
          if (a.distance !== undefined && b.distance !== undefined) {
            return a.distance - b.distance;
          } else if (a.distance !== undefined) {
            return -1;
          } else if (b.distance !== undefined) {
            return 1;
          }

          // Default to most recent
          return new Date(b.created_at || 0).getTime() - new Date(a.created_at || 0).getTime();
        });

        // Return limited number of suggestions
        return enhancedUsers.slice(0, limit);
      } catch (error) {
        console.error('Error in useSuggestedUsers:', error);
        return [];
      }
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // Cache for 5 minutes
  });
}
