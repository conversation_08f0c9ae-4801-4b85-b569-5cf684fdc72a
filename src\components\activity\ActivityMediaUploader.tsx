
import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { ImageIcon, X } from 'lucide-react';
import { MediaFile } from '@/types/activity';

interface ActivityMediaUploaderProps {
  onMediaUpload?: (files: MediaFile[]) => void;
  mediaFiles?: MediaFile[];
  setMediaFiles?: React.Dispatch<React.SetStateAction<MediaFile[]>>;
}

export function ActivityMediaUploader({ 
  onMediaUpload, 
  mediaFiles = [],
  setMediaFiles
}: ActivityMediaUploaderProps) {
  const [files, setFiles] = React.useState<MediaFile[]>(mediaFiles || []);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const newFiles: MediaFile[] = Array.from(e.target.files).map((file, index) => ({
        id: `temp-${Date.now()}-${index}`,
        name: file.name,
        size: file.size,
        type: file.type.startsWith('image/') ? 'image' : 'video',
        url: URL.createObjectURL(file),
        file
      }));
      
      const updatedFiles = [...files, ...newFiles];
      
      setFiles(updatedFiles);
      
      if (onMediaUpload) {
        onMediaUpload(updatedFiles);
      }
      
      if (setMediaFiles) {
        setMediaFiles(updatedFiles);
      }
    }
  };

  const removeFile = (index: number) => {
    const updatedFiles = files.filter((_, i) => i !== index);
    
    setFiles(updatedFiles);
    
    if (onMediaUpload) {
      onMediaUpload(updatedFiles);
    }
    
    if (setMediaFiles) {
      setMediaFiles(updatedFiles);
    }
  };

  return (
    <div className="space-y-4">
      <label className="block text-sm font-medium text-gray-700">Media</label>
      
      <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4">
        {files.map((file, index) => (
          <Card key={index} className="relative overflow-hidden">
            <button
              type="button"
              onClick={() => removeFile(index)}
              className="absolute top-1 right-1 bg-black bg-opacity-50 rounded-full p-1 text-white"
              aria-label="Remove image"
            >
              <X className="h-4 w-4" />
            </button>
            {file.type === 'image' ? (
              <img
                src={file.url}
                alt={file.name}
                className="h-24 w-full object-cover"
              />
            ) : (
              <div className="flex h-24 items-center justify-center bg-gray-100">
                <ImageIcon className="h-8 w-8 text-gray-400" />
              </div>
            )}
            <CardContent className="p-2">
              <p className="truncate text-xs text-muted-foreground">{file.name}</p>
            </CardContent>
          </Card>
        ))}

        <Card className="flex h-24 items-center justify-center">
          <label className="flex h-full w-full cursor-pointer flex-col items-center justify-center p-2">
            <ImageIcon className="mb-2 h-8 w-8 text-muted-foreground" />
            <span className="text-xs text-muted-foreground">Add Media</span>
            <input
              type="file"
              accept="image/*,video/*"
              multiple
              onChange={handleFileChange}
              className="hidden"
            />
          </label>
        </Card>
      </div>
      
      <p className="text-xs text-muted-foreground">
        Upload images or videos related to your activity
      </p>
    </div>
  );
}
