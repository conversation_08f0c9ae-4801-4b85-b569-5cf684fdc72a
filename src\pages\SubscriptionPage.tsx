import React, { useState } from 'react';
import { useAuth } from "@/hooks/use-auth";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Check, Crown, Loader2, Zap, Gem, Shield, Star, Waves, Sparkles } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";
import { useSubscription } from "@/hooks/use-subscription";
import { Badge } from "@/components/ui/badge";
import { motion } from "framer-motion";
import { MainLayout } from "@/components/layouts/MainLayout";
import { SubscriptionButton } from "@/components/subscription";

const SubscriptionPlans = [
  {
    id: "free",
    name: "Free Tier",
    price: "$0",
    period: "forever",
    trial: null,
    description: "Basic features to get started",
    features: [
      "Basic map access",
      "Limited activity creation (1 per week)",
      "Standard chat (text-only, no proposals)",
      "Profile visibility in local searches",
      "Ads-supported",
      "Access to Help Center & FAQ",
      "Community support"
    ],
    icon: <Waves className="h-6 w-6 text-gray-400" />,
    color: "bg-gradient-to-br from-gray-50 to-gray-100",
    borderColor: "border-gray-200",
    textColor: "text-gray-700",
    highlight: false,
    savings: "",
    popular: false
  },
  {
    id: "weekly",
    name: "Weekly Trial",
    price: "$3.99",
    period: "per week",
    trial: "14-day trial",
    description: "Try premium features with a 14-day trial",
    features: [
      "All Free features",
      "Activities: Up to 3 creations/week",
      "Chat: Media sharing + 5 proposal cards/day",
      "Visibility: Boosted in 'Nearby' lists",
      "No ads",
      "Access to premium support (email/chat)",
      "Early access to select new features"
    ],
    icon: <Zap className="h-6 w-6 text-blue-500" />,
    color: "bg-gradient-to-br from-blue-50 to-blue-100",
    borderColor: "border-blue-200",
    textColor: "text-blue-700",
    highlight: false,
    savings: "Try for 14 days",
    popular: false
  },
  {
    id: "monthly",
    name: "Monthly Pro",
    price: "$15",
    period: "per month",
    trial: null,
    description: "Perfect for active social users",
    features: [
      "All Weekly features",
      "Unlimited activity creation",
      "Priority placement in activity queues",
      "Advanced filters (income, verified users, etc.)",
      "Wallet integration (send/receive payments)",
      "10 proposal cards/day",
      "Custom map styles",
      "Profile analytics",
      "Access to exclusive activities/gigs",
      "Premium badge on profile"
    ],
    icon: <Gem className="h-6 w-6 text-purple-500" />,
    color: "bg-gradient-to-br from-purple-50 to-purple-100",
    borderColor: "border-purple-200",
    textColor: "text-purple-700",
    highlight: true,
    savings: "Most popular choice",
    popular: true
  },
  {
    id: "lifetime",
    name: "Lifetime Surf",
    price: "$35",
    period: "one-time",
    trial: null,
    description: "Best value for power users",
    features: [
      "All Monthly features",
      "Permanent ad-free experience",
      "\"Verified\" badge",
      "Early access to all new features",
      "Dedicated support (priority queue)",
      "Unlimited proposals",
      "Exclusive map markers (custom icons/colors)",
      "Lifetime badge on profile",
      "Invitation to beta test new features"
    ],
    icon: <Crown className="h-6 w-6 text-amber-500" />,
    color: "bg-gradient-to-br from-amber-50 to-amber-100",
    borderColor: "border-amber-200",
    textColor: "text-amber-700",
    highlight: false,
    savings: "Save 80% vs monthly",
    popular: false
  }
];

export default function SubscriptionPage() {
  const { user } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [loading, setLoading] = useState<string | null>(null);
  const { data: subscriptionData, isLoading: isSubscriptionLoading } = useSubscription();

  const subscribedPlan = subscriptionData?.plan || null;

  const handleSubscribe = async (planId: string) => {
    try {
      setLoading(planId);

      // Check if user is authenticated
      if (!user) {
        toast({
          title: "Authentication required",
          description: "Please login to subscribe to a plan",
          variant: "destructive"
        });
        navigate("/");
        return;
      }

      // Call Stripe checkout function
      const { data, error } = await supabase.functions.invoke('create-checkout', {
        body: { planId }
      });

      if (error) throw error;

      // Redirect to Stripe checkout
      if (data?.url) {
        window.location.href = data.url;
      }
    } catch (error: any) {
      toast({
        title: "Subscription Error",
        description: error.message || "Failed to process subscription request",
        variant: "destructive"
      });
    } finally {
      setLoading(null);
    }
  };

  const handleManageSubscription = async () => {
    try {
      setLoading('manage');

      // Call customer portal function
      const { data, error } = await supabase.functions.invoke('customer-portal', {});

      if (error) throw error;

      // Redirect to Stripe customer portal
      if (data?.url) {
        window.location.href = data.url;
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to open subscription management",
        variant: "destructive"
      });
    } finally {
      setLoading(null);
    }
  };

  return (
    <MainLayout title="Subscription Plans">
      <div className="container mx-auto py-12 px-4 max-w-6xl">
        <motion.div
          className="text-center mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col sm:flex-row items-center justify-center gap-3 mb-4">
            <motion.div
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{
                duration: 0.6,
                delay: 0.2,
                type: "spring",
                stiffness: 200
              }}
              className="relative"
            >
              <div className="absolute inset-0 bg-gradient-to-r from-amber-300 to-amber-500 rounded-full blur-md opacity-30 animate-pulse"></div>
              <div className="relative bg-gradient-to-r from-amber-400 to-amber-600 p-3 rounded-full shadow-lg">
                <motion.div
                  animate={{
                    rotateZ: [0, -5, 5, -5, 0],
                    y: [0, -2, 0, -2, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "loop",
                    ease: "easeInOut",
                    repeatDelay: 3
                  }}
                >
                  <Crown className="h-8 w-8 text-white drop-shadow-md" />
                </motion.div>
              </div>
            </motion.div>
            <div className="relative">
              <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-primary-purple to-blue-600 bg-clip-text text-transparent">
                BuddySurf Premium Plans
              </h1>
              <motion.div
                className="absolute -top-2 -right-2 hidden sm:block"
                animate={{
                  scale: [1, 1.2, 1],
                  opacity: [0.7, 1, 0.7]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  repeatType: "loop"
                }}
              >
                <Sparkles className="h-5 w-5 text-amber-400" />
              </motion.div>
            </div>
          </div>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Unlock the full potential of BuddySurf with our premium subscription options
          </p>
        </motion.div>

        {isSubscriptionLoading ? (
          <div className="flex justify-center items-center py-10">
            <Loader2 className="h-8 w-8 animate-spin text-primary-purple" />
            <span className="ml-2 text-muted-foreground">Checking subscription status...</span>
          </div>
        ) : subscribedPlan ? (
          <motion.div
            className="mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            <div className="bg-gradient-to-r from-green-50 to-blue-50 border border-green-200 rounded-xl p-6 flex flex-col md:flex-row items-center justify-between gap-4">
              <div className="flex items-center gap-4">
                <div className="bg-green-100 p-3 rounded-full">
                  <Shield className="h-8 w-8 text-green-600" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-green-800 mb-1">
                    You're currently on the <span className="text-primary-purple">{subscribedPlan}</span> plan
                  </h2>
                  <p className="text-green-700">
                    Enjoy full access to all BuddySurf premium features!
                  </p>
                </div>
              </div>
              <Button
                onClick={handleManageSubscription}
                variant="outline"
                className="flex items-center gap-2 border-green-300 text-green-700 hover:bg-green-50"
                size="lg"
                disabled={loading === 'manage'}
              >
                {loading === 'manage' ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <>Manage Subscription</>
                )}
              </Button>
            </div>
          </motion.div>
        ) : (
          <motion.div
            className="mb-12 overflow-hidden border-none shadow-lg"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
          >
            <Card className="mb-12 overflow-hidden border-none shadow-lg">
              <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-0.5">
                <div className="bg-white p-6">
                  <div className="flex flex-col md:flex-row justify-between items-center gap-6">
                    <div className="flex items-start gap-4">
                      <div className="bg-gray-100 p-3 rounded-full">
                        <Waves className="h-8 w-8 text-gray-500" />
                      </div>
                      <div>
                        <h2 className="text-xl font-bold mb-1">Your Current Plan: Free Tier</h2>
                        <p className="text-muted-foreground mb-3">
                          You're currently on the free plan with limited features
                        </p>
                        <div className="flex flex-wrap gap-2 mb-2">
                          <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">Basic map access</span>
                          <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">1 activity/week</span>
                          <span className="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">Text-only chat</span>
                        </div>
                      </div>
                    </div>
                    <SubscriptionButton
                      className="bg-primary-purple hover:bg-primary-deep-purple"
                      size="lg"
                      showBadge={true}
                      badgeText="Upgrade"
                    >
                      <Sparkles className="h-5 w-5 mr-2" />
                      Unlock Premium
                    </SubscriptionButton>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        )}

        <div className="grid gap-8 md:grid-cols-2 lg:grid-cols-4 mb-12">
          {SubscriptionPlans.map((plan, index) => (
            <motion.div
              key={plan.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: 0.1 * index }}
              className={`${plan.popular ? 'md:-mt-4' : ''}`}
            >
              <div
                className={`rounded-xl border overflow-hidden bg-white h-full flex flex-col transition-all duration-300 ${
                  plan.highlight ? `border-2 ${plan.borderColor} shadow-lg` : `border ${plan.borderColor} hover:shadow-md`
                } ${subscribedPlan === plan.id ? 'ring-2 ring-primary-purple' : ''}`}
              >
                {plan.popular && (
                  <div className="absolute top-0 right-0 w-24 h-24">
                    <div className="absolute transform rotate-45 bg-primary-purple text-white font-semibold text-xs py-1 right-[-35px] top-[20px] w-[140px] text-center">
                      POPULAR
                    </div>
                  </div>
                )}

                <div className={`${plan.color} px-6 py-4 relative`}>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {plan.icon}
                      <h3 className={`text-xl font-bold ${plan.textColor}`}>{plan.name}</h3>
                    </div>
                    {plan.savings && (
                      <span className="bg-white/80 backdrop-blur-sm text-xs px-2 py-1 rounded-full font-medium">
                        {plan.savings}
                      </span>
                    )}
                  </div>
                </div>

                <div className="p-6 flex-grow flex flex-col">
                  <div className="mb-4">
                    <div className="flex items-end gap-1">
                      <span className="text-3xl font-bold">{plan.price}</span>
                      <span className="text-sm text-muted-foreground">{plan.period}</span>
                    </div>
                    {plan.trial && (
                      <div className="mt-1.5 inline-block bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded-full">
                        Includes {plan.trial}
                      </div>
                    )}
                    <p className="text-sm text-muted-foreground mt-1">{plan.description}</p>
                  </div>

                  <div className="space-y-3 mb-6 flex-grow">
                    {plan.features.map((feature, index) => (
                      <div key={index} className="flex items-start gap-2">
                        <div className="bg-green-50 rounded-full p-0.5 mt-0.5 flex-shrink-0">
                          <Check className="h-3.5 w-3.5 text-green-600" />
                        </div>
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  {plan.id === "free" ? (
                    <Button
                      className="w-full"
                      variant="outline"
                      disabled={true}
                    >
                      Free Plan
                    </Button>
                  ) : (
                    <Button
                      className={`w-full ${
                        plan.highlight ? 'bg-primary-purple hover:bg-primary-deep-purple' :
                        `border-${plan.textColor} text-${plan.textColor} hover:bg-${plan.color}`
                      }`}
                      variant={plan.highlight ? "default" : "outline"}
                      onClick={() => handleSubscribe(plan.id)}
                      disabled={loading === plan.id || subscribedPlan === plan.id}
                    >
                      {loading === plan.id ? (
                        <Loader2 className="h-4 w-4 animate-spin" />
                      ) : subscribedPlan === plan.id ? (
                        "Current Plan"
                      ) : (
                        <>
                          {plan.trial ? (
                            <>Start {plan.trial}</>
                          ) : (
                            <>Subscribe to {plan.name}</>
                          )}
                        </>
                      )}
                    </Button>
                  )}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="text-center bg-gradient-to-r from-purple-50 to-blue-50 p-8 rounded-xl mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <div className="flex justify-center mb-4">
            <Shield className="h-10 w-10 text-primary-purple" />
          </div>
          <h2 className="text-2xl font-bold mb-3">Secure Payment & Satisfaction Guaranteed</h2>
          <p className="text-muted-foreground mb-6 max-w-2xl mx-auto">
            All plans come with a 30-day money-back guarantee. Upgrade today and experience the full potential of BuddySurf.
          </p>
          <div className="flex flex-wrap justify-center gap-4">
            <img src="/images/payment/visa.svg" alt="Visa" className="h-8" />
            <img src="/images/payment/mastercard.svg" alt="Mastercard" className="h-8" />
            <img src="/images/payment/amex.svg" alt="American Express" className="h-8" />
            <img src="/images/payment/paypal.svg" alt="PayPal" className="h-8" />
            <img src="/images/payment/apple-pay.svg" alt="Apple Pay" className="h-8" />
          </div>
        </motion.div>

        <motion.div
          className="mt-8 text-center text-sm text-muted-foreground"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <p>
            By subscribing, you agree to our <a href="/terms" className="underline hover:text-primary">Terms of Service</a> and <a href="/privacy" className="underline hover:text-primary">Privacy Policy</a>.
          </p>
        </motion.div>
      </div>
    </MainLayout>
  );
}
