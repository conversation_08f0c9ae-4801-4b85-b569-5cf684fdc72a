-- Improved RLS Policies for BuddySurf
-- Using (select auth.uid()) for better performance

-- ==========================================
-- Helper Functions
-- ==========================================

-- Function to check if a user is an admin
CREATE OR REPLACE FUNCTION public.is_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN (
    SELECT email = '<EMAIL>'
    FROM auth.users
    WHERE id = auth.uid()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ==========================================
-- Activities Table Policies
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view all activities" ON "public"."activities";
DROP POLICY IF EXISTS "Users can create activities" ON "public"."activities";
DROP POLICY IF EXISTS "Users can update their own activities" ON "public"."activities";
DROP POLICY IF EXISTS "Users can delete their own activities" ON "public"."activities";

-- Create improved policies
CREATE POLICY "Users can view all activities"
ON "public"."activities"
FOR SELECT
USING (true);

CREATE POLICY "Users can create activities"
ON "public"."activities"
FOR INSERT
WITH CHECK ((host_id = (select auth.uid())));

CREATE POLICY "Users can update their own activities"
ON "public"."activities"
FOR UPDATE
USING ((host_id = (select auth.uid())))
WITH CHECK ((host_id = (select auth.uid())));

CREATE POLICY "Users can delete their own activities"
ON "public"."activities"
FOR DELETE
USING ((host_id = (select auth.uid())));

CREATE POLICY "Admins can manage all activities"
ON "public"."activities"
FOR ALL
USING (is_admin());

-- ==========================================
-- Activity Queue Policies
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view all activity queues" ON "public"."activity_queue";
DROP POLICY IF EXISTS "Users can join activity queues" ON "public"."activity_queue";
DROP POLICY IF EXISTS "Users can update their own queue entries" ON "public"."activity_queue";
DROP POLICY IF EXISTS "Hosts can update queue entries for their activities" ON "public"."activity_queue";
DROP POLICY IF EXISTS "Users can delete their own queue entries" ON "public"."activity_queue";

-- Create improved policies
CREATE POLICY "Users can view all activity queues"
ON "public"."activity_queue"
FOR SELECT
USING (true);

CREATE POLICY "Users can join activity queues"
ON "public"."activity_queue"
FOR INSERT
WITH CHECK ((user_id = (select auth.uid())));

CREATE POLICY "Users can update their own queue entries"
ON "public"."activity_queue"
FOR UPDATE
USING ((user_id = (select auth.uid())))
WITH CHECK ((user_id = (select auth.uid())));

CREATE POLICY "Hosts can update queue entries for their activities"
ON "public"."activity_queue"
FOR UPDATE
USING (EXISTS (
  SELECT 1 FROM activities a
  WHERE a.id = activity_id
  AND a.host_id = (select auth.uid())
));

CREATE POLICY "Users can delete their own queue entries"
ON "public"."activity_queue"
FOR DELETE
USING ((user_id = (select auth.uid())));

CREATE POLICY "Admins can manage all queue entries"
ON "public"."activity_queue"
FOR ALL
USING (is_admin());

-- ==========================================
-- Chat Conversations Policies
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their conversations" ON "public"."chat_conversations";
DROP POLICY IF EXISTS "Users can create conversations" ON "public"."chat_conversations";
DROP POLICY IF EXISTS "Users can update their conversations" ON "public"."chat_conversations";
DROP POLICY IF EXISTS "Users can delete their conversations" ON "public"."chat_conversations";

-- Create improved policies
CREATE POLICY "Users can view their conversations"
ON "public"."chat_conversations"
FOR SELECT
USING (
  is_admin_conversation = true
  OR
  EXISTS (
    SELECT 1 FROM chat_participants cp
    WHERE cp.conversation_id = id
    AND cp.user_id = (select auth.uid())
  )
);

CREATE POLICY "Users can create conversations"
ON "public"."chat_conversations"
FOR INSERT
WITH CHECK (true);

CREATE POLICY "Users can update their conversations"
ON "public"."chat_conversations"
FOR UPDATE
USING (
  EXISTS (
    SELECT 1 FROM chat_participants cp
    WHERE cp.conversation_id = id
    AND cp.user_id = (select auth.uid())
  )
);

CREATE POLICY "Users can delete their conversations"
ON "public"."chat_conversations"
FOR DELETE
USING (
  EXISTS (
    SELECT 1 FROM chat_participants cp
    WHERE cp.conversation_id = id
    AND cp.user_id = (select auth.uid())
  )
);

CREATE POLICY "Admins can manage all conversations"
ON "public"."chat_conversations"
FOR ALL
USING (is_admin());

-- ==========================================
-- Chat Messages Policies
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view messages in their conversations" ON "public"."chat_messages";
DROP POLICY IF EXISTS "Users can send messages to their conversations" ON "public"."chat_messages";
DROP POLICY IF EXISTS "Users can update their own messages" ON "public"."chat_messages";
DROP POLICY IF EXISTS "Users can delete their own messages" ON "public"."chat_messages";

-- Create improved policies
CREATE POLICY "Users can view messages in their conversations"
ON "public"."chat_messages"
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM chat_participants cp
    WHERE cp.conversation_id = conversation_id
    AND cp.user_id = (select auth.uid())
  )
  OR
  EXISTS (
    SELECT 1 FROM chat_conversations cc
    WHERE cc.id = conversation_id
    AND cc.is_admin_conversation = true
  )
);

CREATE POLICY "Users can send messages to their conversations"
ON "public"."chat_messages"
FOR INSERT
WITH CHECK (
  (sender_id = (select auth.uid()) OR is_admin = true)
  AND
  (
    EXISTS (
      SELECT 1 FROM chat_participants cp
      WHERE cp.conversation_id = conversation_id
      AND cp.user_id = (select auth.uid())
    )
    OR
    EXISTS (
      SELECT 1 FROM chat_conversations cc
      WHERE cc.id = conversation_id
      AND cc.is_admin_conversation = true
    )
  )
);

CREATE POLICY "Users can update their own messages"
ON "public"."chat_messages"
FOR UPDATE
USING (sender_id = (select auth.uid()))
WITH CHECK (sender_id = (select auth.uid()));

CREATE POLICY "Users can delete their own messages"
ON "public"."chat_messages"
FOR DELETE
USING (sender_id = (select auth.uid()));

CREATE POLICY "Admins can manage all messages"
ON "public"."chat_messages"
FOR ALL
USING (is_admin());

-- ==========================================
-- Profiles Policies
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view all profiles" ON "public"."profiles";
DROP POLICY IF EXISTS "Users can update their own profile" ON "public"."profiles";

-- Create improved policies
CREATE POLICY "Users can view all profiles"
ON "public"."profiles"
FOR SELECT
USING (true);

CREATE POLICY "Users can update their own profile"
ON "public"."profiles"
FOR UPDATE
USING (id = (select auth.uid()))
WITH CHECK (id = (select auth.uid()));

CREATE POLICY "Admins can update any profile"
ON "public"."profiles"
FOR UPDATE
USING (is_admin());

-- ==========================================
-- User Locations Policies
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view all user locations" ON "public"."user_locations";
DROP POLICY IF EXISTS "Users can update their own location" ON "public"."user_locations";

-- Create improved policies
CREATE POLICY "Users can view all user locations"
ON "public"."user_locations"
FOR SELECT
USING (true);

CREATE POLICY "Users can update their own location"
ON "public"."user_locations"
FOR UPDATE
USING (user_id = (select auth.uid()))
WITH CHECK (user_id = (select auth.uid()));

CREATE POLICY "Users can insert their own location"
ON "public"."user_locations"
FOR INSERT
WITH CHECK (user_id = (select auth.uid()));

CREATE POLICY "Admins can manage all user locations"
ON "public"."user_locations"
FOR ALL
USING (is_admin());

-- ==========================================
-- Messages Table Policies
-- ==========================================

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view messages in their conversations" ON "public"."messages";
DROP POLICY IF EXISTS "Users can send messages" ON "public"."messages";
DROP POLICY IF EXISTS "Users can update their own messages" ON "public"."messages";
DROP POLICY IF EXISTS "Users can delete their own messages" ON "public"."messages";

-- Create improved policies
CREATE POLICY "Users can view messages in their conversations"
ON "public"."messages"
FOR SELECT
USING (
  EXISTS (
    SELECT 1 FROM chat_participants cp
    WHERE cp.conversation_id = conversation_id
    AND cp.user_id = (select auth.uid())
  )
  OR
  EXISTS (
    SELECT 1 FROM chat_conversations cc
    WHERE cc.id = conversation_id
    AND cc.is_admin_conversation = true
  )
);

CREATE POLICY "Users can send messages"
ON "public"."messages"
FOR INSERT
WITH CHECK (
  sender_id = (select auth.uid())
  AND
  (
    EXISTS (
      SELECT 1 FROM chat_participants cp
      WHERE cp.conversation_id = conversation_id
      AND cp.user_id = (select auth.uid())
    )
    OR
    EXISTS (
      SELECT 1 FROM chat_conversations cc
      WHERE cc.id = conversation_id
      AND cc.is_admin_conversation = true
      AND is_admin()
    )
  )
);

CREATE POLICY "Users can update their own messages"
ON "public"."messages"
FOR UPDATE
USING (sender_id = (select auth.uid()));

CREATE POLICY "Users can delete their own messages"
ON "public"."messages"
FOR DELETE
USING (sender_id = (select auth.uid()));

CREATE POLICY "Admins can manage all messages"
ON "public"."messages"
FOR ALL
USING (is_admin());

-- Note: Admin privileges are granted through the is_admin() function
-- which checks if the user's <NAME_EMAIL>
-- No need to update any columns in the profiles table
