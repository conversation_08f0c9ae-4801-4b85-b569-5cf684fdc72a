
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { EnhancedUserProfile } from '@/types/enhanced-profile';
import { useProfileConnections } from '@/hooks/use-profile-connections';
import { FollowButton } from './FollowButton';
import { useFollowUser } from '@/hooks/use-follow-user';
import { useAuth } from '@/hooks/use-auth';
import { Search, User, UserCheck } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { cn } from '@/lib/utils';

interface ProfileConnectionsProps {
  profile: EnhancedUserProfile;
  isLoading?: boolean;
  className?: string;
}

export function ProfileConnections({ profile, isLoading = false, className }: ProfileConnectionsProps) {
  const [activeTab, setActiveTab] = useState<'followers' | 'following'>('followers');
  const [searchQuery, setSearchQuery] = useState('');
  const { user } = useAuth();
  const navigate = useNavigate();
  
  // Get connections data
  const { 
    data: followersData,
    isLoading: followersLoading
  } = useProfileConnections(profile.id, 'followers', 20);
  
  const { 
    data: followingData,
    isLoading: followingLoading
  } = useProfileConnections(profile.id, 'following', 20);
  
  // Filter connections based on search query
  const filterConnections = (data: any) => {
    if (!data || !data.connections) return [];
    if (!searchQuery.trim()) return data.connections;
    
    const query = searchQuery.toLowerCase();
    return data.connections.filter((conn: any) => {
      if (activeTab === 'followers') {
        return (
          conn.follower_username?.toLowerCase().includes(query) ||
          conn.follower_display_name?.toLowerCase().includes(query)
        );
      } else {
        return (
          conn.following_username?.toLowerCase().includes(query) ||
          conn.following_display_name?.toLowerCase().includes(query)
        );
      }
    });
  };
  
  // Get filtered connections based on active tab
  const connections = activeTab === 'followers' 
    ? filterConnections(followersData)
    : filterConnections(followingData);
  
  const isTabLoading = activeTab === 'followers' ? followersLoading : followingLoading;
  
  // Handle view profile
  const handleViewProfile = (userId: string) => {
    navigate(`/profile/${userId}`);
  };
  
  if (isLoading) {
    return <ProfileConnectionsSkeleton className={className} />;
  }
  
  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle>Connections</CardTitle>
        <CardDescription>People you connect with on the platform</CardDescription>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'followers' | 'following')}>
          <div className="px-6 border-b">
            <TabsList className="w-full">
              <TabsTrigger value="followers" className="flex-1">
                <UserCheck className="h-4 w-4 mr-2" />
                Followers ({profile.follower_count || 0})
              </TabsTrigger>
              <TabsTrigger value="following" className="flex-1">
                <User className="h-4 w-4 mr-2" />
                Following ({profile.following_count || 0})
              </TabsTrigger>
            </TabsList>
          </div>
          
          {/* Search input */}
          <div className="p-4 border-b">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={`Search ${activeTab}...`}
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9"
              />
            </div>
          </div>
          
          <TabsContent value="followers" className="mt-0">
            {isTabLoading ? (
              <ConnectionsListSkeleton count={5} />
            ) : connections.length > 0 ? (
              <div className="divide-y">
                {connections.map((connection) => (
                  <ConnectionItem
                    key={connection.id}
                    userId={connection.follower_id}
                    username={connection.follower_username}
                    displayName={connection.follower_display_name}
                    avatarUrl={connection.follower_avatar_url}
                    isVerified={connection.follower_is_verified}
                    isMutual={connection.is_mutual}
                    isCurrentUser={user?.id === connection.follower_id}
                    onViewProfile={handleViewProfile}
                  />
                ))}
              </div>
            ) : (
              <EmptyConnectionsState type="followers" />
            )}
          </TabsContent>
          
          <TabsContent value="following" className="mt-0">
            {isTabLoading ? (
              <ConnectionsListSkeleton count={5} />
            ) : connections.length > 0 ? (
              <div className="divide-y">
                {connections.map((connection) => (
                  <ConnectionItem
                    key={connection.id}
                    userId={connection.following_id}
                    username={connection.following_username}
                    displayName={connection.following_display_name}
                    avatarUrl={connection.following_avatar_url}
                    isVerified={connection.following_is_verified}
                    isMutual={connection.is_mutual}
                    isCurrentUser={user?.id === connection.following_id}
                    onViewProfile={handleViewProfile}
                  />
                ))}
              </div>
            ) : (
              <EmptyConnectionsState type="following" />
            )}
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
}

interface ConnectionItemProps {
  userId: string;
  username: string;
  displayName: string;
  avatarUrl?: string;
  isVerified: boolean;
  isMutual: boolean;
  isCurrentUser: boolean;
  onViewProfile: (userId: string) => void;
}

function ConnectionItem({
  userId,
  username,
  displayName,
  avatarUrl,
  isVerified,
  isMutual,
  isCurrentUser,
  onViewProfile
}: ConnectionItemProps) {
  const { isFollowing, toggleFollow, isLoading } = useFollowUser(userId);
  
  return (
    <div className="p-4 flex items-center justify-between">
      <div className="flex items-center gap-3 flex-1 min-w-0">
        <Avatar className="h-10 w-10 cursor-pointer" onClick={() => onViewProfile(userId)}>
          <AvatarImage src={avatarUrl || ''} alt={displayName || username} />
          <AvatarFallback>
            {displayName?.substring(0, 1) || username?.substring(0, 1) || 'U'}
          </AvatarFallback>
        </Avatar>
        
        <div className="min-w-0">
          <div className="flex items-center gap-1">
            <h3 
              className="font-medium truncate cursor-pointer hover:text-primary transition-colors"
              onClick={() => onViewProfile(userId)}
            >
              {displayName || username}
            </h3>
            {isVerified && (
              <svg className="h-4 w-4 text-primary-blue" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
            )}
          </div>
          
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground truncate">@{username}</span>
            
            {isMutual && (
              <Badge variant="outline" className="text-xs h-5 bg-primary/5 border-primary/20">
                Mutual
              </Badge>
            )}
          </div>
        </div>
      </div>
      
      {!isCurrentUser && (
        <FollowButton
          userId={userId}
          isFollowing={!!isFollowing}
          onToggle={toggleFollow}
          disabled={isLoading}
          size="sm"
        />
      )}
    </div>
  );
}

function EmptyConnectionsState({ type }: { type: 'followers' | 'following' }) {
  return (
    <div className="flex flex-col items-center justify-center py-12 px-4 text-center">
      <div className="bg-muted rounded-full p-4 mb-4">
        {type === 'followers' ? (
          <UserCheck className="h-8 w-8 text-muted-foreground" />
        ) : (
          <User className="h-8 w-8 text-muted-foreground" />
        )}
      </div>
      <h3 className="text-lg font-medium mb-1">No {type} yet</h3>
      <p className="text-muted-foreground max-w-sm">
        {type === 'followers' 
          ? "When someone follows this profile, they'll appear here." 
          : "When this profile follows someone, they'll appear here."}
      </p>
    </div>
  );
}

function ConnectionsListSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="divide-y">
      {Array(count).fill(0).map((_, i) => (
        <div key={i} className="p-4 flex items-center justify-between">
          <div className="flex items-center gap-3 flex-1">
            <Skeleton className="h-10 w-10 rounded-full" />
            <div className="space-y-1 flex-1">
              <Skeleton className="h-5 w-24" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
          <Skeleton className="h-9 w-20" />
        </div>
      ))}
    </div>
  );
}

function ProfileConnectionsSkeleton({ className }: { className?: string }) {
  return (
    <Card className={className}>
      <CardHeader>
        <Skeleton className="h-6 w-32 mb-2" />
        <Skeleton className="h-4 w-64" />
      </CardHeader>
      <CardContent className="p-0">
        <div className="px-6 border-b pb-2">
          <Skeleton className="h-10 w-full rounded-md" />
        </div>
        <div className="p-4 border-b">
          <Skeleton className="h-10 w-full rounded-md" />
        </div>
        <ConnectionsListSkeleton count={4} />
      </CardContent>
    </Card>
  );
}
