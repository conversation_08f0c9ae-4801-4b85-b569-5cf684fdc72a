
import React, { useState, useCallback, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { LocationSearch } from './MapControls/LocationSearch';
import { useLocationServices } from '@/hooks/use-location-services';
import { MapPin, Loader2, XCircle } from 'lucide-react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { useMapInstance } from '@/hooks/use-map-instance';

interface MapLocationPickerProps {
  mapboxToken: string;
  onLocationSelect: (location: { lng: number; lat: number; name: string }) => void;
  defaultLocation?: { lng: number; lat: number };
  className?: string;
}

export function MapLocationPicker({
  mapboxToken,
  onLocationSelect,
  defaultLocation,
  className
}: MapLocationPickerProps) {
  const [selectedLocation, setSelectedLocation] = useState<{
    lng: number;
    lat: number;
    name: string;
  } | null>(null);
  const [marker, setMarker] = useState<mapboxgl.Marker | null>(null);
  
  const { getPosition, isRequesting, error, accuracy, permissionStatus } = useLocationServices();
  
  const { mapInstanceRef, containerRef } = useMapInstance({
    mapboxToken,
    mapStyle: 'streets-v11',
    enablePitch: false,
    enableRotation: false,
    showBuildings: false
  });
  
  const updateLocationMarker = useCallback((location: { lng: number; lat: number }) => {
    if (!mapInstanceRef.current) return;
    
    // Remove existing marker
    if (marker) {
      marker.remove();
    }
    
    // Create new marker with pulse animation
    const el = document.createElement('div');
    el.className = 'location-marker';
    el.innerHTML = `
      <div class="relative">
        <div class="absolute inset-0 animate-ping rounded-full bg-blue-400 opacity-75"></div>
        <div class="relative rounded-full bg-blue-500 h-4 w-4 shadow-lg"></div>
        ${accuracy ? `
          <div class="absolute -inset-${Math.min(Math.round(accuracy / 10), 12)} rounded-full border-2 border-blue-500/20">
          </div>
        ` : ''}
      </div>
    `;
    
    const newMarker = new mapboxgl.Marker({
      element: el,
      anchor: 'center'
    })
      .setLngLat([location.lng, location.lat])
      .addTo(mapInstanceRef.current);
    
    setMarker(newMarker);
    setSelectedLocation({
      lng: location.lng,
      lat: location.lat,
      name: 'Current Location'
    });
    
    // Pan map to location with smooth animation
    mapInstanceRef.current.flyTo({
      center: [location.lng, location.lat],
      zoom: 15,
      speed: 0.8,
      curve: 1.4
    });
  }, [marker, accuracy]);

  const handleAutoDetect = async () => {
    try {
      const position = await getPosition();
      updateLocationMarker(position);
    } catch (error) {
      console.error('Error getting location:', error);
    }
  };

  useEffect(() => {
    if (!mapInstanceRef.current) return;

    // Add click handler to map
    mapInstanceRef.current.on('click', (e) => {
      const { lng, lat } = e.lngLat;
      
      // Remove existing marker
      if (marker) {
        marker.remove();
      }
      
      // Create new marker
      const newMarker = new mapboxgl.Marker({ draggable: true })
        .setLngLat([lng, lat])
        .addTo(mapInstanceRef.current!);
      
      // Handle marker dragend
      newMarker.on('dragend', () => {
        const position = newMarker.getLngLat();
        setSelectedLocation({
          lng: position.lng,
          lat: position.lat,
          name: 'Custom Location'
        });
      });
      
      setMarker(newMarker);
      setSelectedLocation({
        lng,
        lat,
        name: 'Custom Location'
      });
    });

    // Set initial view
    if (defaultLocation) {
      mapInstanceRef.current.flyTo({
        center: [defaultLocation.lng, defaultLocation.lat],
        zoom: 13
      });
    } else {
      mapInstanceRef.current.flyTo({
        center: [0, 20],
        zoom: 2
      });
    }

    return () => {
      marker?.remove();
    };
  }, [mapInstanceRef.current]);

  useEffect(() => {
    if (permissionStatus === 'granted') {
      handleAutoDetect();
    }
  }, [permissionStatus]);

  const handleSearchSelect = (location: { name: string; coordinates: [number, number] }) => {
    const [lng, lat] = location.coordinates;
    
    // Remove existing marker
    if (marker) {
      marker.remove();
    }
    
    // Add new marker
    const newMarker = new mapboxgl.Marker({ draggable: true })
      .setLngLat([lng, lat])
      .addTo(mapInstanceRef.current!);
    
    setMarker(newMarker);
    setSelectedLocation({
      lng,
      lat,
      name: location.name
    });
    
    // Pan map to location
    mapInstanceRef.current?.flyTo({
      center: [lng, lat],
      zoom: 13
    });
  };

  React.useEffect(() => {
    if (selectedLocation) {
      onLocationSelect(selectedLocation);
    }
  }, [selectedLocation, onLocationSelect]);

  return (
    <Card className={className}>
      <div className="p-4 space-y-4">
        <LocationSearch
          mapboxToken={mapboxToken}
          onSelectLocation={handleSearchSelect}
          placeholder="Search for a location..."
          className="w-full"
        />
        
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={handleAutoDetect}
            disabled={isRequesting || permissionStatus === 'denied'}
            className="flex-1"
          >
            {isRequesting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Detecting...
              </>
            ) : (
              <>
                <MapPin className="mr-2 h-4 w-4" />
                {permissionStatus === 'denied' 
                  ? 'Location Access Denied' 
                  : selectedLocation ? 'Update Location' : 'Share Location'}
              </>
            )}
          </Button>
          
          {selectedLocation && (
            <Button
              variant="outline"
              onClick={() => {
                setSelectedLocation(null);
                marker?.remove();
                setMarker(null);
              }}
            >
              <XCircle className="h-4 w-4" />
            </Button>
          )}
        </div>

        <div className="relative w-full h-[300px] rounded-md overflow-hidden border">
          <div ref={containerRef} className="absolute inset-0" />
        </div>
        
        {selectedLocation && (
          <div className="text-sm text-muted-foreground">
            Selected: {selectedLocation.name}
            {accuracy && (
              <span className="ml-2 text-xs">
                (Accuracy: ±{Math.round(accuracy)}m)
              </span>
            )}
          </div>
        )}
        
        {error && (
          <div className="text-sm text-destructive">
            {error}
            {permissionStatus === 'denied' && (
              <div className="mt-1 text-xs">
                Please enable location access in your browser settings to use this feature.
              </div>
            )}
          </div>
        )}
      </div>
    </Card>
  );
}
