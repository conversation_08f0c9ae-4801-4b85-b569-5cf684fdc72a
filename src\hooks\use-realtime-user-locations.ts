
import { useEffect, useState, useRef } from "react";
import { supabase } from "@/integrations/supabase/client";
import { UserLocation } from "@/hooks/use-user-locations";
import { useToast } from "@/hooks/use-toast";

export function useRealtimeUserLocations() {
  const [locations, setLocations] = useState<UserLocation[]>([]);
  const initialized = useRef(false);
  const { toast } = useToast();

  // Fetch initial data
  useEffect(() => {
    let active = true;
    supabase
      .from("user_locations")
      .select("*")
      .then(({ data, error }) => {
        if (!active) return;
        
        if (error) {
          console.error("Error fetching initial locations:", error);
          return;
        }
        
        if (data) {
          const formattedLocations = data.map(item => ({
            ...item,
            location: {
              x: typeof item.location === 'object' && item.location !== null ? Number((item.location as any).x || 0) : 0,
              y: typeof item.location === 'object' && item.location !== null ? Number((item.location as any).y || 0) : 0
            }
          }));
          
          setLocations(formattedLocations as UserLocation[]);
          initialized.current = true;
        }
      });

    return () => {
      active = false;
    };
  }, []);

  // Listen for realtime changes
  useEffect(() => {
    // We must use an async handler if we want to await supabase.auth.getSession() inside it.
    const handler = async (payload: any) => {
      console.log("Realtime update received:", payload);

      setLocations((prev) => {
        let next = [...prev];

        if (payload.eventType === "INSERT") {
          const newLocation = {
            ...payload.new,
            location: {
              x: typeof payload.new.location === 'object' && payload.new.location !== null 
                ? Number((payload.new.location as any).x || 0) : 0,
              y: typeof payload.new.location === 'object' && payload.new.location !== null 
                ? Number((payload.new.location as any).y || 0) : 0
            }
          } as UserLocation;

          next.push(newLocation);

          // Only show toast for other users' locations, not our own
          supabase.auth.getSession().then(({ data }) => {
            if (data && data.session && data.session.user.id !== newLocation.user_id) {
              toast({
                title: "New location shared",
                description: "Someone just shared their location on the map",
              });
            }
          });
        }

        if (payload.eventType === "UPDATE") {
          next = next.map((u) =>
            u.id === payload.new.id ? {
              ...payload.new,
              location: {
                x: typeof payload.new.location === 'object' && payload.new.location !== null 
                  ? Number((payload.new.location as any).x || 0) : 0,
                y: typeof payload.new.location === 'object' && payload.new.location !== null 
                  ? Number((payload.new.location as any).y || 0) : 0
              }
            } as UserLocation : u
          );
        }

        if (payload.eventType === "DELETE") {
          next = next.filter((u) => u.id !== payload.old.id);
        }

        return next;
      });
    };

    // Wrap the handler to match Supabase's callback signature
    const channel = supabase
      .channel("public:user_locations")
      .on(
        "postgres_changes",
        { event: "*", schema: "public", table: "user_locations" },
        handler
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [toast]);

  return { data: locations };
}

