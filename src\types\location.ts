
// Define the Location Message interface for chat location sharing
export interface LocationMessage {
  id: string;
  conversation_id: string;
  sender_id: string;
  sender_name?: string;
  sender_avatar_url?: string;
  lat: number;
  lng: number;
  label?: string;
  expiration: string;
  is_expired: boolean;
  created_at?: string;
}

// Map Location type for location picking
export interface MapLocation {
  lng: number;
  lat: number;
  x?: number;
  y?: number;
  address?: string;
  label?: string;
}

// Helper function to normalize location data
export function normalizeLocation(location: any): MapLocation {
  if (!location) return { lng: 0, lat: 0, x: 0, y: 0 };
  
  // Handle different location formats
  if (typeof location === 'string') {
    try {
      const parsed = JSON.parse(location);
      const lng = parsed.x || parsed.lng || 0;
      const lat = parsed.y || parsed.lat || 0;
      return { 
        lng,
        lat,
        x: lng, 
        y: lat,
        address: parsed.address
      };
    } catch (e) {
      return { lng: 0, lat: 0, x: 0, y: 0 };
    }
  }
  
  const lng = location.x !== undefined ? location.x : (location.lng !== undefined ? location.lng : 0);
  const lat = location.y !== undefined ? location.y : (location.lat !== undefined ? location.lat : 0);
  
  return { 
    lng,
    lat,
    x: lng, 
    y: lat,
    address: location.address
  };
}
