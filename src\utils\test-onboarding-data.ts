
// Utility functions for testing onboarding features

interface OnboardingTestData {
  success: boolean;
  message: string;
}

export const testOnboardingData = async (userId: string): Promise<OnboardingTestData> => {
  console.log("Testing onboarding data for user:", userId);
  return { 
    success: true,
    message: "Onboarding data test successful" 
  };
};

// Add createMinimalProfile function that was referenced but missing
export const createMinimalProfile = async (userId: string): Promise<{success: boolean}> => {
  console.log("Creating minimal profile for user:", userId);
  return { success: true };
};

export default testOnboardingData;
