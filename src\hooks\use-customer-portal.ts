
import { useMutation } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export function useCustomerPortal() {
  const { toast } = useToast();
  
  return useMutation({
    mutationFn: async () => {
      const { data, error } = await supabase.functions.invoke('customer-portal', {});
      
      if (error) throw error;
      if (!data?.url) throw new Error('No portal URL returned');
      
      return data.url;
    },
    onSuccess: (url) => {
      window.location.href = url;
    },
    onError: (error: Error) => {
      toast({
        title: 'Failed to open customer portal',
        description: error.message,
        variant: 'destructive'
      });
    }
  });
}
