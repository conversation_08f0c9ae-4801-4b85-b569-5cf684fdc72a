
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface BuddyProvider {
  id: string;
  displayName: string;
  username: string;
  avatarUrl: string;
  bio: string;
  vibes: string[];
  location: string;
  distance: number;
  hourlyRate: number;
  rating: number;
  reviewCount: number;
  isAvailable: boolean;
  hirePurposes: string[];
}

export function useBuddyProviders(categoryId?: string, searchQuery?: string) {
  return useQuery({
    queryKey: ['buddy-providers', categoryId, searchQuery],
    queryFn: async (): Promise<BuddyProvider[]> => {
      // For now, return mock data since we don't have service_providers table
      // This will be replaced once the proper tables are created
      return [
        {
          id: '1',
          displayName: '<PERSON>',
          username: 'alexj',
          avatarUrl: '',
          bio: 'Local guide and adventure enthusiast',
          vibes: ['Adventure', 'Outdoors'],
          location: 'San Francisco, CA',
          distance: 2.5,
          hourlyRate: 50,
          rating: 4.8,
          reviewCount: 24,
          isAvailable: true,
          hirePurposes: ['Local Guide', 'Adventure Buddy']
        },
        {
          id: '2',
          displayName: '<PERSON>',
          username: 'sarah<PERSON>',
          avatarUrl: '',
          bio: 'Food lover and city explorer',
          vibes: ['Foodie', 'Social'],
          location: 'San Francisco, CA',
          distance: 1.2,
          hourlyRate: 40,
          rating: 4.9,
          reviewCount: 31,
          isAvailable: true,
          hirePurposes: ['Food Guide', 'Event Buddy']
        }
      ];
    }
  });
}
