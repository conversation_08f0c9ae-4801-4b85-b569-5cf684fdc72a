
import React from 'react';
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Activity, ActivityQueueEntry } from '@/types/activity';
import { CheckCircle, Clock, AlertCircle, Loader2 } from 'lucide-react';

interface ActivityQueueStatusProps {
  activity: Activity;
  userEntry: ActivityQueueEntry;
  confirmedCount: number;
  totalCapacity: number | null;
  onLeaveQueue: () => void;
  isProcessing: boolean;
  className?: string;
}

export function ActivityQueueStatus({
  activity,
  userEntry,
  confirmedCount,
  totalCapacity,
  onLeaveQueue,
  isProcessing,
  className = ''
}: ActivityQueueStatusProps) {
  const getStatusDisplay = () => {
    switch (userEntry.status) {
      case 'confirmed':
        return (
          <div className="flex items-center p-3 bg-green-50 rounded-md border border-green-100">
            <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
            <div>
              <div className="font-medium text-green-800">You're confirmed!</div>
              <p className="text-sm text-green-700">
                You have a spot in this activity. Check your notifications for updates.
              </p>
            </div>
          </div>
        );
      case 'pending':
        return (
          <div className="flex items-center p-3 bg-amber-50 rounded-md border border-amber-100">
            <Clock className="h-5 w-5 text-amber-600 mr-3" />
            <div>
              <div className="font-medium text-amber-800">Waiting for confirmation</div>
              <p className="text-sm text-amber-700">
                You're #{userEntry.position} in line. {totalCapacity && 
                  `${Math.max(0, totalCapacity - confirmedCount)} spots remaining.`
                }
              </p>
              {activity.is_paid && (
                <Badge variant="outline" className="mt-1 bg-amber-100 text-amber-800 border-amber-200">
                  Payment required if confirmed
                </Badge>
              )}
            </div>
          </div>
        );
      case 'waitlisted':
        return (
          <div className="flex items-center p-3 bg-blue-50 rounded-md border border-blue-100">
            <AlertCircle className="h-5 w-5 text-blue-600 mr-3" />
            <div>
              <div className="font-medium text-blue-800">You're on the waitlist</div>
              <p className="text-sm text-blue-700">
                Position #{userEntry.position} on waitlist. You'll be notified if a spot opens up.
              </p>
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className={`space-y-3 ${className}`}>
      {getStatusDisplay()}
      
      {(userEntry.status === 'pending' || userEntry.status === 'waitlisted') && (
        <Button 
          variant="outline" 
          size="sm" 
          className="w-full mt-1 border-red-200 text-red-600 hover:bg-red-50 hover:text-red-700"
          onClick={onLeaveQueue}
          disabled={isProcessing}
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-3 w-3 mr-2 animate-spin" />
              Processing...
            </>
          ) : (
            "Leave Queue"
          )}
        </Button>
      )}
    </div>
  );
}
