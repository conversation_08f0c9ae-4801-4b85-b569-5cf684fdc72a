
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { OnboardingFormValues } from "@/types/onboarding";
import { Switch } from "@/components/ui/switch";

interface NotificationsStepProps {
  form: UseFormReturn<OnboardingFormValues>;
}

export function NotificationsStep({ form }: NotificationsStepProps) {
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-xl font-semibold">Notification preferences</h2>
        <p className="text-sm text-muted-foreground mt-1">
          Choose how you want to be notified
        </p>
      </div>
      
      <FormField
        control={form.control}
        name="notifyMessages"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <FormLabel className="text-base">New messages</FormLabel>
              <FormDescription>
                Get notified when you receive a new message
              </FormDescription>
            </div>
            <FormControl>
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
          </FormItem>
        )}
      />
      
      <FormField
        control={form.control}
        name="notifyActivities"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <FormLabel className="text-base">Activity updates</FormLabel>
              <FormDescription>
                Get notified about nearby activities matching your interests
              </FormDescription>
            </div>
            <FormControl>
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
          </FormItem>
        )}
      />
      
      <FormField
        control={form.control}
        name="notifyFollows"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <FormLabel className="text-base">New followers</FormLabel>
              <FormDescription>
                Get notified when someone follows you
              </FormDescription>
            </div>
            <FormControl>
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
          </FormItem>
        )}
      />
    </div>
  );
}
