
import React, { useState, useEffect } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { Button } from "@/components/ui/button";
import MapView from "@/components/map/MapView";
import MapExploreFilters from "@/components/map/MapExploreFilters";
import MapSidebar from "@/components/map/MapSidebar";
import { Filter, MapIcon, Activity, Users, MapPin, Compass } from 'lucide-react';
import { MapTabs } from "@/components/map/MapTabs";
import { ActivityMapFilters } from '@/hooks/use-map-activities';
import { MapActivityFilters } from '@/components/map/MapActivityFilters';
import { useToast } from '@/hooks/use-toast';
import { useUserLocation } from '@/hooks/use-user-location';
import { useUserLocations } from '@/hooks/use-user-locations';
import { MapRightSidebarTabValue } from '@/components/map/MapRightSidebar/MapRightSidebarTypes';

export default function MapPage() {
  const [activeTab, setActiveTab] = useState<MapRightSidebarTabValue>("explore");
  const [activityFilters, setActivityFilters] = useState<ActivityMapFilters>({
    radius: 10 // Initialize with a default radius
  });
  const { toast } = useToast();
  const { currentLocation, getPosition, permissionStatus } = useUserLocation();
  const { data: locations, isLoading } = useUserLocations();

  useEffect(() => {
    if (permissionStatus === 'prompt') {
      getPosition()
        .then(() => {
          toast({
            title: "Location accessed",
            description: "We can now show you content near you"
          });
        })
        .catch((error) => {
          console.error("Location error:", error);
          toast({
            title: "Location access denied",
            description: "Enable location services to see content near you",
            variant: "destructive"
          });
        });
    }
  }, [permissionStatus]);

  const handleMyLocation = async () => {
    try {
      await getPosition();
      toast({
        title: "Location updated",
        description: "Map centered on your current location",
      });
    } catch (error) {
      toast({
        title: "Location error",
        description: "Unable to get your location. Please check permissions.",
        variant: "destructive"
      });
    }
  };

  const handleFilterChange = (filters: ActivityMapFilters) => {
    setActivityFilters(filters);
  };

  const handleTabChange = (tab: string) => {
    setActiveTab(tab as MapRightSidebarTabValue);
  };

  return (
    <MainLayout title="Map" fullWidth noContainer>
      <div className="relative h-[calc(100vh-65px)]">
        <MapView 
          activeTab={activeTab} 
          activityFilters={activityFilters}
          locations={locations || []}
          isLoading={isLoading} 
        />
        
        <div className="absolute top-4 right-4 z-10 flex flex-col items-end gap-2">
          {activeTab === "explore" && (
            <MapExploreFilters className="mr-2" />
          )}
          
          {activeTab === "activities" && (
            <MapActivityFilters 
              onFilterChange={handleFilterChange}
              className="mr-2"
            />
          )}
        </div>
        
        <div className="absolute bottom-4 left-0 right-0 z-10 flex justify-center">
          <MapTabs activeTab={activeTab} onTabChange={handleTabChange} />
        </div>
        
        <div className="absolute bottom-24 right-4 z-10">
          <Button
            variant="secondary"
            size="icon"
            className="h-10 w-10 rounded-full bg-white shadow-md"
            onClick={handleMyLocation}
          >
            <Compass className="h-5 w-5" />
          </Button>
        </div>
        
        <MapSidebar />
      </div>
    </MainLayout>
  );
}
