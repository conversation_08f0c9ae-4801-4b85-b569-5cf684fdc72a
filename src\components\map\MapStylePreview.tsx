
import { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { cn } from '@/lib/utils';

interface MapStylePreviewProps {
  mapboxToken?: string;
  styleUrl: string;
  className?: string;
  previewLocation?: { lng: number; lat: number };
  zoom?: number;
  onClick?: () => void;
}

export function MapStylePreview({ 
  mapboxToken, 
  styleUrl, 
  className,
  previewLocation = { lng: -74.0066, lat: 40.7135 }, // Default to NYC
  zoom = 13,
  onClick
}: MapStylePreviewProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  
  useEffect(() => {
    if (!mapContainer.current || !mapboxToken) return;
    
    // Initialize the preview map
    try {
      mapboxgl.accessToken = mapboxToken;
      
      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: styleUrl,
        center: [previewLocation.lng, previewLocation.lat],
        zoom: zoom,
        interactive: false, // No interaction needed for a preview
      });
      
      // Add minimal UI and options
      map.current.scrollZoom.disable();
      map.current.dragPan.disable();
      map.current.dragRotate.disable();
      map.current.touchZoomRotate.disable();
      map.current.doubleClickZoom.disable();
      map.current.keyboard.disable();
      
      return () => {
        map.current?.remove();
        map.current = null;
      };
    } catch (error) {
      console.error('Error initializing map preview:', error);
    }
  }, [mapboxToken, styleUrl, previewLocation]);
  
  return (
    <div 
      className={cn(
        "relative h-24 w-full rounded-md overflow-hidden border border-border cursor-pointer hover:border-primary transition-colors",
        className
      )}
      onClick={onClick}
      ref={mapContainer}
    >
      {/* Optional overlay with style name */}
      <div className="absolute inset-0 flex items-center justify-center bg-black/20 backdrop-blur-sm opacity-0 hover:opacity-100 transition-opacity">
        <div className="bg-background/80 px-2 py-1 rounded text-xs font-medium">
          {styleUrl.split('/').pop()?.replace('-v9', '').replace('-v11', '').split('-').map(word => 
            word.charAt(0).toUpperCase() + word.slice(1)
          ).join(' ')}
        </div>
      </div>
    </div>
  );
}
