
import React, { useState } from 'react';
import { Image } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogTrigger,
} from "@/components/ui/dialog";

interface ChatMessageMediaProps {
  url: string;
  caption?: string;
}

export function ChatMessageMedia({ url, caption }: ChatMessageMediaProps) {
  const [isOpen, setIsOpen] = useState(false);

  // Determine if this is an image or video
  const isImage = /\.(jpe?g|png|gif|bmp|webp)$/i.test(url);
  const isVideo = /\.(mp4|webm|ogg|mov)$/i.test(url);

  if (!url) {
    return (
      <div className="flex items-center justify-center p-4 bg-gray-100 rounded-lg">
        <Image className="h-6 w-6 text-gray-400" />
        <span className="ml-2 text-sm text-gray-500">Media not available</span>
      </div>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <div className="cursor-pointer">
          {isImage && (
            <img 
              src={url} 
              alt={caption || "Shared image"} 
              className="max-h-64 rounded-lg object-contain" 
            />
          )}
          {isVideo && (
            <video 
              src={url}
              className="max-h-64 rounded-lg"
              controls
            />
          )}
          {!isImage && !isVideo && (
            <div className="flex items-center justify-center p-4 bg-gray-100 rounded-lg">
              <Image className="h-6 w-6 text-gray-400" />
              <span className="ml-2 text-sm text-gray-500">Attachment</span>
            </div>
          )}
          {caption && (
            <div className="mt-1 text-xs text-gray-500">{caption}</div>
          )}
        </div>
      </DialogTrigger>
      <DialogContent className="max-w-4xl p-0 bg-black/90">
        <div className="flex items-center justify-center h-full p-4">
          {isImage && (
            <img 
              src={url} 
              alt={caption || "Shared image"} 
              className="max-h-[80vh] max-w-full object-contain" 
            />
          )}
          {isVideo && (
            <video 
              src={url}
              className="max-h-[80vh] max-w-full"
              controls
              autoPlay
            />
          )}
        </div>
        {caption && (
          <div className="p-4 text-white/80 text-center">{caption}</div>
        )}
      </DialogContent>
    </Dialog>
  );
}
