
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Activity } from '@/types/activity';
import { ActivityShareModule } from './ActivityShareModule';
import { Share2, MapPin } from 'lucide-react';

interface ActivityCardActionsProps {
  activity: Activity;
  onViewOnMap?: () => void;
  onViewDetails?: () => void;
}

export function ActivityCardActions({ 
  activity, 
  onViewOnMap,
  onViewDetails 
}: ActivityCardActionsProps) {
  return (
    <div className="flex flex-wrap gap-2">
      {onViewDetails && (
        <Button 
          variant="default" 
          size="sm" 
          className="flex-1" 
          onClick={onViewDetails}
        >
          View Details
        </Button>
      )}
      
      {onViewOnMap && (
        <Button 
          variant="outline" 
          size="sm" 
          onClick={onViewOnMap}
          className="flex-none"
        >
          <MapPin className="h-4 w-4" />
        </Button>
      )}
      
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm" className="flex-none">
            <Share2 className="h-4 w-4" />
          </Button>
        </DialogTrigger>
        <DialogContent>
          <ActivityShareModule activity={activity} />
        </DialogContent>
      </Dialog>
    </div>
  );
}
