-- Script to fix remaining duplicate RLS policies
-- This script targets specific tables identified by the linter

-- Function to consolidate policies for a specific table and action
CREATE OR REPLACE FUNCTION consolidate_specific_table_policies(p_table_name text, p_cmd text)
RETURNS void AS $$
DECLARE
    policy_names text[];
    consolidated_using text;
    consolidated_check text;
    new_policy_name text;
    policy_count integer;
    policy_name text;
    i integer;
BEGIN
    -- Get the count of policies for this table and action
    SELECT COUNT(*) INTO policy_count
    FROM pg_policies
    WHERE schemaname = 'public' 
      AND tablename = p_table_name
      AND cmd = p_cmd;
    
    -- If there's more than one policy for this action, consolidate them
    IF policy_count > 1 THEN
        RAISE NOTICE 'Consolidating % policies for % on table %', 
            policy_count, p_cmd, p_table_name;
        
        -- Create a new policy name with timestamp to ensure uniqueness
        new_policy_name := 'Consolidated_' || p_cmd || '_policy_' || extract(epoch from now())::bigint % 10000;
        
        -- Get all the policy names and expressions for this action
        SELECT array_agg(policyname), 
               string_agg(COALESCE(qual, 'true'), ' OR ') 
        INTO policy_names, consolidated_using
        FROM pg_policies
        WHERE schemaname = 'public' 
          AND tablename = p_table_name
          AND cmd = p_cmd;
        
        -- For INSERT and UPDATE, also get the WITH CHECK expressions
        IF p_cmd IN ('INSERT', 'UPDATE') THEN
            SELECT string_agg(COALESCE(with_check, 'true'), ' OR ') 
            INTO consolidated_check
            FROM pg_policies
            WHERE schemaname = 'public' 
              AND tablename = p_table_name
              AND cmd = p_cmd;
        END IF;
        
        -- Create the new consolidated policy
        IF p_cmd = 'ALL' THEN
            EXECUTE format(
                'CREATE POLICY %I ON %I FOR ALL TO public USING (%s) WITH CHECK (%s)',
                new_policy_name,
                p_table_name,
                COALESCE(consolidated_using, 'true'),
                COALESCE(consolidated_check, 'true')
            );
        ELSIF p_cmd = 'SELECT' THEN
            EXECUTE format(
                'CREATE POLICY %I ON %I FOR SELECT TO public USING (%s)',
                new_policy_name,
                p_table_name,
                COALESCE(consolidated_using, 'true')
            );
        ELSIF p_cmd = 'INSERT' THEN
            EXECUTE format(
                'CREATE POLICY %I ON %I FOR INSERT TO public WITH CHECK (%s)',
                new_policy_name,
                p_table_name,
                COALESCE(consolidated_check, 'true')
            );
        ELSIF p_cmd = 'UPDATE' THEN
            EXECUTE format(
                'CREATE POLICY %I ON %I FOR UPDATE TO public USING (%s) WITH CHECK (%s)',
                new_policy_name,
                p_table_name,
                COALESCE(consolidated_using, 'true'),
                COALESCE(consolidated_check, 'true')
            );
        ELSIF p_cmd = 'DELETE' THEN
            EXECUTE format(
                'CREATE POLICY %I ON %I FOR DELETE TO public USING (%s)',
                new_policy_name,
                p_table_name,
                COALESCE(consolidated_using, 'true')
            );
        END IF;
        
        -- Drop the old policies
        FOR i IN 1..array_length(policy_names, 1) LOOP
            policy_name := policy_names[i];
            EXECUTE format(
                'DROP POLICY IF EXISTS %I ON %I',
                policy_name,
                p_table_name
            );
        END LOOP;
        
        RAISE NOTICE 'Created consolidated policy % for % on table %', 
            new_policy_name, p_cmd, p_table_name;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- Process each table with duplicate policies
DO $$
DECLARE
    tables_with_duplicates text[] := ARRAY[
        'activity_exceptions', 'activity_flags', 'activity_geofences', 
        'admin_notification_rules', 'admin_notification_templates',
        'analytics_dashboards', 'analytics_page_views', 'analytics_user_segments',
        'bot_messages', 'chat_proposal_types', 'content_flags',
        'content_moderation_events', 'content_moderation_rules', 'content_moderation_templates',
        'dispute_categories', 'dispute_templates', 'gig_packages',
        'gig_portfolio', 'gigs', 'notification_preferences',
        'provider_verification_levels', 'report_categories', 'report_templates',
        'role_permissions', 'saved_locations', 'service_fulfillment_stages',
        'service_milestones', 'typing_indicators', 'user_moderation_events',
        'user_permissions', 'user_roles'
    ];
    actions text[] := ARRAY['SELECT', 'INSERT', 'UPDATE', 'DELETE'];
    t text;
    a text;
BEGIN
    FOREACH t IN ARRAY tables_with_duplicates
    LOOP
        FOREACH a IN ARRAY actions
        LOOP
            PERFORM consolidate_specific_table_policies(t, a);
        END LOOP;
    END LOOP;
END $$;

-- Clean up by dropping the function
DROP FUNCTION consolidate_specific_table_policies(text, text);
