
import React, { useState, useEffect } from 'react';
import { SearchIcon, Users, MessageSquare, Plus, Loader2 } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { 
  Sheet,
  SheetContent,
  SheetDescription,
  SheetHeader,
  SheetT<PERSON>le,
  SheetTrigger
} from "@/components/ui/sheet";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { format, addDays, isWeekend } from 'date-fns';
import { CalendarIcon } from 'lucide-react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/hooks/use-auth';
import { useChatConversations } from '@/hooks/use-chat-conversations';
import { useAdminConversation } from '@/hooks/use-buddy-admin-conversation';
import { useBuddyList } from '@/hooks/use-buddy-list';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

// Extend the Conversation interface to include needed properties
export interface ExtendedConversation {
  id: string;
  activity_id?: string | null;
  last_message: string | null;
  last_message_at: string | null;
  is_group: boolean;
  is_announcement_only?: boolean;
  is_admin_conversation?: boolean;
  is_support?: boolean;
  created_at: string;
  updated_at: string;
  participants?: any[];
  other_participant?: {
    display_name?: string;
    username?: string;
    avatar_url?: string;
  };
  unread_count?: number;
}

export function ChatSidebar() {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [searchQuery, setSearchQuery] = useState('');
  const [isNewChatOpen, setIsNewChatOpen] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date>();

  const { 
    adminConversation, 
    supportConversation,
    isLoading: isAdminLoading 
  } = useAdminConversation();

  const { 
    conversations, 
    isLoading: conversationsLoading,
    refetch
  } = useChatConversations();

  const { 
    data: buddies, 
    isLoading: buddyListLoading 
  } = useBuddyList();

  // Get selected conversation ID from URL
  const selectedConversationId = searchParams.get('conversation');
  const selectedActivityId = searchParams.get('activity');
  const selectedUserId = searchParams.get('user');

  // Handle clicking on a conversation
  const handleConversationClick = (conversationId: string) => {
    setSearchParams({ conversation: conversationId });
  };

  // Handle starting a new conversation with a buddy
  const handleStartConversation = async (buddyId: string) => {
    if (!user) return;

    try {
      // Create a conversation with the selected buddy
      // Use your backend API or service to create a conversation
      // For now, we'll navigate to the chat page with user parameter
      navigate(`/chat?user=${buddyId}`);
      
      // Close the sheet
      setIsNewChatOpen(false);
    } catch (error) {
      console.error('Error creating conversation:', error);
      toast({
        title: 'Error',
        description: 'Failed to create conversation',
        variant: 'destructive'
      });
    }
  };

  // Filter conversations based on search query
  const filteredConversations = conversations?.filter(conversation => {
    // Safe access pattern for potentially missing properties
    const otherParticipantName = conversation.other_participant?.display_name || 
      conversation.other_participant?.username || 
      'Unknown';
    
    return otherParticipantName.toLowerCase().includes(searchQuery.toLowerCase()) || 
      (conversation.last_message && 
        conversation.last_message.toLowerCase().includes(searchQuery.toLowerCase()));
  }) as ExtendedConversation[] | undefined;

  return (
    <div className="w-80 border-r flex flex-col h-full">
      <div className="p-3 border-b">
        <Input
          placeholder="Search conversations..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="w-full"
        />
      </div>
      
      <div className="p-2 border-b">
        <Sheet open={isNewChatOpen} onOpenChange={setIsNewChatOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              New Chat
            </Button>
          </SheetTrigger>
          <SheetContent>
            <SheetHeader>
              <SheetTitle>Start a new conversation</SheetTitle>
              <SheetDescription>
                Select a buddy to start chatting with
              </SheetDescription>
            </SheetHeader>
            
            <div className="mt-4 space-y-4">
              <div className="space-y-2">
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !selectedDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {selectedDate ? format(selectedDate, "PPP") : "Filter by date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={selectedDate}
                      onSelect={setSelectedDate}
                      initialFocus
                      disabled={(date) => 
                        date > new Date() || 
                        date < addDays(new Date(), -30) || 
                        isWeekend(date)
                      }
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <ScrollArea className="h-72">
                {buddyListLoading ? (
                  <div className="flex justify-center items-center h-20">
                    <Loader2 className="h-6 w-6 animate-spin" />
                  </div>
                ) : (
                  <div className="space-y-1">
                    {buddies?.map((buddy) => (
                      <Button
                        key={buddy.id}
                        variant="ghost"
                        className="w-full justify-start"
                        onClick={() => handleStartConversation(buddy.id)}
                      >
                        <Avatar className="h-6 w-6 mr-2">
                          <AvatarImage src={buddy.avatar_url || ''} />
                          <AvatarFallback>
                            {(buddy.display_name || buddy.username || '?').substring(0, 2)}
                          </AvatarFallback>
                        </Avatar>
                        <span>{buddy.display_name || buddy.username || 'Unknown'}</span>
                      </Button>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </div>
          </SheetContent>
        </Sheet>
      </div>
      
      <Separator />
      
      <div className="font-medium text-sm px-3 py-2">
        {buddyListLoading ? (
          <div className="flex items-center">
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
            Loading buddies...
          </div>
        ) : (
          <div className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            {buddies?.length || 0} Buddies
          </div>
        )}
      </div>
      
      <ScrollArea className="flex-1">
        {/* Admin conversation */}
        {adminConversation && (
          <>
            <Button
              variant={selectedConversationId === adminConversation.id ? "secondary" : "ghost"}
              className="w-full justify-start p-3 h-auto"
              onClick={() => handleConversationClick(adminConversation.id)}
            >
              <Avatar className="h-10 w-10 mr-3 border-2 border-blue-300">
                <AvatarImage src="https://api.dicebear.com/7.x/bottts/svg?seed=buddy&backgroundColor=b6e3f4" />
                <AvatarFallback>B</AvatarFallback>
              </Avatar>
              <div className="text-left">
                <div className="font-medium">Buddy Admin</div>
                <div className="text-xs text-muted-foreground truncate max-w-[180px]">
                  Official announcements and updates
                </div>
              </div>
            </Button>
            <Separator />
          </>
        )}
        
        {/* Support conversation */}
        {supportConversation && (
          <>
            <Button
              variant={selectedConversationId === supportConversation.id ? "secondary" : "ghost"}
              className="w-full justify-start p-3 h-auto"
              onClick={() => handleConversationClick(supportConversation.id)}
            >
              <Avatar className="h-10 w-10 mr-3 border-2 border-green-300">
                <AvatarImage src="https://api.dicebear.com/7.x/bottts/svg?seed=support&backgroundColor=d1fae5" />
                <AvatarFallback>S</AvatarFallback>
              </Avatar>
              <div className="text-left">
                <div className="font-medium">Support</div>
                <div className="text-xs text-muted-foreground truncate max-w-[180px]">
                  Get help and support
                </div>
              </div>
            </Button>
            <Separator />
          </>
        )}
        
        {/* Regular conversations */}
        {conversationsLoading ? (
          <div className="flex justify-center items-center p-8">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : filteredConversations && filteredConversations.length > 0 ? (
          filteredConversations.map((conversation) => {
            const otherParticipant = conversation.other_participant;
            const isSelected = selectedConversationId === conversation.id;
            
            return (
              <Button
                key={conversation.id}
                variant={isSelected ? "secondary" : "ghost"}
                className="w-full justify-start p-3 h-auto"
                onClick={() => handleConversationClick(conversation.id)}
              >
                <Avatar className="h-10 w-10 mr-3">
                  <AvatarImage src={otherParticipant?.avatar_url || ''} />
                  <AvatarFallback>
                    {otherParticipant?.display_name?.substring(0, 2) || 
                     otherParticipant?.username?.substring(0, 2) || 'UN'}
                  </AvatarFallback>
                </Avatar>
                <div className="text-left flex-1 min-w-0">
                  <div className="font-medium truncate">
                    {otherParticipant?.display_name || 
                     otherParticipant?.username || 
                     'Unknown User'}
                  </div>
                  {conversation.last_message && (
                    <div className="text-xs text-muted-foreground truncate max-w-[180px]">
                      {conversation.last_message}
                    </div>
                  )}
                </div>
                {conversation.unread_count ? conversation.unread_count > 0 && (
                  <div className="ml-2 bg-primary text-primary-foreground rounded-full h-5 min-w-5 flex items-center justify-center text-xs">
                    {conversation.unread_count}
                  </div>
                ) : null}
              </Button>
            );
          })
        ) : (
          <div className="p-8 text-center">
            <MessageSquare className="h-8 w-8 mx-auto text-muted-foreground mb-2" />
            <p className="text-sm text-muted-foreground">No conversations found</p>
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
