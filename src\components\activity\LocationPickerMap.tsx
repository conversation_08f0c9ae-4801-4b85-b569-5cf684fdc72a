
import React, { useRef, useEffect, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Loader2, Check, X, Layers } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useUserLocation } from '@/hooks/use-user-location';
import { supabase } from '@/integrations/supabase/client';

interface LocationPickerMapProps {
  onLocationSelect: (location: { lng: number; lat: number; address: string }) => void;
  initialLocation?: { lng: number; lat: number } | null;
  className?: string;
}

export function LocationPickerMap({ 
  onLocationSelect, 
  initialLocation = null,
  className = ''
}: LocationPickerMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const marker = useRef<mapboxgl.Marker | null>(null);
  const [mapboxToken, setMapboxToken] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedLocation, setSelectedLocation] = useState<{ lng: number; lat: number } | null>(initialLocation);
  const [address, setAddress] = useState<string>('');
  const [mapStyle, setMapStyle] = useState<string>('streets-v11');
  const { toast } = useToast();
  const { currentLocation } = useUserLocation();

  // Fetch Mapbox token
  useEffect(() => {
    const fetchMapboxToken = async () => {
      try {
        const { data, error } = await supabase.functions.invoke('get-mapbox-token');
        if (error) {
          console.error('Error fetching Mapbox token:', error);
          toast({
            title: "Error fetching map token",
            description: "Please check your Mapbox configuration",
            variant: "destructive"
          });
          return;
        }
        if (data?.token) {
          setMapboxToken(data.token);
        }
      } catch (error) {
        console.error('Error:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchMapboxToken();
  }, [toast]);

  // Initialize map
  useEffect(() => {
    if (!mapboxToken || !mapContainer.current) return;

    // Initialize map
    mapboxgl.accessToken = mapboxToken;
    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: `mapbox://styles/mapbox/${mapStyle}`,
      center: selectedLocation 
        ? [selectedLocation.lng, selectedLocation.lat]
        : currentLocation 
          ? [currentLocation.lng, currentLocation.lat] 
          : [-74.5, 40], // Default to NYC if no location
      zoom: 12,
      pitch: 45,
      bearing: 0,
      attributionControl: false
    });

    // Add navigation control
    map.current.addControl(new mapboxgl.NavigationControl(), 'top-right');

    // Add marker
    if (selectedLocation) {
      marker.current = new mapboxgl.Marker({ draggable: true, color: '#0EA5E9' })
        .setLngLat([selectedLocation.lng, selectedLocation.lat])
        .addTo(map.current);
      
      // Get address for initial location
      fetchAddress(selectedLocation.lng, selectedLocation.lat);
      
      // Add drag end event
      marker.current.on('dragend', () => {
        const lngLat = marker.current?.getLngLat();
        if (lngLat) {
          setSelectedLocation({ lng: lngLat.lng, lat: lngLat.lat });
          fetchAddress(lngLat.lng, lngLat.lat);
        }
      });
    }

    // Add click event to map
    map.current.on('click', (e) => {
      const { lng, lat } = e.lngLat;
      
      // Update or create marker
      if (marker.current) {
        marker.current.setLngLat([lng, lat]);
      } else {
        marker.current = new mapboxgl.Marker({ draggable: true, color: '#0EA5E9' })
          .setLngLat([lng, lat])
          .addTo(map.current!);
        
        // Add drag end event
        marker.current.on('dragend', () => {
          const lngLat = marker.current?.getLngLat();
          if (lngLat) {
            setSelectedLocation({ lng: lngLat.lng, lat: lngLat.lat });
            fetchAddress(lngLat.lng, lngLat.lat);
          }
        });
      }
      
      setSelectedLocation({ lng, lat });
      fetchAddress(lng, lat);
    });

    // Cleanup
    return () => {
      map.current?.remove();
    };
  }, [mapboxToken, mapStyle, currentLocation, selectedLocation]);

  // Fetch address from coordinates
  const fetchAddress = async (lng: number, lat: number) => {
    try {
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${mapboxToken}`
      );
      const data = await response.json();
      
      if (data.features && data.features.length > 0) {
        setAddress(data.features[0].place_name);
      } else {
        setAddress('Unknown location');
      }
    } catch (error) {
      console.error('Error fetching address:', error);
      setAddress('Error fetching address');
    }
  };

  // Toggle map style
  const toggleMapStyle = () => {
    const newStyle = mapStyle === 'streets-v11' ? 'satellite-v9' : 'streets-v11';
    setMapStyle(newStyle);
    
    if (map.current) {
      map.current.setStyle(`mapbox://styles/mapbox/${newStyle}`);
    }
  };

  // Handle confirm location
  const handleConfirmLocation = () => {
    if (selectedLocation && address) {
      onLocationSelect({
        ...selectedLocation,
        address
      });
    }
  };

  // Use current location
  const useCurrentLocation = () => {
    if (currentLocation) {
      setSelectedLocation({ lng: currentLocation.lng, lat: currentLocation.lat });
      
      if (map.current) {
        map.current.flyTo({
          center: [currentLocation.lng, currentLocation.lat],
          zoom: 14,
          duration: 1000
        });
      }
      
      if (marker.current) {
        marker.current.setLngLat([currentLocation.lng, currentLocation.lat]);
      } else {
        marker.current = new mapboxgl.Marker({ draggable: true, color: '#0EA5E9' })
          .setLngLat([currentLocation.lng, currentLocation.lat])
          .addTo(map.current!);
        
        // Add drag end event
        marker.current.on('dragend', () => {
          const lngLat = marker.current?.getLngLat();
          if (lngLat) {
            setSelectedLocation({ lng: lngLat.lng, lat: lngLat.lat });
            fetchAddress(lngLat.lng, lngLat.lat);
          }
        });
      }
      
      fetchAddress(currentLocation.lng, currentLocation.lat);
    }
  };

  if (isLoading || !mapboxToken) {
    return (
      <div className={`flex items-center justify-center h-[300px] bg-gray-100 rounded-lg ${className}`}>
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <div ref={mapContainer} className="h-[300px] rounded-lg overflow-hidden" />
      
      <div className="absolute top-2 right-2 z-10">
        <Button 
          variant="outline" 
          size="sm" 
          className="bg-white"
          onClick={toggleMapStyle}
        >
          <Layers className="h-4 w-4 mr-2" />
          {mapStyle === 'streets-v11' ? 'Satellite' : 'Map'}
        </Button>
      </div>
      
      <div className="absolute bottom-2 left-2 z-10">
        <Button 
          variant="outline" 
          size="sm" 
          className="bg-white"
          onClick={useCurrentLocation}
        >
          <MapPin className="h-4 w-4 mr-2" />
          Use My Location
        </Button>
      </div>
      
      {selectedLocation && (
        <Card className="absolute bottom-2 right-2 z-10 w-[calc(100%-80px)] max-w-md">
          <CardContent className="p-3">
            <div className="flex items-start gap-2">
              <div className="flex-1">
                <p className="text-sm font-medium">Selected Location</p>
                <p className="text-xs text-muted-foreground truncate">{address}</p>
              </div>
              <Button 
                size="sm" 
                onClick={handleConfirmLocation}
              >
                <Check className="h-4 w-4 mr-2" />
                Confirm
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
