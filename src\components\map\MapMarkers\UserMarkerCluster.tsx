
import React, { useEffect, useRef } from 'react';
import mapboxgl from 'mapbox-gl';
import { UnifiedMapUser } from '@/types/map';
import { createUserMarker } from './createMapMarker';

interface UserMarkerClusterProps {
  map: mapboxgl.Map;
  users: UnifiedMapUser[];
  sourceId: string;
  onUserClick: (user: UnifiedMapUser) => void;
}

export function UserMarkerCluster({
  map,
  users,
  sourceId,
  onUserClick
}: UserMarkerClusterProps) {
  const markersRef = useRef<{ [key: string]: mapboxgl.Marker }>({});
  
  // Update user markers whenever users data changes
  useEffect(() => {
    if (!map) return;

    // Remove existing markers
    Object.values(markersRef.current).forEach(marker => marker.remove());
    markersRef.current = {};
    
    // Add new markers for each user
    users.forEach(user => {
      if (!user.location) return;
      
      const marker = createUserMarker(
        user,
        map,
        () => onUserClick(user)
      );
      
      if (marker) {
        marker.addTo(map);
        markersRef.current[user.user_id] = marker;
      }
    });
    
    // Cleanup on unmount
    return () => {
      Object.values(markersRef.current).forEach(marker => marker.remove());
      markersRef.current = {};
    };
  }, [map, users, onUserClick]);

  return null; // This is a non-visual component
}
