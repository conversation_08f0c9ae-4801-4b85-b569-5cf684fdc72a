
export interface QueueManagementStats {
  confirmed: number;
  pending: number;
  waitlisted: number;
  total: number;
  currentParticipants?: number;
  maxParticipants?: number;
  estimatedWaitTime?: number;
  capacity?: number | null;
  remaining?: number | null;
  entries?: any[];
  confirmedCount?: number;
  pendingCount?: number;
  totalCapacity?: number | null;
  capacityRemaining?: number | null;
  queueProgress?: number;
}

export interface QueueAnalytics {
  acceptanceRate: number;
  averageWaitTime: number;
  avgWaitTime: number;  // Added for compatibility
  peakHours: string[];
  popularityScore: number;
  dropoffRate?: number;
  joinRate?: number;
  confirmationRate?: number;
  average_wait_time?: number;
  conversion_rate?: number;
  filled_percentage?: number;
  peak_times?: Record<string, number>;
}
