
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';

export interface BuddyProfile {
  id: string;
  display_name: string;
  username: string;
  avatar_url: string;
  bio: string;
  last_seen_at: string;
}

export function useBuddyList() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['buddies', user?.id],
    queryFn: async () => {
      if (!user) return [];
      
      const { data, error } = await supabase
        .from('profiles')
        .select('id, display_name, username, avatar_url, bio')
        .order('updated_at', { ascending: false })
        .limit(50);
      
      if (error) {
        console.error('Error fetching buddies:', error);
        return [];
      }
      
      // Ensure each profile has the required properties and handle errors gracefully
      const buddyProfiles: BuddyProfile[] = data.map(profile => ({
        id: profile.id || '',
        display_name: profile.display_name || '',
        username: profile.username || '',
        avatar_url: profile.avatar_url || '',
        bio: profile.bio || '',
        last_seen_at: new Date().toISOString() // Since we couldn't get last_seen_at, use current time
      }));
      
      return buddyProfiles;
    },
    enabled: !!user
  });
}
