
import { useState, useEffect, useContext } from "react";
import { useAuth } from "@/hooks/use-auth";
import { isProfileIncomplete } from "@/components/onboarding/OnboardingController";
import { OnboardingModalWrapper } from "@/components/onboarding/OnboardingModalWrapper";
import { useLocation, UNSAFE_NavigationContext } from "react-router-dom";
import { verifyOnboardingStatus } from "@/utils/verify-onboarding-status";

interface RequireOnboardingProps {
  children: React.ReactNode;
}

/**
 * RequireOnboarding - Ensures a user has completed onboarding before accessing protected routes
 *
 * This component checks if a user's profile is complete according to the required fields.
 * If the profile is incomplete, it shows the onboarding modal and blocks access to the
 * protected route until onboarding is completed.
 */
export function RequireOnboarding({ children }: RequireOnboardingProps) {
  const { user, profile, isLoading, refreshProfile } = useAuth();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [isCheckingProfile, setIsCheckingProfile] = useState(true);

  // Check if we're inside a Router context
  const hasRouter = useContext(UNSAFE_NavigationContext) !== undefined;
  const location = hasRouter ? useLocation() : null;
  const currentPath = location?.pathname || '';

  useEffect(() => {
    // Only check once auth is loaded
    const checkProfileCompletion = async () => {
      if (!isLoading && user && profile) {
        console.log("Checking if profile needs onboarding:", profile);

        // First check if onboarding_completed flag is set directly
        const isCompleted = await verifyOnboardingStatus(user.id);
        console.log("Direct DB check for onboarding_completed:", isCompleted);

        if (isCompleted) {
          // If DB says onboarding is complete, trust that
          setShowOnboarding(false);
          setIsCheckingProfile(false);
          return;
        }

        // Don't show onboarding in RequireOnboarding - let OnboardingController handle it
        // This ensures onboarding only shows once after signup
        setShowOnboarding(false);

        setIsCheckingProfile(false);
      } else if (!isLoading && !user) {
        // If not logged in, no need to check profile
        setIsCheckingProfile(false);
      }
    };

    checkProfileCompletion();
  }, [user, profile, isLoading]);

  // Show loading state while checking profile
  if (isLoading || isCheckingProfile) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-purple"></div>
      </div>
    );
  }

  // If profile is incomplete, show onboarding modal and block access
  if (showOnboarding && user && profile) {
    return (
      <>
        <OnboardingModalWrapper
          open={showOnboarding}
          onOpenChange={async (open) => {
            // Only allow closing if profile is now complete
            if (!open) {
              // Refresh the profile data to get latest onboarding status
              await refreshProfile();

              // Check directly with the database first
              const isCompleted = await verifyOnboardingStatus(user.id);
              if (isCompleted) {
                console.log("Onboarding is now complete according to DB");
                setShowOnboarding(false);
                return;
              }

              // Fall back to the standard check
              const stillIncomplete = isProfileIncomplete(profile);
              if (!stillIncomplete) {
                console.log("Onboarding is now complete according to profile data");
                setShowOnboarding(false);
              }
            }
          }}
          user={user}
          profile={profile}
          forceComplete={true}
          redirectPath={currentPath} // Pass the current path so the user can be redirected back
        />
        <div className="flex items-center justify-center h-screen">
          <div className="text-center p-6 max-w-md">
            <h2 className="text-2xl font-bold mb-4">Complete Your Profile</h2>
            <p className="text-muted-foreground mb-4">
              Please complete your profile to access this feature. This helps us provide you with a better experience.
            </p>
          </div>
        </div>
      </>
    );
  }

  // If profile is complete, render the protected route
  return <>{children}</>;
}
