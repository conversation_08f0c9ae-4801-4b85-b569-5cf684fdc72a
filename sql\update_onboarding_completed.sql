-- Function to update onboarding_completed status
-- This is used as a fallback mechanism when regular updates fail

CREATE OR REPLACE FUNCTION public.update_onboarding_completed(user_id UUID, completed BOOLEAN)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE public.profiles
  SET 
    onboarding_completed = completed,
    updated_at = NOW()
  WHERE id = user_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- <PERSON> execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.update_onboarding_completed(UUID, BOOLEAN) TO authenticated;

-- Comment explaining the function
COMMENT ON FUNCTION public.update_onboarding_completed IS 'Updates the onboarding_completed flag for a user. Used as a fallback when regular updates fail.';
