
import React from 'react';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { formatDistance, formatLastSeen, getActivityStatus } from '@/utils/enhanced-distance';
import { UnifiedMapUser } from '@/types/map';
import { Button } from '@/components/ui/button';
import { MessageSquare, MapPin, Clock } from 'lucide-react';

interface UserListItemProps {
  user: UnifiedMapUser;
  distance?: number;
  onClick?: () => void;
  onMessage?: () => void;
  isSelected?: boolean;
}

export function UserListItem({
  user,
  distance,
  onClick,
  onMessage,
  isSelected = false
}: UserListItemProps) {
  const displayName = user.display_name || user.username || `User ${user.user_id?.substring?.(0, 5)}`;
  const activityStatus = user.updated_at ? getActivityStatus(user.updated_at) : null;
  const isOnline = user.is_online || (activityStatus?.status === 'online');

  return (
    <div 
      className={`flex items-center p-2 rounded-md ${
        isSelected ? 'bg-primary/10' : 'hover:bg-muted/50'
      } cursor-pointer transition-colors`}
      onClick={onClick}
    >
      <div className="relative mr-3">
        <Avatar className="h-10 w-10 border border-border">
          <AvatarImage src={user.avatar_url || ''} />
          <AvatarFallback>
            {displayName.substring(0, 2).toUpperCase()}
          </AvatarFallback>
        </Avatar>
        
        {isOnline && (
          <span className="absolute bottom-0 right-0 w-2.5 h-2.5 bg-green-500 rounded-full border border-background"></span>
        )}
      </div>
      
      <div className="flex-1 min-w-0">
        <div className="flex items-center">
          <h4 className="font-medium text-sm truncate">{displayName}</h4>
          {user.is_verified && (
            <Badge variant="outline" className="ml-1 h-4 px-1">✓</Badge>
          )}
        </div>
        
        <div className="flex items-center text-xs text-muted-foreground">
          {distance !== undefined ? (
            <div className="flex items-center mr-2">
              <MapPin className="h-3 w-3 mr-0.5" />
              <span>{formatDistance(distance)}</span>
            </div>
          ) : null}
          
          {activityStatus && (
            <div className="flex items-center">
              <Clock className="h-3 w-3 mr-0.5" />
              <span>{isOnline ? 'Online now' : activityStatus.label}</span>
            </div>
          )}
        </div>
      </div>
      
      {onMessage && (
        <Button 
          variant="ghost" 
          size="sm" 
          className="ml-2"
          onClick={(e) => {
            e.stopPropagation();
            onMessage();
          }}
        >
          <MessageSquare className="h-4 w-4" />
        </Button>
      )}
    </div>
  );
}
