import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { 
  Award, 
  Search, 
  Plus, 
  Edit, 
  Trash2, 
  Star,
  Calendar,
  TrendingUp,
  Crown,
  Zap
} from "lucide-react";
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { formatDistanceToNow } from 'date-fns';
import { getInitials } from '@/utils/get-initials';

interface FeaturedService {
  id: string;
  gig_id: string;
  provider_id: string;
  featured_type: 'premium' | 'sponsored' | 'trending' | 'editor_choice';
  start_date: string;
  end_date?: string;
  priority: number;
  is_active: boolean;
  created_at: string;
  gig: {
    id: string;
    title: string;
    description: string;
    price_starting: number;
    rating: number;
    total_orders: number;
    provider: {
      id: string;
      name: string;
      user: {
        display_name?: string;
        avatar_url?: string;
      };
    };
  };
}

interface FeaturedServicesPanelProps {
  className?: string;
}

export function FeaturedServicesPanel({ className }: FeaturedServicesPanelProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Fetch featured services
  const { data: featuredServices = [], isLoading } = useQuery({
    queryKey: ['admin-featured-services', searchQuery, filterType],
    queryFn: async (): Promise<FeaturedService[]> => {
      let query = supabase
        .from('featured_services')
        .select(`
          *,
          gig:gigs(
            id,
            title,
            description,
            price_starting,
            rating,
            total_orders,
            provider:service_providers(
              id,
              name,
              user:profiles!service_providers_user_id_fkey(
                display_name,
                avatar_url
              )
            )
          )
        `)
        .order('priority', { ascending: false })
        .order('created_at', { ascending: false });

      if (filterType !== 'all') {
        query = query.eq('featured_type', filterType);
      }

      const { data, error } = await query;
      if (error) throw error;

      let results = data || [];

      // Apply search filter
      if (searchQuery) {
        results = results.filter(item => 
          item.gig?.title?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.gig?.provider?.name?.toLowerCase().includes(searchQuery.toLowerCase())
        );
      }

      return results;
    }
  });

  // Remove featured service
  const removeFeatured = useMutation({
    mutationFn: async (featuredId: string) => {
      const { error } = await supabase
        .from('featured_services')
        .delete()
        .eq('id', featuredId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-featured-services'] });
      toast({
        title: 'Featured service removed',
        description: 'The service has been removed from featured list.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive'
      });
    }
  });

  // Toggle featured service status
  const toggleStatus = useMutation({
    mutationFn: async ({ featuredId, isActive }: { featuredId: string; isActive: boolean }) => {
      const { error } = await supabase
        .from('featured_services')
        .update({ 
          is_active: !isActive,
          updated_at: new Date().toISOString()
        })
        .eq('id', featuredId);

      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-featured-services'] });
      toast({
        title: 'Status updated',
        description: 'Featured service status has been updated.',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive'
      });
    }
  });

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'premium': return <Crown className="h-4 w-4" />;
      case 'sponsored': return <Zap className="h-4 w-4" />;
      case 'trending': return <TrendingUp className="h-4 w-4" />;
      case 'editor_choice': return <Award className="h-4 w-4" />;
      default: return <Star className="h-4 w-4" />;
    }
  };

  const getTypeBadge = (type: string) => {
    const colors = {
      premium: 'bg-purple-100 text-purple-800',
      sponsored: 'bg-blue-100 text-blue-800',
      trending: 'bg-green-100 text-green-800',
      editor_choice: 'bg-orange-100 text-orange-800'
    };

    return (
      <Badge className={colors[type as keyof typeof colors]}>
        {getTypeIcon(type)}
        <span className="ml-1 capitalize">{type.replace('_', ' ')}</span>
      </Badge>
    );
  };

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Award className="h-5 w-5" />
          Featured Services Management
        </CardTitle>
        <CardDescription>
          Manage featured, sponsored, and promoted services across the platform
        </CardDescription>
      </CardHeader>

      <CardContent>
        {/* Controls */}
        <div className="flex flex-col sm:flex-row gap-4 mb-6">
          <div className="flex-1">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search services or providers..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filter by type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Types</SelectItem>
              <SelectItem value="premium">Premium</SelectItem>
              <SelectItem value="sponsored">Sponsored</SelectItem>
              <SelectItem value="trending">Trending</SelectItem>
              <SelectItem value="editor_choice">Editor's Choice</SelectItem>
            </SelectContent>
          </Select>

          <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Add Featured
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Add Featured Service</DialogTitle>
                <DialogDescription>
                  Promote a service by featuring it on the platform
                </DialogDescription>
              </DialogHeader>
              <AddFeaturedServiceForm onClose={() => setIsAddModalOpen(false)} />
            </DialogContent>
          </Dialog>
        </div>

        {/* Featured Services List */}
        <div className="space-y-4">
          {isLoading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
              <p className="mt-2 text-sm text-gray-500">Loading featured services...</p>
            </div>
          ) : featuredServices.length === 0 ? (
            <div className="text-center py-8">
              <Award className="h-12 w-12 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No featured services found</p>
            </div>
          ) : (
            featuredServices.map((featured) => (
              <Card key={featured.id} className="border-l-4 border-l-blue-500">
                <CardContent className="p-4">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <Avatar className="w-12 h-12">
                        <AvatarImage src={featured.gig?.provider?.user?.avatar_url || ''} />
                        <AvatarFallback>
                          {getInitials(featured.gig?.provider?.user?.display_name || featured.gig?.provider?.name || 'U')}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="font-medium">
                            {featured.gig?.provider?.user?.display_name || featured.gig?.provider?.name}
                          </h4>
                          {getTypeBadge(featured.featured_type)}
                          {!featured.is_active && (
                            <Badge variant="secondary">Inactive</Badge>
                          )}
                        </div>
                        
                        <div className="flex items-center gap-2 text-sm text-muted-foreground">
                          <span>Priority: {featured.priority}</span>
                          <span>•</span>
                          <span>{formatDistanceToNow(new Date(featured.created_at), { addSuffix: true })}</span>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => toggleStatus.mutate({ 
                          featuredId: featured.id, 
                          isActive: featured.is_active 
                        })}
                        disabled={toggleStatus.isPending}
                      >
                        {featured.is_active ? 'Deactivate' : 'Activate'}
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeFeatured.mutate(featured.id)}
                        disabled={removeFeatured.isPending}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  </div>

                  <h5 className="font-medium mb-2">{featured.gig?.title}</h5>
                  <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                    {featured.gig?.description}
                  </p>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center gap-4">
                      <div className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400 text-yellow-400" />
                        <span>{featured.gig?.rating?.toFixed(1) || '0.0'}</span>
                        <span className="text-muted-foreground">
                          ({featured.gig?.total_orders || 0} orders)
                        </span>
                      </div>
                      
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4 text-muted-foreground" />
                        <span className="text-muted-foreground">
                          {featured.end_date 
                            ? `Until ${new Date(featured.end_date).toLocaleDateString()}`
                            : 'No end date'
                          }
                        </span>
                      </div>
                    </div>
                    
                    <div className="font-bold text-primary">
                      From ${featured.gig?.price_starting}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Placeholder for Add Featured Service Form
function AddFeaturedServiceForm({ onClose }: { onClose: () => void }) {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="gig-search">Search Service</Label>
        <Input
          id="gig-search"
          placeholder="Search for a service to feature..."
        />
      </div>
      
      <div>
        <Label htmlFor="featured-type">Featured Type</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Select type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="premium">Premium</SelectItem>
            <SelectItem value="sponsored">Sponsored</SelectItem>
            <SelectItem value="trending">Trending</SelectItem>
            <SelectItem value="editor_choice">Editor's Choice</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div>
        <Label htmlFor="priority">Priority (1-100)</Label>
        <Input
          id="priority"
          type="number"
          min="1"
          max="100"
          placeholder="50"
        />
      </div>
      
      <DialogFooter>
        <Button variant="outline" onClick={onClose}>
          Cancel
        </Button>
        <Button onClick={onClose}>
          Add Featured Service
        </Button>
      </DialogFooter>
    </div>
  );
}
