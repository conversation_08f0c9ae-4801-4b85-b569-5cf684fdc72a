
import React, { useState, useEffect, useRef } from 'react';
import { Activity } from '@/types/activity';
import { useAuth } from '@/hooks/use-auth';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Loader2, Send, Users, AlertCircle, ExternalLink, MessageSquare } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { useActivityGroupChat, GroupChatMessage } from '@/hooks/use-activity-group-chat';
import { getInitials } from '@/utils/get-initials';

interface ActivityGroupChatProps {
  activity: Activity;
  className?: string;
}

// Simplified chat message component to avoid dependencies
function SimpleChatMessage({ message }: { message: GroupChatMessage }) {
  const { user } = useAuth();
  const isCurrentUser = user?.id === message.sender_id;
  
  return (
    <div className={`flex gap-3 ${isCurrentUser ? 'flex-row-reverse' : ''}`}>
      <Avatar className="h-8 w-8">
        <AvatarImage src={message.sender?.avatar_url} />
        <AvatarFallback>
          {getInitials(message.sender?.display_name || message.sender?.username)}
        </AvatarFallback>
      </Avatar>
      <div className={`space-y-1 max-w-[75%]`}>
        {!isCurrentUser && (
          <p className="text-xs font-medium">
            {message.sender?.display_name || message.sender?.username || 'Unknown'}
          </p>
        )}
        <div className={`p-3 rounded-lg ${
          isCurrentUser 
            ? 'bg-primary text-primary-foreground ml-auto' 
            : 'bg-muted'
        }`}>
          <p className="text-sm whitespace-pre-wrap break-words">{message.content}</p>
        </div>
      </div>
    </div>
  );
}

export function ActivityGroupChat({ activity, className }: ActivityGroupChatProps) {
  const { user } = useAuth();
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const { toast } = useToast();

  // Use the extracted hook to handle complex chat logic
  const {
    conversation,
    isLoadingConversation,
    messages,
    isLoadingMessages,
    participants,
    isLoadingParticipants,
    sendMessage
  } = useActivityGroupChat({ activityId: activity.id });

  // Update refs to scroll to bottom when messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Simplified typing indicator functions
  const startTyping = () => {};
  const stopTyping = () => {};

  const handleSendMessage = (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim()) return;
    sendMessage.mutate(newMessage);
    setNewMessage('');
    stopTyping();
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(e.target.value);

    // Trigger typing indicator when user types
    if (e.target.value.trim() && conversation) {
      startTyping();
    }
  };

  const isHost = user?.id === activity.host_id;

  if (!user) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle>Group Chat</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center py-8">
            <AlertCircle className="h-10 w-10 text-muted-foreground mb-2" />
            <p className="text-muted-foreground">Please sign in to access the group chat</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isLoadingConversation) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Activity Chat
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex justify-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const handleOpenFullChat = () => {
    if (conversation) {
      navigate(`/chat?conversation=${conversation.id}`);
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Activity Chat
          </CardTitle>
          <div className="flex items-center gap-2">
            <div className="text-xs text-muted-foreground">
              {participants?.length || 0} participants
            </div>
            {conversation && (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 w-8 p-0"
                onClick={handleOpenFullChat}
                title="Open in full chat"
              >
                <ExternalLink className="h-4 w-4" />
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="p-0">
        <ScrollArea className="h-[350px] px-4">
          {isLoadingMessages ? (
            <div className="space-y-4 py-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="flex items-start gap-3">
                  <Skeleton className="h-8 w-8 rounded-full" />
                  <div className="space-y-2">
                    <Skeleton className="h-4 w-24" />
                    <Skeleton className="h-10 w-64" />
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="space-y-4 py-4">
              {messages && messages.length > 0 ? messages.map((message, index) => {
                if (message.is_system_message) {
                  return (
                    <div key={message.id} className="flex justify-center my-2">
                      <div className="bg-gray-100 text-gray-600 text-xs px-3 py-1 rounded-full">
                        {message.content}
                      </div>
                    </div>
                  );
                }
                
                return (
                  <SimpleChatMessage 
                    key={message.id} 
                    message={message}
                  />
                );
              }) : (
                <div className="flex flex-col items-center justify-center h-full">
                  <p className="text-muted-foreground">No messages yet</p>
                  <p className="text-xs text-muted-foreground mt-1">Be the first to send a message</p>
                </div>
              )}

              <div ref={messagesEndRef} />
            </div>
          )}
        </ScrollArea>
      </CardContent>

      <CardFooter className="p-4 pt-3 border-t flex flex-col gap-3">
        <form onSubmit={handleSendMessage} className="flex w-full gap-2">
          <Input
            placeholder="Type a message..."
            value={newMessage}
            onChange={handleInputChange}
            disabled={sendMessage.isPending}
            className="flex-1"
          />
          <Button type="submit" size="icon" disabled={sendMessage.isPending || !newMessage.trim()}>
            {sendMessage.isPending ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Send className="h-4 w-4" />
            )}
          </Button>
        </form>

        {conversation && (
          <Button
            variant="outline"
            size="sm"
            className="w-full"
            onClick={handleOpenFullChat}
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Open Full Chat
          </Button>
        )}
      </CardFooter>
    </Card>
  );
}
