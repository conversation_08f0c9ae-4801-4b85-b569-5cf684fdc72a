import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';

export interface SavedLocation {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  location: { x: number; y: number };
  created_at: string;
  updated_at: string;
  category?: string;
  tags?: string[];
  metadata?: any;
  is_shared?: boolean;
  shared_with?: string[];
}

export function useSavedLocations() {
  const [savedLocations, setSavedLocations] = useState<SavedLocation[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();
  
  const fetchSavedLocations = async () => {
    if (!user) {
      setSavedLocations([]);
      setIsLoading(false);
      return;
    }
    
    try {
      setIsLoading(true);
      setError(null);
      
      const { data, error: fetchError } = await supabase
        .from('saved_locations')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });
      
      if (fetchError) throw fetchError;
      setSavedLocations(data as SavedLocation[]);
      
    } catch (err: any) {
      console.error('Error fetching saved locations:', err);
      setError(err);
    } finally {
      setIsLoading(false);
    }
  };
  
  const saveLocation = async (locationData: Omit<SavedLocation, 'id' | 'user_id' | 'created_at' | 'updated_at'>) => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'You need to be logged in to save locations',
        variant: 'destructive'
      });
      return null;
    }
    
    try {
      const { data, error: saveError } = await supabase
        .from('saved_locations')
        .insert({
          ...locationData,
          user_id: user.id
        })
        .select()
        .single();
      
      if (saveError) throw saveError;
      
      setSavedLocations(prev => [data as SavedLocation, ...prev]);
      
      toast({
        title: 'Location saved',
        description: `"${locationData.name}" has been added to your saved locations`
      });
      
      return data as SavedLocation;
    } catch (err: any) {
      console.error('Error saving location:', err);
      toast({
        title: 'Failed to save location',
        description: err.message || 'An error occurred while saving the location',
        variant: 'destructive'
      });
      return null;
    }
  };
  
  const deleteLocation = async (locationId: string) => {
    if (!user) return false;
    
    try {
      const { error: deleteError } = await supabase
        .from('saved_locations')
        .delete()
        .eq('id', locationId)
        .eq('user_id', user.id);
      
      if (deleteError) throw deleteError;
      
      setSavedLocations(prev => prev.filter(loc => loc.id !== locationId));
      
      toast({
        title: 'Location deleted',
        description: 'The saved location has been removed'
      });
      
      return true;
    } catch (err: any) {
      console.error('Error deleting location:', err);
      toast({
        title: 'Failed to delete location',
        description: err.message || 'An error occurred while deleting the location',
        variant: 'destructive'
      });
      return false;
    }
  };

  const updateLocation = async (locationId: string, locationData: Partial<Omit<SavedLocation, 'id' | 'user_id' | 'created_at' | 'updated_at'>>) => {
    if (!user) {
      toast({
        title: 'Authentication required',
        description: 'You need to be logged in to update locations',
        variant: 'destructive'
      });
      return null;
    }
    
    try {
      const { data, error: updateError } = await supabase
        .from('saved_locations')
        .update(locationData)
        .eq('id', locationId)
        .eq('user_id', user.id)
        .select()
        .single();
      
      if (updateError) throw updateError;
      
      setSavedLocations(prev => prev.map(loc => 
        loc.id === locationId ? { ...loc, ...data } as SavedLocation : loc
      ));
      
      toast({
        title: 'Location updated',
        description: `"${locationData.name || 'Location'}" has been updated`
      });
      
      return data as SavedLocation;
    } catch (err: any) {
      console.error('Error updating location:', err);
      toast({
        title: 'Failed to update location',
        description: err.message || 'An error occurred while updating the location',
        variant: 'destructive'
      });
      return null;
    }
  };

  useEffect(() => {
    fetchSavedLocations();
  }, [user?.id]);
  
  return {
    savedLocations,
    isLoading,
    error,
    saveLocation,
    deleteLocation,
    updateLocation,
    refreshLocations: fetchSavedLocations
  };
}
