
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FormControl } from "@/components/ui/form";
import { Eye, EyeOff, Globe } from 'lucide-react';

interface VisibilitySelectProps {
  field: any;
}

export function VisibilitySelect({ field }: VisibilitySelectProps) {
  return (
    <Select onValueChange={field.onChange} defaultValue={field.value}>
      <FormControl>
        <SelectTrigger>
          <SelectValue placeholder="Select visibility" />
        </SelectTrigger>
      </FormControl>
      <SelectContent>
        <SelectItem value="public" className="flex items-center">
          <div className="flex items-center">
            <Globe className="h-4 w-4 mr-2" />
            <span>Public</span>
          </div>
        </SelectItem>
        <SelectItem value="private">
          <div className="flex items-center">
            <EyeOff className="h-4 w-4 mr-2" />
            <span>Private</span>
          </div>
        </SelectItem>
        <SelectItem value="unlisted">
          <div className="flex items-center">
            <Eye className="h-4 w-4 mr-2" />
            <span>Unlisted</span>
          </div>
        </SelectItem>
      </SelectContent>
    </Select>
  );
}
