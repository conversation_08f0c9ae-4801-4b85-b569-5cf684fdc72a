import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

export interface ServiceBooking {
  id: string;
  gig_id?: string;
  package_id?: string;
  client_id: string;
  provider_id: string;
  status: 'pending' | 'accepted' | 'in_progress' | 'completed' | 'cancelled' | 'disputed';
  title: string;
  description?: string;
  price: number;
  delivery_date?: string;
  scheduled_for?: string;
  payment_status?: string;
  payment_id?: string;
  conversation_id?: string;
  created_at: string;
  updated_at: string;
  // Joined data
  gig?: {
    id: string;
    title: string;
    description: string;
  };
  client?: {
    id: string;
    display_name: string;
    avatar_url: string;
    username: string;
  };
  provider?: {
    id: string;
    name: string;
    avatar_url: string;
  };
}

export interface CreateBookingData {
  gig_id?: string;
  package_id?: string;
  provider_id: string;
  title: string;
  description?: string;
  price: number;
  delivery_date?: string;
  scheduled_for?: string;
}

// Hook to create a new service booking
export function useCreateBooking() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async (bookingData: CreateBookingData) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('service_bookings')
        .insert({
          ...bookingData,
          client_id: user.id,
          status: 'pending',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select(`
          *,
          gig:gigs(id, title, description),
          provider:service_providers(id, name, avatar_url)
        `)
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['service-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['user-bookings'] });
      toast({
        title: 'Booking Created',
        description: 'Your service booking request has been sent to the provider.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Booking Failed',
        description: error.message || 'Failed to create booking',
        variant: 'destructive'
      });
    }
  });
}

// Hook to fetch user's bookings (as client)
export function useUserBookings() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['user-bookings', user?.id],
    queryFn: async (): Promise<ServiceBooking[]> => {
      if (!user) return [];

      const { data, error } = await supabase
        .from('service_bookings')
        .select(`
          *,
          gig:gigs(id, title, description),
          provider:service_providers(id, name, avatar_url)
        `)
        .eq('client_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    enabled: !!user
  });
}

// Hook to fetch provider's bookings
export function useProviderBookings() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['provider-bookings', user?.id],
    queryFn: async (): Promise<ServiceBooking[]> => {
      if (!user) return [];

      // First get the provider ID for this user
      const { data: provider, error: providerError } = await supabase
        .from('service_providers')
        .select('id')
        .eq('user_id', user.id)
        .single();

      if (providerError || !provider) return [];

      const { data, error } = await supabase
        .from('service_bookings')
        .select(`
          *,
          gig:gigs(id, title, description),
          client:profiles!service_bookings_client_id_fkey(id, display_name, avatar_url, username)
        `)
        .eq('provider_id', provider.id)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data || [];
    },
    enabled: !!user
  });
}

// Hook to fetch a single booking
export function useBooking(bookingId: string) {
  return useQuery({
    queryKey: ['service-booking', bookingId],
    queryFn: async (): Promise<ServiceBooking | null> => {
      if (!bookingId) return null;

      const { data, error } = await supabase
        .from('service_bookings')
        .select(`
          *,
          gig:gigs(id, title, description),
          client:profiles!service_bookings_client_id_fkey(id, display_name, avatar_url, username),
          provider:service_providers(id, name, avatar_url)
        `)
        .eq('id', bookingId)
        .single();

      if (error) throw error;
      return data;
    },
    enabled: !!bookingId
  });
}

// Hook to update booking status
export function useUpdateBookingStatus() {
  const { user } = useAuth();
  const queryClient = useQueryClient();
  const { toast } = useToast();

  return useMutation({
    mutationFn: async ({ 
      bookingId, 
      status, 
      isProvider = false 
    }: { 
      bookingId: string; 
      status: ServiceBooking['status'];
      isProvider?: boolean;
    }) => {
      if (!user) throw new Error('User not authenticated');

      const { data, error } = await supabase
        .from('service_bookings')
        .update({
          status,
          updated_at: new Date().toISOString()
        })
        .eq('id', bookingId)
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ['service-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['user-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['provider-bookings'] });
      queryClient.invalidateQueries({ queryKey: ['service-booking', variables.bookingId] });
      
      const statusMessages = {
        accepted: 'Booking accepted successfully',
        in_progress: 'Booking marked as in progress',
        completed: 'Booking completed successfully',
        cancelled: 'Booking cancelled',
        disputed: 'Booking marked as disputed'
      };

      toast({
        title: 'Status Updated',
        description: statusMessages[variables.status] || 'Booking status updated',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Update Failed',
        description: error.message || 'Failed to update booking status',
        variant: 'destructive'
      });
    }
  });
}

// Hook to get booking statistics
export function useBookingStats() {
  const { user } = useAuth();

  return useQuery({
    queryKey: ['booking-stats', user?.id],
    queryFn: async () => {
      if (!user) return null;

      // Get stats as client
      const { data: clientStats, error: clientError } = await supabase
        .from('service_bookings')
        .select('status')
        .eq('client_id', user.id);

      if (clientError) throw clientError;

      // Get stats as provider
      const { data: provider } = await supabase
        .from('service_providers')
        .select('id')
        .eq('user_id', user.id)
        .single();

      let providerStats = [];
      if (provider) {
        const { data, error } = await supabase
          .from('service_bookings')
          .select('status')
          .eq('provider_id', provider.id);

        if (!error) providerStats = data || [];
      }

      return {
        asClient: {
          total: clientStats?.length || 0,
          pending: clientStats?.filter(b => b.status === 'pending').length || 0,
          accepted: clientStats?.filter(b => b.status === 'accepted').length || 0,
          completed: clientStats?.filter(b => b.status === 'completed').length || 0,
          cancelled: clientStats?.filter(b => b.status === 'cancelled').length || 0,
        },
        asProvider: {
          total: providerStats.length || 0,
          pending: providerStats.filter(b => b.status === 'pending').length || 0,
          accepted: providerStats.filter(b => b.status === 'accepted').length || 0,
          completed: providerStats.filter(b => b.status === 'completed').length || 0,
          cancelled: providerStats.filter(b => b.status === 'cancelled').length || 0,
        }
      };
    },
    enabled: !!user
  });
}
