import React, { useState } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/hooks/use-auth";
import { OnboardingModal } from "@/components/onboarding/OnboardingModal";
import { UserPlus, RefreshCw } from "lucide-react";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";

export default function SettingsPage() {
  const { user, profile, refreshProfile } = useAuth();
  const { toast } = useToast();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [forceFullOnboarding, setForceFullOnboarding] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);

  // Handler for profile privacy toggle
  const handleProfilePrivacyChange = async (isPublic: boolean) => {
    if (!user) return;

    setIsUpdating(true);

    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          is_profile_public: isPublic,
          updated_at: new Date().toISOString()
        })
        .eq('id', user.id);

      if (error) throw error;

      // Refresh profile data
      await refreshProfile();

      toast({
        title: "Settings updated",
        description: `Your profile is now ${isPublic ? 'public' : 'private'}.`,
      });
    } catch (error) {
      console.error('Error updating profile privacy:', error);
      toast({
        title: "Error",
        description: "Failed to update profile privacy settings.",
        variant: "destructive",
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle opening the onboarding modal from Settings
  const handleStartOnboarding = () => {
    // Clear the "just signed up" flag if it exists
    localStorage.removeItem('just_signed_up');
    setShowOnboarding(true);
  };

  // Track profile completion status
  const isProfileIncomplete = profile && (!profile.display_name || !profile.gender || !profile.birthday);

  // Calculate profile completion percentage
  const getProfileCompletionPercentage = () => {
    if (!profile) return 0;

    // If onboarding is explicitly marked as completed, return 100%
    if (profile.onboarding_completed === true) {
      return 100;
    }

    const fields = [
      !!profile.display_name,
      !!profile.username,
      !!profile.bio,
      !!profile.avatar_url,
      !!profile.birthday,
      !!profile.gender,
      !!profile.default_location,
      !!profile.location_permission_granted,
      !!profile.notifications_enabled,
      Array.isArray(profile.purposes) && profile.purposes.length > 0,
      Array.isArray(profile.vibes) && profile.vibes.length > 0,
      Array.isArray(profile.gallery) && profile.gallery.length > 0
    ];

    const completedFields = fields.filter(Boolean).length;
    return Math.round((completedFields / fields.length) * 100);
  };

  const completionPercentage = getProfileCompletionPercentage();

  return (
    <MainLayout title="Settings">
      <div className="space-y-6 max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>Profile Settings</CardTitle>
            <CardDescription>
              Manage your profile information and setup
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-medium">Complete Profile Setup</h3>
                <p className="text-sm text-muted-foreground">
                  Go through the complete onboarding process to update your profile
                </p>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="h-2 rounded-full bg-primary-purple"
                    style={{ width: `${completionPercentage}%` }}
                  />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {completionPercentage}% Complete
                </p>
              </div>
              <Button
                onClick={handleStartOnboarding}
                className="bg-primary-purple hover:bg-primary-deep-purple flex items-center gap-1.5"
              >
                <UserPlus className="h-4 w-4" />
                {isProfileIncomplete ? "Complete Profile" : "Update Profile"}
              </Button>
            </div>

            <Separator />

            <div className="flex justify-between items-center">
              <div>
                <h3 className="font-medium">Reset Onboarding Progress</h3>
                <p className="text-sm text-muted-foreground">
                  Start the onboarding process from the beginning
                </p>
              </div>
              <Button
                onClick={() => {
                  setForceFullOnboarding(true);
                  setShowOnboarding(true);
                }}
                variant="outline"
                className="flex items-center gap-1.5"
              >
                <RefreshCw className="h-4 w-4" />
                Restart Onboarding
              </Button>
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">Privacy Settings</h3>
                <p className="text-sm text-muted-foreground">
                  Control who can view your profile
                </p>
              </div>
            </div>

            <div className="flex items-center justify-between pt-2">
              <div>
                <Label htmlFor="public-profile" className="font-medium">Public Profile</Label>
                <p className="text-sm text-muted-foreground">
                  Allow anyone to view your profile without logging in
                </p>
              </div>
              <Switch
                id="public-profile"
                checked={profile?.is_profile_public !== false}
                onCheckedChange={handleProfilePrivacyChange}
                disabled={isUpdating}
              />
            </div>

            <Separator />

            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-medium">More Settings</h3>
                <p className="text-sm text-muted-foreground">
                  Additional settings coming soon
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {user && (
        <OnboardingModal
          open={showOnboarding}
          onOpenChange={setShowOnboarding}
          user={user}
          profile={profile}
          initialStep={forceFullOnboarding ? 1 : undefined}
        />
      )}
    </MainLayout>
  );
}
