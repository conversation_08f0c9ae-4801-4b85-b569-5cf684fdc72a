
import { supabase } from "@/integrations/supabase/client";
import { useQuery } from "@tanstack/react-query";
import { useEffect, useState } from "react";
import { useToast } from "@/hooks/use-toast";

export interface UserLocation {
  id: string;
  user_id: string;
  bio: string | null;
  location: { x: number; y: number };
  created_at: string;
  updated_at: string;
}

export const useUserLocations = () => {
  const [locations, setLocations] = useState<UserLocation[]>([]);
  const { toast } = useToast();

  // Initial fetch
  const { data: initialData, isLoading, error } = useQuery({
    queryKey: ["userLocations"],
    queryFn: async (): Promise<UserLocation[]> => {
      const { data, error } = await supabase
        .from("user_locations")
        .select("*");

      if (error) throw error;
      
      return data?.map(item => ({
        ...item,
        location: {
          x: typeof item.location === 'object' && item.location !== null ? Number((item.location as any).x || 0) : 0,
          y: typeof item.location === 'object' && item.location !== null ? Number((item.location as any).y || 0) : 0
        }
      })) || [];
    },
  });

  // Set initial data
  useEffect(() => {
    if (initialData) {
      setLocations(initialData);
    }
  }, [initialData]);

  // Subscribe to realtime updates
  useEffect(() => {
    console.log('Setting up realtime subscription for user_locations');
    
    const channel = supabase
      .channel('user_locations_changes')
      .on(
        'postgres_changes',
        { 
          event: '*',
          schema: 'public',
          table: 'user_locations'
        },
        (payload) => {
          console.log('Received realtime update:', payload);

          const formatLocation = (loc: any) => ({
            x: typeof loc === 'object' && loc !== null ? Number(loc.x || 0) : 0,
            y: typeof loc === 'object' && loc !== null ? Number(loc.y || 0) : 0
          });

          if (payload.eventType === 'INSERT') {
            const newLocation: UserLocation = {
              id: payload.new.id,
              user_id: payload.new.user_id,
              bio: payload.new.bio,
              location: formatLocation(payload.new.location),
              created_at: payload.new.created_at,
              updated_at: payload.new.updated_at
            };
            setLocations(prev => [...prev, newLocation]);
            
            toast({
              title: "New Location",
              description: "A new user location has been added to the map",
            });
          }
          
          if (payload.eventType === 'UPDATE') {
            const updatedLocation: UserLocation = {
              id: payload.new.id,
              user_id: payload.new.user_id,
              bio: payload.new.bio,
              location: formatLocation(payload.new.location),
              created_at: payload.new.created_at,
              updated_at: payload.new.updated_at
            };
            setLocations(prev => 
              prev.map(loc => 
                loc.id === updatedLocation.id ? updatedLocation : loc
              )
            );
          }
          
          if (payload.eventType === 'DELETE') {
            setLocations(prev => 
              prev.filter(loc => loc.id !== payload.old.id)
            );
          }
        }
      )
      .subscribe();

    return () => {
      console.log('Cleaning up realtime subscription');
      supabase.removeChannel(channel);
    };
  }, [toast]);

  return {
    data: locations,
    isLoading,
    error,
  };
};
