
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useState } from 'react';
import { LocationMessage } from '@/types/location';
import { useAuth } from './use-auth';
import { useProfile } from './use-profile';
import { supabase } from '@/integrations/supabase/client';
import { createMissingTables } from '@/utils/create-missing-tables';

export function useLocationMessages(conversationId?: string) {
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { data: profile } = useProfile(user?.id);
  const [newLocation, setNewLocation] = useState<{ lat: number; lng: number; label?: string } | null>(null);
  const [expiration, setExpiration] = useState<Date | null>(null);

  // Ensure the location_messages table exists
  const ensureTableExists = async () => {
    try {
      await createMissingTables();
    } catch (error) {
      console.error("Error ensuring location_messages table exists:", error);
    }
  };

  const locationMessagesQuery = useQuery({
    queryKey: ['location-messages', conversationId],
    queryFn: async (): Promise<LocationMessage[]> => {
      if (!conversationId) return [];

      try {
        // Make sure table exists before querying
        await ensureTableExists();

        // Use raw SQL query to work around type issues
        const { data, error } = await supabase.rpc('exec_sql', {
          sql: `
            SELECT * FROM public.location_messages 
            WHERE conversation_id = '${conversationId}'
            ORDER BY created_at DESC
          `
        });

        if (error) {
          console.error('Error fetching location messages:', error);
          return [];
        }

        // Parse and convert to LocationMessage type
        // Explicitly check if data is an array and cast it to any[] before mapping
        if (data && Array.isArray(data)) {
          const dataArray = data as any[];
          return dataArray.map((item) => ({
            id: item.id,
            conversation_id: item.conversation_id,
            sender_id: item.sender_id,
            sender_name: item.sender_name || '',
            sender_avatar_url: item.sender_avatar_url,
            lat: parseFloat(item.lat),
            lng: parseFloat(item.lng),
            label: item.label || '',
            expiration: item.expiration,
            is_expired: item.is_expired || false,
            created_at: item.created_at
          }));
        }

        return [];
      } catch (error) {
        console.error('Error in location messages query:', error);
        return [];
      }
    },
    enabled: !!conversationId
  });

  // Mark location as expired
  const markLocationAsExpired = useMutation({
    mutationFn: async (locationId: string) => {
      if (!locationId) {
        throw new Error('Missing location ID');
      }

      // Ensure table exists
      await ensureTableExists();

      // Use raw SQL to update the location
      const { error } = await supabase.rpc('exec_sql', {
        sql: `
          UPDATE public.location_messages 
          SET is_expired = true 
          WHERE id = '${locationId}' AND sender_id = '${user?.id}'
        `
      });

      if (error) {
        console.error('Error marking location as expired:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['location-messages', conversationId] });
    }
  });

  // Insert a new location message
  const insertLocation = useMutation({
    mutationFn: async () => {
      if (!user || !newLocation || !expiration || !conversationId) {
        throw new Error('Missing required data');
      }

      // Ensure table exists
      await ensureTableExists();

      const locationMessageData = {
        conversation_id: conversationId,
        sender_id: user.id,
        sender_name: profile?.display_name || profile?.username || 'User',
        sender_avatar_url: profile?.avatar_url,
        lat: newLocation.lat,
        lng: newLocation.lng,
        label: newLocation.label || 'My location',
        expiration: expiration.toISOString(),
        is_expired: false
      };

      // Use raw SQL to insert the location
      const { data, error } = await supabase.rpc('exec_sql', {
        sql: `
          INSERT INTO public.location_messages (
            conversation_id, sender_id, sender_name, sender_avatar_url,
            lat, lng, label, expiration, is_expired
          )
          VALUES (
            '${locationMessageData.conversation_id}',
            '${locationMessageData.sender_id}',
            '${locationMessageData.sender_name}',
            '${locationMessageData.sender_avatar_url || ''}',
            ${locationMessageData.lat},
            ${locationMessageData.lng},
            '${locationMessageData.label}',
            '${locationMessageData.expiration}',
            ${locationMessageData.is_expired}
          )
          RETURNING *
        `
      });

      if (error) {
        console.error('Error inserting location message:', error);
        throw error;
      }

      // Handle the case when data is null or empty
      if (!data) {
        throw new Error('No data returned from insert operation');
      }
      
      // Check if data is an array with elements and properly type assert
      // Cast data to any[] first to ensure we can safely access the first item
      const dataArray = Array.isArray(data) ? data as any[] : [];
      const insertedData = dataArray.length > 0 ? dataArray[0] : null;
      
      if (!insertedData) {
        throw new Error('Failed to retrieve inserted location data');
      }
      
      return {
        id: insertedData.id,
        conversation_id: insertedData.conversation_id,
        sender_id: insertedData.sender_id,
        sender_name: insertedData.sender_name || '',
        sender_avatar_url: insertedData.sender_avatar_url,
        lat: parseFloat(insertedData.lat),
        lng: parseFloat(insertedData.lng),
        label: insertedData.label || '',
        expiration: insertedData.expiration,
        is_expired: insertedData.is_expired || false,
        created_at: insertedData.created_at
      } as LocationMessage;
    },
    onSuccess: () => {
      // Reset the state
      setNewLocation(null);
      setExpiration(null);

      // Update the query cache
      queryClient.invalidateQueries({ queryKey: ['location-messages', conversationId] });
    }
  });

  return {
    locationMessages: locationMessagesQuery.data || [],
    data: locationMessagesQuery.data || [],
    isLoading: locationMessagesQuery.isLoading,
    insertLocation,
    setNewLocation,
    setExpiration,
    markLocationAsExpired
  };
}
