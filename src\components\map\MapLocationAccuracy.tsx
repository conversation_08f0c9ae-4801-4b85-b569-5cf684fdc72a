
import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { Signal, SignalHigh, SignalLow, SignalMedium } from 'lucide-react';
import { cn } from '@/lib/utils';

interface MapLocationAccuracyProps {
  accuracy: number | null;
  className?: string;
}

export function MapLocationAccuracy({ accuracy, className }: MapLocationAccuracyProps) {
  if (!accuracy) return null;

  let accuracyLevel: 'high' | 'medium' | 'low' = 'low';
  let AccuracyIcon = SignalLow;
  let description = '';
  let color = 'text-red-500';

  if (accuracy <= 10) {
    accuracyLevel = 'high';
    AccuracyIcon = SignalHigh;
    description = 'High accuracy (within 10 meters)';
    color = 'text-green-500';
  } else if (accuracy <= 50) {
    accuracyLevel = 'medium';
    AccuracyIcon = SignalMedium;
    description = 'Medium accuracy (within 50 meters)';
    color = 'text-yellow-500';
  } else {
    accuracyLevel = 'low';
    AccuracyIcon = Signal;
    description = `Low accuracy (within ${Math.round(accuracy)} meters)`;
    color = 'text-red-500';
  }

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <Badge 
            variant="outline" 
            className={cn(
              "flex items-center gap-1.5 px-3 py-1.5 shadow-lg",
              color,
              className
            )}
          >
            <AccuracyIcon className="h-3.5 w-3.5" />
            <span className="font-medium">GPS {accuracyLevel}</span>
          </Badge>
        </TooltipTrigger>
        <TooltipContent>
          <p>{description}</p>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  );
}
