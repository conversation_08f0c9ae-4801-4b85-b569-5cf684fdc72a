
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Send, Image, MapPin, Smile } from 'lucide-react';
import { SendMessageInput } from '@/hooks/use-chat-messages';
import { useTypingIndicator } from '@/hooks/use-typing-indicator';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import EmojiPicker from 'emoji-picker-react';

interface ChatInputProps {
  onSendMessage: (input: SendMessageInput) => Promise<any>;
  conversationId?: string;
  activityId?: string;
  isNewActivityChat?: boolean;
}

export function ChatInput({ onSendMessage, conversationId, activityId, isNewActivityChat }: ChatInputProps) {
  const [messageText, setMessageText] = useState('');
  const [isEmojiPickerOpen, setIsEmojiPickerOpen] = useState(false);
  const { startTyping, stopTyping } = conversationId ? useTypingIndicator(conversationId) : { startTyping: () => {}, stopTyping: () => {} };

  // Handle typing indicator
  useEffect(() => {
    if (messageText.trim() && conversationId) {
      startTyping();
    }

    return () => {
      if (conversationId) {
        stopTyping();
      }
    };
  }, [conversationId, startTyping, stopTyping]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (messageText.trim() && conversationId) {
      onSendMessage({
        content: messageText,
        conversationId
      }).catch(error => console.error('Failed to send message:', error));

      setMessageText('');
      stopTyping();
    }
  };

  const handleEmojiSelect = (emojiData: any) => {
    setMessageText(prev => prev + emojiData.emoji);
    setIsEmojiPickerOpen(false);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMessageText(e.target.value);

    // Trigger typing indicator when user types
    if (e.target.value.trim() && conversationId) {
      startTyping();
    }
  };

  return (
    <form onSubmit={handleSubmit} className="border-t p-4">
      <div className="flex items-center gap-2">
        {/* Media upload button (placeholder) */}
        <Button
          type="button"
          size="icon"
          variant="ghost"
          className="text-muted-foreground"
          onClick={() => alert('Media upload not implemented yet')}
        >
          <Image className="h-5 w-5" />
        </Button>

        {/* Location share button (placeholder) */}
        <Button
          type="button"
          size="icon"
          variant="ghost"
          className="text-muted-foreground"
          onClick={() => alert('Location sharing not implemented yet')}
        >
          <MapPin className="h-5 w-5" />
        </Button>

        {/* Emoji picker */}
        <Popover open={isEmojiPickerOpen} onOpenChange={setIsEmojiPickerOpen}>
          <PopoverTrigger asChild>
            <Button
              type="button"
              size="icon"
              variant="ghost"
              className="text-muted-foreground"
            >
              <Smile className="h-5 w-5" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-full p-0 border-none" align="start">
            <EmojiPicker onEmojiClick={handleEmojiSelect} />
          </PopoverContent>
        </Popover>

        {/* Message input */}
        <Input
          value={messageText}
          onChange={handleInputChange}
          placeholder="Type your message..."
          className="flex-1"
        />

        {/* Send button */}
        <Button type="submit" size="icon">
          <Send className="h-4 w-4" />
        </Button>
      </div>
    </form>
  );
}
