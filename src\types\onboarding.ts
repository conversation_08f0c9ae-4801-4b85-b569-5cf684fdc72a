
import { z } from "zod";

// Define the purpose options
export const purposeOptions = [
  { id: "make_friends", label: "Make Friends", icon: "💬" },
  { id: "talk", label: "Talk", icon: "🎤" },
  { id: "find_love", label: "Find Love", icon: "❤️" },
  { id: "meet_nearby", label: "Meet Nearby", icon: "📍" },
  { id: "hangout", label: "Hangout", icon: "☕" },
  { id: "earn_money", label: "Earn Money", icon: "💰" }
];

// Define vibe categories with their options
export const vibeCategories = {
  romantic: [
    { id: "sunset_lover", label: "Sunset Lover", icon: "🌅" },
    { id: "kisses", label: "Kisses", icon: "💋" },
    { id: "cuddling_movie", label: "Cuddling & Movie", icon: "🎬" },
    { id: "virtual_romantic", label: "Virtual Romantic", icon: "💻" },
    { id: "breakfast_in_bed", label: "Breakfast in Bed", icon: "🍳" },
    { id: "watching_sunset", label: "Watching Sunset", icon: "🌇" },
    { id: "traveling_together", label: "Traveling Together", icon: "✈️" },
    { id: "words_of_affirmation", label: "Words of Affirmation", icon: "💬" },
    { id: "receiving_gifts", label: "Receiving Gifts", icon: "🎁" },
    { id: "physical_touch", label: "Physical Touch", icon: "👐" },
    { id: "acts_of_service", label: "Acts of Service", icon: "🛠️" },
    { id: "date_nights", label: "Date Nights", icon: "🍷" },
    { id: "flowers", label: "Flowers", icon: "💐" },
    { id: "music_exchanging", label: "Music Exchanging", icon: "🎵" },
    { id: "reading_together", label: "Reading Together", icon: "📚" },
    { id: "cooking_together", label: "Cooking Together", icon: "👨‍🍳" },
    { id: "love_letters", label: "Love Letters", icon: "💌" },
    { id: "playful", label: "Playful", icon: "🎮" },
    { id: "beach_vacations", label: "Beach Vacations", icon: "🏖️" },
    { id: "cruise_vacations", label: "Cruise Vacations", icon: "🚢" },
    { id: "long_walks", label: "Long Walks", icon: "🚶" },
    { id: "theater_together", label: "Theater Together", icon: "🎭" },
    { id: "lunapark_dates", label: "Lunapark Dates", icon: "🎡" },
    { id: "quality_time", label: "Quality Time", icon: "⏱️" },
    { id: "stargazing_together", label: "Stargazing Together", icon: "🌠" },
    { id: "bath_for_two", label: "Bath for Two", icon: "🛁" }
  ],
  sexuality: [
    { id: "full_of_love", label: "Full of love", icon: "🔥" },
    { id: "its_complicated", label: "It's Complicated...", icon: "❤️" },
    { id: "heterosexual", label: "Heterosexual", icon: "👫" },
    { id: "homosexual", label: "Homosexual", icon: "🏳️‍🌈" },
    { id: "demisexual", label: "Demisexual", icon: "🤍" },
    { id: "pansexual", label: "Pansexual", icon: "🌍" },
    { id: "asexual", label: "Asexual", icon: "⭕" },
    { id: "single", label: "Single & Fabulous", icon: "🤩" },
    { id: "sapiosexual", label: "Sapiosexual", icon: "🧠" },
    { id: "open_minded", label: "Open-minded", icon: "😌" },
    { id: "wild", label: "Wild", icon: "😈" }
  ],
  money: [
    { id: "money_oriented", label: "Money Oriented", icon: "💰" },
    { id: "extravagant", label: "Extravagant", icon: "🤑" },
    { id: "generous", label: "Generous", icon: "🌟" },
    { id: "simple_living", label: "Simple Living", icon: "🌿" },
    { id: "spiritual", label: "Spiritual", icon: "🙏" },
    { id: "frugal", label: "Frugal", icon: "💵" },
    { id: "supportive", label: "Supportive", icon: "🤝" },
    { id: "growth_focused", label: "Growth-Focused", icon: "🚀" }
  ],
  politic: [
    { id: "civic_minded", label: "Civic-Minded", icon: "🗳️" },
    { id: "collaborative", label: "Collaborative", icon: "🤝" },
    { id: "activist", label: "Activist", icon: "💪" },
    { id: "analytical", label: "Analytical", icon: "🤔" },
    { id: "radical", label: "Radical", icon: "😎" },
    { id: "green_politics", label: "Green Politics", icon: "🌿" },
    { id: "internationalist", label: "Internationalist", icon: "🌍" },
    { id: "patriot", label: "Patriot", icon: "✊" },
    { id: "centrist", label: "Centrist", icon: "🎯" }
  ],
  tastes: [
    { id: "foodie", label: "Foodie", icon: "🍽️" },
    { id: "music_lover", label: "Music Lover", icon: "🎵" },
    { id: "art_enthusiast", label: "Art Enthusiast", icon: "🎨" },
    { id: "bookworm", label: "Bookworm", icon: "📚" },
    { id: "movie_buff", label: "Movie Buff", icon: "🎬" }
  ],
  food: [
    { id: "vegan", label: "Vegan", icon: "🥗" },
    { id: "vegetarian", label: "Vegetarian", icon: "🥦" },
    { id: "meat_lover", label: "Meat Lover", icon: "🥩" },
    { id: "sweet_tooth", label: "Sweet Tooth", icon: "🍰" },
    { id: "spicy_food", label: "Spicy Food", icon: "🌶️" }
  ]
};

// Flatten all vibes for easier validation
export const allVibes = Object.values(vibeCategories).flat();

// Create a gender enum
export const Gender = z.enum(["man", "woman", "non_binary", "prefer_not_to_say"]);
export type Gender = z.infer<typeof Gender>;

// Define the favorite location type
export interface FavoriteLocation {
  name: string;
  location: {
    x: number;
    y: number;
  };
  availability?: string;
}

// Create the OnboardingSchema with all the new fields
export const OnboardingSchema = z.object({
  display_name: z.string().min(2, "Name must be at least 2 characters").max(50),
  username: z.string().min(3, "Username must be at least 3 characters").max(30).optional(), // Optional as it's collected at signup
  bio: z.string().max(500, "Bio must be 500 characters or less").optional(),
  avatar_url: z.string({
    required_error: "Profile picture is required"
  }), // Required in onboarding
  birthday: z.date({
    required_error: "Please select your birthday",
    invalid_type_error: "That's not a valid date",
  }).refine(
    (date) => {
      if (!date) return false;

      // Calculate age
      const today = new Date();
      let age = today.getFullYear() - date.getFullYear();
      const monthDiff = today.getMonth() - date.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < date.getDate())) {
        age--;
      }

      // Must be at least 13 years old
      return age >= 13;
    },
    {
      message: "You must be at least 13 years old to use BuddySurf",
    }
  ),
  gender: Gender,
  purposes: z.array(z.string()).min(1, "Select at least one purpose").max(6),
  vibes: z.array(z.string()).min(1, "Select at least 1 vibe").max(5, "You can select up to 5 vibes"),
  gallery: z.array(z.string()).max(6, "You can upload up to 6 images").optional(),
  favorite_locations: z.array(
    z.object({
      name: z.string(),
      location: z.object({
        x: z.number(),
        y: z.number()
      }),
      availability: z.string().optional()
    })
  ).optional(),
  // Default location from browser geolocation (required)
  defaultLocation: z.object({
    x: z.number(),
    y: z.number()
  }, {
    required_error: "Default location is required. Please allow location access or select a location manually."
  }),
  // Location string (city, country)
  location: z.string().max(100).optional(),
  // Permission flags (required)
  locationPermissionGranted: z.boolean({
    required_error: "Location permission is required"
  }).refine(val => val === true, {
    message: "Location permission must be granted"
  }),
  notificationsEnabled: z.boolean().default(false),
  // Notification preferences
  notifyMessages: z.boolean().default(true),
  notifyActivities: z.boolean().default(true),
  notifyFollows: z.boolean().default(true),
  // Onboarding completion flag (standard name)
  onboarding_completed: z.boolean().default(false)
  // Remove onboardingCompleted as it doesn't exist in the database
});

export type OnboardingFormValues = z.infer<typeof OnboardingSchema>;
