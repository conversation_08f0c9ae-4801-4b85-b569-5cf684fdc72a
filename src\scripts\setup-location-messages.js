// This script sets up the database tables needed for location sharing and activity chat integration
// Run with: node src/scripts/setup-location-messages.js

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Initialize Supabase client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_SERVICE_KEY; // Use service key for admin operations

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase credentials. Please check your .env file.');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupLocationMessages() {
  try {
    console.log('Setting up location_messages table...');
    
    // Check if the table already exists
    const { error: checkError } = await supabase
      .from('location_messages')
      .select('id')
      .limit(1);
    
    if (!checkError) {
      console.log('location_messages table already exists.');
    } else {
      console.log('Creating location_messages table...');
      
      // Create the location_messages table
      const { error: createError } = await supabase.rpc('create_location_messages_table');
      
      if (createError) {
        // If the RPC function doesn't exist, create the table directly
        const { error: sqlError } = await supabase.sql(`
          CREATE TABLE IF NOT EXISTS public.location_messages (
            id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
            conversation_id UUID NOT NULL REFERENCES public.chat_conversations(id) ON DELETE CASCADE,
            sender_id UUID NOT NULL REFERENCES public.profiles(id) ON DELETE CASCADE,
            sender_name TEXT NOT NULL,
            sender_avatar_url TEXT,
            lat DOUBLE PRECISION NOT NULL,
            lng DOUBLE PRECISION NOT NULL,
            label TEXT NOT NULL,
            expiration TIMESTAMP WITH TIME ZONE NOT NULL,
            is_expired BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW()
          );
          
          -- Add indexes for better performance
          CREATE INDEX IF NOT EXISTS location_messages_conversation_id_idx ON public.location_messages(conversation_id);
          CREATE INDEX IF NOT EXISTS location_messages_sender_id_idx ON public.location_messages(sender_id);
          CREATE INDEX IF NOT EXISTS location_messages_created_at_idx ON public.location_messages(created_at);
          
          -- Add RLS policies
          ALTER TABLE public.location_messages ENABLE ROW LEVEL SECURITY;
          
          -- Policy to allow users to select location messages in their conversations
          CREATE POLICY "Users can view location messages in their conversations" 
          ON public.location_messages FOR SELECT 
          USING (
            EXISTS (
              SELECT 1 FROM public.chat_participants
              WHERE chat_participants.conversation_id = location_messages.conversation_id
              AND chat_participants.user_id = auth.uid()
            )
          );
          
          -- Policy to allow users to insert their own location messages
          CREATE POLICY "Users can insert their own location messages" 
          ON public.location_messages FOR INSERT 
          WITH CHECK (
            sender_id = auth.uid() AND
            EXISTS (
              SELECT 1 FROM public.chat_participants
              WHERE chat_participants.conversation_id = location_messages.conversation_id
              AND chat_participants.user_id = auth.uid()
            )
          );
          
          -- Policy to allow users to update their own location messages
          CREATE POLICY "Users can update their own location messages" 
          ON public.location_messages FOR UPDATE 
          USING (sender_id = auth.uid())
          WITH CHECK (sender_id = auth.uid());
        `);
        
        if (sqlError) {
          throw sqlError;
        }
      }
      
      console.log('location_messages table created successfully.');
    }
    
    // Add is_system_message column to messages table if it doesn't exist
    console.log('Adding is_system_message column to messages table...');
    
    const { error: alterError } = await supabase.sql(`
      ALTER TABLE public.messages 
      ADD COLUMN IF NOT EXISTS is_system_message BOOLEAN DEFAULT FALSE;
    `);
    
    if (alterError) {
      throw alterError;
    }
    
    console.log('is_system_message column added successfully.');
    
    // Enable realtime for location_messages table
    console.log('Enabling realtime for location_messages table...');
    
    const { error: realtimeError } = await supabase.sql(`
      BEGIN;
      -- Check if the table is already in the publication
      DO $$
      DECLARE
        table_exists BOOLEAN;
      BEGIN
        SELECT EXISTS (
          SELECT 1
          FROM pg_publication_tables
          WHERE pubname = 'supabase_realtime'
          AND schemaname = 'public'
          AND tablename = 'location_messages'
        ) INTO table_exists;
        
        IF NOT table_exists THEN
          -- Add the table to the publication
          ALTER PUBLICATION supabase_realtime ADD TABLE public.location_messages;
        END IF;
      END $$;
      COMMIT;
    `);
    
    if (realtimeError) {
      throw realtimeError;
    }
    
    console.log('Realtime enabled for location_messages table.');
    
    // Add joined_at column to chat_participants table if it doesn't exist
    console.log('Adding joined_at column to chat_participants table...');
    
    const { error: alterParticipantsError } = await supabase.sql(`
      ALTER TABLE public.chat_participants 
      ADD COLUMN IF NOT EXISTS joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    `);
    
    if (alterParticipantsError) {
      throw alterParticipantsError;
    }
    
    console.log('joined_at column added successfully.');
    
    console.log('Setup completed successfully!');
  } catch (error) {
    console.error('Error setting up location messages:', error);
  }
}

setupLocationMessages();
