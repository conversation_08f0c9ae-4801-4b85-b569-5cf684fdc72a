
import React, { useState, useRef, useEffect, useCallback } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardFooter } from '@/components/ui/card';
import { useToast } from '@/hooks/use-toast';
import { useMapboxToken } from '@/hooks/use-mapbox-token';
import { Loader2, MapPin, Map as MapIcon, Satellite, Save, X, MoveIcon } from 'lucide-react';
import { Toggle } from '@/components/ui/toggle';
import { MapLocation, normalizeLocation } from '@/types/location';

interface AddressMinimapProps {
  initialLocation?: Partial<MapLocation>;
  onSaveLocation?: (location: {
    lng: number;
    lat: number;
    address: string;
    coordinates?: [number, number];
  }) => void;
  className?: string;
  title?: string;
  markerColor?: string;
  height?: string;
  editable?: boolean;
  centerMarker?: boolean;
  showStyleToggle?: boolean;
  showFooter?: boolean | string;
  adjustButtonText?: string;
  saveButtonText?: string;
  cancelButtonText?: string;
}

export function AddressMinimap({
  initialLocation,
  onSaveLocation,
  className,
  title,
  markerColor = '#0EA5E9',
  height = '300px',
  editable = true,
  centerMarker = false,
  showStyleToggle = true,
  showFooter = true,
  adjustButtonText = "Adjust Marker",
  saveButtonText = "Save Location",
  cancelButtonText = "Cancel"
}: AddressMinimapProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const marker = useRef<mapboxgl.Marker | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [address, setAddress] = useState<string>('');
  const [mapStyle, setMapStyle] = useState<'streets-v11' | 'satellite-streets-v12'>('streets-v11');
  const { toast } = useToast();
  const { token: mapboxToken, isLoading: tokenLoading } = useMapboxToken();

  const normalizedInitialLocation = useCallback(() => {
    if (initialLocation) {
      return normalizeLocation(initialLocation);
    }
    return { lng: -122.4194, lat: 37.7749, x: -122.4194, y: 37.7749 }; // Default coordinates (San Francisco)
  }, [initialLocation]);

  useEffect(() => {
    if (!mapboxToken || !mapContainer.current) return;

    if (map.current && initialLocation && marker.current) {
      const location = normalizeLocation(initialLocation);
      marker.current.setLngLat([location.lng, location.lat]);
      map.current.flyTo({
        center: [location.lng, location.lat],
        zoom: 15,
        duration: 800
      });
      updateLocationInfo(location.lng, location.lat);
      return;
    }

    try {
      setIsLoading(true);
      mapboxgl.accessToken = mapboxToken;

      const location = normalizedInitialLocation();

      map.current = new mapboxgl.Map({
        container: mapContainer.current,
        style: `mapbox://styles/mapbox/${mapStyle}`,
        center: [location.lng, location.lat],
        zoom: 15,
        pitch: centerMarker ? 0 : 30,
        attributionControl: false
      });

      map.current.addControl(new mapboxgl.NavigationControl({
        showCompass: true,
        showZoom: true,
        visualizePitch: !centerMarker
      }), 'top-right');

      if (!centerMarker) {
        map.current.scrollZoom.enable();
        map.current.dragPan.enable();
        map.current.dragRotate.enable();
      } else {
        map.current.scrollZoom.disable();
        map.current.dragPan.disable();
        map.current.dragRotate.disable();
      }

      const markerElement = document.createElement('div');
      markerElement.className = 'address-marker';
      markerElement.style.width = '30px';
      markerElement.style.height = '30px';
      markerElement.style.display = 'flex';
      markerElement.style.alignItems = 'center';
      markerElement.style.justifyContent = 'center';

      const pin = document.createElement('div');
      pin.style.width = editable ? '24px' : '18px';
      pin.style.height = editable ? '24px' : '18px';
      pin.style.borderRadius = '50% 50% 50% 0';
      pin.style.backgroundColor = markerColor;
      pin.style.border = '2px solid white';
      pin.style.boxShadow = '0 2px 4px rgba(0,0,0,0.3)';
      pin.style.transform = 'rotate(-45deg)';
      pin.style.position = 'relative';

      if (editable) {
        const pulse = document.createElement('div');
        pulse.style.width = '30px';
        pulse.style.height = '30px';
        pulse.style.borderRadius = '50%';
        pulse.style.backgroundColor = `${markerColor}40`;
        pulse.style.position = 'absolute';
        pulse.style.animation = 'pulse 2s infinite';
        markerElement.appendChild(pulse);

        const style = document.createElement('style');
        style.innerHTML = `
          @keyframes pulse {
            0% { transform: scale(0.8); opacity: 0.8; }
            70% { transform: scale(1.5); opacity: 0; }
            100% { transform: scale(1.8); opacity: 0; }
          }
        `;
        document.head.appendChild(style);
      }

      markerElement.appendChild(pin);

      marker.current = new mapboxgl.Marker({
        element: markerElement,
        draggable: true, // Always make the marker draggable
        anchor: 'bottom',
      })
        .setLngLat([location.lng, location.lat])
        .addTo(map.current);

      if (!centerMarker) {
        map.current.on('style.load', () => {
          map.current?.setFog({
            'color': 'rgb(255, 255, 255)',
            'high-color': 'rgb(200, 200, 225)',
            'horizon-blend': 0.2,
          });
        });
      }

      updateLocationInfo(location.lng, location.lat);

      map.current.on('load', () => {
        setIsLoading(false);
      });

      return () => {
        if (map.current) {
          if (marker.current) marker.current.remove();
          map.current.remove();
          map.current = null;
        }
      };
    } catch (error) {
      console.error('Error initializing address minimap:', error);
      toast({
        title: "Map Error",
        description: "Failed to initialize map. Please try again later.",
        variant: "destructive"
      });
      setIsLoading(false);
    }
  }, [mapboxToken, mapStyle, centerMarker, initialLocation]);

  useEffect(() => {
    if (!marker.current || !map.current) return;

    // Always keep the marker draggable
    marker.current.setDraggable(true);

    // Always attach the dragend event handler
    marker.current.on('dragend', handleMarkerDragEnd);

    const markerElement = marker.current.getElement();
    markerElement.style.zIndex = '10';

    if (isEditing) {
      const position = marker.current.getLngLat();
      map.current.flyTo({
        center: [position.lng, position.lat],
        zoom: 16,
        duration: 800
      });
    }

    return () => {
      if (marker.current) {
        marker.current.off('dragend', handleMarkerDragEnd);
      }
    };
  }, [isEditing]);

  useEffect(() => {
    if (!map.current || !map.current.isStyleLoaded()) return;
    map.current.setStyle(`mapbox://styles/mapbox/${mapStyle}`);
  }, [mapStyle]);

  const handleMarkerDragEnd = useCallback(() => {
    if (!marker.current) return;
    const { lng, lat } = marker.current.getLngLat();

    // Update the address information
    updateLocationInfo(lng, lat);

    // If we're not in edit mode, automatically save the location
    if (!isEditing && onSaveLocation) {
      // Use a short timeout to allow the address to be fetched first
      setTimeout(() => {
        const currentAddress = address || 'Location selected';
        onSaveLocation({
          lng,
          lat,
          address: currentAddress,
          coordinates: [lng, lat]
        });
      }, 300);
    }
  }, [isEditing, address, onSaveLocation]);

  const updateLocationInfo = useCallback(async (lng: number, lat: number) => {
    if (!mapboxToken) return;

    try {
      setIsLoading(true);
      const response = await fetch(
        `https://api.mapbox.com/geocoding/v5/mapbox.places/${lng},${lat}.json?access_token=${mapboxToken}`
      );

      if (!response.ok) {
        throw new Error('Failed to fetch address');
      }

      const data = await response.json();
      console.log("Geocoding response:", data);

      const addressFeature = data.features?.[0];
      if (addressFeature) {
        const formattedAddress = addressFeature.place_name;
        console.log("Setting address to:", formattedAddress);
        setAddress(formattedAddress);
      } else {
        console.warn("No address features found in geocoding response");
        setAddress('Address not found');
      }
    } catch (error) {
      console.error('Error fetching address:', error);
      setAddress('Address not found');
    } finally {
      setIsLoading(false);
    }
  }, [mapboxToken]);

  const toggleEditMode = useCallback(() => {
    setIsEditing(!isEditing);
  }, [isEditing]);

  const handleSaveLocation = useCallback(() => {
    if (!marker.current || !onSaveLocation) return;

    const { lng, lat } = marker.current.getLngLat();

    // Make sure we have the most up-to-date address before saving
    const currentAddress = address || 'Location selected';
    console.log("Saving location with address:", currentAddress);

    onSaveLocation({
      lng,
      lat,
      address: currentAddress,
      coordinates: [lng, lat]
    });

    setIsEditing(false);
    toast({
      title: "Location Saved",
      description: "The location has been updated successfully."
    });
  }, [address, onSaveLocation, toast]);

  const toggleMapStyle = useCallback(() => {
    setMapStyle(prev =>
      prev === 'streets-v11' ? 'satellite-streets-v12' : 'streets-v11'
    );
  }, []);

  const centerMapOnMarker = useCallback(() => {
    if (!map.current || !marker.current) return;

    const position = marker.current.getLngLat();
    map.current.flyTo({
      center: [position.lng, position.lat],
      zoom: 16,
      duration: 800
    });
  }, []);

  if (tokenLoading) {
    return (
      <Card className={className}>
        <div className="p-4 flex items-center justify-center" style={{ height }}>
          <Loader2 className="h-6 w-6 animate-spin" />
        </div>
      </Card>
    );
  }

  const footerText = typeof showFooter === 'string'
    ? showFooter
    : showFooter
      ? "Drag the pin to adjust the location"
      : null;

  return (
    <Card className={cn("overflow-hidden", className)}>
      <div className="relative" style={{ height }}>
        <div ref={mapContainer} className="absolute inset-0" />

        {title && (
          <div className="absolute top-2 left-2 z-10 bg-background/80 backdrop-blur-sm px-2 py-1 rounded text-xs font-medium">
            {title}
          </div>
        )}

        {isLoading && (
          <div className="absolute inset-0 bg-background/50 backdrop-blur-sm flex items-center justify-center z-30">
            <Loader2 className="h-6 w-6 animate-spin" />
          </div>
        )}

        <div className="absolute top-2 right-2 z-10 flex flex-col gap-2">
          {showStyleToggle && (
            <Toggle
              className="h-8 w-8 p-0 bg-background/80 backdrop-blur-sm"
              pressed={mapStyle === 'satellite-streets-v12'}
              onPressedChange={toggleMapStyle}
              title="Toggle map style"
            >
              {mapStyle === 'streets-v11' ? <Satellite className="h-4 w-4" /> : <MapIcon className="h-4 w-4" />}
            </Toggle>
          )}

          <Button
            size="sm"
            variant="secondary"
            className="bg-background/80 backdrop-blur-sm p-2 h-8 w-8"
            onClick={centerMapOnMarker}
            title="Center on marker"
          >
            <MapPin className="h-4 w-4" />
          </Button>
        </div>

        {/* Removed the bottom buttons to avoid duplication with the onboarding navigation */}

        {address && (
          <div className="absolute top-12 left-0 right-0 z-10 flex justify-center">
            <div className="bg-background/80 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-sm max-w-[80%]">
              <p className="text-xs text-center truncate">{address}</p>
            </div>
          </div>
        )}

        {/* Draggable marker hint - only show when not editing */}
        {!isEditing && (
          <div className="absolute bottom-16 left-0 right-0 z-10 flex justify-center">
            <div className="bg-primary/90 backdrop-blur-sm px-3 py-1.5 rounded-full shadow-sm animate-pulse">
              <p className="text-xs text-center flex items-center text-white">
                <MoveIcon className="h-3 w-3 mr-1" />
                Drag pin to adjust location
              </p>
            </div>
          </div>
        )}

        {isEditing && (
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-10 flex items-center justify-center pointer-events-none">
            <div className="bg-background/60 backdrop-blur-sm p-2 rounded-full shadow-md animate-bounce">
              <MoveIcon className="h-6 w-6 text-primary" />
            </div>
          </div>
        )}
      </div>

      {footerText && isEditing && (
        <CardFooter className="px-4 py-2 bg-muted/50">
          <p className="text-xs text-center w-full text-muted-foreground">{footerText}</p>
        </CardFooter>
      )}
    </Card>
  );
}
