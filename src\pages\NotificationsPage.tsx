
import React, { useState } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import {
  Bell,
  Calendar,
  MessageCircle,
  CreditCard,
  Settings,
  CheckCheck,
  Trash2,
  Search,
  UserPlus,
  Heart,
  ThumbsUp,
  AlertCircle,
  Info,
  Clock,
  Filter
} from 'lucide-react';
import { Link } from 'react-router-dom';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { useToast } from '@/hooks/use-toast';

// Mock notification data
const mockNotifications = [
  {
    id: '1',
    type: 'message',
    title: 'New message from <PERSON>',
    description: 'Hey, are you interested in joining our beach cleanup activity?',
    timestamp: '10 minutes ago',
    read: false,
    icon: <MessageCircle className="h-5 w-5" />,
    iconBg: 'bg-blue-100',
    iconColor: 'text-blue-600',
    action: '/chat?conversation=123',
    actionText: 'Reply',
    user: {
      name: 'Sarah Johnson',
      avatar: 'https://i.pravatar.cc/150?img=1'
    }
  },
  {
    id: '2',
    type: 'activity',
    title: 'Activity reminder',
    description: 'Beach Volleyball starts in 2 hours',
    timestamp: '1 hour ago',
    read: false,
    icon: <Calendar className="h-5 w-5" />,
    iconBg: 'bg-green-100',
    iconColor: 'text-green-600',
    action: '/activity/456',
    actionText: 'View Details',
  },
  {
    id: '3',
    type: 'system',
    title: 'Profile 80% complete',
    description: 'Add a profile picture to complete your profile',
    timestamp: '2 hours ago',
    read: true,
    icon: <UserPlus className="h-5 w-5" />,
    iconBg: 'bg-purple-100',
    iconColor: 'text-purple-600',
    action: '/profile',
    actionText: 'Complete Profile',
  },
  {
    id: '4',
    type: 'booking',
    title: 'Booking confirmed',
    description: 'Your surf lesson with Mike has been confirmed',
    timestamp: '1 day ago',
    read: true,
    icon: <CheckCheck className="h-5 w-5" />,
    iconBg: 'bg-green-100',
    iconColor: 'text-green-600',
    action: '/bookings',
    actionText: 'View Booking',
  },
  {
    id: '5',
    type: 'like',
    title: 'New activity like',
    description: 'John liked your Beach Cleanup activity',
    timestamp: '2 days ago',
    read: true,
    icon: <Heart className="h-5 w-5" />,
    iconBg: 'bg-red-100',
    iconColor: 'text-red-600',
    action: '/activity/789',
    actionText: 'View Activity',
    user: {
      name: 'John Smith',
      avatar: 'https://i.pravatar.cc/150?img=2'
    }
  },
  {
    id: '6',
    type: 'system',
    title: 'Welcome to BuddySurf!',
    description: 'Thanks for joining. Complete your profile to get started.',
    timestamp: '3 days ago',
    read: true,
    icon: <Bell className="h-5 w-5" />,
    iconBg: 'bg-blue-100',
    iconColor: 'text-blue-600',
    action: '/profile',
    actionText: 'Complete Profile',
  },
];

export default function NotificationsPage() {
  const [activeTab, setActiveTab] = useState('all');
  const [selectedNotifications, setSelectedNotifications] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const { toast } = useToast();

  // Filter notifications based on active tab and search query
  const filteredNotifications = mockNotifications.filter(notification => {
    const matchesTab =
      activeTab === 'all' ||
      (activeTab === 'unread' && !notification.read) ||
      (activeTab === notification.type);

    const matchesSearch =
      searchQuery === '' ||
      notification.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      notification.description.toLowerCase().includes(searchQuery.toLowerCase());

    return matchesTab && matchesSearch;
  });

  const unreadCount = mockNotifications.filter(n => !n.read).length;

  const handleSelectAll = () => {
    if (selectedNotifications.length === filteredNotifications.length) {
      setSelectedNotifications([]);
    } else {
      setSelectedNotifications(filteredNotifications.map(n => n.id));
    }
  };

  const handleSelect = (id: string) => {
    if (selectedNotifications.includes(id)) {
      setSelectedNotifications(selectedNotifications.filter(nId => nId !== id));
    } else {
      setSelectedNotifications([...selectedNotifications, id]);
    }
  };

  const handleMarkAsRead = () => {
    toast({
      title: "Notifications marked as read",
      description: `${selectedNotifications.length} notification(s) marked as read`,
    });
    setSelectedNotifications([]);
  };

  const handleDelete = () => {
    toast({
      title: "Notifications deleted",
      description: `${selectedNotifications.length} notification(s) deleted`,
      variant: "destructive",
    });
    setSelectedNotifications([]);
  };

  return (
    <MainLayout>
      <div className="container mx-auto py-8 px-4 max-w-4xl">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold">Notifications</h1>
            <p className="text-muted-foreground">
              Stay updated with your activities, messages, and more
            </p>
          </div>

          <div className="flex items-center gap-2">
            <Link to="/settings">
              <Button variant="outline" size="sm" className="flex items-center gap-2">
                <Settings className="h-4 w-4" />
                Notification Settings
              </Button>
            </Link>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border overflow-hidden mb-8">
          <div className="p-4 border-b flex flex-col md:flex-row justify-between gap-4">
            <Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid grid-cols-3 md:grid-cols-6 w-full">
                <TabsTrigger value="all" className="flex items-center gap-2">
                  All
                  {unreadCount > 0 && (
                    <Badge variant="secondary" className="ml-1">{unreadCount}</Badge>
                  )}
                </TabsTrigger>
                <TabsTrigger value="unread">Unread</TabsTrigger>
                <TabsTrigger value="message">Messages</TabsTrigger>
                <TabsTrigger value="activity">Activities</TabsTrigger>
                <TabsTrigger value="booking">Bookings</TabsTrigger>
                <TabsTrigger value="system">System</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="p-4 border-b flex flex-col md:flex-row justify-between gap-4">
            <div className="flex items-center gap-2">
              <Checkbox
                id="select-all"
                checked={selectedNotifications.length === filteredNotifications.length && filteredNotifications.length > 0}
                onCheckedChange={handleSelectAll}
              />
              <label htmlFor="select-all" className="text-sm font-medium">
                Select All
              </label>

              {selectedNotifications.length > 0 && (
                <div className="flex items-center gap-2 ml-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleMarkAsRead}
                    className="flex items-center gap-1"
                  >
                    <CheckCheck className="h-4 w-4" />
                    Mark as Read
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleDelete}
                    className="flex items-center gap-1 text-destructive hover:text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                    Delete
                  </Button>
                </div>
              )}
            </div>

            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
              <Input
                placeholder="Search notifications"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-9 w-full md:w-64"
              />
            </div>
          </div>

          <div className="divide-y">
            {filteredNotifications.length > 0 ? (
              filteredNotifications.map((notification) => (
                <NotificationItem
                  key={notification.id}
                  notification={notification}
                  isSelected={selectedNotifications.includes(notification.id)}
                  onSelect={() => handleSelect(notification.id)}
                />
              ))
            ) : (
              <div className="p-8 text-center">
                <div className="mx-auto w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center mb-4">
                  <Bell className="h-6 w-6 text-gray-400" />
                </div>
                <h3 className="text-lg font-medium mb-1">No notifications</h3>
                <p className="text-muted-foreground">
                  {searchQuery ? 'No notifications match your search' : 'You\'re all caught up!'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>
    </MainLayout>
  );
}

interface NotificationItemProps {
  notification: any;
  isSelected: boolean;
  onSelect: () => void;
}

function NotificationItem({ notification, isSelected, onSelect }: NotificationItemProps) {
  return (
    <div className={`p-4 hover:bg-gray-50 transition-colors ${!notification.read ? 'bg-blue-50/30' : ''}`}>
      <div className="flex items-start gap-3">
        <Checkbox checked={isSelected} onCheckedChange={() => onSelect()} className="mt-1" />

        <div className={`w-10 h-10 rounded-full ${notification.iconBg} flex items-center justify-center flex-shrink-0`}>
          <div className={notification.iconColor}>
            {notification.icon}
          </div>
        </div>

        <div className="flex-1 min-w-0">
          <div className="flex items-start justify-between gap-2">
            <div>
              <div className="flex items-center gap-2">
                <h3 className="font-medium text-sm">
                  {notification.title}
                </h3>
                {!notification.read && (
                  <Badge variant="secondary" className="h-1.5 w-1.5 rounded-full p-0 bg-blue-500" />
                )}
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                {notification.description}
              </p>

              {notification.user && (
                <div className="flex items-center gap-2 mt-2">
                  <Avatar className="h-6 w-6">
                    <AvatarImage src={notification.user.avatar} alt={notification.user.name} />
                    <AvatarFallback>{notification.user.name.charAt(0)}</AvatarFallback>
                  </Avatar>
                  <span className="text-xs text-muted-foreground">{notification.user.name}</span>
                </div>
              )}
            </div>

            <div className="flex flex-col items-end gap-2">
              <span className="text-xs text-muted-foreground whitespace-nowrap">
                {notification.timestamp}
              </span>

              {notification.action && (
                <Link to={notification.action}>
                  <Button variant="ghost" size="sm" className="text-xs h-7 px-2">
                    {notification.actionText}
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
