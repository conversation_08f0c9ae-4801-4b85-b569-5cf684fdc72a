
import React from 'react';
import { BuddyProfile } from '@/types/chat';
import { ChatListItem } from './ChatListItem';
import { format, isToday, isYesterday } from 'date-fns';

interface BuddyListProps {
  buddyList: BuddyProfile[];
  selectedUser: string | null;
  onSelectUser: (userId: string) => void;
}

export function BuddyList({ buddyList, selectedUser, onSelectUser }: BuddyListProps) {
  const formatLastSeen = (lastSeen: string | undefined) => {
    if (!lastSeen) return '';
    
    const date = new Date(lastSeen);
    if (isToday(date)) {
      return `Today at ${format(date, 'p')}`;
    } else if (isYesterday(date)) {
      return `Yesterday at ${format(date, 'p')}`;
    } else {
      return format(date, 'MMM d');
    }
  };

  return (
    <div className="space-y-1">
      {buddyList.map((buddy) => (
        <ChatListItem
          key={buddy.id}
          id={buddy.id}
          name={buddy.display_name || buddy.username || 'Unknown User'}
          imageUrl={buddy.avatar_url || undefined}
          lastMessage={buddy.bio?.slice(0, 30)}
          timestamp={formatLastSeen(buddy.last_seen_at)}
          isSelected={selectedUser === buddy.id}
          onClick={() => onSelectUser(buddy.id)}
        />
      ))}
      
      {buddyList.length === 0 && (
        <div className="text-center py-4 text-sm text-muted-foreground">
          No buddies found
        </div>
      )}
    </div>
  );
}
