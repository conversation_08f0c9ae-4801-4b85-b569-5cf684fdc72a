-- SQL script to complete the profile enhancements
-- This script creates the missing views, functions, triggers, and RLS policies

-- 1. Create the profile_statistics view
CREATE OR REPLACE VIEW profile_statistics AS
SELECT
  p.id,
  p.username,
  p.display_name,
  p.avatar_url,
  p.cover_url,
  p.is_verified,
  p.last_seen_at,
  (SELECT COUNT(*) FROM follows WHERE following_id = p.id) AS follower_count,
  (SELECT COUNT(*) FROM follows WHERE follower_id = p.id) AS following_count,
  (SELECT COUNT(*) FROM activities WHERE host_id = p.id) AS hosted_activities_count,
  (SELECT COUNT(*) FROM activity_participants WHERE user_id = p.id AND status = 'confirmed') AS participated_activities_count,
  (SELECT COUNT(*) FROM profile_media WHERE profile_id = p.id) AS media_count
FROM
  profiles p;

-- 2. Create the profile_connections view
CREATE OR REPLACE VIEW profile_connections AS
SELECT
  f.id,
  f.follower_id,
  f.following_id,
  f.created_at,
  p1.username AS follower_username,
  p1.display_name AS follower_display_name,
  p1.avatar_url AS follower_avatar_url,
  p1.is_verified AS follower_is_verified,
  p2.username AS following_username,
  p2.display_name AS following_display_name,
  p2.avatar_url AS following_avatar_url,
  p2.is_verified AS following_is_verified,
  (SELECT COUNT(*) FROM follows WHERE follower_id = f.following_id AND following_id = f.follower_id) > 0 AS is_mutual
FROM
  follows f
JOIN
  profiles p1 ON f.follower_id = p1.id
JOIN
  profiles p2 ON f.following_id = p2.id;

-- 3. Create the update_last_seen function
CREATE OR REPLACE FUNCTION update_last_seen()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  UPDATE profiles
  SET last_seen_at = NOW()
  WHERE id = auth.uid();
  RETURN NEW;
END;
$$;

-- 4. Create the update_profile_media_updated_at function
CREATE OR REPLACE FUNCTION update_profile_media_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

-- 5. Create the update_user_last_seen trigger
DROP TRIGGER IF EXISTS update_user_last_seen ON messages;
CREATE TRIGGER update_user_last_seen
AFTER INSERT OR UPDATE ON messages
FOR EACH ROW
EXECUTE FUNCTION update_last_seen();

-- 6. Create the set_profile_media_updated_at trigger
DROP TRIGGER IF EXISTS set_profile_media_updated_at ON profile_media;
CREATE TRIGGER set_profile_media_updated_at
BEFORE UPDATE ON profile_media
FOR EACH ROW
EXECUTE FUNCTION update_profile_media_updated_at();

-- 7. Set up RLS policies for profile_media
ALTER TABLE public.profile_media ENABLE ROW LEVEL SECURITY;

-- Users can view public profile media
CREATE POLICY "Users can view public profile media"
ON public.profile_media
FOR SELECT
USING (
  profile_id IN (
    SELECT id FROM profiles WHERE is_profile_public = TRUE
  )
  OR
  profile_id IN (
    SELECT following_id FROM follows WHERE follower_id = auth.uid()
  )
  OR
  profile_id = auth.uid()
);

-- Users can manage their own profile media
CREATE POLICY "Users can manage their own profile media"
ON public.profile_media
FOR ALL
USING (profile_id = auth.uid());

-- 8. Set up RLS policies for profile_views
ALTER TABLE public.profile_views ENABLE ROW LEVEL SECURITY;

-- Users can view their profile views
CREATE POLICY "Users can view their profile views"
ON public.profile_views
FOR SELECT
USING (profile_id = auth.uid());

-- Users can create profile views
CREATE POLICY "Users can create profile views"
ON public.profile_views
FOR INSERT
WITH CHECK (true);

-- 9. Set up RLS policies for user_blocks
ALTER TABLE public.user_blocks ENABLE ROW LEVEL SECURITY;

-- Users can view their blocks
CREATE POLICY "Users can view their blocks"
ON public.user_blocks
FOR SELECT
USING (blocker_id = auth.uid());

-- Users can manage their blocks
CREATE POLICY "Users can manage their blocks"
ON public.user_blocks
FOR ALL
USING (blocker_id = auth.uid());

-- 10. Data Migration: Migrate gallery images to profile_media
-- This function will migrate gallery images from profiles to profile_media
CREATE OR REPLACE FUNCTION migrate_gallery_images()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  profile_record RECORD;
  gallery_item TEXT;
  position_counter INTEGER;
BEGIN
  FOR profile_record IN SELECT id, gallery FROM profiles WHERE gallery IS NOT NULL AND jsonb_array_length(gallery) > 0
  LOOP
    position_counter := 0;
    
    FOR i IN 0..jsonb_array_length(profile_record.gallery) - 1
    LOOP
      gallery_item := profile_record.gallery->i;
      
      -- Insert into profile_media if not already exists
      INSERT INTO profile_media (
        profile_id, 
        url, 
        thumbnail_url, 
        type, 
        position, 
        is_featured, 
        created_at, 
        updated_at
      )
      SELECT 
        profile_record.id, 
        gallery_item, 
        gallery_item, 
        'image', 
        position_counter, 
        position_counter = 0, 
        NOW(), 
        NOW()
      WHERE NOT EXISTS (
        SELECT 1 FROM profile_media 
        WHERE profile_id = profile_record.id AND url = gallery_item
      );
      
      position_counter := position_counter + 1;
    END LOOP;
  END LOOP;
END;
$$;

-- 11. Initialize profile_visibility for existing profiles
UPDATE profiles
SET profile_visibility = '{"followers": true, "following": true, "activities": true, "gallery": true, "about": true}'
WHERE profile_visibility IS NULL;

-- 12. Execute the gallery migration function
SELECT migrate_gallery_images();

-- 13. Drop the migration function (cleanup)
DROP FUNCTION migrate_gallery_images();
