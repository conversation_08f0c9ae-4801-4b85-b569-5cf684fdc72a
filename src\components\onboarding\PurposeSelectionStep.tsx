
import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Checkbox } from "@/components/ui/checkbox";
import { OnboardingFormValues, purposeOptions } from "@/types/onboarding";
import { PurposeSelectionStepProps } from "@/types/onboarding-props";

export const PurposeSelectionStep = ({ form, onSubmit, isLoading, initialValues }: PurposeSelectionStepProps) => {
  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold">Why are you here?</h2>
        <p className="text-muted-foreground">Select all that apply</p>
      </div>
      
      <FormField
        control={form.control}
        name="purposes"
        render={() => (
          <FormItem>
            <div className="grid grid-cols-2 gap-4">
              {purposeOptions.map((purpose) => (
                <FormField
                  key={purpose.id}
                  control={form.control}
                  name="purposes"
                  render={({ field }) => {
                    return (
                      <FormItem
                        key={purpose.id}
                        className="flex flex-col items-start space-y-0 rounded-md border-2 p-4 hover:bg-accent/5 transition-colors"
                      >
                        <FormControl>
                          <Checkbox
                            checked={field.value?.includes(purpose.id)}
                            onCheckedChange={(checked) => {
                              const currentValues = field.value || [];
                              return checked
                                ? field.onChange([...currentValues, purpose.id])
                                : field.onChange(
                                    currentValues.filter((value) => value !== purpose.id)
                                  );
                            }}
                          />
                        </FormControl>
                        <div className="flex items-center space-x-2 leading-none pt-1.5 pl-6">
                          <span className="text-2xl">{purpose.icon}</span>
                          <span className="font-medium">{purpose.label}</span>
                        </div>
                      </FormItem>
                    );
                  }}
                />
              ))}
            </div>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
