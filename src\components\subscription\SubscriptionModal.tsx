
import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/components/ui/custom-dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Check, X, Zap, Award, Crown, Star, Sparkles, Waves, Sailboat, Gem, Shield } from "lucide-react";
import { cn } from "@/lib/utils";
import { motion, AnimatePresence } from "framer-motion";

interface SubscriptionModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubscribe?: (plan: string) => void;
}

export function SubscriptionModal({
  open,
  onOpenChange,
  onSubscribe
}: SubscriptionModalProps) {
  const [selectedPlan, setSelectedPlan] = useState<string | null>("monthly");
  const [showConfetti, setShowConfetti] = useState(false);

  // Reset selection when modal opens
  useEffect(() => {
    if (open) {
      setSelectedPlan("monthly");
    }
  }, [open]);

  const handleSubscribe = () => {
    if (selectedPlan && onSubscribe) {
      setShowConfetti(true);
      setTimeout(() => {
        onSubscribe(selectedPlan);
        onOpenChange(false);
        setTimeout(() => setShowConfetti(false), 500);
      }, 1000);
    }
  };

  const plans = [
    {
      id: "free",
      name: "Free Tier",
      price: "$0",
      period: "forever",
      description: "Basic features to get started",
      features: [
        "Basic map access",
        "Limited activity creation (1 per week)",
        "Standard chat (text-only, no proposals)",
        "Profile visibility in local searches",
        "Ads-supported",
        "Access to Help Center & FAQ",
        "Community support"
      ],
      icon: <Waves className="h-6 w-6 text-gray-400" />,
      color: "bg-gradient-to-br from-gray-50 to-gray-100",
      borderColor: "border-gray-200",
      textColor: "text-gray-700",
      buttonVariant: "outline" as const,
      popular: false,
      savings: "",
      highlight: false
    },
    {
      id: "weekly",
      name: "Weekly Trial",
      price: "$3.99",
      period: "per week",
      description: "Try premium features with a 14-day trial",
      features: [
        "All Free features",
        "Activities: Up to 3 creations/week",
        "Chat: Media sharing + 5 proposal cards/day",
        "Visibility: Boosted in 'Nearby' lists",
        "No ads",
        "Access to premium support (email/chat)",
        "Early access to select new features"
      ],
      icon: <Zap className="h-6 w-6 text-blue-500" />,
      color: "bg-gradient-to-br from-blue-50 to-blue-100",
      borderColor: "border-blue-200",
      textColor: "text-blue-700",
      buttonVariant: "outline" as const,
      popular: false,
      savings: "Try for 14 days",
      highlight: false
    },
    {
      id: "monthly",
      name: "Monthly Pro",
      price: "$15",
      period: "per month",
      description: "Perfect for active social users",
      features: [
        "All Weekly features",
        "Unlimited activity creation",
        "Priority placement in activity queues",
        "Advanced filters (income, verified users, etc.)",
        "Wallet integration (send/receive payments)",
        "10 proposal cards/day",
        "Custom map styles",
        "Profile analytics",
        "Access to exclusive activities/gigs",
        "Premium badge on profile"
      ],
      icon: <Gem className="h-6 w-6 text-purple-500" />,
      color: "bg-gradient-to-br from-purple-50 to-purple-100",
      borderColor: "border-purple-200",
      textColor: "text-purple-700",
      buttonVariant: "default" as const,
      popular: true,
      savings: "Most popular choice",
      highlight: true
    },
    {
      id: "lifetime",
      name: "Lifetime Surf",
      price: "$35",
      period: "one-time",
      description: "Best value for power users",
      features: [
        "All Monthly features",
        "Permanent ad-free experience",
        "\"Verified\" badge",
        "Early access to all new features",
        "Dedicated support (priority queue)",
        "Unlimited proposals",
        "Exclusive map markers (custom icons/colors)",
        "Lifetime badge on profile",
        "Invitation to beta test new features"
      ],
      icon: <Crown className="h-6 w-6 text-amber-500" />,
      color: "bg-gradient-to-br from-amber-50 to-amber-100",
      borderColor: "border-amber-200",
      textColor: "text-amber-700",
      buttonVariant: "outline" as const,
      popular: false,
      savings: "Save 80% vs monthly",
      highlight: false
    }
  ];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[900px] p-0 overflow-hidden bg-gradient-to-b from-white to-gray-50">
        {/* Confetti effect */}
        {showConfetti && (
          <div className="absolute inset-0 z-50 pointer-events-none">
            {Array.from({ length: 100 }).map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 rounded-full"
                style={{
                  top: `${Math.random() * 100}%`,
                  left: `${Math.random() * 100}%`,
                  backgroundColor: ['#7C3AED', '#3B82F6', '#F59E0B', '#10B981'][Math.floor(Math.random() * 4)],
                }}
                initial={{ y: -20, opacity: 0 }}
                animate={{
                  y: [0, Math.random() * 500 + 200],
                  opacity: [1, 0],
                  x: [0, (Math.random() - 0.5) * 200]
                }}
                transition={{ duration: 2, ease: "easeOut" }}
              />
            ))}
          </div>
        )}

        <DialogHeader className="p-8 pb-4 bg-gradient-to-r from-purple-50 to-blue-50">
          <motion.div
            className="flex items-center justify-center mb-3"
            initial={{ y: -20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <div className="relative">
              <Sparkles className="h-8 w-8 text-primary-purple mr-3" />
              <motion.div
                className="absolute -top-1 -right-1 w-3 h-3 bg-amber-400 rounded-full"
                animate={{ scale: [1, 1.5, 1] }}
                transition={{ duration: 2, repeat: Infinity }}
              />
            </div>
            <DialogTitle className="text-3xl font-bold text-center bg-gradient-to-r from-primary-purple to-blue-600 bg-clip-text text-transparent">
              Upgrade Your BuddySurf Experience
            </DialogTitle>
          </motion.div>
          <motion.p
            className="text-center text-muted-foreground text-lg"
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Choose the plan that fits your social lifestyle
          </motion.p>
        </DialogHeader>

        <div className="p-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 max-h-[70vh] overflow-y-auto">
          {plans.map((plan, index) => (
            <motion.div
              key={plan.id}
              className={cn(
                "rounded-xl border p-5 relative transition-all cursor-pointer hover:shadow-lg",
                selectedPlan === plan.id
                  ? "border-primary-purple ring-2 ring-primary-purple shadow-lg shadow-primary-purple/10"
                  : cn("border-border", plan.borderColor),
                plan.highlight ? "shadow-md" : ""
              )}
              onClick={() => setSelectedPlan(plan.id)}
              initial={{ y: 30, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.1 * index }}
              whileHover={{ y: -5 }}
            >
              {plan.popular && (
                <div className="absolute -top-3 left-0 right-0 flex justify-center">
                  <Badge className="bg-primary-purple hover:bg-primary-deep-purple px-3 py-1">
                    <Star className="h-3 w-3 mr-1 fill-white" /> Most Popular
                  </Badge>
                </div>
              )}

              <div className={cn(
                "w-14 h-14 rounded-full flex items-center justify-center mb-4",
                plan.color,
                "shadow-md"
              )}>
                {plan.icon}
              </div>

              <h3 className={cn("text-xl font-bold", plan.textColor)}>{plan.name}</h3>
              <div className="mt-2 mb-1">
                <span className="text-3xl font-bold">{plan.price}</span>
                <span className="text-sm text-muted-foreground ml-1">{plan.period}</span>
              </div>

              {plan.savings && (
                <div className="mb-3">
                  <span className="text-xs font-medium px-2 py-1 bg-green-50 text-green-600 rounded-full">
                    {plan.savings}
                  </span>
                </div>
              )}

              <p className="text-sm text-muted-foreground mb-4">{plan.description}</p>

              <ul className="space-y-3 mb-6">
                {plan.features.map((feature, index) => (
                  <motion.li
                    key={index}
                    className="flex items-start"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.3 + (index * 0.05) }}
                  >
                    <div className="bg-green-50 rounded-full p-0.5 mt-0.5 mr-2 flex-shrink-0">
                      <Check className="h-3.5 w-3.5 text-green-600" />
                    </div>
                    <span className="text-sm">{feature}</span>
                  </motion.li>
                ))}
              </ul>

              <Button
                variant={selectedPlan === plan.id ? "default" : plan.buttonVariant}
                className={cn(
                  "w-full mt-auto font-medium",
                  selectedPlan === plan.id
                    ? "bg-primary-purple hover:bg-primary-deep-purple text-white"
                    : plan.id === "monthly"
                      ? "border-primary-purple text-primary-purple hover:bg-primary-purple/10"
                      : ""
                )}
                onClick={() => setSelectedPlan(plan.id)}
              >
                {plan.id === "free" ? "Current Plan" : selectedPlan === plan.id ? "Selected" : `Choose ${plan.name}`}
              </Button>
            </motion.div>
          ))}
        </div>

        <motion.div
          className="p-6 bg-gradient-to-r from-gray-50 to-gray-100 flex flex-col sm:flex-row justify-between items-center gap-4 border-t"
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          <div className="text-sm text-muted-foreground max-w-md">
            <div className="flex items-center mb-1">
              <Shield className="h-4 w-4 text-primary-purple mr-1.5" />
              <span className="font-medium text-gray-700">Secure payment & 30-day money back guarantee</span>
            </div>
            <p>All plans include our core features. Upgrade anytime to unlock more possibilities.</p>
          </div>
          <div className="flex gap-3">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Maybe Later
            </Button>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button
                onClick={handleSubscribe}
                disabled={!selectedPlan || selectedPlan === "free"}
                className="bg-primary-purple hover:bg-primary-deep-purple font-medium px-6"
                size="lg"
              >
                {selectedPlan && selectedPlan !== "free" ? (
                  <span className="flex items-center">
                    <Sparkles className="h-4 w-4 mr-2" />
                    Subscribe to {selectedPlan.charAt(0).toUpperCase() + selectedPlan.slice(1)}
                  </span>
                ) : "Select a Plan"}
              </Button>
            </motion.div>
          </div>
        </motion.div>
      </DialogContent>
    </Dialog>
  );
}
