
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

interface ProfileServicesProps {
  userId?: string;
}

export function ProfileServices({ userId }: ProfileServicesProps) {
  // This would normally fetch from a services table in Supabase
  const hasServices = false;
  
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>Services</CardTitle>
          <CardDescription>Showcase your skills and offerings</CardDescription>
        </div>
        <Button size="sm" variant="outline" className="gap-1">
          <Plus className="h-4 w-4" />
          <span>Add Service</span>
        </Button>
      </CardHeader>
      <CardContent>
        {hasServices ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Services would go here */}
            <p>Services will appear here</p>
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <p>No services listed yet.</p>
            <p className="text-sm">Add services to let others hire you for your skills!</p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
