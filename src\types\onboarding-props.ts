
import { UseFormReturn } from "react-hook-form";
import { OnboardingFormValues } from "./onboarding";
import { UserProfile } from "./user-profile";

// Base props shared by all onboarding step components
export interface BaseOnboardingStepProps {
  initialValues: Partial<OnboardingFormValues>;
  isLoading: boolean;
  onSubmit: (stepData?: Partial<OnboardingFormValues>) => Promise<void>;
}

// Props for components that need form functionality
export interface FormOnboardingStepProps extends BaseOnboardingStepProps {
  form: UseFormReturn<OnboardingFormValues>;
}

// Props for steps with profile photo handling
export interface PhotoUploadStepProps extends FormOnboardingStepProps {
  previewUrl: string | null;
  handleThumbnailClick: () => void;
  fileInputRef: React.RefObject<HTMLInputElement>;
  handleFileChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  user: any;
}

// Location step needs different props
export interface LocationStepProps extends BaseOnboardingStepProps {
  userId?: string;
  user?: any;
  form?: UseFormReturn<OnboardingFormValues>;
}

// Specific interfaces for each step - ensuring they all extend the appropriate base props
export interface PurposeSelectionStepProps extends FormOnboardingStepProps {}

export interface InterestsStepProps extends FormOnboardingStepProps {}

export interface VibeSelectionStepProps extends FormOnboardingStepProps {}

export interface PermissionsStepProps extends BaseOnboardingStepProps {
  form?: UseFormReturn<OnboardingFormValues>;
}

export interface CompletionStepProps extends BaseOnboardingStepProps {
  form?: UseFormReturn<OnboardingFormValues>;
}

export interface BirthdayGenderStepProps extends FormOnboardingStepProps {}
