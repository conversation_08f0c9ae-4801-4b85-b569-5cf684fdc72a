-- Row Level Security policies for gig economy tables

-- Service Providers RLS Policies
CREATE POLICY "Service providers are viewable by everyone" ON public.service_providers
  FOR SELECT USING (true);

CREATE POLICY "Users can create their own service provider profile" ON public.service_providers
  FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own service provider profile" ON public.service_providers
  FOR UPDATE USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own service provider profile" ON public.service_providers
  FOR DELETE USING ((SELECT auth.uid()) = user_id);

-- Gigs RLS Policies
CREATE POLICY "Gigs are viewable by everyone" ON public.gigs
  FOR SELECT USING (true);

CREATE POLICY "Users can create their own gigs" ON public.gigs
  FOR INSERT WITH CHECK ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can update their own gigs" ON public.gigs
  FOR UPDATE USING ((SELECT auth.uid()) = user_id);

CREATE POLICY "Users can delete their own gigs" ON public.gigs
  FOR DELETE USING ((SELECT auth.uid()) = user_id);

-- Gig Packages RLS Policies
CREATE POLICY "Gig packages are viewable by everyone" ON public.gig_packages
  FOR SELECT USING (true);

CREATE POLICY "Gig owners can manage their gig packages" ON public.gig_packages
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.gigs 
      WHERE gigs.id = gig_packages.gig_id 
      AND gigs.user_id = (SELECT auth.uid())
    )
  );

-- Service Bookings RLS Policies
CREATE POLICY "Users can view their own bookings as client" ON public.service_bookings
  FOR SELECT USING ((SELECT auth.uid()) = client_id);

CREATE POLICY "Service providers can view their bookings" ON public.service_bookings
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.service_providers 
      WHERE service_providers.id = service_bookings.provider_id 
      AND service_providers.user_id = (SELECT auth.uid())
    )
  );

CREATE POLICY "Users can create bookings" ON public.service_bookings
  FOR INSERT WITH CHECK ((SELECT auth.uid()) = client_id);

CREATE POLICY "Clients can update their bookings" ON public.service_bookings
  FOR UPDATE USING ((SELECT auth.uid()) = client_id);

CREATE POLICY "Service providers can update their bookings" ON public.service_bookings
  FOR UPDATE USING (
    EXISTS (
      SELECT 1 FROM public.service_providers 
      WHERE service_providers.id = service_bookings.provider_id 
      AND service_providers.user_id = (SELECT auth.uid())
    )
  );

-- Reviews RLS Policies
CREATE POLICY "Reviews are viewable by everyone" ON public.reviews
  FOR SELECT USING (is_public = true);

CREATE POLICY "Users can view their own reviews" ON public.reviews
  FOR SELECT USING ((SELECT auth.uid()) = reviewer_id);

CREATE POLICY "Service providers can view reviews about them" ON public.reviews
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.service_providers 
      WHERE service_providers.id = reviews.provider_id 
      AND service_providers.user_id = (SELECT auth.uid())
    )
  );

CREATE POLICY "Users can create reviews for completed bookings" ON public.reviews
  FOR INSERT WITH CHECK (
    (SELECT auth.uid()) = reviewer_id AND
    EXISTS (
      SELECT 1 FROM public.service_bookings 
      WHERE service_bookings.id = reviews.booking_id 
      AND service_bookings.client_id = (SELECT auth.uid())
      AND service_bookings.status = 'completed'
    )
  );

CREATE POLICY "Users can update their own reviews" ON public.reviews
  FOR UPDATE USING ((SELECT auth.uid()) = reviewer_id);

CREATE POLICY "Users can delete their own reviews" ON public.reviews
  FOR DELETE USING ((SELECT auth.uid()) = reviewer_id);

-- Gig Portfolio RLS Policies
CREATE POLICY "Gig portfolio is viewable by everyone" ON public.gig_portfolio
  FOR SELECT USING (true);

CREATE POLICY "Gig owners can manage their portfolio" ON public.gig_portfolio
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.gigs 
      WHERE gigs.id = gig_portfolio.gig_id 
      AND gigs.user_id = (SELECT auth.uid())
    )
  );

-- Create functions to update ratings
CREATE OR REPLACE FUNCTION update_provider_rating()
RETURNS TRIGGER AS $$
BEGIN
  -- Update service provider rating
  UPDATE public.service_providers 
  SET 
    rating = (
      SELECT COALESCE(AVG(rating), 0) 
      FROM public.reviews 
      WHERE provider_id = NEW.provider_id AND is_public = true
    ),
    total_reviews = (
      SELECT COUNT(*) 
      FROM public.reviews 
      WHERE provider_id = NEW.provider_id AND is_public = true
    )
  WHERE id = NEW.provider_id;
  
  -- Update gig rating if gig_id is provided
  IF NEW.gig_id IS NOT NULL THEN
    UPDATE public.gigs 
    SET rating = (
      SELECT COALESCE(AVG(rating), 0) 
      FROM public.reviews 
      WHERE gig_id = NEW.gig_id AND is_public = true
    )
    WHERE id = NEW.gig_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for rating updates
CREATE TRIGGER update_ratings_on_review_insert
  AFTER INSERT ON public.reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_provider_rating();

CREATE TRIGGER update_ratings_on_review_update
  AFTER UPDATE ON public.reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_provider_rating();

-- Create function to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER update_service_providers_updated_at
  BEFORE UPDATE ON public.service_providers
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gigs_updated_at
  BEFORE UPDATE ON public.gigs
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_gig_packages_updated_at
  BEFORE UPDATE ON public.gig_packages
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_service_bookings_updated_at
  BEFORE UPDATE ON public.service_bookings
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reviews_updated_at
  BEFORE UPDATE ON public.reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();
