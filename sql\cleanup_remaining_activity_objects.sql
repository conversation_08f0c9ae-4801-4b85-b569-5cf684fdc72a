-- <PERSON><PERSON> script to clean up remaining activity-related database objects
-- This script removes any remaining activity-related columns, functions, and views

-- First, drop any remaining activity-related functions
DROP FUNCTION IF EXISTS add_activity_to_moderation_queue() CASCADE;
DROP FUNCTION IF EXISTS add_participant_to_activity_chat() CASCADE;
DROP FUNCTION IF EXISTS assign_activity_moderation() CASCADE;
DROP FUNCTION IF EXISTS auto_fill_activity_queue() CASCADE;
DROP FUNCTION IF EXISTS auto_flag_activity() CASCADE;
DROP FUNCTION IF EXISTS create_activity_from_template() CASCADE;
DROP FUNCTION IF EXISTS create_activity_group_chat() CASCADE;
DROP FUNCTION IF EXISTS ensure_activity_chat_created() CASCADE;
DROP FUNCTION IF EXISTS find_activity_geofences_containing_point() CASCADE;
DROP FUNCTION IF EXISTS generate_next_recurring_activity() CASCADE;
DROP FUNCTION IF EXISTS get_activity_analytics() CASCADE;
DROP FUNCTION IF EXISTS get_activity_queue_stats() CASCADE;
DROP FUNCTION IF EXISTS get_admin_activity_log() CASCADE;
DROP FUNCTION IF EXISTS handle_activity_flag() CASCADE;
DROP FUNCTION IF EXISTS initialize_activity_analytics() CASCADE;
DROP FUNCTION IF EXISTS moderate_activity() CASCADE;
DROP FUNCTION IF EXISTS notify_on_activity_participation() CASCADE;
DROP FUNCTION IF EXISTS notify_on_activity_update() CASCADE;
DROP FUNCTION IF EXISTS process_activity_cancellation_refunds() CASCADE;
DROP FUNCTION IF EXISTS process_activity_refund() CASCADE;
DROP FUNCTION IF EXISTS rate_activity() CASCADE;
DROP FUNCTION IF EXISTS remove_participant_from_activity_chat() CASCADE;
DROP FUNCTION IF EXISTS report_activity_safety_incident() CASCADE;
DROP FUNCTION IF EXISTS revoke_activity_verification() CASCADE;
DROP FUNCTION IF EXISTS track_activity_view() CASCADE;
DROP FUNCTION IF EXISTS update_activity_analytics_on_queue_change() CASCADE;
DROP FUNCTION IF EXISTS update_activity_moderation() CASCADE;
DROP FUNCTION IF EXISTS validate_activity_category() CASCADE;
DROP FUNCTION IF EXISTS verify_activity() CASCADE;

-- Remove activity-related columns from other tables
-- chat_conversations table
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'chat_conversations' AND column_name = 'is_activity_chat') THEN
        ALTER TABLE chat_conversations DROP COLUMN is_activity_chat;
    END IF;
END $$;

-- geofences table
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'geofences' AND column_name = 'activity_id') THEN
        ALTER TABLE geofences DROP COLUMN activity_id;
    END IF;
END $$;

-- messages table
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'messages' AND column_name = 'activity_id') THEN
        ALTER TABLE messages DROP COLUMN activity_id;
    END IF;
END $$;

-- notification_preferences table
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notification_preferences' AND column_name = 'activity_comments') THEN
        ALTER TABLE notification_preferences DROP COLUMN activity_comments;
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'notification_preferences' AND column_name = 'activity_updates') THEN
        ALTER TABLE notification_preferences DROP COLUMN activity_updates;
    END IF;
END $$;

-- Drop any activity-related views
DROP VIEW IF EXISTS activity_analytics_view CASCADE;
DROP VIEW IF EXISTS activity_participants_view CASCADE;
DROP VIEW IF EXISTS activity_queue_view CASCADE;
DROP VIEW IF EXISTS activity_details_view CASCADE;

-- Drop any activity-related types
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'activity_status') THEN
        DROP TYPE activity_status;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'activity_visibility') THEN
        DROP TYPE activity_visibility;
    END IF;
    
    IF EXISTS (SELECT 1 FROM pg_type WHERE typname = 'queue_type') THEN
        DROP TYPE queue_type;
    END IF;
END $$;

-- Notify of completion
DO $$ 
BEGIN
    RAISE NOTICE 'Remaining activity-related database objects have been removed successfully.';
END $$;
