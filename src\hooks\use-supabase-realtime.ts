
import { useState, useEffect, useCallback, useRef } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';
import { RealtimeChannel } from '@supabase/supabase-js';

export type SubscriptionCallback = (payload: any) => void;

export interface SubscriptionOptions {
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  schema?: string;
  table: string;
  filter?: string;
}

export interface PresenceState {
  [key: string]: any[];
}

export interface SupabaseRealtimeManager {
  subscribe: (
    channelName: string,
    options: SubscriptionOptions,
    callback: SubscriptionCallback
  ) => () => void;
  subscribeToPresence: (
    channelName: string,
    userState: Record<string, any>,
    onSync?: (state: PresenceState) => void,
    onJoin?: (payload: { key: string; newPresences: any[] }) => void,
    onLeave?: (payload: { key: string; leftPresences: any[] }) => void
  ) => () => void;
  isConnected: boolean;
  lastError: string | null;
}

/**
 * A hook that provides a centralized way to manage Supabase real-time subscriptions
 */
export function useSupabaseRealtime(): SupabaseRealtimeManager {
  const [isConnected, setIsConnected] = useState(false);
  const [lastError, setLastError] = useState<string | null>(null);
  const { user } = useAuth();
  const { toast } = useToast();
  const channelsRef = useRef<Map<string, RealtimeChannel>>(new Map());

  // Function to subscribe to database changes
  const subscribe = useCallback(
    (
      channelName: string,
      options: SubscriptionOptions,
      callback: SubscriptionCallback
    ) => {
      try {
        // Check if we already have this channel
        if (channelsRef.current.has(channelName)) {
          console.warn(`Channel ${channelName} already exists. Using existing channel.`);
          const existingChannel = channelsRef.current.get(channelName)!;

          // Add the new subscription to the existing channel
          existingChannel.on(
            'postgres_changes' as any,
            {
              event: options.event || '*',
              schema: options.schema || 'public',
              table: options.table,
              filter: options.filter
            },
            callback
          );

          return () => {
            // We don't unsubscribe the whole channel, just remove this specific listener
            existingChannel.unsubscribe();
          };
        }

        // Create a new channel with browser-compatible options
        const channel = supabase.channel(channelName, {
          config: {
            broadcast: { self: true },
            presence: { key: 'user-presence' }
          }
        });

        // Subscribe to the specified changes
        channel.on(
          'postgres_changes' as any,
          {
            event: options.event || '*',
            schema: options.schema || 'public',
            table: options.table,
            filter: options.filter
          },
          callback
        );

        // Handle connection status
        channel.subscribe((status) => {
          if (status === 'SUBSCRIBED') {
            setIsConnected(true);
            console.log(`Subscribed to ${channelName}`);
          } else if (status === 'CLOSED' || status === 'CHANNEL_ERROR') {
            setIsConnected(false);
            setLastError(`Connection to ${channelName} closed or errored`);
            console.error(`Channel ${channelName} status: ${status}`);
          }
        });

        // Store the channel reference
        channelsRef.current.set(channelName, channel);

        // Return a cleanup function
        return () => {
          channel.unsubscribe();
          channelsRef.current.delete(channelName);
        };
      } catch (error) {
        console.error('Error setting up realtime subscription:', error);
        setLastError(`Failed to set up subscription: ${error}`);
        return () => {};
      }
    },
    []
  );

  // Function to subscribe to presence
  const subscribeToPresence = useCallback(
    (
      channelName: string,
      userState: Record<string, any>,
      onSync?: (state: PresenceState) => void,
      onJoin?: (payload: { key: string; newPresences: any[] }) => void,
      onLeave?: (payload: { key: string; leftPresences: any[] }) => void
    ) => {
      try {
        // Check if we already have this channel
        if (channelsRef.current.has(channelName)) {
          console.warn(`Channel ${channelName} already exists. Using existing channel.`);
          return () => {}; // Return empty cleanup function
        }

        // Create a new channel with presence enabled and browser-compatible options
        const channel = supabase.channel(channelName, {
          config: {
            presence: { key: 'user-presence' },
            broadcast: { self: true }
          }
        });

        // Set up presence handlers
        if (onSync) {
          channel.on('presence', { event: 'sync' }, () => {
            const state = channel.presenceState();
            onSync(state);
          });
        }

        if (onJoin) {
          channel.on('presence', { event: 'join' }, onJoin);
        }

        if (onLeave) {
          channel.on('presence', { event: 'leave' }, onLeave);
        }

        // Subscribe and track presence
        channel.subscribe(async (status) => {
          if (status === 'SUBSCRIBED') {
            setIsConnected(true);
            console.log(`Subscribed to presence channel ${channelName}`);
            await channel.track(userState);
          } else if (status === 'CLOSED' || status === 'CHANNEL_ERROR') {
            setIsConnected(false);
            setLastError(`Presence connection to ${channelName} closed or errored`);
            console.error(`Presence channel ${channelName} status: ${status}`);
          }
        });

        // Store the channel reference
        channelsRef.current.set(channelName, channel);

        // Return a cleanup function
        return () => {
          channel.unsubscribe();
          channelsRef.current.delete(channelName);
        };
      } catch (error) {
        console.error('Error setting up presence subscription:', error);
        setLastError(`Failed to set up presence: ${error}`);
        return () => {};
      }
    },
    []
  );

  // Clean up all channels when component unmounts
  useEffect(() => {
    return () => {
      channelsRef.current.forEach((channel) => {
        channel.unsubscribe();
      });
      channelsRef.current.clear();
    };
  }, []);

  return {
    subscribe,
    subscribeToPresence,
    isConnected,
    lastError
  };
}
