
import React from 'react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { FormControl } from "@/components/ui/form";

interface ActivityCategorySelectProps {
  field: any;
}

export function ActivityCategorySelect({ field }: ActivityCategorySelectProps) {
  return (
    <Select onValueChange={field.onChange} defaultValue={field.value}>
      <FormControl>
        <SelectTrigger>
          <SelectValue placeholder="Select a category" />
        </SelectTrigger>
      </FormControl>
      <SelectContent>
        <SelectItem value="sports">Sports</SelectItem>
        <SelectItem value="education">Education</SelectItem>
        <SelectItem value="community">Community</SelectItem>
        <SelectItem value="entertainment">Entertainment</SelectItem>
        <SelectItem value="networking">Networking</SelectItem>
        <SelectItem value="other">Other</SelectItem>
      </SelectContent>
    </Select>
  );
}
