import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface Category {
  id: string;
  name: string;
  type: 'activity' | 'service';
  icon: string;
}

function isCategory(category: any): category is Category {
  // Validate that "type" is either "activity" or "service"
  return (
    typeof category.id === "string" &&
    typeof category.name === "string" &&
    (category.type === "activity" || category.type === "service") &&
    typeof category.icon === "string"
  );
}

export function useActivityCategories() {
  return useQuery({
    queryKey: ['categories', 'activity'],
    queryFn: async (): Promise<Category[]> => {
      const { data, error } = await supabase
        .from('categories')
        .select('*')
        .eq('type', 'activity');

      if (error) {
        throw error;
      }

      // Fix: Explicitly map and narrow the type property for TypeScript compatibility
      return (data ?? []).map(category => {
        // Coerce type to the string literal type if it matches
        if (category.type === 'activity' || category.type === 'service') {
          return {
            id: category.id,
            name: category.name,
            type: category.type as 'activity' | 'service',
            icon: category.icon ?? '',
          };
        }
        // Otherwise, ignore this entry
        return null;
      }).filter((c): c is Category => !!c);
    }
  });
}
