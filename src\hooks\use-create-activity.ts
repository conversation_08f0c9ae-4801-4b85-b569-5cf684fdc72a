
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { ActivityCreationData } from '@/types/activity';
import { locationToPostgresPoint } from '@/utils/location-normalizer';

export function useCreateActivity() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const mutation = useMutation({
    mutationFn: async (activity: ActivityCreationData): Promise<string> => {
      // Get the current user
      const { data: sessionData, error: sessionError } = await supabase.auth.getSession();
      if (sessionError) {
        throw new Error('You must be logged in to create an activity');
      }

      const userId = sessionData.session?.user.id;
      if (!userId) {
        throw new Error('User ID not found');
      }

      // Format the location as a PostgreSQL point
      const formattedLocation = locationToPostgresPoint(activity.location);

      // Insert the activity
      const { data, error } = await supabase.rpc('insert_activity', {
        p_title: activity.title,
        p_description: activity.description || '',
        p_category_id: activity.category_id || null,
        p_location_x: activity.location.x,
        p_location_y: activity.location.y,
        p_address: activity.address || '',
        p_start_time: activity.start_time,
        p_end_time: activity.end_time || null,
        p_is_paid: activity.is_paid,
        p_price: activity.price || 0,
        p_max_participants: activity.max_participants || null,
        p_host_id: userId,
        p_media_urls: activity.media_urls || [],
        p_queue_type: activity.queue_type || 'fifo',
        p_visibility: activity.visibility || 'public',
        p_allow_waitlist: activity.allow_waitlist ?? true,
        p_status: 'active'
      });

      if (error) {
        console.error('Error creating activity:', error);
        throw error;
      }

      return data[0].id;
    },
    onSuccess: (activityId) => {
      queryClient.invalidateQueries({ queryKey: ['activities'] });

      toast({
        title: "Activity created!",
        description: "Your activity has been successfully created.",
      });

      return activityId;
    },
    onError: (error: Error) => {
      console.error('Failed to create activity:', error);

      toast({
        title: "Error creating activity",
        description: error.message || "There was a problem creating your activity.",
        variant: "destructive"
      });
    }
  });

  return {
    createActivity: mutation.mutateAsync,
    isCreating: mutation.isPending
  };
}
