
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MapLocationSearch } from './MapLocationSearch';
import { MapPin, Loader2 } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { useMapboxToken } from '@/hooks/use-mapbox-token';
import { AddressMinimap } from '@/components/map/MapMarkers/AddressMinimap';
import { MapLocation, normalizeLocation } from '@/types/location';

interface EnhancedMapLocationPickerProps {
  onLocationSelect: (location: MapLocation) => void;
  defaultLocation?: { x?: number; y?: number; lng?: number; lat?: number };
  className?: string;
  title?: string;
  description?: string;
  mapboxToken?: string; // Make mapboxToken optional as we can use useMapboxToken hook
}

export function EnhancedMapLocationPicker({
  onLocationSelect,
  defaultLocation,
  className,
  title = "Select Location",
  description = "Search for a location or adjust the marker on the map",
  mapboxToken: providedMapboxToken // Rename to avoid conflicts with the hook
}: EnhancedMapLocationPickerProps) {
  const [address, setAddress] = useState('');
  const [selectedLocation, setSelectedLocation] = useState<MapLocation | undefined>(
    defaultLocation ? normalizeLocation(defaultLocation) : undefined
  );
  const { toast } = useToast();
  const { token: hooksMapboxToken, isLoading: tokenLoading, error: tokenError } = useMapboxToken();

  // Use provided token or fall back to the one from the hook
  const mapboxToken = providedMapboxToken || hooksMapboxToken;

  // Update selected location if defaultLocation changes
  useEffect(() => {
    if (defaultLocation) {
      setSelectedLocation(normalizeLocation(defaultLocation));
    }
  }, [defaultLocation]);

  const handleLocationSave = (location: { lng: number; lat: number; address: string; coordinates?: [number, number] }) => {
    console.log("Location saved in EnhancedMapLocationPicker:", location);

    const newLocation: MapLocation = {
      lng: location.lng,
      lat: location.lat,
      address: location.address,
      x: location.lng,
      y: location.lat
    };

    setAddress(location.address);
    setSelectedLocation(newLocation);
    onLocationSelect(newLocation);
  };

  const handleSearchSelect = (location: MapLocation) => {
    console.log("Search location selected:", location);

    // Update address from search
    setAddress(location.address || '');
    setSelectedLocation(location);

    // Pass the MapLocation object directly to parent
    onLocationSelect(location);
  };

  const handleSearch = (query: string) => {
    console.log("Searching for:", query);
  };

  if (tokenError && !providedMapboxToken) {
    return (
      <Card className={className}>
        <div className="p-4 text-center text-destructive">
          <p>Failed to load map. Please check your Mapbox configuration.</p>
        </div>
      </Card>
    );
  }

  if (tokenLoading && !providedMapboxToken) {
    return (
      <Card className={className}>
        <div className="p-4 flex items-center justify-center">
          <Loader2 className="h-6 w-6 animate-spin" />
        </div>
      </Card>
    );
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        {/* Thin search bar */}
        <div className="space-y-2">
          <div className="text-sm font-medium">Your Location <span className="text-red-500">*</span></div>
          <div className="text-xs text-gray-500">This is where you'll appear on the map to other users</div>
          <MapLocationSearch
            onSearch={handleSearch}
            onLocationSelect={handleSearchSelect}
            isLoading={false}
            className="w-full"
            mapboxToken={mapboxToken}
          />
        </div>

        {/* Thin map with marker */}
        <div className="border border-gray-200 rounded-md overflow-hidden">
          <AddressMinimap
            initialLocation={selectedLocation}
            onSaveLocation={handleLocationSave}
            height="250px"
            editable={true}
            showStyleToggle={false}
            showFooter={false}
            markerColor="#7C3AED"
            title={title}
            adjustButtonText="Adjust Location"
            saveButtonText="Save Location"
          />
        </div>

        {/* Location selection button */}
        <div className="flex justify-center">
          <Button
            variant="outline"
            className="bg-primary-purple/10 border-primary-purple/20 text-primary-purple hover:bg-primary-purple/20"
            onClick={() => {
              if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                  (position) => {
                    const location = {
                      lng: position.coords.longitude,
                      lat: position.coords.latitude,
                      address: "Current Location",
                      x: position.coords.longitude,
                      y: position.coords.latitude
                    };
                    handleLocationSave(location);
                  },
                  (error) => {
                    toast({
                      title: "Location Error",
                      description: "Unable to get your current location.",
                      variant: "destructive"
                    });
                  }
                );
              }
            }}
          >
            <MapPin className="h-4 w-4 mr-2" />
            Use My Current Location
          </Button>
        </div>
      </div>
    </div>
  );
}
