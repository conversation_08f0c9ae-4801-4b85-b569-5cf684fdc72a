
import React from 'react';
import { Button } from "@/components/ui/button";
import { MapPin, Activity } from "lucide-react";
import { cn } from "@/lib/utils";

interface MapTabsProps {
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export function MapTabs({ activeTab, onTabChange }: MapTabsProps) {
  return (
    <div className="bg-background/80 backdrop-blur-sm rounded-full p-1 flex gap-1 shadow-lg">
      <Button
        variant={activeTab === "explore" ? "default" : "ghost"}
        size="sm"
        onClick={() => onTabChange("explore")}
        className={cn(
          "rounded-full",
          activeTab === "explore" ? "bg-primary text-primary-foreground" : ""
        )}
      >
        <MapPin className="h-4 w-4 mr-2" />
        Explore
      </Button>
      <Button
        variant={activeTab === "activities" ? "default" : "ghost"}
        size="sm"
        onClick={() => onTabChange("activities")}
        className={cn(
          "rounded-full",
          activeTab === "activities" ? "bg-primary text-primary-foreground" : ""
        )}
      >
        <Activity className="h-4 w-4 mr-2" />
        Activities
      </Button>
    </div>
  );
}
