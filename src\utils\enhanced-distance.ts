
/**
 * Enhanced distance utilities for calculating and formatting distances
 */

// Calculate distance between two coordinates using Haversine formula
export function calculateDistance(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const R = 6371; // Earth radius in km
  const dLat = toRad(lat2 - lat1);
  const dLng = toRad(lng2 - lng1);
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.sin(dLng / 2) * Math.sin(dLng / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
  return R * c; // Distance in km
}

// Format distance in a human-readable way
export function formatDistance(distance: number, options?: { detailed?: boolean }): string {
  if (distance < 1) {
    // Less than 1 kilometer, show in meters
    const meters = Math.round(distance * 1000);
    return `${meters} m`;
  } else if (distance < 10) {
    // Less than 10 kilometers, show with one decimal
    return `${distance.toFixed(1)} km`;
  } else {
    // More than 10 kilometers, show as integer
    return `${Math.round(distance)} km`;
  }
}

// Convert degrees to radians
function toRad(degrees: number): number {
  return (degrees * Math.PI) / 180;
}

// Format last seen date with relative time
export function formatLastSeen(dateString: string): string {
  const now = new Date();
  const date = new Date(dateString);
  const diffMs = date.getTime() - now.getTime();
  
  // Calculate absolute difference in minutes
  const diffMins = Math.abs(Math.round(diffMs / (1000 * 60)));
  
  if (diffMs < 0) {
    // Past date
    if (diffMins < 60) return `${diffMins} min ago`;
    if (diffMins < 60 * 24) return `${Math.floor(diffMins / 60)} hours ago`;
    return `${Math.floor(diffMins / (60 * 24))} days ago`;
  } else {
    // Future date
    if (diffMins < 60) return `in ${diffMins} min`;
    if (diffMins < 60 * 24) return `in ${Math.floor(diffMins / 60)} hours`;
    return `in ${Math.floor(diffMins / (60 * 24))} days`;
  }
}

// Convert kilometers to miles (approximately)
export function kmToMiles(km: number): number {
  return km * 0.621371;
}

// Calculate distance in miles between two coordinates
export function calculateDistanceInMiles(lat1: number, lng1: number, lat2: number, lng2: number): number {
  return kmToMiles(calculateDistance(lat1, lng1, lat2, lng2));
}

// Format distance in miles
export function formatDistanceInMiles(distance: number): string {
  const miles = kmToMiles(distance);
  if (miles < 0.1) {
    // Very short distance, show in feet
    const feet = Math.round(miles * 5280);
    return `${feet} ft`;
  } else if (miles < 10) {
    // Less than 10 miles, show with one decimal
    return `${miles.toFixed(1)} mi`;
  } else {
    // More than 10 miles, show as integer
    return `${Math.round(miles)} mi`;
  }
}

// Get activity status based on last seen timestamp
export function getActivityStatus(lastActive: Date | string) {
  const now = new Date();
  const lastActiveDate = typeof lastActive === 'string' ? new Date(lastActive) : lastActive;
  const diffMs = now.getTime() - lastActiveDate.getTime();
  const diffMins = Math.floor(diffMs / (1000 * 60));
  
  if (diffMins < 5) {
    return { status: 'online', label: 'Online now' };
  } else if (diffMins < 60) {
    return { status: 'away', label: `${diffMins}m ago` };
  } else if (diffMins < 60 * 24) {
    return { status: 'offline', label: `${Math.floor(diffMins / 60)}h ago` };
  } else {
    return { status: 'offline', label: `${Math.floor(diffMins / (60 * 24))}d ago` };
  }
}
