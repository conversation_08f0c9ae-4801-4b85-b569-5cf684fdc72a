
import React from 'react';
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { UseFormReturn } from 'react-hook-form';
import { ActivityCreationValues } from '@/schemas/activity-creation-schema';

interface QueueConfigProps {
  form: UseFormReturn<ActivityCreationValues>;
}

export function QueueConfig({ form }: QueueConfigProps) {
  const showQueueSettings = form.watch('is_paid');

  return (
    <>
      <FormField
        control={form.control}
        name="queue_type"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Queue Type</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select queue type" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="fifo">First Come, First Served</SelectItem>
                <SelectItem value="priority">Priority Queue (Premium)</SelectItem>
              </SelectContent>
            </Select>
            <FormDescription>
              Choose how participants will be queued for this activity
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="allow_waitlist"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
            <div className="space-y-0.5">
              <FormLabel className="text-base">Allow Waitlist</FormLabel>
              <FormDescription>
                Allow users to join a waitlist when the activity is full
              </FormDescription>
            </div>
            <FormControl>
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
          </FormItem>
        )}
      />

      {showQueueSettings && (
        <>
          {/* Early bird price field */}
          <FormField
            control={form.control}
            name="early_bird_price"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Early Bird Price</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter early bird price"
                    value={field.value === null ? '' : field.value}
                    onChange={(e) => {
                      const value = e.target.value === '' ? null : Number(e.target.value);
                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Special price for early confirmations (leave empty to disable)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Group discount threshold field */}
          <FormField
            control={form.control}
            name="group_discount_threshold"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Group Discount Threshold</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Minimum group size"
                    value={field.value === null ? '' : field.value}
                    onChange={(e) => {
                      const value = e.target.value === '' ? null : Number(e.target.value);
                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Minimum number of people needed for a group discount (leave empty to disable)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Group discount percentage field */}
          <FormField
            control={form.control}
            name="group_discount_percentage"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Group Discount Percentage</FormLabel>
                <FormControl>
                  <Input
                    type="number"
                    placeholder="Enter discount percentage"
                    value={field.value === null ? '' : field.value}
                    onChange={(e) => {
                      const value = e.target.value === '' ? null : Number(e.target.value);
                      field.onChange(value);
                    }}
                  />
                </FormControl>
                <FormDescription>
                  Percentage discount for groups (leave empty to disable)
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </>
      )}
    </>
  );
}
