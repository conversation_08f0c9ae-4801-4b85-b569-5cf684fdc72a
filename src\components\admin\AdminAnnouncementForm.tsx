
import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Send, AlertCircle, Info } from 'lucide-react';
import { useAdminConversation } from '@/hooks/use-admin-conversation';
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useProfile } from '@/hooks/use-profile';
import { useAuth } from '@/hooks/use-auth';

export function AdminAnnouncementForm() {
  const [message, setMessage] = useState('');
  const { user } = useAuth();
  const { data: profile } = useProfile(user?.id);
  const { 
    adminConversation, 
    isAdmin,
    sendAdminMessage 
  } = useAdminConversation();
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!message.trim()) return;
    
    try {
      // Pass the message directly as a string
      await sendAdminMessage?.mutateAsync(message);
      setMessage('');
    } catch (error) {
      console.error('Error sending admin message:', error);
    }
  };
  
  if (!isAdmin) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Access Denied</AlertTitle>
        <AlertDescription>
          You don't have permission to send admin announcements.
        </AlertDescription>
      </Alert>
    );
  }
  
  if (!adminConversation) {
    return (
      <div className="flex items-center justify-center p-4">
        <Loader2 className="h-6 w-6 animate-spin text-primary mr-2" />
        <span>Loading admin conversation...</span>
      </div>
    );
  }
  
  return (
    <Card>
      <CardHeader>
        <CardTitle>Send Announcement</CardTitle>
        <CardDescription>
          Send an announcement to all users. This will appear in the Buddy Admin conversation.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit}>
          <div className="space-y-4">
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Announcements are sent to all users and cannot be deleted. Please review your message carefully before sending.
              </AlertDescription>
            </Alert>
            
            <Textarea
              placeholder="Type your announcement here..."
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              className="min-h-[120px]"
            />
          </div>
        </form>
      </CardContent>
      <CardFooter className="flex justify-end">
        <Button 
          onClick={handleSubmit} 
          disabled={!message.trim() || sendAdminMessage?.isPending}
        >
          {sendAdminMessage?.isPending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending...
            </>
          ) : (
            <>
              <Send className="mr-2 h-4 w-4" />
              Send Announcement
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}
