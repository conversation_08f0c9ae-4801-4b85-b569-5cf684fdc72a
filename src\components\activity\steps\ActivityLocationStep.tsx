import React from 'react';
import { UseFormReturn } from 'react-hook-form';
import { ActivityCreationValues } from '@/schemas/activity-creation-schema';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { EnhancedMapLocationPicker } from '@/components/map/EnhancedMapLocationPicker';
import { Loader2, MapPin } from 'lucide-react';
import { MapLocation } from '@/types/location';

interface ActivityLocationStepProps {
  form: UseFormReturn<ActivityCreationValues>;
  mapboxToken: string | null;
  onLocationSelect: (location: MapLocation) => void;
}

export function ActivityLocationStep({ form, mapboxToken, onLocationSelect }: ActivityLocationStepProps) {
  // Get current location values from form
  const location = form.watch('location');
  const address = form.watch('address');

  // Prepare default location if we have coordinates
  const defaultLocation = location.x !== 0 && location.y !== 0
    ? { x: location.x, y: location.y }
    : undefined;

  return (
    <div className="space-y-6">
      <div className="space-y-2">
        <h3 className="text-xl font-medium text-center text-primary-purple">Set Activity Location</h3>
        <p className="text-sm text-muted-foreground text-center">
          <span className="text-red-500">*</span> Required information
        </p>
      </div>

      <FormField
        control={form.control}
        name="location"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Location <span className="text-red-500">*</span></FormLabel>
            <FormControl>
              <div className="space-y-4">
                {mapboxToken ? (
                  <EnhancedMapLocationPicker
                    onLocationSelect={onLocationSelect}
                    mapboxToken={mapboxToken}
                    defaultLocation={defaultLocation}
                    title="Select Activity Location"
                    description="Search for a location or adjust the marker on the map"
                  />
                ) : (
                  <div className="flex items-center justify-center h-[300px] bg-gray-100 rounded-md">
                    <Loader2 className="h-6 w-6 animate-spin mr-2" />
                    Loading map...
                  </div>
                )}

                {address && (
                  <div className="flex items-center p-3 bg-muted rounded-md">
                    <MapPin className="h-5 w-5 mr-2 text-primary" />
                    <span className="text-sm">{address}</span>
                  </div>
                )}
              </div>
            </FormControl>
            <FormDescription>
              This is where participants will meet for your activity
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
