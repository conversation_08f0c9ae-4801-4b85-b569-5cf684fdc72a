
import React from 'react';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ActivityQueueEntry } from '@/types/activity';

interface ActivityQueueParticipantsProps {
  entries: ActivityQueueEntry[];
  isHost?: boolean;
  onUpdateStatus?: (entryId: string, status: 'confirmed' | 'cancelled') => void;
  isProcessing?: boolean;
  className?: string;
}

export function ActivityQueueParticipants({
  entries,
  isHost = false,
  onUpdateStatus,
  isProcessing = false,
  className = ""
}: ActivityQueueParticipantsProps) {
  // Group entries by status
  const confirmedEntries = entries.filter(entry => entry.status === 'confirmed');
  const pendingEntries = entries.filter(entry => entry.status === 'pending');
  const waitlistedEntries = entries.filter(entry => entry.status === 'waitlisted');

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Confirmed participants */}
      {confirmedEntries.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Confirmed ({confirmedEntries.length})</h4>
          <div className="flex flex-wrap gap-2">
            {confirmedEntries.map(entry => (
              <div key={entry.id} className="flex items-center bg-green-50 p-2 rounded-md">
                <Avatar className="h-6 w-6 mr-2">
                  <AvatarImage src={entry.user?.avatar_url} />
                  <AvatarFallback>
                    {entry.user?.display_name?.substring(0, 2) || 
                     entry.user?.username?.substring(0, 2) || 
                     'U'}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm">{entry.user?.display_name || entry.user?.username || 'User'}</span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pending participants - only shown to host */}
      {isHost && pendingEntries.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2 flex justify-between">
            <span>Pending ({pendingEntries.length})</span>
            {pendingEntries.length > 0 && (
              <Badge variant="outline" className="bg-yellow-50 text-yellow-700">Needs Review</Badge>
            )}
          </h4>
          <div className="space-y-2">
            {pendingEntries.map(entry => (
              <div key={entry.id} className="flex items-center justify-between bg-yellow-50 p-2 rounded-md">
                <div className="flex items-center">
                  <Avatar className="h-6 w-6 mr-2">
                    <AvatarImage src={entry.user?.avatar_url} />
                    <AvatarFallback>
                      {entry.user?.display_name?.substring(0, 2) || 
                       entry.user?.username?.substring(0, 2) || 
                       'U'}
                    </AvatarFallback>
                  </Avatar>
                  <span className="text-sm">{entry.user?.display_name || entry.user?.username || 'User'}</span>
                </div>
                {onUpdateStatus && (
                  <div className="flex gap-2">
                    <button 
                      onClick={() => onUpdateStatus(entry.id, 'confirmed')}
                      disabled={isProcessing}
                      className="text-xs px-2 py-1 bg-green-100 text-green-700 rounded hover:bg-green-200 disabled:opacity-50"
                    >
                      Approve
                    </button>
                    <button 
                      onClick={() => onUpdateStatus(entry.id, 'cancelled')}
                      disabled={isProcessing}
                      className="text-xs px-2 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200 disabled:opacity-50"
                    >
                      Reject
                    </button>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Waitlisted participants */}
      {isHost && waitlistedEntries.length > 0 && (
        <div>
          <h4 className="text-sm font-medium mb-2">Waitlist ({waitlistedEntries.length})</h4>
          <div className="flex flex-wrap gap-2">
            {waitlistedEntries.map(entry => (
              <div key={entry.id} className="flex items-center bg-blue-50 p-2 rounded-md">
                <Avatar className="h-6 w-6 mr-2">
                  <AvatarImage src={entry.user?.avatar_url} />
                  <AvatarFallback>
                    {entry.user?.display_name?.substring(0, 2) || 
                     entry.user?.username?.substring(0, 2) || 
                     'U'}
                  </AvatarFallback>
                </Avatar>
                <span className="text-sm">{entry.user?.display_name || entry.user?.username || 'User'}</span>
                <Badge variant="secondary" className="ml-2 text-xs">#{entry.position}</Badge>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* No participants message */}
      {entries.length === 0 && (
        <div className="text-center py-4 text-muted-foreground">
          No participants yet. Be the first to join!
        </div>
      )}
    </div>
  );
}
