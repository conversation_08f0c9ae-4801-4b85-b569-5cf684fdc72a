
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';

export interface SendMessageData {
  conversation_id: string;
  content: string;
  message_type?: string;
  media_url?: string;
  location_data?: any;
  recipient_id?: string;
}

export function useSendMessage() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (messageData: SendMessageData) => {
      if (!user) {
        throw new Error('User not authenticated');
      }

      const { data, error } = await supabase
        .from('messages')
        .insert({
          sender_id: user.id,
          ...messageData,
        })
        .select()
        .single();

      if (error) throw error;
      return data;
    },
    onSuccess: (data) => {
      // Invalidate messages query to refresh the chat
      queryClient.invalidateQueries({
        queryKey: ['messages', data.conversation_id]
      });
    }
  });
}
