
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

export function useRecalculatePositions() {
  const queryClient = useQueryClient();
  const { toast } = useToast();

  const mutation = useMutation({
    mutationFn: async (activityId: string) => {
      const { error } = await supabase.rpc('recalculate_queue_positions', {
        p_activity_id: activityId
      });

      if (error) throw error;
      
      return activityId;
    },
    onSuccess: (activityId) => {
      queryClient.invalidateQueries({ queryKey: ['activity-queue', activityId] });
    },
    onError: (error) => {
      console.error('Error recalculating queue positions:', error);
      toast({
        title: "Queue update failed",
        description: "Failed to recalculate queue positions.",
        variant: "destructive"
      });
    }
  });

  return mutation.mutateAsync;
}
