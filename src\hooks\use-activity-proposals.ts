import { useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';
import { useToast } from '@/hooks/use-toast';

export interface ActivityProposal {
  id: string;
  conversation_id: string;
  sender_id: string;
  content: string;
  message_type: 'activity_proposal';
  metadata: {
    activity_id: string;
    activity_title: string;
    proposal_status: 'pending' | 'accepted' | 'declined';
  };
  created_at: string;
  sender?: {
    id: string;
    display_name?: string;
    avatar_url?: string;
    username?: string;
  };
}

export function useSendActivityProposal() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      conversationId,
      activityId,
      message = 'Check out this activity!'
    }: {
      conversationId: string;
      activityId: string;
      message?: string;
    }) => {
      if (!user) {
        throw new Error('User must be authenticated');
      }

      const { data, error } = await supabase.rpc('send_activity_proposal', {
        p_conversation_id: conversationId,
        p_sender_id: user.id,
        p_activity_id: activityId,
        p_message: message
      });

      if (error) throw error;
      return data;
    },
    onSuccess: (_, variables) => {
      // Invalidate messages for the conversation
      queryClient.invalidateQueries({
        queryKey: ['messages', variables.conversationId]
      });
      queryClient.invalidateQueries({
        queryKey: ['activity-group-messages', variables.conversationId]
      });
      
      toast({
        title: "Activity proposed!",
        description: "Your activity proposal has been sent.",
      });
    },
    onError: (error) => {
      console.error('Error sending activity proposal:', error);
      toast({
        title: "Error",
        description: "Failed to send activity proposal. Please try again.",
        variant: "destructive"
      });
    }
  });
}

export function useRespondToActivityProposal() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      messageId,
      response
    }: {
      messageId: string;
      response: 'accepted' | 'declined';
    }) => {
      if (!user) {
        throw new Error('User must be authenticated');
      }

      const { data, error } = await supabase.rpc('respond_to_activity_proposal', {
        p_message_id: messageId,
        p_user_id: user.id,
        p_response: response
      });

      if (error) throw error;
      return data;
    },
    onSuccess: (_, variables) => {
      // Invalidate relevant queries
      queryClient.invalidateQueries({
        queryKey: ['messages']
      });
      queryClient.invalidateQueries({
        queryKey: ['activity-group-messages']
      });
      queryClient.invalidateQueries({
        queryKey: ['activity-queue']
      });
      
      const responseText = variables.response === 'accepted' ? 'accepted' : 'declined';
      toast({
        title: `Proposal ${responseText}`,
        description: `You have ${responseText} the activity proposal.`,
      });
    },
    onError: (error) => {
      console.error('Error responding to activity proposal:', error);
      toast({
        title: "Error",
        description: "Failed to respond to proposal. Please try again.",
        variant: "destructive"
      });
    }
  });
}

export function useSendActivityShare() {
  const { user } = useAuth();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      conversationId,
      activityId,
      message = 'Check out this activity!'
    }: {
      conversationId: string;
      activityId: string;
      message?: string;
    }) => {
      if (!user) {
        throw new Error('User must be authenticated');
      }

      // Get activity details for the share
      const { data: activity, error: activityError } = await supabase
        .from('activities')
        .select('id, title, description, start_time, location, address')
        .eq('id', activityId)
        .single();

      if (activityError) throw activityError;

      // Send the share message
      const { data, error } = await supabase
        .from('messages')
        .insert({
          conversation_id: conversationId,
          sender_id: user.id,
          content: message,
          message_type: 'activity_share',
          metadata: {
            activity_id: activityId,
            activity_title: activity.title,
            activity_description: activity.description,
            activity_start_time: activity.start_time,
            activity_location: activity.location,
            activity_address: activity.address
          }
        })
        .select()
        .single();

      if (error) throw error;

      // Update conversation last message
      await supabase
        .from('chat_conversations')
        .update({
          last_message: `Shared activity: ${activity.title}`,
          last_message_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .eq('id', conversationId);

      return data;
    },
    onSuccess: (_, variables) => {
      // Invalidate messages for the conversation
      queryClient.invalidateQueries({
        queryKey: ['messages', variables.conversationId]
      });
      queryClient.invalidateQueries({
        queryKey: ['activity-group-messages', variables.conversationId]
      });
      queryClient.invalidateQueries({
        queryKey: ['conversations']
      });
      
      toast({
        title: "Activity shared!",
        description: "The activity has been shared in the chat.",
      });
    },
    onError: (error) => {
      console.error('Error sharing activity:', error);
      toast({
        title: "Error",
        description: "Failed to share activity. Please try again.",
        variant: "destructive"
      });
    }
  });
}
