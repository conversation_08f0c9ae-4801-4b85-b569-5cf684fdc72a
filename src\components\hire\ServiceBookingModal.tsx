import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Calendar } from "@/components/ui/calendar";
import { format } from "date-fns";
import { useCreateBooking } from "@/hooks/use-service-bookings";
import { useServiceBookingPayment } from "@/hooks/use-service-booking-payment";
import { Loader2 } from "lucide-react";

interface ServiceProvider {
  id: string;
  name: string;
  hourlyRate: number;
  avatarUrl?: string;
}

interface Gig {
  id: string;
  title: string;
  description: string;
  price_starting: number;
  provider_id: string;
}

interface ServiceBookingModalProps {
  provider?: ServiceProvider;
  gig?: Gig;
  isOpen: boolean;
  onClose: () => void;
}

const ServiceBookingModal: React.FC<ServiceBookingModalProps> = ({
  provider,
  gig,
  isOpen,
  onClose,
}) => {
  const [date, setDate] = useState<Date>();
  const [duration, setDuration] = useState<string>("2");
  const [title, setTitle] = useState<string>(gig?.title || "");
  const [description, setDescription] = useState<string>("");
  const [customPrice, setCustomPrice] = useState<string>("");

  const createBooking = useCreateBooking();
  const processPayment = useServiceBookingPayment();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!date || !provider) return;

    try {
      // Calculate price
      let finalPrice = 0;
      if (gig) {
        finalPrice = gig.price_starting;
      } else if (customPrice) {
        finalPrice = parseFloat(customPrice);
      } else {
        finalPrice = provider.hourlyRate * parseInt(duration);
      }

      // Create the booking
      const booking = await createBooking.mutateAsync({
        gig_id: gig?.id,
        provider_id: provider.id,
        title: title || `Service from ${provider.name}`,
        description: description,
        price: finalPrice,
        scheduled_for: date.toISOString(),
        delivery_date: gig ? undefined : date.toISOString(),
      });

      // Redirect to payment
      await processPayment.mutateAsync(booking.id);
      
      onClose();
    } catch (error) {
      console.error('Error creating booking:', error);
    }
  };

  const isLoading = createBooking.isPending || processPayment.isPending;

  // Calculate total price
  let totalPrice = 0;
  if (gig) {
    totalPrice = gig.price_starting;
  } else if (customPrice) {
    totalPrice = parseFloat(customPrice) || 0;
  } else if (provider) {
    totalPrice = provider.hourlyRate * parseInt(duration);
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>
            {gig ? `Book: ${gig.title}` : `Book ${provider?.name}`}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-4">
              <div>
                <Label>Select Date</Label>
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  className="rounded-md border mt-2"
                  disabled={(date) => date < new Date()}
                />
              </div>

              {!gig && (
                <div>
                  <Label htmlFor="duration">Duration (hours)</Label>
                  <Select value={duration} onValueChange={setDuration}>
                    <SelectTrigger id="duration">
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 hour</SelectItem>
                      <SelectItem value="2">2 hours</SelectItem>
                      <SelectItem value="3">3 hours</SelectItem>
                      <SelectItem value="4">4 hours</SelectItem>
                      <SelectItem value="6">6 hours</SelectItem>
                      <SelectItem value="8">8 hours</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              )}

              {!gig && (
                <div>
                  <Label htmlFor="customPrice">Custom Price (optional)</Label>
                  <Input
                    id="customPrice"
                    type="number"
                    placeholder="Enter custom price"
                    value={customPrice}
                    onChange={(e) => setCustomPrice(e.target.value)}
                  />
                </div>
              )}
            </div>

            {/* Right Column */}
            <div className="space-y-4">
              <div>
                <Label htmlFor="title">Service Title</Label>
                <Input
                  id="title"
                  placeholder="What service do you need?"
                  value={title}
                  onChange={(e) => setTitle(e.target.value)}
                  disabled={!!gig}
                />
              </div>

              <div>
                <Label htmlFor="description">Description</Label>
                <Textarea
                  id="description"
                  placeholder="Describe your requirements..."
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="h-32"
                />
              </div>

              <div className="border-t pt-4">
                {gig ? (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Gig Price:</span>
                      <span>${gig.price_starting}</span>
                    </div>
                    <div className="flex justify-between font-bold">
                      <span>Total:</span>
                      <span>${totalPrice}</span>
                    </div>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Rate per hour:</span>
                      <span>${provider?.hourlyRate}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Duration:</span>
                      <span>{duration} hours</span>
                    </div>
                    {customPrice && (
                      <div className="flex justify-between">
                        <span>Custom Price:</span>
                        <span>${customPrice}</span>
                      </div>
                    )}
                    <div className="flex justify-between font-bold">
                      <span>Total:</span>
                      <span>${totalPrice}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-4">
            <Button type="button" variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={!date || !title || isLoading}
            >
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              {isLoading ? 'Processing...' : 'Book & Pay'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};

export default ServiceBookingModal;
