
import { Loader2 } from 'lucide-react';

interface MapLoadingOverlayProps {
  isLoading: boolean;
}

export function MapLoadingOverlay({ isLoading }: MapLoadingOverlayProps) {
  if (!isLoading) return null;
  
  return (
    <div className="absolute inset-0 flex items-center justify-center bg-background/50 backdrop-blur-sm z-50">
      <div className="bg-background rounded-lg p-4 shadow-lg">
        <div className="flex items-center gap-3">
          <Loader2 className="h-5 w-5 animate-spin text-primary" />
          <p className="text-sm font-medium">Loading map...</p>
        </div>
      </div>
    </div>
  );
}
