
import React, { useState } from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Plus, MapPin, Calendar, Search, Filter, Activity as ActivityIcon, Users, Compass, Sparkles } from "lucide-react";
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/hooks/use-auth';
import { ActivityCreationModal } from '@/components/activity/ActivityCreationModal';
import { EnhancedActivityCard } from '@/components/activity/EnhancedActivityCard';
import { useActivities } from '@/hooks/use-activities';
import { useUserActivities } from '@/hooks/use-user-activities';
import { Activity } from '@/types/activity';
import { MainLayout } from '@/components/layouts/MainLayout';

const ActivitySurfPage: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [activeTab, setActiveTab] = useState('explore');

  const { activities, isLoading } = useActivities({ limit: 10 });
  const {
    data: userActivitiesData,
    isLoading: isLoadingUserActivities
  } = useUserActivities({
    filter: 'all',
    limit: 10
  });

  const handleCreateActivity = () => {
    if (!user) {
      navigate('/login');
      return;
    }
    setShowCreateModal(true);
  };

  const handleViewDetails = (activity: Activity) => {
    console.log('Viewing activity details:', activity);
  };

  const renderActivityGrid = (activities: Activity[], loading: boolean) => {
    if (loading) {
      return (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <Card key={i} className="animate-pulse">
              <div className="h-48 bg-gray-200 rounded-t-lg"></div>
              <CardContent className="p-4">
                <div className="h-4 bg-gray-200 rounded mb-2"></div>
                <div className="h-3 bg-gray-200 rounded mb-4"></div>
                <div className="flex justify-between">
                  <div className="h-8 bg-gray-200 rounded w-20"></div>
                  <div className="h-8 bg-gray-200 rounded w-16"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      );
    }

    if (!activities || activities.length === 0) {
      return (
        <Card className="col-span-full">
          <CardContent className="flex flex-col items-center justify-center py-12">
            <Calendar className="h-16 w-16 text-gray-400 mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {activeTab === 'explore' ? 'No activities found' : 'No activities yet'}
            </h3>
            <p className="text-gray-500 text-center mb-4">
              {activeTab === 'explore'
                ? 'Check back later for new activities in your area.'
                : 'Create your first activity to get started!'}
            </p>
            {activeTab !== 'explore' && (
              <Button onClick={handleCreateActivity}>
                <Plus className="h-4 w-4 mr-2" />
                Create Activity
              </Button>
            )}
          </CardContent>
        </Card>
      );
    }

    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {activities.map((activity) => (
          <EnhancedActivityCard
            key={activity.id}
            activity={activity}
            onClick={() => handleViewDetails(activity)}
          />
        ))}
      </div>
    );
  };

  return (
    <MainLayout>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Enhanced Activity Header */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 text-white relative overflow-hidden">
        {/* Background decorations */}
        <div className="absolute inset-0">
          <div className="absolute -top-24 -right-24 w-96 h-96 bg-white/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-32 -left-32 w-96 h-96 bg-white/5 rounded-full blur-3xl"></div>
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-white/5 rounded-full blur-2xl"></div>
        </div>

        <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
          <div className="text-center">
            {/* Icon and title */}
            <div className="flex justify-center mb-6">
              <div className="bg-white/20 backdrop-blur-sm rounded-full p-4 border border-white/30">
                <ActivityIcon className="h-12 w-12 text-white" />
              </div>
            </div>

            <h1 className="text-4xl font-bold sm:text-5xl md:text-6xl mb-4">
              Activity Surf
              <span className="block text-2xl sm:text-3xl md:text-4xl font-normal mt-2 text-blue-100">
                Discover Amazing Experiences
              </span>
            </h1>

            <p className="mt-6 max-w-2xl mx-auto text-lg sm:text-xl text-blue-100 leading-relaxed">
              Join exciting activities, meet like-minded people, and create unforgettable memories in your area.
            </p>

            {/* Stats */}
            <div className="mt-8 flex justify-center space-x-8">
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Users className="h-5 w-5 mr-2 text-blue-200" />
                  <span className="text-2xl font-bold text-white">{activities.length}</span>
                </div>
                <p className="text-sm text-blue-200">Active Events</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Compass className="h-5 w-5 mr-2 text-blue-200" />
                  <span className="text-2xl font-bold text-white">50+</span>
                </div>
                <p className="text-sm text-blue-200">Categories</p>
              </div>
              <div className="text-center">
                <div className="flex items-center justify-center mb-2">
                  <Sparkles className="h-5 w-5 mr-2 text-blue-200" />
                  <span className="text-2xl font-bold text-white">1K+</span>
                </div>
                <p className="text-sm text-blue-200">Members</p>
              </div>
            </div>

            {/* Action buttons */}
            <div className="mt-10 flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleCreateActivity}
                size="lg"
                className="bg-white text-blue-600 hover:bg-blue-50 font-semibold px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <Plus className="h-5 w-5 mr-2" />
                Create Activity
              </Button>
              <Button
                variant="outline"
                size="lg"
                className="border-white/30 text-white hover:bg-white/10 backdrop-blur-sm font-semibold px-8 py-3"
                onClick={() => navigate('/meetmap')}
              >
                <MapPin className="h-5 w-5 mr-2" />
                Explore Map
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Quick Actions Bar */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
          <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <div className="flex items-center space-x-4">
              <h2 className="text-xl font-semibold text-gray-900">Browse Activities</h2>
              <Badge variant="secondary" className="bg-blue-100 text-blue-700">
                {activities.length} available
              </Badge>
            </div>
            <div className="flex flex-wrap gap-2">
              <Button variant="outline" size="sm" className="hover:bg-blue-50">
                <Search className="h-4 w-4 mr-2" />
                Search
              </Button>
              <Button variant="outline" size="sm" className="hover:bg-blue-50">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
              <Button
                onClick={handleCreateActivity}
                size="sm"
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create
              </Button>
            </div>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <div className="flex justify-center mb-8">
            <TabsList className="grid w-full max-w-md grid-cols-3 bg-white shadow-sm">
              <TabsTrigger value="explore" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                Explore
              </TabsTrigger>
              <TabsTrigger value="joined" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                Joined
              </TabsTrigger>
              <TabsTrigger value="hosting" className="data-[state=active]:bg-blue-600 data-[state=active]:text-white">
                Hosting
              </TabsTrigger>
            </TabsList>
          </div>

          <TabsContent value="explore" className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-medium text-gray-700 mb-2">Discover New Adventures</h3>
              <p className="text-gray-500">Find exciting activities happening around you</p>
            </div>
            {renderActivityGrid(activities, isLoading)}
          </TabsContent>

          <TabsContent value="joined" className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-medium text-gray-700 mb-2">Your Joined Activities</h3>
              <p className="text-gray-500">Activities you've signed up for</p>
            </div>
            {renderActivityGrid(userActivitiesData || [], isLoadingUserActivities)}
          </TabsContent>

          <TabsContent value="hosting" className="space-y-6">
            <div className="text-center mb-6">
              <h3 className="text-lg font-medium text-gray-700 mb-2">Activities You're Hosting</h3>
              <p className="text-gray-500">Manage and track your created activities</p>
            </div>
            {renderActivityGrid(userActivitiesData || [], isLoadingUserActivities)}
          </TabsContent>
        </Tabs>
      </div>

      <ActivityCreationModal
        isOpen={showCreateModal}
        onClose={() => setShowCreateModal(false)}
      />
      </div>
    </MainLayout>
  );
};

export default ActivitySurfPage;
