
import React from "react";
import { Link, useLocation } from "react-router-dom";
import { Home, MapPin, Users, Briefcase, DollarSign, MessageCircle, User, Settings, Shield, Activity, Compass, Star } from "lucide-react";
import { cn } from "@/lib/utils";

const navItems = [
  { label: "Nearby", icon: MapPin, to: "/meetmap" },
  { label: "Activity", icon: Activity, to: "/activity" },
  { label: "Hire", icon: Briefcase, to: "/hire" },
];

const mainLinks = [
  { label: "Home", icon: Home, to: "/home" },
  { label: "MeetUp", icon: Users, to: "/meetup" },
  { label: "MeetMap", icon: Compass, to: "/meetmap" },
  { label: "Activity Surf", icon: Activity, to: "/activity" },
  { label: "Hire Friend", icon: Briefcase, to: "/hire" },
  { label: "Chat", icon: MessageCircle, to: "/chat" },
  { label: "Earn", icon: DollarSign, to: "/earn" },
  { label: "Profile", icon: User, to: "/profile" },
  { label: "Settings", icon: Settings, to: "/settings" },
  { label: "Admin", icon: Shield, to: "/admin" },
];

export function AppSidebar() {
  const location = useLocation();

  return (
    <aside className="bg-gradient-to-br from-white via-[#f0f0f0] to-[#e3eeff] shadow-xl flex flex-col h-full w-[90px] md:w-64 fixed z-40 left-0 top-0 border-r border-[#e3eeff]">
      <div className="flex-shrink-0 flex items-center justify-center md:justify-start px-4 py-3 border-b border-[#e3eeff]">
        <span className="text-xl font-extrabold text-[#1E90FF] tracking-tight">Buddy<span className="text-[#FFA500]">Surf</span></span>
      </div>
      {/* Pinned Nav Items */}
      <nav className="flex flex-col items-center md:items-stretch mt-4 gap-1">
        {navItems.map(({ label, icon: Icon, to }) => (
          <Link
            key={to}
            to={to}
            className={cn(
              "flex items-center px-4 py-2.5 rounded-xl gap-3 transition-colors w-full md:w-auto",
              location.pathname === to
                ? "bg-blue-50 text-blue-600 font-medium"
                : "hover:bg-gray-50 text-gray-700"
            )}
            aria-current={location.pathname === to ? "page" : undefined}
          >
            <Icon className="mr-0 md:mr-3 text-[18px]" />
            <span className="hidden md:inline">{label}</span>
          </Link>
        ))}
      </nav>
      {/* Main Navigation */}
      <div className="flex-1 overflow-y-auto p-2">
        <div className="space-y-1 mt-6">
          {mainLinks.map(({ label, icon: Icon, to }) => (
            <Link
              key={to}
              to={to}
              className={cn(
                "flex items-center px-4 py-2.5 rounded-xl gap-3 transition-colors w-full",
                location.pathname === to
                  ? "bg-blue-50 text-blue-600 font-medium"
                  : "hover:bg-gray-50 text-gray-700"
              )}
              aria-current={location.pathname === to ? "page" : undefined}
            >
              <Icon className="text-[18px]" />
              <span>{label}</span>
            </Link>
          ))}
        </div>
      </div>
    </aside>
  );
}
