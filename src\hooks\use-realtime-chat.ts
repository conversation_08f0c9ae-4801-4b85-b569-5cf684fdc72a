
import { useState, useEffect, useCallback, useRef } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { supabase } from '@/integrations/supabase/client';

interface UserPresence {
  user_id: string;
  last_seen: string;
  is_online: boolean;
}

interface UseRealtimeChatOptions {
  conversationId?: string;
  enableTyping?: boolean;
  enablePresence?: boolean;
  enableReactions?: boolean;
  enableReadReceipts?: boolean;
}

export function useRealtimeChat({
  conversationId,
  enableTyping = false,
  enablePresence = false,
  enableReactions = false,
  enableReadReceipts = false
}: UseRealtimeChatOptions) {
  const { user } = useAuth();
  const [isConnected, setIsConnected] = useState(false);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [onlineUsers, setOnlineUsers] = useState<UserPresence[]>([]);
  const channelRef = useRef<any>(null);

  const startTyping = useCallback(() => {
    if (!enableTyping || !conversationId || !user) return;
    
    if (channelRef.current) {
      channelRef.current.send({
        type: 'broadcast',
        event: 'typing',
        payload: { user_id: user.id, typing: true }
      });
    }
  }, [conversationId, user, enableTyping]);

  const stopTyping = useCallback(() => {
    if (!enableTyping || !conversationId || !user) return;
    
    if (channelRef.current) {
      channelRef.current.send({
        type: 'broadcast',
        event: 'typing',
        payload: { user_id: user.id, typing: false }
      });
    }
  }, [conversationId, user, enableTyping]);

  const markAsRead = useCallback(async (messageId: string) => {
    if (!enableReadReceipts || !user) return;
    
    try {
      await supabase
        .from('messages')
        .update({ is_read: true })
        .eq('id', messageId)
        .eq('recipient_id', user.id);
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  }, [user, enableReadReceipts]);

  useEffect(() => {
    if (!conversationId || !user) return;

    try {
      const channel = supabase.channel(`chat:${conversationId}`, {
        config: {
          broadcast: { self: false },
          presence: { key: user.id }
        }
      });

      if (enablePresence) {
        channel
          .on('presence', { event: 'sync' }, () => {
            const presenceState = channel.presenceState();
            const users = Object.values(presenceState).flat().map((presence: any) => ({
              user_id: presence.user_id || '',
              last_seen: new Date().toISOString(),
              is_online: true
            })) as UserPresence[];
            setOnlineUsers(users);
          })
          .on('presence', { event: 'join' }, ({ newPresences }) => {
            const users = newPresences.map((presence: any) => ({
              user_id: presence.user_id || '',
              last_seen: new Date().toISOString(),
              is_online: true
            })) as UserPresence[];
            setOnlineUsers(prev => [...prev, ...users]);
          })
          .on('presence', { event: 'leave' }, ({ leftPresences }) => {
            const leftUserIds = leftPresences.map((p: any) => p.user_id);
            setOnlineUsers(prev => prev.filter(u => !leftUserIds.includes(u.user_id)));
          });
      }

      if (enableTyping) {
        channel.on('broadcast', { event: 'typing' }, ({ payload }) => {
          if (payload.user_id === user.id) return;
          
          setTypingUsers(prev => {
            if (payload.typing) {
              return prev.includes(payload.user_id) ? prev : [...prev, payload.user_id];
            } else {
              return prev.filter(id => id !== payload.user_id);
            }
          });
        });
      }

      channel.subscribe((status) => {
        setIsConnected(status === 'SUBSCRIBED');
      });

      channelRef.current = channel;

      return () => {
        supabase.removeChannel(channel);
        channelRef.current = null;
      };
    } catch (error) {
      console.error('Error setting up realtime chat:', error);
      setIsConnected(false);
    }
  }, [conversationId, user, enableTyping, enablePresence]);

  return {
    isConnected,
    typingUsers,
    onlineUsers,
    startTyping,
    stopTyping,
    markAsRead
  };
}
