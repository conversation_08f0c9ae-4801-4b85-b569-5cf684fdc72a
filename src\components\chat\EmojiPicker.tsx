
import React from 'react';
import { Button } from '@/components/ui/button';

interface EmojiPickerProps {
  onEmojiClick: (emoji: string) => void;
  onClose: () => void;
}

export function EmojiPicker({ onEmojiClick, onClose }: EmojiPickerProps) {
  const emojis = [
    '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩',
    '🥳', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣',
    '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉',
    '👆', '👇', '☝️', '✋', '🤚', '🖐️', '🖖', '👋', '🤝', '👏',
    '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔',
    '❣️', '💕', '💞', '💓', '💗', '💖', '💘', '💝', '💟', '☮️'
  ];

  return (
    <div className="bg-white border border-gray-300 rounded-lg shadow-lg p-4 w-64 max-h-48 overflow-y-auto">
      <div className="grid grid-cols-8 gap-2">
        {emojis.map((emoji, index) => (
          <Button
            key={index}
            variant="ghost"
            size="sm"
            className="w-8 h-8 p-0 text-lg hover:bg-gray-100"
            onClick={() => onEmojiClick(emoji)}
          >
            {emoji}
          </Button>
        ))}
      </div>
      <div className="mt-2 pt-2 border-t">
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={onClose}
        >
          Close
        </Button>
      </div>
    </div>
  );
}
