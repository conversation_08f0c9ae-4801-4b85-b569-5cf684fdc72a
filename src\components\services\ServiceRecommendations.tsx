import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { 
  Star, 
  TrendingUp, 
  Award, 
  Clock, 
  MapPin,
  ArrowRight,
  Sparkles,
  Heart,
  Eye
} from "lucide-react";
import { useServiceRecommendations, useFeaturedServices, useTrackServiceInteraction } from "@/hooks/use-advanced-service-search";
import { useTrendingCategories } from "@/hooks/use-service-categories";
import { Gig } from "@/hooks/use-gigs";
import { getInitials } from "@/utils/get-initials";
import { useNavigate } from 'react-router-dom';
import { cn } from "@/lib/utils";

interface ServiceRecommendationsProps {
  className?: string;
}

export function ServiceRecommendations({ className }: ServiceRecommendationsProps) {
  const navigate = useNavigate();
  const trackInteraction = useTrackServiceInteraction();
  
  const { data: recommendations = [] } = useServiceRecommendations(8);
  const { data: featuredServices = [] } = useFeaturedServices('editor_choice', 6);
  const { data: trendingCategories = [] } = useTrendingCategories(5);

  const handleServiceClick = (gig: Gig) => {
    trackInteraction.mutate({
      gigId: gig.id,
      categoryId: gig.category_id,
      interactionType: 'view'
    });
    navigate(`/hire/gig/${gig.id}`);
  };

  const handleCategoryClick = (categoryId: string) => {
    navigate(`/hire?category=${categoryId}`);
  };

  const renderServiceCard = (gig: Gig, size: 'sm' | 'md' = 'md') => {
    const isSmall = size === 'sm';
    
    return (
      <Card 
        key={gig.id} 
        className={cn(
          "cursor-pointer hover:shadow-md transition-shadow",
          isSmall ? "h-48" : "h-64"
        )}
        onClick={() => handleServiceClick(gig)}
      >
        <CardContent className={cn("p-4", isSmall && "p-3")}>
          <div className="flex items-start gap-3 mb-3">
            <Avatar className={cn(isSmall ? "w-8 h-8" : "w-10 h-10")}>
              <AvatarImage src={gig.provider?.user?.avatar_url || ''} />
              <AvatarFallback>
                {getInitials(gig.provider?.user?.display_name || gig.provider?.name || 'U')}
              </AvatarFallback>
            </Avatar>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-2 mb-1">
                <h4 className={cn(
                  "font-medium truncate",
                  isSmall ? "text-sm" : "text-base"
                )}>
                  {gig.provider?.user?.display_name || gig.provider?.name}
                </h4>
                {gig.provider?.is_verified && (
                  <Badge variant="secondary" className="text-xs">
                    <Award className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-2 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Star className="w-3 h-3 fill-yellow-400 text-yellow-400" />
                  <span>{gig.average_rating.toFixed(1)}</span>
                  <span>({gig.total_reviews})</span>
                </div>
                {gig.category && (
                  <>
                    <span>•</span>
                    <span>{gig.category.name}</span>
                  </>
                )}
              </div>
            </div>
          </div>

          <h3 className={cn(
            "font-semibold mb-2 line-clamp-2",
            isSmall ? "text-sm" : "text-base"
          )}>
            {gig.title}
          </h3>

          <p className={cn(
            "text-muted-foreground line-clamp-2 mb-3",
            isSmall ? "text-xs" : "text-sm"
          )}>
            {gig.description}
          </p>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Clock className="w-3 h-3" />
              <span>{gig.delivery_time_days} day{gig.delivery_time_days !== 1 ? 's' : ''}</span>
            </div>
            
            <div className={cn(
              "font-bold text-primary",
              isSmall ? "text-sm" : "text-base"
            )}>
              From ${gig.price_starting}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className={cn("space-y-8", className)}>
      {/* Trending Categories */}
      {trendingCategories.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Trending Categories
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {trendingCategories.map((category) => (
                <Button
                  key={category.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleCategoryClick(category.id)}
                  className="h-auto py-2 px-3"
                >
                  <span className="mr-2">{category.icon}</span>
                  <div className="text-left">
                    <div className="font-medium">{category.name}</div>
                    <div className="text-xs text-muted-foreground">
                      {category.gig_count} services
                    </div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Featured Services */}
      {featuredServices.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Award className="h-5 w-5" />
                Editor's Choice
              </CardTitle>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate('/hire?featured=true')}
              >
                View All
                <ArrowRight className="w-4 h-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {featuredServices.slice(0, 6).map((gig) => renderServiceCard(gig, 'sm'))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Personalized Recommendations */}
      {recommendations.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5" />
                Recommended for You
              </CardTitle>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate('/hire')}
              >
                View All
                <ArrowRight className="w-4 h-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {recommendations.slice(0, 4).map((gig) => renderServiceCard(gig))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Popular Services (fallback if no recommendations) */}
      {recommendations.length === 0 && featuredServices.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center gap-2">
                <Heart className="h-5 w-5" />
                Popular Services
              </CardTitle>
              <Button 
                variant="ghost" 
                size="sm"
                onClick={() => navigate('/hire?sort=popularity')}
              >
                View All
                <ArrowRight className="w-4 h-4 ml-1" />
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {featuredServices.slice(0, 4).map((gig) => renderServiceCard(gig))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {recommendations.length === 0 && featuredServices.length === 0 && (
        <Card>
          <CardContent className="py-12 text-center">
            <Eye className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-medium mb-2">Discover Amazing Services</h3>
            <p className="text-muted-foreground mb-4">
              Browse our marketplace to find the perfect service for your needs
            </p>
            <Button onClick={() => navigate('/hire')}>
              Explore Services
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
