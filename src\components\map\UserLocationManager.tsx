
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { AlertCircle, Loader2, MapPin, LocateFixed } from 'lucide-react';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { useLocationServices } from '@/hooks/use-location-services';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';

interface UserLocationManagerProps {
  onLocationUpdated?: (coords: { lng: number; lat: number }) => void;
  mapRef?: React.MutableRefObject<any>;
  className?: string;
}

export function UserLocationManager({
  onLocationUpdated,
  mapRef,
  className
}: UserLocationManagerProps) {
  const [isUpdating, setIsUpdating] = useState(false);
  const { coords, error, getPosition } = useLocationServices();
  const { toast } = useToast();
  const [retryCount, setRetryCount] = useState(0);
  const [lastUpdateTime, setLastUpdateTime] = useState<Date | null>(null);

  const handleUpdateLocation = async () => {
    if (isUpdating) return;
    setIsUpdating(true);
    
    try {
      const newCoords = await getPosition();
      console.log('Got new coordinates:', newCoords);
      
      if (onLocationUpdated) {
        onLocationUpdated(newCoords);
      }
      
      if (mapRef?.current) {
        console.log('Flying to user coordinates:', newCoords);
        mapRef.current.flyTo({
          center: [newCoords.lng, newCoords.lat],
          zoom: 15,
          pitch: 45,
          bearing: 0,
          duration: 1800,
          essential: true
        });
      }
      
      toast({
        title: "Location updated",
        description: "Your location has been updated on the map",
      });
      
      // Reset retry counter on success and set last update time
      setRetryCount(0);
      setLastUpdateTime(new Date());
    } catch (error: any) {
      console.error('Error updating location:', error);
      toast({
        title: "Location error",
        description: error.message || "Could not update your location",
        variant: "destructive",
      });
      
      // Increment retry counter
      setRetryCount(prev => prev + 1);
    } finally {
      setIsUpdating(false);
    }
  };
  
  // Initial location update with smart retry
  useEffect(() => {
    const attemptLocationUpdate = async () => {
      try {
        await handleUpdateLocation();
      } catch (err) {
        // Progressive retry with increasing delays
        if (retryCount < 3) {
          const delay = 2000 + (retryCount * 1000);
          console.warn(`Initial location update failed, will retry in ${delay}ms (attempt ${retryCount + 1}/3)`);
          setTimeout(handleUpdateLocation, delay);
        }
      }
    };
    
    attemptLocationUpdate();
    
    // Periodically update location (every 5 minutes)
    const intervalId = setInterval(() => {
      if (!isUpdating) {
        handleUpdateLocation();
      }
    }, 5 * 60 * 1000);
    
    return () => clearInterval(intervalId);
  }, []);

  return (
    <TooltipProvider>
      <div className={className}>
        {error && (
          <Alert variant="destructive" className="mb-4 bg-white/90 backdrop-blur-sm border border-destructive/20">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Location Error</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}
        
        <div className="flex gap-2">
          <Button 
            onClick={handleUpdateLocation} 
            disabled={isUpdating}
            className="flex-1 bg-primary/90 hover:bg-primary shadow-md"
            variant="default"
          >
            {isUpdating ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Updating Location...
              </>
            ) : (
              <>
                <MapPin className="h-4 w-4 mr-2" />
                {coords ? 'Update My Location' : 'Share My Location'}
              </>
            )}
          </Button>
          
          {coords && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={() => {
                    if (mapRef?.current && coords) {
                      console.log('Centering map on user location:', coords);
                      mapRef.current.flyTo({
                        center: [coords.lng, coords.lat],
                        zoom: 15,
                        pitch: 45,
                        bearing: 0,
                        duration: 1800
                      });
                      toast({
                        title: "Map centered",
                        description: "Map centered on your location",
                      });
                    }
                  }}
                  variant="outline"
                  size="icon"
                  className="bg-white/90 hover:bg-white shadow-md"
                >
                  <LocateFixed className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Center map on my location</p>
              </TooltipContent>
            </Tooltip>
          )}
        </div>
        
        {lastUpdateTime && (
          <div className="mt-2 text-xs text-muted-foreground text-center">
            Last updated: {lastUpdateTime.toLocaleTimeString()}
          </div>
        )}
      </div>
    </TooltipProvider>
  );
}
