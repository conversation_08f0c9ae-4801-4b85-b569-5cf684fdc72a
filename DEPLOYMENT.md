
# BuddySurf Deployment Guide

1. Configure all required Supabase keys and secrets in the dashboard.
2. Set up Stripe keys for checkout/payment features.
3. Ensure Mapbox token and configuration is correct.
4. Deploy web application to your preferred host (Vercel, Netlify, etc.).
5. Monitor logs and health from the Supabase and deployment dashboards.

_See in-app settings for live configuration changes. Consult the [README.md](./README.md) for details._
