
import { useState } from 'react';
import { SavedLocation, useSavedLocations } from '@/hooks/use-saved-locations';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  Card,
  CardContent,
} from '@/components/ui/card';
import {
  MapPin, 
  Star, 
  Trash2, 
  Edit, 
  Plus,
  Search,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { format } from 'date-fns';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface SavedLocationsListProps {
  onLocationSelected?: (location: SavedLocation) => void;
  className?: string;
}

export function SavedLocationsList({ onLocationSelected, className }: SavedLocationsListProps) {
  const { 
    savedLocations, 
    isLoading, 
    saveLocation, 
    deleteLocation, 
    updateLocation 
  } = useSavedLocations();
  
  const [newLocationDialogOpen, setNewLocationDialogOpen] = useState(false);
  const [editLocationDialogOpen, setEditLocationDialogOpen] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<SavedLocation | null>(null);
  
  const [newLocation, setNewLocation] = useState({
    name: '',
    description: '',
    location: { x: 0, y: 0 }
  });
  
  const [editLocation, setEditLocation] = useState({
    name: '',
    description: ''
  });
  
  const handleSaveNewLocation = async () => {
    const result = await saveLocation(newLocation);
    if (result) {
      setNewLocationDialogOpen(false);
      setNewLocation({
        name: '',
        description: '',
        location: { x: 0, y: 0 }
      });
    }
  };
  
  const handleUpdateLocation = async () => {
    if (!selectedLocation) return;
    
    const success = await updateLocation(selectedLocation.id, editLocation);
    if (success) {
      setEditLocationDialogOpen(false);
      setSelectedLocation(null);
    }
  };
  
  const handleEditLocation = (location: SavedLocation) => {
    setSelectedLocation(location);
    setEditLocation({
      name: location.name,
      description: location.description || ''
    });
    setEditLocationDialogOpen(true);
  };
  
  const handleDeleteLocation = async (locationId: string) => {
    await deleteLocation(locationId);
  };
  
  const handleSelectLocation = (location: SavedLocation) => {
    if (onLocationSelected) {
      onLocationSelected(location);
    }
  };
  
  return (
    <div className={cn("flex flex-col h-full", className)}>
      <div className="flex items-center justify-between p-2">
        <h3 className="font-medium flex items-center gap-1">
          <Star className="h-4 w-4" />
          <span>Saved Locations</span>
        </h3>
        
        <Dialog open={newLocationDialogOpen} onOpenChange={setNewLocationDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
              <Plus className="h-4 w-4" />
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Save New Location</DialogTitle>
              <DialogDescription>
                Add a new location to your favorites.
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="name" className="text-right">
                  Name
                </Label>
                <Input
                  id="name"
                  value={newLocation.name}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, name: e.target.value }))}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="description"
                  value={newLocation.description}
                  onChange={(e) => setNewLocation(prev => ({ ...prev, description: e.target.value }))}
                  className="col-span-3"
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setNewLocationDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleSaveNewLocation} disabled={!newLocation.name}>
                Save Location
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        
        <Dialog open={editLocationDialogOpen} onOpenChange={setEditLocationDialogOpen}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Location</DialogTitle>
              <DialogDescription>
                Update your saved location details.
              </DialogDescription>
            </DialogHeader>
            
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="edit-name"
                  value={editLocation.name}
                  onChange={(e) => setEditLocation(prev => ({ ...prev, name: e.target.value }))}
                  className="col-span-3"
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="edit-description" className="text-right">
                  Description
                </Label>
                <Textarea
                  id="edit-description"
                  value={editLocation.description}
                  onChange={(e) => setEditLocation(prev => ({ ...prev, description: e.target.value }))}
                  className="col-span-3"
                />
              </div>
            </div>
            
            <DialogFooter>
              <Button variant="outline" onClick={() => setEditLocationDialogOpen(false)}>
                Cancel
              </Button>
              <Button onClick={handleUpdateLocation} disabled={!editLocation.name}>
                Update Location
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
      
      <div className="px-2 pb-2">
        <div className="relative">
          <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search saved locations..."
            className="pl-9"
          />
        </div>
      </div>
      
      <ScrollArea className="flex-1">
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          </div>
        ) : savedLocations.length === 0 ? (
          <div className="text-center py-8 px-4 text-muted-foreground text-sm">
            <MapPin className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p>You haven't saved any locations yet.</p>
            <p className="mt-1">Add locations by clicking the + button.</p>
          </div>
        ) : (
          <div className="space-y-2 p-2">
            {savedLocations.map((location) => (
              <Card 
                key={location.id} 
                className="cursor-pointer hover:border-primary transition-colors"
                onClick={() => handleSelectLocation(location)}
              >
                <CardContent className="p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-sm truncate">{location.name}</h4>
                      {location.description && (
                        <p className="text-xs text-muted-foreground line-clamp-2 mt-1">
                          {location.description}
                        </p>
                      )}
                      <p className="text-xs text-muted-foreground mt-1">
                        {format(new Date(location.created_at), 'MMM d, yyyy')}
                      </p>
                    </div>
                    <div className="flex items-center space-x-1 ml-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-7 w-7"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditLocation(location);
                        }}
                      >
                        <Edit className="h-3.5 w-3.5" />
                      </Button>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-7 w-7 text-destructive hover:text-destructive"
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteLocation(location.id);
                        }}
                      >
                        <Trash2 className="h-3.5 w-3.5" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
