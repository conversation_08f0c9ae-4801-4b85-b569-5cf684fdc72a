import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { QueueManagementStats, QueueAnalytics, ActivityQueueEntry } from '@/types/activity';

export function useQueueManagement(activityId: string) {
  const queryClient = useQueryClient();

  // Fetch queue entries for the activity
  const queueEntriesQuery = useQuery({
    queryKey: ['queue-entries', activityId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('activity_queue')
        .select('*')
        .eq('activity_id', activityId);

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    enabled: !!activityId
  });

  // Queue statistics query
  const statsQuery = useQuery({
    queryKey: ['queue-stats', activityId],
    queryFn: async (): Promise<QueueManagementStats> => {
      const { data: queueData, error } = await supabase
        .from('activity_queue')
        .select('*')
        .eq('activity_id', activityId);

      if (error) throw error;

      const totalParticipants = queueData?.length || 0;
      const confirmedParticipants = queueData?.filter(p => p.status === 'confirmed').length || 0;
      const pendingParticipants = queueData?.filter(p => p.status === 'pending').length || 0;
      const waitlistedParticipants = queueData?.filter(p => p.status === 'waitlisted').length || 0;

      // Transform to proper ActivityQueueEntry format
      const entries: ActivityQueueEntry[] = (queueData || []).map(entry => ({
        id: entry.id,
        activity_id: entry.activity_id,
        user_id: entry.user_id,
        status: entry.status as 'confirmed' | 'pending' | 'waitlisted' | 'cancelled',
        position: entry.position,
        created_at: entry.created_at,
        updated_at: entry.updated_at
      }));

      const stats: QueueManagementStats = {
        totalParticipants,
        confirmedParticipants,
        pendingParticipants,
        waitlistedParticipants,
        averageWaitTime: 15, // Mock data
        confirmed: confirmedParticipants,
        pending: pendingParticipants,
        waitlisted: waitlistedParticipants,
        total: totalParticipants,
        maxParticipants: 50, // This should come from activity data
        estimatedWaitTime: 20, // Mock calculation
        capacityRemaining: Math.max(0, 50 - confirmedParticipants),
        entries
      };

      return stats;
    },
    enabled: !!activityId
  });

  // Mutation to add a user to the queue
  const addToQueueMutation = useMutation({
    mutationFn: async (userId: string) => {
      // Get the next position
      const { data: positionData } = await supabase
        .from('activity_queue')
        .select('position')
        .eq('activity_id', activityId)
        .order('position', { ascending: false })
        .limit(1)
        .single();

      const nextPosition = positionData ? positionData.position + 1 : 1;

      const { data, error } = await supabase
        .from('activity_queue')
        .insert([{ 
          activity_id: activityId, 
          user_id: userId, 
          status: 'pending',
          position: nextPosition
        }]);

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['queue-entries', activityId] });
      queryClient.invalidateQueries({ queryKey: ['queue-stats', activityId] });
    }
  });

  // Mutation to remove a user from the queue
  const removeFromQueueMutation = useMutation({
    mutationFn: async (userId: string) => {
      const { data, error } = await supabase
        .from('activity_queue')
        .delete()
        .eq('activity_id', activityId)
        .eq('user_id', userId);

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['queue-entries', activityId] });
      queryClient.invalidateQueries({ queryKey: ['queue-stats', activityId] });
    }
  });

  // Mutation to update a queue entry's status
  const updateQueueStatusMutation = useMutation({
    mutationFn: async ({ userId, status }: { userId: string; status: 'confirmed' | 'pending' | 'waitlisted' | 'cancelled' }) => {
      const { data, error } = await supabase
        .from('activity_queue')
        .update({ status })
        .eq('activity_id', activityId)
        .eq('user_id', userId);

      if (error) {
        throw new Error(error.message);
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['queue-entries', activityId] });
      queryClient.invalidateQueries({ queryKey: ['queue-stats', activityId] });
    }
  });

  // Queue analytics query
  const analyticsQuery = useQuery({
    queryKey: ['queue-analytics', activityId],
    queryFn: async (): Promise<QueueAnalytics> => {
      // Mock analytics data - in real implementation, this would calculate from historical data
      const analytics: QueueAnalytics = {
        joinRate: 0.75,
        dropoutRate: 0.15,
        conversionRate: 0.85,
        peakHours: ['18:00', '19:00', '20:00'],
        averageProcessingTime: 300, // 5 minutes in seconds
        avgWaitTime: 15,
        acceptanceRate: 0.85,
        averageWaitTime: 15,
        popularityScore: 4.2,
        average_wait_time: 15,
        conversion_rate: 0.85,
        filled_percentage: 0.75,
        peak_times: [
          { hour: 18, count: 12 },
          { hour: 19, count: 15 },
          { hour: 20, count: 10 }
        ]
      };

      return analytics;
    },
    enabled: !!activityId
  });

  const getAvailableSpots = (stats: QueueManagementStats | undefined) => {
    if (!stats) return 0;
    return Math.max(0, stats.maxParticipants - stats.confirmed);
  };

  const getQueueEfficiency = (stats: QueueManagementStats | undefined) => {
    if (!stats || stats.maxParticipants === 0) return 0;
    return (stats.confirmed / stats.maxParticipants) * 100;
  };

  return {
    queueEntries: queueEntriesQuery.data,
    queueStats: statsQuery.data,
    queueAnalytics: analyticsQuery.data,
    isLoading: queueEntriesQuery.isLoading || statsQuery.isLoading || analyticsQuery.isLoading,
    error: queueEntriesQuery.error || statsQuery.error || analyticsQuery.error,
    addToQueue: addToQueueMutation.mutateAsync,
    removeFromQueue: removeFromQueueMutation.mutateAsync,
    updateQueueStatus: updateQueueStatusMutation.mutateAsync,
    isAddingToQueue: addToQueueMutation.isPending,
    isRemovingFromQueue: removeFromQueueMutation.isPending,
    isUpdatingQueueStatus: updateQueueStatusMutation.isPending,
    getAvailableSpots,
    getQueueEfficiency
  };
}
