// Admin Conversation Integration Tests
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { <PERSON><PERSON>erRouter } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { useAdminConversation } from '../hooks/use-admin-conversation';
import { ChatSidebar } from '../components/chat/ChatSidebar';
import { ChatContainer } from '../components/chat/ChatContainer';
import { AdminAnnouncementForm } from '../components/admin/AdminAnnouncementForm';
import { supabase } from '../integrations/supabase/client';

// Mock the hooks and Supabase client
jest.mock('../hooks/use-admin-conversation');
jest.mock('../hooks/use-auth', () => ({
  useAuth: () => ({
    user: { id: 'test-user-id' }
  })
}));
jest.mock('../hooks/use-profile', () => ({
  useProfile: () => ({
    data: { is_admin: true }
  })
}));
jest.mock('../integrations/supabase/client', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    update: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    channel: jest.fn().mockReturnValue({
      on: jest.fn().mockReturnThis(),
      subscribe: jest.fn()
    }),
    removeChannel: jest.fn()
  }
}));

// Create a wrapper with necessary providers
const createWrapper = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  });
  
  return ({ children }) => (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        {children}
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('Admin Conversation Integration', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });
  
  describe('ChatSidebar with Admin Conversation', () => {
    it('should display the admin conversation at the top of the sidebar', async () => {
      // Mock the admin conversation hook
      useAdminConversation.mockReturnValue({
        adminConversation: {
          id: 'admin-conv-id',
          is_admin_conversation: true,
          last_message: 'Welcome to BuddySurf!',
          last_message_at: new Date().toISOString()
        },
        isLoading: false,
        refetch: jest.fn()
      });
      
      // Mock other hooks used in ChatSidebar
      jest.mock('../hooks/use-chat-conversations', () => ({
        useChatConversations: () => ({
          conversations: [],
          isLoading: false
        })
      }));
      
      render(<ChatSidebar />, { wrapper: createWrapper() });
      
      // Check if the admin conversation is displayed
      await waitFor(() => {
        expect(screen.getByText('Buddy Admin')).toBeInTheDocument();
        expect(screen.getByText('Welcome to BuddySurf!')).toBeInTheDocument();
        expect(screen.getByText('Official')).toBeInTheDocument();
      });
    });
    
    it('should show a loading state when admin conversation is loading', async () => {
      // Mock the admin conversation hook with loading state
      useAdminConversation.mockReturnValue({
        adminConversation: null,
        isLoading: true,
        refetch: jest.fn()
      });
      
      render(<ChatSidebar />, { wrapper: createWrapper() });
      
      // Check if loading state is displayed
      await waitFor(() => {
        expect(screen.getByText('Loading...')).toBeInTheDocument();
      });
    });
  });
  
  describe('ChatContainer with Admin Conversation', () => {
    it('should redirect to admin conversation when no conversation is selected', async () => {
      // Mock the admin conversation hook
      useAdminConversation.mockReturnValue({
        adminConversation: {
          id: 'admin-conv-id',
          is_admin_conversation: true
        },
        isLoading: false
      });
      
      // Mock navigate function
      const navigateMock = jest.fn();
      jest.mock('react-router-dom', () => ({
        ...jest.requireActual('react-router-dom'),
        useNavigate: () => navigateMock,
        useSearchParams: () => [new URLSearchParams(), jest.fn()]
      }));
      
      render(<ChatContainer />, { wrapper: createWrapper() });
      
      // Check if navigation to admin conversation happens
      await waitFor(() => {
        expect(navigateMock).toHaveBeenCalledWith(
          expect.stringContaining('admin-conv-id'),
          expect.anything()
        );
      });
    });
  });
  
  describe('AdminAnnouncementForm', () => {
    it('should allow admins to send announcements', async () => {
      // Mock the admin conversation hook
      const sendAdminMessageMock = jest.fn().mockResolvedValue({});
      useAdminConversation.mockReturnValue({
        adminConversation: {
          id: 'admin-conv-id',
          is_admin_conversation: true
        },
        isLoading: false,
        isAdmin: true,
        sendAdminMessage: {
          mutateAsync: sendAdminMessageMock,
          isPending: false
        },
        addAllUsersAsParticipants: {
          mutateAsync: jest.fn(),
          isPending: false
        }
      });
      
      render(<AdminAnnouncementForm />, { wrapper: createWrapper() });
      
      // Type a message and submit
      fireEvent.change(screen.getByPlaceholderText('Type your announcement here...'), {
        target: { value: 'Test announcement' }
      });
      
      fireEvent.click(screen.getByText('Send Announcement'));
      
      // Check if the send function was called
      await waitFor(() => {
        expect(sendAdminMessageMock).toHaveBeenCalledWith({
          content: 'Test announcement'
        });
      });
    });
    
    it('should show access denied for non-admins', async () => {
      // Mock the admin conversation hook for non-admin
      useAdminConversation.mockReturnValue({
        adminConversation: {
          id: 'admin-conv-id',
          is_admin_conversation: true
        },
        isLoading: false,
        isAdmin: false
      });
      
      render(<AdminAnnouncementForm />, { wrapper: createWrapper() });
      
      // Check if access denied message is shown
      expect(screen.getByText('Access Denied')).toBeInTheDocument();
      expect(screen.getByText('You don\'t have permission to send admin announcements.')).toBeInTheDocument();
    });
  });
});
