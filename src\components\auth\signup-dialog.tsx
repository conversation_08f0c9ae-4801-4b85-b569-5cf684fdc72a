import * as React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useId, useState } from "react";
import { useAuth } from "@/hooks/use-auth";
import { Loader2 } from "lucide-react";
import { useNavigate } from "react-router-dom";

interface SignupDialogProps {
  open: boolean;
  setOpen: (open: boolean) => void;
  setLoginOpen: (open: boolean) => void;
}

export function SignupDialog({ open, setOpen, setLoginOpen }: SignupDialogProps) {
  const id = useId();
  const { signUp, user } = useAuth();
  const navigate = useNavigate();

  const [firstName, setFirstName] = useState("");
  const [lastName, setLastName] = useState("");
  const [email, setEmail] = useState("");
  const [username, setUsername] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const handleLoginClick = () => {
    setOpen(false);
    setLoginOpen(true);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setErrorMessage("");

    if (password !== confirmPassword) {
      setErrorMessage("Passwords don't match");
      setIsLoading(false);
      return;
    }

    try {
      const { error } = await signUp(email, password, {
        first_name: firstName,
        last_name: lastName,
        username: username,
      });

      if (!error) {
        // Close the signup dialog
        setOpen(false);

        // Reset form
        setFirstName("");
        setLastName("");
        setEmail("");
        setUsername("");
        setPassword("");
        setConfirmPassword("");

        // Set a flag that we just signed up - this is the ONLY trigger for onboarding
        localStorage.setItem('just_signed_up', 'true');

        // Clear any existing onboarding flags to ensure fresh start
        if (user?.id) {
          localStorage.removeItem(`onboarding_shown_${user.id}`);
        }

        // Redirect to home page where onboarding will be triggered once and only once
        navigate("/");

        console.log("Signup successful - onboarding will be shown immediately");
      } else {
        setErrorMessage(error.message);
      }
    } catch (error) {
      setErrorMessage("An unexpected error occurred");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogContent>
        <div className="flex flex-col items-center gap-2">
          <div
            className="flex size-11 shrink-0 items-center justify-center rounded-full border border-border bg-primary-purple/20"
            aria-hidden="true"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-primary-purple"
            >
              <path d="M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </div>
          <DialogHeader>
            <DialogTitle className="sm:text-center">Create an account</DialogTitle>
            <DialogDescription className="sm:text-center">
              Join MeetSphere Social to connect and meet new people
            </DialogDescription>
          </DialogHeader>
        </div>

        <form className="space-y-4" onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor={`${id}-first-name`}>First name</Label>
              <Input
                id={`${id}-first-name`}
                placeholder="Enter first name"
                required
                value={firstName}
                onChange={(e) => setFirstName(e.target.value)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor={`${id}-last-name`}>Last name</Label>
              <Input
                id={`${id}-last-name`}
                placeholder="Enter last name"
                required
                value={lastName}
                onChange={(e) => setLastName(e.target.value)}
              />
            </div>
          </div>
          <div className="space-y-2">
            <Label htmlFor={`${id}-email`}>Email</Label>
            <Input
              id={`${id}-email`}
              placeholder="<EMAIL>"
              type="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor={`${id}-username`}>Username</Label>
            <Input
              id={`${id}-username`}
              placeholder="Choose a username"
              required
              value={username}
              onChange={(e) => setUsername(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor={`${id}-password`}>Password</Label>
            <Input
              id={`${id}-password`}
              placeholder="Create a password"
              type="password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
            />
          </div>
          <div className="space-y-2">
            <Label htmlFor={`${id}-confirm-password`}>Confirm Password</Label>
            <Input
              id={`${id}-confirm-password`}
              placeholder="Confirm your password"
              type="password"
              required
              value={confirmPassword}
              onChange={(e) => setConfirmPassword(e.target.value)}
            />
          </div>

          {errorMessage && (
            <div className="bg-red-50 p-2 rounded text-sm text-red-600">
              {errorMessage}
            </div>
          )}

          <Button
            type="submit"
            className="w-full bg-primary-purple hover:bg-primary-deep-purple"
            disabled={isLoading}
          >
            {isLoading ? (
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            ) : null}
            Sign up
          </Button>
        </form>

        <div className="flex items-center gap-3 before:h-px before:flex-1 before:bg-border after:h-px after:flex-1 after:bg-border">
          <span className="text-xs text-muted-foreground">Or</span>
        </div>

        <Button variant="outline">Sign up with Google</Button>

        <div className="text-center text-sm">
          Already have an account?{" "}
          <button
            className="text-primary-purple font-medium hover:underline"
            onClick={handleLoginClick}
            type="button"
          >
            Log in
          </button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
