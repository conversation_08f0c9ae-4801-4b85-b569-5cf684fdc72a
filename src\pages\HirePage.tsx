import React, { useState } from 'react';
import { MainLayout } from "@/components/layouts/MainLayout";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Slider } from "@/components/ui/slider";
import ProfileDetailModal from "@/components/hire/ProfileDetailModal";
import BookingModal from "@/components/hire/BookingModal";
import ServiceBookingModal from "@/components/hire/ServiceBookingModal";
import { ServiceProviderRegistration } from "@/components/hire/ServiceProviderRegistration";
import { AdvancedServiceFilters } from "@/components/services/AdvancedServiceFilters";
import { ServiceRecommendations } from "@/components/services/ServiceRecommendations";
import { GigCard } from "@/components/gigs/GigCard";
import { useBuddyProviders } from "@/hooks/use-buddy-providers";
import { useUserServiceProvider } from "@/hooks/use-service-providers";
import { useServiceCategories } from "@/hooks/use-service-categories";
import { useAdvancedServiceSearch, ServiceSearchFilters } from "@/hooks/use-advanced-service-search";
import { BuddyProvider } from "@/hooks/use-buddy-providers";
import { BookingData } from "@/types/hire";
import { Users, Search, MapPin, DollarSign, Filter, Plus, Loader2, Briefcase, Heart } from "lucide-react";

const HirePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState("services");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPurpose, setSelectedPurpose] = useState<string>("");
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [selectedDistance, setSelectedDistance] = useState<string>("any");
  const [selectedProfile, setSelectedProfile] = useState<BuddyProvider | null>(null);
  const [isProfileModalOpen, setIsProfileModalOpen] = useState(false);
  const [isBookingModalOpen, setIsBookingModalOpen] = useState(false);
  const [isRegistrationModalOpen, setIsRegistrationModalOpen] = useState(false);

  // Service search filters
  const [serviceFilters, setServiceFilters] = useState<ServiceSearchFilters>({
    searchQuery: '',
    sortBy: 'relevance',
    limit: 20,
    offset: 0
  });

  const { data: providers, isLoading, error } = useBuddyProviders(undefined, searchQuery);
  const { data: categories } = useServiceCategories();
  const { data: userProvider } = useUserServiceProvider();

  // Advanced service search
  const {
    data: serviceSearchResult,
    isLoading: isServicesLoading
  } = useAdvancedServiceSearch(serviceFilters);

  const handleViewProfile = (profile: BuddyProvider) => {
    setSelectedProfile(profile);
    setIsProfileModalOpen(true);
  };

  const handleHireProfile = (profile: BuddyProvider) => {
    setSelectedProfile(profile);
    setIsBookingModalOpen(true);
  };

  const handleBookingConfirm = (bookingData: BookingData) => {
    // This is now handled by the ServiceBookingModal component
    setIsBookingModalOpen(false);
  };

  // Service filter handlers
  const handleServiceFiltersChange = (newFilters: ServiceSearchFilters) => {
    setServiceFilters(newFilters);
  };

  const handleClearServiceFilters = () => {
    setServiceFilters({
      searchQuery: '',
      sortBy: 'relevance',
      limit: 20,
      offset: 0
    });
  };

  // Filter providers based on search criteria
  const filteredProviders = providers?.filter(provider => {
    const matchesPrice = provider.hourlyRate >= priceRange[0] && provider.hourlyRate <= priceRange[1];
    const matchesPurpose = !selectedPurpose || provider.hirePurposes.includes(selectedPurpose);
    return matchesPrice && matchesPurpose;
  }) || [];

  if (isLoading) {
    return (
      <MainLayout title="Hire a Buddy">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      </MainLayout>
    );
  }

  if (error) {
    return (
      <MainLayout title="Hire a Buddy">
        <div className="text-center py-8">
          <p className="text-red-600">Error loading buddy providers: {error.message}</p>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout title="Hire a Friend">
      <div className="container mx-auto px-4 py-8">
        {/* Header Section */}
        <Card className="mb-8 border-none bg-gradient-to-r from-primary/10 to-secondary/10 shadow-sm">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <div className="flex items-center gap-2 mb-2">
                  <Briefcase className="h-5 w-5 text-primary" />
                  <CardTitle>Hire a Friend</CardTitle>
                </div>
                <CardDescription>
                  Discover professional services and connect with local buddies.
                  Find skilled freelancers for your projects or book time with amazing people in your area.
                </CardDescription>
              </div>
              {!userProvider && (
                <Button onClick={() => setIsRegistrationModalOpen(true)} className="gap-2">
                  <Plus className="h-4 w-4" />
                  Become a Provider
                </Button>
              )}
            </div>
          </CardHeader>
        </Card>

        {/* Main Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="services" className="flex items-center gap-2">
              <Briefcase className="h-4 w-4" />
              Professional Services
            </TabsTrigger>
            <TabsTrigger value="buddies" className="flex items-center gap-2">
              <Users className="h-4 w-4" />
              Local Buddies
            </TabsTrigger>
            <TabsTrigger value="recommendations" className="flex items-center gap-2">
              <Heart className="h-4 w-4" />
              For You
            </TabsTrigger>
          </TabsList>

          {/* Services Tab */}
          <TabsContent value="services" className="mt-6">
            <div className="space-y-6">
              {/* Advanced Service Filters */}
              <AdvancedServiceFilters
                filters={serviceFilters}
                onFiltersChange={handleServiceFiltersChange}
                onClearFilters={handleClearServiceFilters}
              />

              {/* Services Results */}
              {isServicesLoading ? (
                <div className="flex justify-center items-center h-64">
                  <Loader2 className="h-8 w-8 animate-spin text-primary" />
                </div>
              ) : serviceSearchResult?.gigs.length ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">
                      {serviceSearchResult.totalCount} services found
                    </h3>
                    <div className="text-sm text-muted-foreground">
                      Showing {serviceSearchResult.gigs.length} of {serviceSearchResult.totalCount}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {serviceSearchResult.gigs.map((gig) => (
                      <GigCard key={gig.id} gig={gig} />
                    ))}
                  </div>

                  {serviceSearchResult.hasMore && (
                    <div className="text-center pt-6">
                      <Button
                        variant="outline"
                        onClick={() => setServiceFilters(prev => ({
                          ...prev,
                          offset: (prev.offset || 0) + (prev.limit || 20)
                        }))}
                      >
                        Load More Services
                      </Button>
                    </div>
                  )}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Briefcase className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No services found</h3>
                  <p className="text-gray-600 mb-4">
                    Try adjusting your search filters or browse our recommendations
                  </p>
                  <Button onClick={() => setActiveTab('recommendations')}>
                    View Recommendations
                  </Button>
                </div>
              )}
            </div>
          </TabsContent>

          {/* Buddies Tab */}
          <TabsContent value="buddies" className="mt-6">
            <div className="space-y-6">
              {/* Search and Filters Section */}
              <Card className="mb-8 border shadow-sm">
                <CardHeader>
                  <div className="flex items-center gap-2 mb-2">
                    <Filter className="h-5 w-5 text-muted-foreground" />
                    <CardTitle className="text-lg">Search & Filters</CardTitle>
                  </div>
                  <div className="space-y-4">
                    <div className="flex gap-4 flex-wrap">
                      <div className="relative flex-1 max-w-md">
                        <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                        <Input
                          placeholder="Search by name, interest, or location..."
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          className="pl-9"
                        />
                      </div>
                      <Select value={selectedPurpose} onValueChange={setSelectedPurpose}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Purpose" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All Purposes</SelectItem>
                          <SelectItem value="Hangout">Hang Out</SelectItem>
                          <SelectItem value="Local Guide">Local Guide</SelectItem>
                          <SelectItem value="Event Buddy">Event Buddy</SelectItem>
                        </SelectContent>
                      </Select>
                      <Select value={selectedDistance} onValueChange={setSelectedDistance}>
                        <SelectTrigger className="w-[180px]">
                          <SelectValue placeholder="Distance" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="nearby">Nearby</SelectItem>
                          <SelectItem value="city">City</SelectItem>
                          <SelectItem value="any">Any Distance</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="flex items-center gap-4">
                      <DollarSign className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm text-gray-500">Price Range:</span>
                      <Slider
                        value={priceRange}
                        onValueChange={(value) => setPriceRange(value as [number, number])}
                        max={100}
                        step={5}
                        className="w-[200px]"
                      />
                      <span className="text-sm">${priceRange[0]} - ${priceRange[1]}/hr</span>
                    </div>
                  </div>
                </CardHeader>
              </Card>

              {/* Profiles Grid */}
              {filteredProviders.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {filteredProviders.map((profile) => (
                    <Card key={profile.id} className="p-4 hover:shadow-lg transition-shadow">
                      <div className="flex items-start gap-4">
                        <Avatar className="h-16 w-16">
                          <AvatarImage src={profile.avatarUrl} />
                          <AvatarFallback>{profile.displayName[0]}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <h3 className="font-semibold">{profile.displayName}</h3>
                          <p className="text-sm text-gray-500">{profile.username}</p>
                          <div className="flex items-center gap-1 mt-1">
                            <span className="text-yellow-400">⭐</span>
                            <span className="text-sm">{profile.rating}</span>
                            <span className="text-sm text-gray-500">({profile.reviewCount})</span>
                          </div>
                        </div>
                      </div>
                      <p className="mt-3 text-sm text-gray-600 line-clamp-2">{profile.bio}</p>
                      <div className="mt-3 flex flex-wrap gap-2">
                        {profile.vibes.slice(0, 3).map((vibe) => (
                          <Badge key={vibe} variant="secondary">{vibe}</Badge>
                        ))}
                      </div>
                      <div className="mt-4 flex items-center justify-between">
                        <div className="text-sm">
                          <span className="font-semibold">${profile.hourlyRate}/hr</span>
                          <span className="text-gray-500 ml-2">• {profile.distance.toFixed(1)}km away</span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleViewProfile(profile)}
                          >
                            View
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleHireProfile(profile)}
                          >
                            Hire
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold mb-2">No buddy providers found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchQuery || selectedPurpose
                      ? "Try adjusting your search or filters"
                      : "Be the first to register as a buddy provider!"}
                  </p>
                  {!userProvider && (
                    <Button onClick={() => setIsRegistrationModalOpen(true)}>
                      Become a Buddy Provider
                    </Button>
                  )}
                </div>
              )}
            </div>
          </TabsContent>

          {/* Recommendations Tab */}
          <TabsContent value="recommendations" className="mt-6">
            <ServiceRecommendations />
          </TabsContent>
        </Tabs>

        {/* Modals */}
        {selectedProfile && (
          <>
            <ProfileDetailModal
              profile={selectedProfile}
              isOpen={isProfileModalOpen}
              onClose={() => setIsProfileModalOpen(false)}
            />
            <ServiceBookingModal
              provider={{
                id: selectedProfile.id,
                name: selectedProfile.displayName,
                hourlyRate: selectedProfile.hourlyRate,
                avatarUrl: selectedProfile.avatarUrl
              }}
              isOpen={isBookingModalOpen}
              onClose={() => setIsBookingModalOpen(false)}
            />
          </>
        )}

        <ServiceProviderRegistration
          isOpen={isRegistrationModalOpen}
          onClose={() => setIsRegistrationModalOpen(false)}
        />
      </div>
    </MainLayout>
  );
};

export default HirePage;
