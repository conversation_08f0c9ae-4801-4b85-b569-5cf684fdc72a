# BuddySurf Database Schema Enhancements

## Core Tables

### profiles
- `id` (PK): User ID
- `display_name`: User's display name
- `username`: Unique username
- `avatar_url`: Profile picture URL
- `bio`: User biography
- `location`: PostgreSQL point (x, y) for default location
- `is_verified`: Boolean for verified status
- `birthday`: Date of birth
- `gender`: User gender
- `purposes`: Array of user purposes (make friends, find love, etc.)
- `vibes`: Array of user vibes/interests
- `gallery`: Array of image URLs
- `favorite_locations`: Array of saved locations
- `default_location`: PostgreSQL point for default location
- `location_display`: Human-readable location name
- `location_permission_granted`: Boolean for location permission
- `notifications_enabled`: Boolean for notification settings
- `is_admin`: Boolean for admin status

### user_locations
- `id` (PK): Location entry ID
- `user_id` (FK): References profiles.id
- `location`: PostgreSQL point (x, y) for current location
- `created_at`: Timestamp
- `updated_at`: Timestamp
- `is_online`: Boolean for online status
- `last_seen_at`: Timestamp for last activity

## Activity System

### activities
- `id` (PK): Activity ID
- `title`: Activity title
- `description`: Activity description
- `category_id` (FK): References categories.id
- `location`: PostgreSQL point (x, y) for activity location
- `address`: Human-readable address
- `start_time`: Activity start time
- `end_time`: Activity end time
- `is_paid`: Boolean for paid status
- `price`: Numeric price (if paid)
- `max_participants`: Maximum number of participants
- `host_id` (FK): References profiles.id
- `status`: Activity status (active, cancelled, completed)
- `visibility`: Public/private/unlisted
- `queue_type`: FIFO or priority
- `allow_waitlist`: Boolean for waitlist
- `group_chat_id` (FK): References chat_conversations.id

### activity_participants
- `id` (PK): Participant entry ID
- `activity_id` (FK): References activities.id
- `user_id` (FK): References profiles.id
- `status`: Status (pending, confirmed, waitlisted, cancelled)
- `joined_at`: Timestamp
- `payment_status`: Payment status (if applicable)
- `payment_id`: Payment reference (if applicable)

### activity_categories
- `id` (PK): Category ID
- `name`: Category name
- `icon`: Category icon
- `type`: Category type (activity, service, etc.)

## Chat System

### chat_conversations
- `id` (PK): Conversation ID
- `activity_id` (FK): References activities.id (for activity chats)
- `is_group`: Boolean for group conversations
- `is_admin_conversation`: Boolean for admin announcements
- `is_support`: Boolean for support conversations
- `last_message`: Preview of last message
- `last_message_at`: Timestamp of last message
- `is_archived`: Boolean for archived status

### chat_participants
- `id` (PK): Participant entry ID
- `conversation_id` (FK): References chat_conversations.id
- `user_id` (FK): References profiles.id
- `joined_at`: Timestamp
- `last_read_at`: Timestamp for read receipts

### messages
- `id` (PK): Message ID
- `conversation_id` (FK): References chat_conversations.id
- `sender_id` (FK): References profiles.id
- `recipient_id` (FK): References profiles.id (for direct messages)
- `content`: Message content
- `created_at`: Timestamp
- `is_read`: Boolean for read status
- `media_url`: URL for attached media
- `is_admin_message`: Boolean for admin messages
- `activity_id` (FK): References activities.id (for activity proposals)

### chat_proposals
- `id` (PK): Proposal ID
- `conversation_id` (FK): References chat_conversations.id
- `sender_id` (FK): References profiles.id
- `recipient_id` (FK): References profiles.id
- `activity_id` (FK): References activities.id
- `service_id` (FK): References services.id
- `status`: Status (pending, accepted, rejected)
- `created_at`: Timestamp
- `expires_at`: Expiration timestamp

## Hire Marketplace

### service_providers
- `id` (PK): Provider ID
- `user_id` (FK): References profiles.id
- `name`: Provider name
- `bio`: Provider biography
- `avatar_url`: Provider avatar URL
- `hourly_rate`: Hourly rate
- `location`: Location string
- `tags`: Array of service tags
- `is_verified`: Boolean for verified status
- `rating`: Average rating
- `total_reviews`: Count of reviews
- `trust_score`: Trust score metric
- `verification_status`: Verification status

### services
- `id` (PK): Service ID
- `provider_id` (FK): References service_providers.id
- `title`: Service title
- `description`: Service description
- `price`: Service price
- `duration`: Service duration in minutes
- `category_id` (FK): References categories.id
- `is_featured`: Boolean for featured status

### service_bookings
- `id` (PK): Booking ID
- `service_id` (FK): References services.id
- `client_id` (FK): References profiles.id
- `provider_id` (FK): References service_providers.id
- `status`: Booking status
- `created_at`: Timestamp
- `scheduled_for`: Scheduled time
- `payment_status`: Payment status
- `payment_id`: Payment reference
- `conversation_id` (FK): References chat_conversations.id

## Integration Tables

### location_messages
- `id` (PK): Location message ID
- `conversation_id` (FK): References chat_conversations.id
- `sender_id` (FK): References profiles.id
- `lat`: Latitude
- `lng`: Longitude
- `label`: Location label
- `expiration`: Expiration timestamp
- `is_expired`: Boolean for expired status

### activity_chat_links
- `id` (PK): Link ID
- `activity_id` (FK): References activities.id
- `conversation_id` (FK): References chat_conversations.id
- `created_at`: Timestamp

### service_chat_links
- `id` (PK): Link ID
- `service_id` (FK): References services.id
- `booking_id` (FK): References service_bookings.id
- `conversation_id` (FK): References chat_conversations.id
- `created_at`: Timestamp
