
import React from 'react';
import { Button } from '@/components/ui/button';
import { Share2 } from 'lucide-react';
import { Activity } from '@/types/activity';
import { useToast } from '@/hooks/use-toast';

interface ShareActivityButtonProps {
  activity: Activity;
}

export function ShareActivityButton({ activity }: ShareActivityButtonProps) {
  const { toast } = useToast();
  
  const handleShare = () => {
    // Check if Web Share API is available
    if (navigator.share) {
      navigator.share({
        title: activity.title,
        text: activity.description || `Join ${activity.title}`,
        url: window.location.href
      })
      .then(() => {
        console.log('Activity shared successfully');
      })
      .catch((error) => {
        console.error('Error sharing activity:', error);
        fallbackShare();
      });
    } else {
      fallbackShare();
    }
  };
  
  const fallbackShare = () => {
    // Copy link to clipboard as fallback
    navigator.clipboard.writeText(window.location.href)
      .then(() => {
        toast({
          title: "Link Copied!",
          description: "Activity link copied to clipboard",
        });
      })
      .catch((err) => {
        console.error('Failed to copy link:', err);
        toast({
          title: "Couldn't copy link",
          description: "Please try again or copy manually",
          variant: "destructive"
        });
      });
  };
  
  return (
    <Button variant="outline" className="w-full" onClick={handleShare}>
      <Share2 className="h-4 w-4 mr-2" />
      Share Activity
    </Button>
  );
}
