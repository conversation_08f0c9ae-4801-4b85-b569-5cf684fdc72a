
import { useEffect } from 'react';
import mapboxgl from 'mapbox-gl';

interface AtmosphereLayerProps {
  map: mapboxgl.Map | null;
}

export function AtmosphereLayer({ map }: AtmosphereLayerProps) {
  useEffect(() => {
    if (!map || !map.isStyleLoaded()) return;
    
    // Enhanced fog and atmosphere configuration
    map.setFog({
      'color': 'rgb(236, 245, 240)', // Light mint tinted fog
      'high-color': 'rgb(225, 240, 235)', // Subtle green-blue high fog
      'horizon-blend': 0.15, // Smoother transition
      'space-color': 'rgb(220, 240, 235)', // Slightly greener space color
      'star-intensity': 0.05, // Very subtle stars
      'range': [0.5, 10], // Enhanced depth perception
    });
    
    // Enhanced lighting configuration - removed transition property
    map.setLight({
      anchor: 'viewport',
      color: 'white',
      intensity: 0.4,
      position: [1.5, 180, 45] // 45-degree lighting angle
    });
    
    // Set terrain configuration if source exists
    try {
      if (map.getSource('mapbox-dem')) {
        map.setTerrain({
          source: 'mapbox-dem',
          exaggeration: 1.5, // Subtle terrain exaggeration
        });
      }
    } catch (error) {
      console.log('Terrain source not available');
    }
    
  }, [map]);
  
  return null;
}
