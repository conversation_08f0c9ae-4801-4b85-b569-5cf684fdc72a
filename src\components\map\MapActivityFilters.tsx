
import React, { useState, useEffect } from 'react';
import { Check, ChevronDown, MapPin, CircleDollarSign, Dices } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { Checkbox } from '@/components/ui/checkbox';
import { Badge } from '@/components/ui/badge';
import { ActivityMapFilters } from '@/hooks/use-map-activities';

export interface MapActivityFiltersProps {
  onFilterChange?: (filters: any) => void;
  onFiltersChange?: (filters: ActivityMapFilters) => void;
  className?: string;
}

export function MapActivityFilters({ onFilterChange, onFiltersChange, className = '' }: MapActivityFiltersProps) {
  const [distance, setDistance] = useState<number>(5);
  const [isOpen, setIsOpen] = useState(false);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);

  // Example categories - in a real app these would be fetched from the backend
  const categories = [
    { id: 'food', name: 'Food & Drink' },
    { id: 'outdoor', name: 'Outdoor' },
    { id: 'social', name: 'Social' },
    { id: 'entertainment', name: 'Entertainment' },
  ];

  // Notify parent component when filters change
  useEffect(() => {
    const filters = {
      categories: selectedCategories,
      distance,
      price: { min: priceRange[0], max: priceRange[1] }
    };
    
    if (onFilterChange) {
      onFilterChange(filters);
    }
    
    if (onFiltersChange) {
      onFiltersChange({
        category: selectedCategories[0],
        categoryIds: selectedCategories,
        radius: distance,
        minPrice: priceRange[0],
        maxPrice: priceRange[1]
      });
    }
  }, [selectedCategories, distance, priceRange, onFilterChange, onFiltersChange]);

  const toggleCategory = (categoryId: string) => {
    setSelectedCategories(current => 
      current.includes(categoryId)
        ? current.filter(id => id !== categoryId)
        : [...current, categoryId]
    );
  };

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden ${className}`}>
      <div className="p-4 border-b">
        <Button 
          variant="ghost" 
          className="w-full justify-between" 
          onClick={() => setIsOpen(!isOpen)}
        >
          <span className="font-medium">Filters</span>
          <ChevronDown className={`h-4 w-4 transition-transform ${isOpen ? 'transform rotate-180' : ''}`} />
        </Button>
      </div>
      
      {isOpen && (
        <div className="p-4 space-y-4">
          {/* Distance slider */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium flex items-center">
                <MapPin className="h-4 w-4 mr-2 text-muted-foreground" />
                Distance
              </span>
              <span className="text-sm text-muted-foreground">{distance} miles</span>
            </div>
            <Slider
              value={[distance]}
              min={1}
              max={20}
              step={1}
              onValueChange={(values) => setDistance(values[0])}
            />
          </div>

          {/* Price range slider */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium flex items-center">
                <CircleDollarSign className="h-4 w-4 mr-2 text-muted-foreground" />
                Price Range
              </span>
              <span className="text-sm text-muted-foreground">${priceRange[0]} - ${priceRange[1]}</span>
            </div>
            <Slider
              value={priceRange}
              min={0}
              max={100}
              step={5}
              onValueChange={(values) => setPriceRange(values as [number, number])}
            />
          </div>

          {/* Categories */}
          <div>
            <span className="text-sm font-medium mb-2 block">Categories</span>
            <div className="flex flex-wrap gap-2 mt-2">
              {categories.map(category => (
                <Badge
                  key={category.id}
                  variant={selectedCategories.includes(category.id) ? 'default' : 'outline'}
                  className={`cursor-pointer ${selectedCategories.includes(category.id) ? 'bg-primary' : ''}`}
                  onClick={() => toggleCategory(category.id)}
                >
                  {category.name}
                  {selectedCategories.includes(category.id) && (
                    <Check className="ml-1 h-3 w-3" />
                  )}
                </Badge>
              ))}
            </div>
          </div>

          {/* Apply/Reset buttons */}
          <div className="flex gap-2 mt-4">
            <Button 
              variant="outline" 
              size="sm" 
              className="flex-1"
              onClick={() => {
                setSelectedCategories([]);
                setDistance(5);
                setPriceRange([0, 100]);
              }}
            >
              Reset
            </Button>
            <Button 
              size="sm" 
              className="flex-1"
              onClick={() => setIsOpen(false)}
            >
              Apply
            </Button>
          </div>

          <Button 
            variant="secondary" 
            size="sm" 
            className="w-full flex items-center justify-center mt-2"
            onClick={() => {
              // Randomly select filters for fun
              const randomCategories = categories
                .filter(() => Math.random() > 0.5)
                .map(c => c.id);
              const randomDistance = Math.floor(Math.random() * 19) + 1;
              const randomMin = Math.floor(Math.random() * 50);
              const randomMax = randomMin + Math.floor(Math.random() * (100 - randomMin));
              
              setSelectedCategories(randomCategories);
              setDistance(randomDistance);
              setPriceRange([randomMin, randomMax]);
            }}
          >
            <Dices className="mr-2 h-4 w-4" />
            Random Filters
          </Button>
        </div>
      )}
    </div>
  );
}

export default MapActivityFilters;
