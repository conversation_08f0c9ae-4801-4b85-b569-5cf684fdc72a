import React, { useState } from 'react';
import { Button } from "@/components/ui/button";
import { SubscriptionModal } from "./SubscriptionModal";
import { <PERSON><PERSON><PERSON>, <PERSON>, Star } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface SubscriptionButtonProps {
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  children?: React.ReactNode;
  showBadge?: boolean;
  badgeText?: string;
  animated?: boolean;
  data?: any;
}

export function SubscriptionButton({
  variant = "default",
  size = "default",
  className,
  children,
  showBadge = false,
  badgeText = "Premium",
  animated = true,
  data,
  ...props
}: SubscriptionButtonProps) {
  const [showModal, setShowModal] = useState(false);
  const [isHovering, setIsHovering] = useState(false);
  const { toast } = useToast();

  const handleSubscribe = (plan: string) => {
    // This would be replaced with your actual subscription logic
    toast({
      title: "Subscription Selected",
      description: `You selected the ${plan} plan. Implement payment processing here.`,
      variant: "default",
    });
  };

  const ButtonComponent = animated ? motion.div : React.Fragment;
  const motionProps = animated ? {
    whileHover: { scale: 1.05 },
    whileTap: { scale: 0.95 },
    onHoverStart: () => setIsHovering(true),
    onHoverEnd: () => setIsHovering(false)
  } : {};

  return (
    <>
      <ButtonComponent {...motionProps}>
        <div className="relative inline-block">
          {showBadge && (
            <div className="absolute -top-2 -right-2 z-10">
              <div className="bg-amber-400 text-white text-xs font-bold px-2 py-0.5 rounded-full flex items-center">
                <Crown className="h-3 w-3 mr-0.5" />
                {badgeText}
              </div>
            </div>
          )}

          <Button
            variant={variant}
            size={size}
            className={cn(
              className,
              animated && isHovering ? "shadow-md" : ""
            )}
            onClick={() => setShowModal(true)}
            {...props}
          >
            {children || (
              <>
                <Sparkles className="h-4 w-4 mr-2" />
                Upgrade
                {animated && isHovering && (
                  <Star className="h-3 w-3 ml-1 text-yellow-300 fill-yellow-300" />
                )}
              </>
            )}
          </Button>
        </div>
      </ButtonComponent>

      <SubscriptionModal
        open={showModal}
        onOpenChange={setShowModal}
        onSubscribe={handleSubscribe}
      />
    </>
  );
}
