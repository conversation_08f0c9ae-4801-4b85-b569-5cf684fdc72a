-- Transaction and payment related indexes
CREATE INDEX IF NOT EXISTS idx_transaction_flags_resolved_by ON public.transaction_flags(resolved_by);
CREATE INDEX IF NOT EXISTS idx_transaction_reviews_reviewer_id ON public.transaction_reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_transaction_reviews_transaction_id ON public.transaction_reviews(transaction_id);
CREATE INDEX IF NOT EXISTS idx_payment_dispute_events_actor_id ON public.payment_dispute_events(actor_id);
CREATE INDEX IF NOT EXISTS idx_payment_dispute_events_dispute_id ON public.payment_dispute_events(dispute_id);
CREATE INDEX IF NOT EXISTS idx_payment_dispute_messages_dispute_id ON public.payment_dispute_messages(dispute_id);
CREATE INDEX IF NOT EXISTS idx_payment_dispute_messages_sender_id ON public.payment_dispute_messages(sender_id);

-- Payment disputes indexes
CREATE INDEX IF NOT EXISTS idx_payment_disputes_activity_id ON public.payment_disputes(activity_id);
CREATE INDEX IF NOT EXISTS idx_payment_disputes_appeal_reviewed_by ON public.payment_disputes(appeal_reviewed_by);
CREATE INDEX IF NOT EXISTS idx_payment_disputes_booking_id ON public.payment_disputes(booking_id);
CREATE INDEX IF NOT EXISTS idx_payment_disputes_provider_id ON public.payment_disputes(provider_id);
CREATE INDEX IF NOT EXISTS idx_payment_disputes_refund_transaction_id ON public.payment_disputes(refund_transaction_id);
CREATE INDEX IF NOT EXISTS idx_payment_disputes_resolved_by ON public.payment_disputes(resolved_by);
CREATE INDEX IF NOT EXISTS idx_payment_disputes_transaction_id ON public.payment_disputes(transaction_id);
CREATE INDEX IF NOT EXISTS idx_payment_disputes_user_id ON public.payment_disputes(user_id);

-- Service related indexes
CREATE INDEX IF NOT EXISTS idx_service_providers_user_id ON public.service_providers(user_id);
CREATE INDEX IF NOT EXISTS idx_service_providers_verification_level_id ON public.service_providers(verification_level_id);
CREATE INDEX IF NOT EXISTS idx_services_category_id ON public.services(category_id);
CREATE INDEX IF NOT EXISTS idx_services_provider_id ON public.services(provider_id);
CREATE INDEX IF NOT EXISTS idx_service_bookings_client_id ON public.service_bookings(client_id);
CREATE INDEX IF NOT EXISTS idx_service_bookings_provider_id ON public.service_bookings(provider_id);
CREATE INDEX IF NOT EXISTS idx_service_bookings_service_id ON public.service_bookings(service_id);

-- Service fulfillment indexes
CREATE INDEX IF NOT EXISTS idx_service_deliverables_booking_id ON public.service_deliverables(booking_id);
CREATE INDEX IF NOT EXISTS idx_service_deliverables_milestone_id ON public.service_deliverables(milestone_id);
CREATE INDEX IF NOT EXISTS idx_service_fulfillment_stages_booking_id ON public.service_fulfillment_stages(booking_id);
CREATE INDEX IF NOT EXISTS idx_service_milestones_booking_id ON public.service_milestones(booking_id);
CREATE INDEX IF NOT EXISTS idx_service_milestones_stage_id ON public.service_milestones(stage_id);

-- Gig related indexes
CREATE INDEX IF NOT EXISTS idx_gigs_category_id ON public.gigs(category_id);
CREATE INDEX IF NOT EXISTS idx_gigs_user_id ON public.gigs(user_id);
CREATE INDEX IF NOT EXISTS idx_gig_orders_client_id ON public.gig_orders(client_id);
CREATE INDEX IF NOT EXISTS idx_gig_orders_conversation_id ON public.gig_orders(conversation_id);
CREATE INDEX IF NOT EXISTS idx_gig_orders_gig_id ON public.gig_orders(gig_id);
CREATE INDEX IF NOT EXISTS idx_gig_orders_package_id ON public.gig_orders(package_id);
CREATE INDEX IF NOT EXISTS idx_gig_orders_provider_id ON public.gig_orders(provider_id);
CREATE INDEX IF NOT EXISTS idx_gig_packages_gig_id ON public.gig_packages(gig_id);
CREATE INDEX IF NOT EXISTS idx_gig_portfolio_gig_id ON public.gig_portfolio(gig_id);

-- Review related indexes
CREATE INDEX IF NOT EXISTS idx_provider_reviews_reviewer_id ON public.provider_reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_reviews_booking_id ON public.reviews(booking_id);
CREATE INDEX IF NOT EXISTS idx_reviews_provider_id ON public.reviews(provider_id);
CREATE INDEX IF NOT EXISTS idx_reviews_reviewer_id ON public.reviews(reviewer_id);
CREATE INDEX IF NOT EXISTS idx_reviews_service_id ON public.reviews(service_id);
CREATE INDEX IF NOT EXISTS idx_review_flags_resolved_by ON public.review_flags(resolved_by);

-- Verification related indexes
CREATE INDEX IF NOT EXISTS idx_provider_verifications_appeal_processed_by ON public.provider_verifications(appeal_processed_by);
CREATE INDEX IF NOT EXISTS idx_provider_verifications_processed_by ON public.provider_verifications(processed_by);
CREATE INDEX IF NOT EXISTS idx_verification_steps_assigned_to ON public.verification_steps(assigned_to);
CREATE INDEX IF NOT EXISTS idx_verification_steps_workflow_id ON public.verification_steps(workflow_id);
CREATE INDEX IF NOT EXISTS idx_verification_workflows_assigned_to ON public.verification_workflows(assigned_to);
CREATE INDEX IF NOT EXISTS idx_verification_workflows_provider_id ON public.verification_workflows(provider_id);
CREATE INDEX IF NOT EXISTS idx_verification_workflows_verification_id ON public.verification_workflows(verification_id);

-- Dispute related indexes
CREATE INDEX IF NOT EXISTS idx_dispute_categories_parent_id ON public.dispute_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_dispute_templates_category_id ON public.dispute_templates(category_id);
CREATE INDEX IF NOT EXISTS idx_dispute_templates_created_by ON public.dispute_templates(created_by);
CREATE INDEX IF NOT EXISTS idx_report_categories_parent_id ON public.report_categories(parent_id);
CREATE INDEX IF NOT EXISTS idx_report_templates_category_id ON public.report_templates(category_id);
CREATE INDEX IF NOT EXISTS idx_report_templates_created_by ON public.report_templates(created_by);

-- Geofence related indexes
CREATE INDEX IF NOT EXISTS idx_geofences_activity_id ON public.geofences(activity_id);
CREATE INDEX IF NOT EXISTS idx_geofences_created_by ON public.geofences(created_by);

-- Message related indexes
CREATE INDEX IF NOT EXISTS idx_message_reactions_user_id ON public.message_reactions(user_id);
CREATE INDEX IF NOT EXISTS idx_messages_activity_id ON public.messages(activity_id);
CREATE INDEX IF NOT EXISTS idx_messages_recipient_id ON public.messages(recipient_id);
CREATE INDEX IF NOT EXISTS idx_typing_indicators_user_id ON public.typing_indicators(user_id);

-- Proposal related indexes
CREATE INDEX IF NOT EXISTS idx_proposals_activity_id ON public.proposals(activity_id);
CREATE INDEX IF NOT EXISTS idx_proposals_recipient_id ON public.proposals(recipient_id);
CREATE INDEX IF NOT EXISTS idx_proposals_sender_id ON public.proposals(sender_id);
CREATE INDEX IF NOT EXISTS idx_proposals_service_id ON public.proposals(service_id);

-- Analytics related indexes
CREATE INDEX IF NOT EXISTS idx_analytics_metrics_created_by ON public.analytics_metrics(created_by);
CREATE INDEX IF NOT EXISTS idx_analytics_user_segments_created_by ON public.analytics_user_segments(created_by);

-- Moderation related indexes
CREATE INDEX IF NOT EXISTS idx_moderation_audit_log_moderator_id ON public.moderation_audit_log(moderator_id);
CREATE INDEX IF NOT EXISTS idx_moderation_rules_created_by ON public.moderation_rules(created_by);
