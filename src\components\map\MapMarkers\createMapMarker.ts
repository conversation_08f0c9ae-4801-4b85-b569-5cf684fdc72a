
import mapboxgl from 'mapbox-gl';
import { UnifiedMapUser } from '@/types/map';
import { Activity } from '@/types/activity';

export function createUserMarker(
  user: UnifiedMapUser,
  map: mapboxgl.Map,
  onClick: () => void,
  isSelected: boolean = false
): mapboxgl.Marker | null {
  if (!user.location) return null;

  // Create marker element
  const markerEl = document.createElement('div');
  markerEl.className = `user-marker relative group transition-all duration-300 ${isSelected ? 'selected-user' : ''}`;

  // User avatar container
  const avatarContainer = document.createElement('div');
  avatarContainer.className = `relative rounded-full overflow-hidden border-2 ${isSelected ? 'border-primary border-opacity-100' : 'border-primary border-opacity-70'} shadow-lg`;
  avatarContainer.style.width = isSelected ? '44px' : '36px';
  avatarContainer.style.height = isSelected ? '44px' : '36px';
  avatarContainer.style.transition = 'all 0.3s ease';

  // Add avatar image
  const avatarImg = document.createElement('img');
  avatarImg.src = user.avatar_url || `https://i.pravatar.cc/150?u=${user.user_id}`;
  avatarImg.alt = user.display_name || user.username || 'User';
  avatarImg.className = 'w-full h-full object-cover';
  avatarImg.onerror = () => {
    avatarImg.style.display = 'none';
    initials.style.display = 'flex';
  };

  // Create initials fallback
  const initials = document.createElement('div');
  initials.className = 'w-full h-full flex items-center justify-center bg-primary text-primary-foreground font-bold text-sm';
  initials.style.display = 'none';
  const displayName = user.display_name || user.username || `User ${user.user_id?.substring?.(0, 5)}`;
  initials.textContent = displayName
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);

  // Online status indicator
  const isOnline = user.is_online;
  if (isOnline) {
    const statusDot = document.createElement('div');
    statusDot.className = 'online-indicator absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white';
    avatarContainer.appendChild(statusDot);
  }

  // Tooltip with user name
  const tooltip = document.createElement('div');
  tooltip.className = 'marker-tooltip';
  tooltip.textContent = user.display_name || user.username || 'User';
  tooltip.style.whiteSpace = 'nowrap';

  // Verification badge
  if (user.is_verified) {
    const badge = document.createElement('div');
    badge.className = 'absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full border border-white flex items-center justify-center';
    badge.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round" class="w-3 h-3"><polyline points="20 6 9 17 4 12"></polyline></svg>';
    avatarContainer.appendChild(badge);
  }

  // If selected, add a pulsing effect around the marker
  if (isSelected) {
    const pulseRing = document.createElement('div');
    pulseRing.className = 'absolute inset-0 rounded-full animate-pulse';
    pulseRing.style.animation = 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite';
    pulseRing.style.boxShadow = '0 0 0 8px rgba(79, 70, 229, 0.3)';
    pulseRing.style.zIndex = '-1';
    markerEl.appendChild(pulseRing);
  }

  // Assemble the marker
  avatarContainer.appendChild(avatarImg);
  avatarContainer.appendChild(initials);
  markerEl.appendChild(avatarContainer);
  markerEl.appendChild(tooltip);

  // Add hover effect
  markerEl.addEventListener('mouseenter', () => {
    avatarContainer.style.transform = 'scale(1.1)';
    avatarContainer.style.borderColor = '#0ea5e9';
    tooltip.style.opacity = '1';
  });

  markerEl.addEventListener('mouseleave', () => {
    avatarContainer.style.transform = 'scale(1)';
    avatarContainer.style.borderColor = isSelected ? '#4f46e5' : '';
    tooltip.style.opacity = '0';
  });

  // Add click handler
  markerEl.addEventListener('click', (e) => {
    e.stopPropagation();
    onClick();
  });

  // Create and return marker
  return new mapboxgl.Marker({
    element: markerEl,
    anchor: 'bottom',
    offset: [0, -3]
  }).setLngLat([user.location.x, user.location.y]);
}

export function createActivityMarker(
  activity: Activity,
  map: mapboxgl.Map,
  onClick: () => void,
  isSelected: boolean = false,
  isNew: boolean = false
): mapboxgl.Marker | null {
  if (!activity.location) return null;

  // Create marker element
  const markerEl = document.createElement('div');
  markerEl.className = `activity-marker relative group transition-all duration-300 ${isSelected ? 'selected-activity' : ''} ${isNew ? 'new-activity' : ''}`;

  // Activity marker container
  const markerContainer = document.createElement('div');
  markerContainer.className = `rounded-lg overflow-hidden shadow-lg ${isSelected ? 'ring-2 ring-primary ring-offset-2' : ''}`;
  markerContainer.style.width = isSelected ? '48px' : '40px';
  markerContainer.style.height = isSelected ? '48px' : '40px';
  markerContainer.style.backgroundColor = getCategoryColor(activity.category?.name || '');
  markerContainer.style.transition = 'all 0.3s ease';

  // Activity icon container
  const iconContainer = document.createElement('div');
  iconContainer.className = 'w-full h-full flex items-center justify-center text-white';

  // Activity icon
  const activityIcon = document.createElement('div');
  activityIcon.innerHTML = getActivityIcon(activity.category?.name || '');

  // Tooltip with activity title
  const tooltip = document.createElement('div');
  tooltip.className = 'marker-tooltip';
  tooltip.textContent = activity.title;

  // If it's a new activity, add a pulse effect
  if (isNew) {
    const pulseEffect = document.createElement('div');
    pulseEffect.className = 'absolute inset-0 bg-primary rounded-lg';
    pulseEffect.style.animation = 'ping 1.5s cubic-bezier(0, 0, 0.2, 1) infinite';
    pulseEffect.style.opacity = '0.3';
    markerEl.appendChild(pulseEffect);
  }

  // If selected, add a highlighted effect
  if (isSelected) {
    const highlightEffect = document.createElement('div');
    highlightEffect.className = 'absolute inset-0 rounded-lg';
    highlightEffect.style.boxShadow = '0 0 0 4px rgba(79, 70, 229, 0.5)';
    highlightEffect.style.zIndex = '-1';
    markerEl.appendChild(highlightEffect);
  }

  // If it has price, add price badge
  if (activity.is_paid && activity.price) {
    const priceBadge = document.createElement('div');
    priceBadge.className = 'price-indicator absolute -top-2 -right-2 min-w-5 h-5 bg-green-500 rounded-full border border-white flex items-center justify-center px-1 text-xs text-white font-bold';
    priceBadge.textContent = `$${Math.round(Number(activity.price))}`;
    markerContainer.appendChild(priceBadge);
  }

  // Assemble the marker
  iconContainer.appendChild(activityIcon);
  markerContainer.appendChild(iconContainer);
  markerEl.appendChild(markerContainer);
  markerEl.appendChild(tooltip);

  // Add hover effect
  markerEl.addEventListener('mouseenter', () => {
    markerContainer.style.transform = 'scale(1.1)';
    tooltip.style.opacity = '1';
  });

  markerEl.addEventListener('mouseleave', () => {
    markerContainer.style.transform = 'scale(1)';
    tooltip.style.opacity = '0';
  });

  // Add click handler
  markerEl.addEventListener('click', (e) => {
    e.stopPropagation();
    onClick();
  });

  // Create and return marker
  return new mapboxgl.Marker({
    element: markerEl,
    anchor: 'bottom',
    offset: [0, 0]
  }).setLngLat([activity.location.x, activity.location.y]);
}

// Helper function to get color based on category
function getCategoryColor(category: string): string {
  const categoryColors: Record<string, string> = {
    'Sports': '#2563eb', // blue-600
    'Social': '#8b5cf6', // violet-500
    'Food': '#f97316', // orange-500
    'Education': '#10b981', // emerald-500
    'Arts': '#ec4899', // pink-500
    'Music': '#6366f1', // indigo-500
    'Outdoors': '#65a30d', // lime-600
    'Games': '#d946ef', // fuchsia-500
    'Technology': '#0284c7', // sky-600
    'Wellness': '#14b8a6', // teal-500
    'Business': '#475569', // slate-600
    'Other': '#6b7280', // gray-500
  };

  return categoryColors[category] || '#6b7280'; // Default to gray
}

// Helper function to get icon based on category
function getActivityIcon(category: string): string {
  const icons: Record<string, string> = {
    'Sports': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="M17.92 12c.05-.33.08-.66.08-1 0-3.35-2.69-6.05-6-6.05S6 7.65 6 11c0 .34.03.67.08 1"/><path d="M12 12v9"/><path d="M12 12h4"/><path d="M12 12h-3"/></svg>',
    'Social': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"/><circle cx="9" cy="7" r="4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/></svg>',
    'Food': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8h1a4 4 0 0 1 0 8h-1"/><path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"/><line x1="6" y1="1" x2="6" y2="4"/><line x1="10" y1="1" x2="10" y2="4"/><line x1="14" y1="1" x2="14" y2="4"/></svg>',
    'Education': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M4 19.5A2.5 2.5 0 0 1 6.5 17H20"/><path d="M6.5 2H20v20H6.5A2.5 2.5 0 0 1 4 19.5v-15A2.5 2.5 0 0 1 6.5 2z"/></svg>',
    'Arts': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><path d="m14.31 8 5.74 9.94"/><path d="M9.69 8h11.48"/><path d="m7.38 12 5.74-9.94"/><path d="M9.69 16 3.95 6.06"/><path d="M14.31 16H2.83"/><path d="m16.62 12-5.74 9.94"/></svg>',
    'Music': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 18V5l12-2v13"/><circle cx="6" cy="18" r="3"/><circle cx="18" cy="16" r="3"/></svg>',
    'Outdoors': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M8 3v3a2 2 0 0 1-2 2H3"/><path d="M21 3v3a2 2 0 0 0 2 2h3"/><path d="M3 16v3a2 2 0 0 0 2 2h3"/><path d="M16 21h3a2 2 0 0 0 2-2v-3"/><path d="M19 12c0-3.87-3.13-7-7-7"/><path d="M5 12a7 7 0 0 0 7 7"/><polyline points="12 12 12 16 16 16"/><polyline points="9 9 12 12 12 12"/></svg>',
    'Games': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 11h4"/><path d="M8 9v4"/><path d="M15 12h.01"/><path d="M18 10h.01"/><rect width="20" height="12" x="2" y="6" rx="2"/><path d="M2 10h20"/></svg>',
    'Technology': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="14" height="20" x="5" y="2" rx="2" ry="2"/><path d="M12 18h.01"/></svg>',
    'Wellness': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M18 8a6 6 0 0 0-6-6 6 6 0 0 0-6 6c0 7 6 13 6 13s6-6 6-13Z"/><circle cx="12" cy="8" r="2"/></svg>',
    'Business': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect width="20" height="14" x="2" y="7" rx="2" ry="2"/><path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"/></svg>',
    'default': '<svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/></svg>'
  };

  return icons[category] || icons['default'];
}
