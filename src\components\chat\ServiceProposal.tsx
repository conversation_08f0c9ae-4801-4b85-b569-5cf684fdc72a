import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Check, X, Loader2, Calendar, DollarSign } from 'lucide-react';
import { useProvider } from '@/hooks/use-provider';
import { useAuth } from '@/hooks/use-auth';
import { format, isPast } from 'date-fns';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';

interface ServiceProposalProps {
  proposal: {
    id: string;
    sender_id: string;
    recipient_id: string;
    status: 'pending' | 'accepted' | 'rejected';
    expires_at?: string;
    created_at: string;
    service_id?: string;
  };
  onRespond?: (proposalId: string, status: 'accepted' | 'rejected') => Promise<void>;
  compact?: boolean;
}

export function ServiceProposal({ proposal, onRespond, compact = false }: ServiceProposalProps) {
  const { user } = useAuth();
  const [isResponding, setIsResponding] = useState(false);
  const providerId = proposal.recipient_id;
  const { data: provider } = useProvider(providerId);
  
  const isRecipient = user?.id === proposal.recipient_id;
  const canRespond = isRecipient && proposal.status === 'pending';
  const isExpired = proposal.expires_at ? isPast(new Date(proposal.expires_at)) : false;

  const handleRespond = async (status: 'accepted' | 'rejected') => {
    if (!onRespond) return;
    
    setIsResponding(true);
    try {
      await onRespond(proposal.id, status);
    } catch (error) {
      console.error('Error responding to proposal:', error);
    } finally {
      setIsResponding(false);
    }
  };

  const displayName = provider?.name || 'Service provider';
  
  return (
    <Card className={`${compact ? 'p-3' : 'p-4'} mb-2 bg-slate-50/80`}>
      <CardContent className="p-0">
        <div className="flex flex-col gap-2">
          {!compact && (
            <div className="flex items-center gap-2">
              <Avatar className="h-8 w-8">
                <AvatarImage src={provider?.profiles?.avatar_url || ''} />
                <AvatarFallback>
                  {displayName.slice(0, 2).toUpperCase()}
                </AvatarFallback>
              </Avatar>
              <div>
                <div className="text-sm font-medium">{displayName}</div>
                <div className="text-xs text-muted-foreground">Service Proposal</div>
              </div>
            </div>
          )}
          
          <div className={`flex ${compact ? 'flex-row items-center justify-between' : 'flex-col gap-2'}`}>
            <div className="flex items-center text-sm gap-1">
              <Calendar className="h-4 w-4 text-blue-500" />
              <span className="font-medium">Service Booking</span>
            </div>
            
            {provider?.hourly_rate && (
              <div className="text-sm text-gray-600 flex items-center">
                <DollarSign className="h-3 w-3 mr-1" />
                <span>${provider.hourly_rate}/hr</span>
              </div>
            )}
          </div>
          
          {!compact && (
            <div className="text-sm text-gray-500 flex items-center justify-between">
              <span>Status:</span>
              <Badge 
                variant={
                  proposal.status === 'accepted' ? 'default' :
                  proposal.status === 'rejected' ? 'destructive' : 
                  'secondary'
                }
              >
                {proposal.status.charAt(0).toUpperCase() + proposal.status.slice(1)}
              </Badge>
            </div>
          )}
          
          {proposal.expires_at && !compact && (
            <div className="text-xs text-gray-400">
              {isExpired 
                ? <span className="text-red-500">Expired: {format(new Date(proposal.expires_at), 'PPp')}</span>
                : <span>Expires: {format(new Date(proposal.expires_at), 'PPp')}</span>
              }
            </div>
          )}
          
          {canRespond && !isExpired && (
            <div className="flex gap-2 mt-2">
              <Button
                size="sm"
                variant="default"
                className="w-full"
                onClick={() => handleRespond('accepted')}
                disabled={isResponding}
              >
                {isResponding ? (
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                ) : (
                  <Check className="h-4 w-4 mr-1" />
                )}
                Accept
              </Button>
              <Button
                size="sm"
                variant="destructive"
                className="w-full"
                onClick={() => handleRespond('rejected')}
                disabled={isResponding}
              >
                {isResponding ? (
                  <Loader2 className="h-4 w-4 mr-1 animate-spin" />
                ) : (
                  <X className="h-4 w-4 mr-1" />
                )}
                Decline
              </Button>
            </div>
          )}
          
          {compact && (
            <div className={`text-xs ${
              proposal.status === 'accepted' ? 'text-green-600' : 
              proposal.status === 'rejected' ? 'text-red-600' : 
              'text-amber-600'
            }`}>
              {proposal.status === 'accepted' ? 'Accepted' : 
               proposal.status === 'rejected' ? 'Declined' : 
               isExpired ? 'Expired' : 'Awaiting response'}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
