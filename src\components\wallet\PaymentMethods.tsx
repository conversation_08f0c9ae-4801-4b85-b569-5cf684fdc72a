
import React from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card } from '@/components/ui/card';
import { CreditCard, Plus, Loader2 } from 'lucide-react';
import { useCustomerPortal } from '@/hooks/use-customer-portal';

export function PaymentMethods() {
  const { mutate: openCustomerPortal, isPending } = useCustomerPortal();

  const handleManagePaymentMethods = () => {
    openCustomerPortal();
  };

  return (
    <div className="space-y-4">
      <div className="text-center py-8 text-muted-foreground">
        <CreditCard className="mx-auto h-12 w-12 text-muted-foreground/50" />
        <p className="mt-2">No payment methods added yet</p>
        <p className="text-sm">Add a payment method to make purchases and join paid activities</p>
      </div>
      
      <Button 
        className="w-full"
        onClick={handleManagePaymentMethods}
        disabled={isPending}
      >
        {isPending ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Loading...
          </>
        ) : (
          <>
            <Plus className="mr-2 h-4 w-4" /> 
            Manage Payment Methods
          </>
        )}
      </Button>
    </div>
  );
}
