import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { useToast } from "@/components/ui/use-toast"
import { Switch } from "@/components/ui/switch"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { ActivityCreationValues } from '@/schemas/activity-creation-schema';
import { ActivityCreationData, Location, MediaFile } from '@/types/activity';
import { useCreateActivity } from '@/hooks/use-create-activity';
import { useAuth } from '@/hooks/use-auth';
import { ActivityCategorySelect } from './ActivityCategorySelect';
import { ActivityLocationPicker } from './ActivityLocationPicker';
import { ActivityMediaUploader } from './ActivityMediaUploader';
import { VisibilitySelect } from './VisibilitySelect';
import { QueueTypeSelect } from './QueueTypeSelect';

const activityFormSchema = z.object({
  title: z.string().min(3, {
    message: "Title must be at least 3 characters.",
  }),
  description: z.string().min(10, {
    message: "Description must be at least 10 characters.",
  }),
  categoryId: z.string().optional(),
  location: z.object({
    latitude: z.number(),
    longitude: z.number(),
    x: z.number(),
    y: z.number(),
  }),
  address: z.string().optional(),
  startTime: z.date(),
  endTime: z.date().optional(),
  maxParticipants: z.number().optional(),
  isPaid: z.boolean().default(false),
  price: z.number().optional(),
  visibility: z.enum(["public", "private", "unlisted"]).default("public"),
  queueType: z.enum(["fifo", "priority"]).default("fifo"),
  allowWaitlist: z.boolean().default(true),
});

interface ActivityCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  categoryId?: string;
}

type ActivityFormValues = z.infer<typeof activityFormSchema>;

export function ActivityCreationModal({
  isOpen,
  onClose,
  categoryId
}: ActivityCreationModalProps) {
  const [mediaFiles, setMediaFiles] = useState<MediaFile[]>([]);
  const navigate = useNavigate();
  const { toast } = useToast();

  const form = useForm<ActivityFormValues>({
    resolver: zodResolver(activityFormSchema),
    defaultValues: {
      title: "",
      description: "",
      categoryId: categoryId,
      location: {
        latitude: 0,
        longitude: 0,
        x: 0,
        y: 0
      },
      address: "",
      startTime: new Date(),
      endTime: undefined,
      maxParticipants: undefined,
      isPaid: false,
      price: undefined,
      visibility: "public",
      queueType: "fifo",
      allowWaitlist: true,
    },
    mode: "onChange",
  });

  const { user } = useAuth();
  const { createActivity, isCreating } = useCreateActivity();

  const handleSubmit = async (data: ActivityFormValues) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to create activities.",
        variant: "destructive"
      });
      return;
    }

    try {
      // Format the activity submission data
      const activityData: ActivityCreationData = {
        title: data.title,
        description: data.description,
        location: {
          x: data.location.longitude,
          y: data.location.latitude,
          latitude: data.location.latitude,
          longitude: data.location.longitude
        },
        address: data.address || "",
        start_time: data.startTime.toISOString(),
        end_time: data.endTime?.toISOString(),
        category_id: data.categoryId || categoryId,
        is_paid: data.isPaid,
        price: data.isPaid ? data.price : undefined,
        max_participants: data.maxParticipants,
        visibility: data.visibility as 'public' | 'private' | 'unlisted',
        queue_type: data.queueType as 'fifo' | 'priority',
        allow_waitlist: data.allowWaitlist,
        media_urls: mediaFiles.map(file => file.url),
      };

      // Submit the activity
      const newActivityId = await createActivity(activityData);
      
      toast({
        title: "Activity Created",
        description: "Your activity has been created successfully!",
      });
      
      onClose();
      
      // Navigate to the new activity
      if (newActivityId) {
        navigate(`/activity/${newActivityId}`);
      }
    } catch (error) {
      console.error("Failed to create activity:", error);
      toast({
        title: "Failed to create activity",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[525px]">
        <DialogHeader>
          <DialogTitle>Create a new activity</DialogTitle>
          <DialogDescription>
            Let's get started with your new activity.
          </DialogDescription>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="title"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Title</FormLabel>
                  <FormControl>
                    <Input placeholder="Activity title" {...field} />
                  </FormControl>
                  <FormDescription>
                    This is the title of your activity.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Write a description for your activity"
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Describe your activity in detail.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="categoryId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Category</FormLabel>
                  <ActivityCategorySelect field={field} />
                  <FormDescription>
                    Select a category for your activity.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <ActivityLocationPicker form={form} />
                  </FormControl>
                  <FormDescription>
                    Select a location for your activity.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="address"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Address</FormLabel>
                  <FormControl>
                    <Input placeholder="Activity address" {...field} />
                  </FormControl>
                  <FormDescription>
                    This is the address of your activity.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="startTime"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Start Time</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      {...field}
                      value={field.value ? new Date(field.value).toISOString().slice(0, 16) : ""}
                      onChange={(e) => field.onChange(new Date(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    This is the start time of your activity.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="endTime"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>End Time</FormLabel>
                  <FormControl>
                    <Input
                      type="datetime-local"
                      {...field}
                      value={field.value ? new Date(field.value).toISOString().slice(0, 16) : ""}
                      onChange={(e) => field.onChange(new Date(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>
                    This is the end time of your activity.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="maxParticipants"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Max Participants</FormLabel>
                  <FormControl>
                    <Input
                      type="number"
                      placeholder="Max participants"
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    This is the maximum number of participants for your activity.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="isPaid"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel>Paid Activity</FormLabel>
                    <FormDescription>
                      Is this a paid activity?
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            {form.getValues().isPaid && (
              <FormField
                control={form.control}
                name="price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Price</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Price"
                        {...field}
                      />
                    </FormControl>
                    <FormDescription>
                      This is the price of your activity.
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            )}
            <FormField
              control={form.control}
              name="visibility"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Visibility</FormLabel>
                  <VisibilitySelect field={field} />
                  <FormDescription>
                    Set the visibility of your activity.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="queueType"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Queue Type</FormLabel>
                  <QueueTypeSelect field={field} />
                  <FormDescription>
                    Set the queue type for your activity.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="allowWaitlist"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                  <div className="space-y-0.5">
                    <FormLabel>Allow Waitlist</FormLabel>
                    <FormDescription>
                      Allow users to join the waitlist if the activity is full?
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            <div>
              <FormLabel>Media</FormLabel>
              <ActivityMediaUploader 
                mediaFiles={mediaFiles} 
                setMediaFiles={setMediaFiles} 
              />
              <FormDescription>
                Upload media for your activity.
              </FormDescription>
            </div>
            <Button type="submit" disabled={isCreating}>
              {isCreating ? "Creating..." : "Create Activity"}
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
