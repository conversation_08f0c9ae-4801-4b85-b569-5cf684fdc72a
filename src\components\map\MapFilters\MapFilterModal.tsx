
import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerFooter,
  Drawer<PERSON>eader,
  Drawer<PERSON><PERSON><PERSON>,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { Button } from '@/components/ui/button';
import { Filter, X } from 'lucide-react';
import { useIsMobile } from '@/hooks/use-mobile';
import { cn } from '@/lib/utils';

interface MapFilterModalProps {
  className?: string;
  children: React.ReactNode;
  title?: string;
  description?: string;
  footerContent?: React.ReactNode;
}

const MapFilterModal = ({ 
  className, 
  children, 
  title = "Filter Map", 
  description = "Find users and activities based on your preferences.",
  footerContent
}: MapFilterModalProps) => {
  const [open, setOpen] = useState(false);
  const isMobile = useIsMobile();

  if (isMobile) {
    return (
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>
          <Button 
            variant="outline" 
            size="icon" 
            className={cn("bg-white shadow-md z-20 rounded-full", className)}
          >
            <Filter className="h-4 w-4" />
          </Button>
        </DrawerTrigger>
        <DrawerContent className="h-[80vh]">
          <DrawerHeader className="text-left">
            <DrawerTitle>{title}</DrawerTitle>
            <DrawerDescription>{description}</DrawerDescription>
          </DrawerHeader>
          <div className="px-4 overflow-y-auto flex-1 pb-16">
            {children}
          </div>
          <DrawerFooter className="pt-2 border-t">
            {footerContent || (
              <DrawerClose asChild>
                <Button variant="outline" className="w-full">Close</Button>
              </DrawerClose>
            )}
          </DrawerFooter>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <Button 
        variant="outline" 
        size="icon" 
        onClick={() => setOpen(true)}
        className={cn("bg-white shadow-md z-20 rounded-full", className)}
      >
        <Filter className="h-4 w-4" />
      </Button>
      <DialogContent className="max-w-md backdrop-blur-sm bg-background/90">
        <DialogHeader>
          <DialogTitle>{title}</DialogTitle>
          <DialogDescription>{description}</DialogDescription>
        </DialogHeader>
        <DialogClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
          <X className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogClose>
        <div className="overflow-y-auto max-h-[60vh]">
          {children}
        </div>
        {footerContent && (
          <div className="flex justify-end gap-2 mt-4 pt-2 border-t">
            {footerContent}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default MapFilterModal;
