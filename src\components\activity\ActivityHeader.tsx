
import React from 'react';
import { Activity } from '@/types/activity';
import { format } from 'date-fns';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { CalendarIcon, MapPinIcon, Clock, Users, DollarSign } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { useAuth } from '@/hooks/use-auth';
import { useJoinActivity } from '@/hooks/use-activity-participants';
import { ActivityJoin } from './ActivityJoin';

interface ActivityHeaderProps {
  activity: Activity;
  canJoin?: boolean;
}

export function ActivityHeader({ activity, canJoin = false }: ActivityHeaderProps) {
  const { user } = useAuth();
  const startDate = new Date(activity.start_time);
  const endDate = activity.end_time ? new Date(activity.end_time) : null;
  
  // Format date and time
  const formattedDate = format(startDate, 'EEEE, MMMM d, yyyy');
  const formattedStartTime = format(startDate, 'h:mm a');
  const formattedEndTime = endDate ? format(endDate, 'h:mm a') : null;
  
  // Get host initials for fallback
  const hostName = activity.host?.display_name || activity.host?.username || 'Unknown Host';
  const hostInitials = hostName.split(' ')
    .map(part => part[0])
    .join('')
    .substring(0, 2)
    .toUpperCase();

  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex flex-col gap-4">
          {/* Title and category */}
          <div className="space-y-2">
            <div className="flex items-start justify-between">
              <h1 className="text-3xl font-bold">{activity.title}</h1>
              {activity.category && (
                <Badge variant="outline" className="text-xs">
                  {activity.category.name}
                </Badge>
              )}
            </div>
            
            {/* Host information */}
            <div className="flex items-center gap-2">
              <Avatar className="h-6 w-6">
                {activity.host?.avatar_url ? (
                  <AvatarImage src={activity.host.avatar_url} alt={hostName} />
                ) : (
                  <AvatarFallback>{hostInitials}</AvatarFallback>
                )}
              </Avatar>
              <span className="text-sm text-muted-foreground">Hosted by {hostName}</span>
            </div>
          </div>
          
          {/* Date, time, and location info */}
          <div className="flex flex-wrap gap-4 text-sm text-muted-foreground">
            <div className="flex items-center gap-1">
              <CalendarIcon className="h-4 w-4" />
              <span>{formattedDate}</span>
            </div>
            <div className="flex items-center gap-1">
              <Clock className="h-4 w-4" />
              <span>
                {formattedStartTime} {formattedEndTime && `- ${formattedEndTime}`}
              </span>
            </div>
            {activity.address && (
              <div className="flex items-center gap-1">
                <MapPinIcon className="h-4 w-4" />
                <span>{activity.address}</span>
              </div>
            )}
            {activity.max_participants && (
              <div className="flex items-center gap-1">
                <Users className="h-4 w-4" />
                <span>
                  {activity.current_participants || 0}/{activity.max_participants} participants
                </span>
              </div>
            )}
            {activity.is_paid && activity.price && (
              <div className="flex items-center gap-1">
                <DollarSign className="h-4 w-4" />
                <span>${activity.price.toFixed(2)}</span>
              </div>
            )}
          </div>
          
          {/* Action buttons */}
          <div className="pt-2">
            {canJoin && user && (
              <ActivityJoin activity={activity} />
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
