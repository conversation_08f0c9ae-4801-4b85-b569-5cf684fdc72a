
import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/use-auth';
import { OnboardingModalWrapper } from './OnboardingModalWrapper';
import { SubscriptionModal } from '../subscription/SubscriptionModal';

/**
 * Criteria for determining if a user needs onboarding:
 * 1. Missing required profile fields (display_name, avatar_url) - Step 1
 * 2. Missing birthday and gender - Step 2
 * 3. Missing default location - Step 3
 * 4. Missing vibes selection - Step 6
 * 5. Missing location permissions - Step 7
 */
export function isProfileIncomplete(profile: any): boolean {
  if (!profile) return true;

  // IMPORTANT: First check if onboarding is explicitly marked as completed
  if (profile.onboarding_completed === true) {
    console.log('Profile has onboarding_completed flag set to true, skipping other checks');
    return false;
  }

  // Log all relevant fields for debugging
  console.log('Checking profile completeness:', {
    display_name: !!profile.display_name,
    avatar_url: !!profile.avatar_url, // Avatar is required
    birthday: !!profile.birthday,
    gender: !!profile.gender,
    default_location: !!profile.default_location,
    vibes: Array.isArray(profile.vibes) ? profile.vibes.length : 0,
    location_permission_granted: !!profile.location_permission_granted,
    onboarding_completed: !!profile.onboarding_completed
  });

  // Check for all required fields
  const hasRequiredFields =
    !!profile.display_name &&
    !!profile.avatar_url &&
    !!profile.birthday &&
    !!profile.gender &&
    !!profile.default_location &&
    !!profile.location_permission_granted;

  // Check for vibes (required)
  const hasVibes = Array.isArray(profile.vibes) && profile.vibes.length > 0;

  // If any required field is missing, onboarding is incomplete
  if (!hasRequiredFields || !hasVibes) {
    console.log('Profile is incomplete. Missing required fields:', {
      display_name: !!profile.display_name,
      avatar_url: !!profile.avatar_url,
      birthday: !!profile.birthday,
      gender: !!profile.gender,
      default_location: !!profile.default_location,
      vibes: hasVibes,
      location_permission_granted: !!profile.location_permission_granted
    });
    return true;
  }

  console.log('Profile is complete, all required fields are present');
  return false;
}

/**
 * OnboardingController - Automatically shows the onboarding modal when needed
 *
 * This component checks if a user needs to complete their profile and
 * automatically shows the onboarding modal if necessary.
 */
export function OnboardingController() {
  const { user, profile, refreshProfile } = useAuth();
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [showSubscription, setShowSubscription] = useState(false);
  const [hasChecked, setHasChecked] = useState(false);
  const [justCompletedOnboarding, setJustCompletedOnboarding] = useState(false);

  // Store onboarding state in localStorage to track if it was already shown after signup
  const onboardingKey = user ? `onboarding_shown_${user.id}` : null;

  useEffect(() => {
    // Only check once auth is loaded and we haven't checked yet
    if (user && profile && !hasChecked) {
      // Check if this is a fresh signup that needs onboarding
      const isNewSignup = localStorage.getItem('just_signed_up') === 'true';

      // ONLY show onboarding modal for new signups - nowhere else
      if (isNewSignup) {
        console.log('New signup detected, showing onboarding modal');
        setShowOnboarding(true);
        // Clear the signup flag
        localStorage.removeItem('just_signed_up');
      }

      setHasChecked(true);
    }
  }, [user, profile, hasChecked]);

  // When onboarding is closed
  const handleOnboardingClose = async (open: boolean) => {
    // If trying to close the modal
    if (!open && user) {
      // Force refresh profile data to get updated onboarding status
      await refreshProfile();

      // Check if onboarding is complete
      const updatedProfile = await waitForProfileUpdate(user.id);
      console.log("Profile after refresh:", updatedProfile);

      // Only allow closing if onboarding is complete
      if (updatedProfile?.onboarding_completed === true) {
        console.log("Onboarding is complete, allowing modal to close");

        // Store that we've shown onboarding to this user with a special "completed" flag
        // This will prevent the onboarding modal from showing again on any page
        if (onboardingKey) {
          localStorage.setItem(onboardingKey, "completed:" + new Date().toISOString());
        }

        setJustCompletedOnboarding(true);
        setShowOnboarding(false);

        // Show subscription modal after a short delay for better UX
        setTimeout(() => {
          setShowSubscription(true);
        }, 500);
      } else {
        // If onboarding is not complete, prevent closing the modal
        console.log("Onboarding is not complete, preventing modal from closing");
        setShowOnboarding(true);
        return;
      }
    } else {
      // If opening the modal, update state
      setShowOnboarding(open);
    }
  };

  // Wait for profile update and return the updated profile
  const waitForProfileUpdate = async (userId: string): Promise<any> => {
    // Force a refresh of the profile data
    await refreshProfile();

    // Return the updated profile
    return profile;
  };

  // Handle subscription modal close
  const handleSubscriptionClose = (open: boolean) => {
    setShowSubscription(open);
    if (!open) {
      setJustCompletedOnboarding(false);
    }
  };

  // Handle subscription selection
  const handleSubscribe = (plan: string) => {
    console.log(`User selected ${plan} subscription plan after onboarding`);
    // You can implement actual subscription logic here
    setShowSubscription(false);
  };

  if (!user || !profile) {
    return null;
  }

  return (
    <>
      <OnboardingModalWrapper
        open={showOnboarding}
        onOpenChange={handleOnboardingClose}
        user={user}
        profile={profile}
      />

      <SubscriptionModal
        open={showSubscription}
        onOpenChange={handleSubscriptionClose}
        onSubscribe={handleSubscribe}
      />
    </>
  );
}
