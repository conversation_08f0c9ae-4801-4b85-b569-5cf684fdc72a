
// Import the fixed UserProfile type
import { UserProfile, UserWithProfile } from '@/types/user-profile';
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  MapPin,
  Clock,
  ChevronRight,
  MessageCircle,
  X
} from 'lucide-react';
import { formatDistanceToNow } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { supabase } from '@/integrations/supabase/client';

interface MeetupUsersSidebarProps {
  activityId?: string;
  onClose: () => void;
  className?: string;
  userLocation?: { lng: number; lat: number; }; // Add userLocation prop
}

export function MeetupUsersSidebar({
  activityId,
  onClose,
  className = "",
  userLocation // Add to props destructuring
}: MeetupUsersSidebarProps) {
  const navigate = useNavigate();
  const [users, setUsers] = useState<UserWithProfile[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!activityId) return;

    const fetchUsers = async () => {
      setLoading(true);
      try {
        // Get participants for this activity
        const { data: participants, error } = await supabase
          .from('activity_participants')
          .select('*');

        if (error) throw error;

        if (!participants || participants.length === 0) {
          setUsers([]);
          setLoading(false);
          return;
        }

        // Get profiles for these users
        const userIds = participants.map(p => p.user_id);
        const { data: profiles, error: profilesError } = await supabase
          .from('profiles')
          .select('id, display_name, username, avatar_url, bio, location')
          .in('id', userIds);

        if (profilesError) throw profilesError;

        // Combine participant data with profile data
        const usersWithProfiles = participants.map(participant => {
          // Find matching profile
          const profile = profiles?.find(p => p.id === participant.user_id);

          // For each user, determine if they're online and their distance
          const isOnline = Math.random() > 0.5; // Placeholder logic
          const distance = Math.floor(Math.random() * 10); // Placeholder distance in km

          return {
            user_id: participant.user_id,
            activity_id: activityId,
            created_at: participant.created_at,
            updated_at: participant.updated_at,
            status: participant.status || 'confirmed',
            role: 'participant', // Default role value if not present
            profile: profile ? {
              display_name: profile.display_name,
              username: profile.username,
              avatar_url: profile.avatar_url,
              bio: profile.bio,
              location: profile.location,
              last_seen_at: new Date().toISOString() // Placeholder
            } : {
              display_name: 'Unknown User',
              username: 'unknown',
              avatar_url: null
            },
            is_online: isOnline,
            distance: distance
          } as UserWithProfile;
        });

        setUsers(usersWithProfiles);
      } catch (error) {
        console.error('Error fetching users for activity:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchUsers();
  }, [activityId]);

  const handleStartChat = (userId: string) => {
    navigate(`/chat?user=${userId}`);
  };

  const handleViewProfile = (userId: string) => {
    // Find the user to get their username
    const user = users.find(u => u.user_id === userId);
    // Use clean URL structure if username is available, otherwise fall back to /profile/id
    navigate(user?.profile?.username ? `/${user.profile.username}` : `/profile/${userId}`);
  };

  return (
    <div className={`border-l h-full flex flex-col bg-background w-72 ${className}`}>
      <div className="p-3 border-b flex justify-between items-center">
        <h3 className="font-semibold text-lg">
          People ({users.length})
        </h3>
        <Button variant="ghost" size="sm" onClick={onClose} className="h-8 w-8 p-0">
          <X className="h-4 w-4" />
        </Button>
      </div>

      <ScrollArea className="flex-1">
        {loading ? (
          <div className="p-4 text-center text-muted-foreground">
            Loading participants...
          </div>
        ) : users.length > 0 ? (
          <div className="p-2">
            {users.map((user) => (
              <div key={user.user_id} className="rounded-lg p-3 hover:bg-accent">
                <div className="flex items-start gap-3">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={user.profile?.avatar_url || ''} />
                    <AvatarFallback>
                      {(user.profile?.display_name || user.profile?.username || '?').substring(0, 2)}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="font-medium truncate">
                        {user.profile?.display_name || user.profile?.username}
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {user.role === 'host' ? 'Host' : 'Participant'}
                      </Badge>
                    </div>

                    <div className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                      <Clock className="h-3 w-3" />
                      {user.profile?.last_seen_at ? (
                        <>Active {formatDistanceToNow(new Date(user.profile.last_seen_at), { addSuffix: true })}</>
                      ) : (
                        <>Unknown</>
                      )}
                    </div>

                    {user.profile?.location && (
                      <div className="text-sm text-muted-foreground flex items-center gap-1 mt-1">
                        <MapPin className="h-3 w-3" />
                        {user.distance}km away
                      </div>
                    )}

                    <div className="flex gap-1 mt-2">
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-xs px-2 flex-1"
                        onClick={() => handleViewProfile(user.user_id)}
                      >
                        Profile
                        <ChevronRight className="h-3 w-3 ml-1" />
                      </Button>

                      <Button
                        size="sm"
                        className="text-xs px-2"
                        onClick={() => handleStartChat(user.user_id)}
                      >
                        <MessageCircle className="h-3 w-3 mr-1" />
                        Chat
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="p-6 text-center">
            <div className="text-muted-foreground mb-2">No participants yet</div>
            <p className="text-sm text-muted-foreground">
              Be the first to join this activity!
            </p>
          </div>
        )}
      </ScrollArea>
    </div>
  );
}
