
import { useState, useEffect, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/hooks/use-auth';

interface LocationOptions {
  enableHighAccuracy?: boolean;
  maximumAge?: number;
  timeout?: number;
}

export function useRealtimeLocation(options: LocationOptions = {}) {
  const [location, setLocation] = useState<GeolocationCoordinates | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isTracking, setIsTracking] = useState(false);
  const [accuracy, setAccuracy] = useState<number | null>(null);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const { toast } = useToast();
  const { user } = useAuth();

  // Helper function to update location
  const updateLocation = useCallback(async (position: GeolocationPosition) => {
    setLocation(position.coords);
    setAccuracy(position.coords.accuracy);
    setError(null);
    setLastUpdate(new Date());

    // Update location in Supabase if user is logged in
    if (user && position.coords.latitude && position.coords.longitude) {
      // Format the point data correctly for PostGIS
      const { error } = await supabase
        .from('user_locations')
        .upsert({
          user_id: user.id,
          location: `POINT(${position.coords.longitude} ${position.coords.latitude})`,
          updated_at: new Date().toISOString()
        });

      if (error) {
        console.error('Error updating location:', error);
        toast({
          title: 'Location Update Failed',
          description: 'Could not save your location',
          variant: 'destructive'
        });
      }
    }
  }, [user, toast]);

  // Start tracking with optimized settings
  const startTracking = useCallback(() => {
    if (!user) {
      setError('Authentication required');
      toast({
        title: 'Authentication Required',
        description: 'Please log in to share your location',
        variant: 'destructive'
      });
      return;
    }

    if (!navigator.geolocation) {
      setError('Geolocation is not supported by your browser');
      return;
    }

    setIsTracking(true);

    // Initial location update with longer timeout and optional high accuracy
    navigator.geolocation.getCurrentPosition(
      updateLocation,
      (error) => {
        setError(getLocationErrorMessage(error));
        setIsTracking(false);
        toast({
          title: 'Location Error',
          description: getLocationErrorMessage(error),
          variant: 'destructive'
        });
      },
      {
        enableHighAccuracy: options.enableHighAccuracy ?? false, // Make high accuracy optional
        maximumAge: options.maximumAge ?? 30000, // Allow cached positions up to 30 seconds old
        timeout: options.timeout ?? 10000 // Increase timeout to 10 seconds
      }
    );

    // Set up 5-minute interval updates with more lenient settings
    const intervalId = setInterval(() => {
      navigator.geolocation.getCurrentPosition(
        updateLocation,
        (error) => {
          console.error('Error updating location:', error);
          // Don't show toast for background updates to avoid spam
          setError(getLocationErrorMessage(error));
        },
        {
          enableHighAccuracy: false, // Use low accuracy for background updates
          maximumAge: 60000, // Allow positions up to 1 minute old
          timeout: 10000 // 10 second timeout
        }
      );
    }, 5 * 60 * 1000);

    return () => {
      clearInterval(intervalId);
      setIsTracking(false);
    };
  }, [options, toast, user, updateLocation]);

  const stopTracking = useCallback(() => {
    setIsTracking(false);
  }, []);

  // Helper function to get readable error messages
  const getLocationErrorMessage = (error: GeolocationPositionError): string => {
    switch (error.code) {
      case error.PERMISSION_DENIED:
        return 'Location permission denied. Please enable location services.';
      case error.POSITION_UNAVAILABLE:
        return 'Location information is unavailable.';
      case error.TIMEOUT:
        return 'Location request timed out.';
      default:
        return 'An unknown error occurred while getting location.';
    }
  };

  return {
    location,
    error,
    isTracking,
    accuracy,
    lastUpdate,
    startTracking,
    stopTracking
  };
}
