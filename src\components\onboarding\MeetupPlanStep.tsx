import React, { useState } from 'react';
import { UseFormReturn } from 'react-hook-form';
import { FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import { OnboardingFormValues, FavoriteLocation } from "@/types/onboarding";
import { MapPin, Clock, Coffee, Briefcase, X } from "lucide-react";
import { Card, CardContent } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AddressLocationPicker } from '@/components/map/AddressLocationPicker';

interface MeetupPlanStepProps {
  form: UseFormReturn<OnboardingFormValues>;
}

export const MeetupPlanStep = ({ form }: MeetupPlanStepProps) => {
  const initialLocations = (form.watch("favorite_locations") || []) as FavoriteLocation[];
  const [locations, setLocations] = useState<FavoriteLocation[]>(initialLocations);
  const [locationName, setLocationName] = useState("");
  const [availability, setAvailability] = useState("");
  const [selectedLocation, setSelectedLocation] = useState<{ lng: number; lat: number; address: string; coordinates: [number, number]; } | null>(null);
  const [isAddingCustom, setIsAddingCustom] = useState(false);
  
  const handleTemplateClick = (template: string) => {
    if (template === "coffee") {
      const newLocation: FavoriteLocation = {
        name: "Coffee Chat",
        location: { x: 0, y: 0 },
        availability: "Weekends"
      };
      const newLocations = [...locations, newLocation];
      setLocations(newLocations);
      form.setValue("favorite_locations", newLocations);
    } else if (template === "task") {
      const newLocation: FavoriteLocation = {
        name: "Task Helper",
        location: { x: 0, y: 0 },
        availability: "Weekday afternoons"
      };
      const newLocations = [...locations, newLocation];
      setLocations(newLocations);
      form.setValue("favorite_locations", newLocations);
    }
  };

  const handleAddLocation = () => {
    if (locationName && selectedLocation) {
      const newLocation: FavoriteLocation = {
        name: locationName,
        location: { 
          x: selectedLocation.lng, 
          y: selectedLocation.lat 
        },
        availability: availability || "Anytime"
      };
      
      const updatedLocations = [...locations, newLocation];
      setLocations(updatedLocations);
      form.setValue("favorite_locations", updatedLocations);
      
      setLocationName("");
      setAvailability("");
      setSelectedLocation(null);
      setIsAddingCustom(false);
    }
  };

  const handleRemoveLocation = (index: number) => {
    const updatedLocations = [...locations];
    updatedLocations.splice(index, 1);
    setLocations(updatedLocations);
    form.setValue("favorite_locations", updatedLocations);
  };

  const handleLocationSelect = (location: { lng: number; lat: number; address: string; coordinates: [number, number]; }) => {
    setSelectedLocation(location);
  };

  const availabilityOptions = [
    { value: "weekends", label: "Weekends" },
    { value: "weekdays", label: "Weekdays" },
    { value: "evenings", label: "Evenings" },
    { value: "mornings", label: "Mornings" },
    { value: "afternoons", label: "Afternoons" },
    { value: "anytime", label: "Anytime" },
  ];

  return (
    <div className="space-y-6">
      <div className="text-center mb-6">
        <h2 className="text-2xl font-bold">Meetup Plan</h2>
        <p className="text-muted-foreground">Set your favorite locations and availability</p>
      </div>
      
      <FormField
        control={form.control}
        name="favorite_locations"
        render={({ field }) => (
          <FormItem>
            {!isAddingCustom ? (
              <>
                <FormLabel>Quick Start Templates</FormLabel>
                <div className="grid grid-cols-2 gap-3 mb-6">
                  <Button 
                    type="button" 
                    variant="outline"
                    className="flex gap-2 justify-center h-auto py-6 flex-col relative overflow-hidden group"
                    onClick={() => handleTemplateClick("coffee")}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-amber-50 to-amber-100 opacity-50 group-hover:opacity-70 transition-opacity"></div>
                    <Coffee className="h-8 w-8 z-10 text-amber-600" />
                    <div className="z-10">
                      <span className="block font-medium">Coffee & Chat</span>
                      <span className="text-xs text-muted-foreground">Weekend meetups at cafés</span>
                    </div>
                  </Button>
                  <Button 
                    type="button" 
                    variant="outline"
                    className="flex gap-2 justify-center h-auto py-6 flex-col relative overflow-hidden group"
                    onClick={() => handleTemplateClick("task")}
                  >
                    <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-blue-100 opacity-50 group-hover:opacity-70 transition-opacity"></div>
                    <Briefcase className="h-8 w-8 z-10 text-blue-600" />
                    <div className="z-10">
                      <span className="block font-medium">Task Helper</span>
                      <span className="text-xs text-muted-foreground">Weekday assistance nearby</span>
                    </div>
                  </Button>
                </div>

                <div className="flex justify-between items-center mb-3">
                  <FormLabel className="mb-0">Your Locations</FormLabel>
                  <Button 
                    type="button" 
                    variant="outline" 
                    size="sm"
                    onClick={() => setIsAddingCustom(true)}
                    className="text-xs"
                  >
                    Add Custom Location
                  </Button>
                </div>
              </>
            ) : (
              <div className="mb-6">
                <div className="flex justify-between items-center mb-3">
                  <FormLabel className="mb-0">Add Custom Location</FormLabel>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsAddingCustom(false)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
                <div className="space-y-4">
                  <Input 
                    placeholder="Location name" 
                    value={locationName}
                    onChange={(e) => setLocationName(e.target.value)}
                  />
                  
                  <Select value={availability} onValueChange={setAvailability}>
                    <SelectTrigger>
                      <SelectValue placeholder="When are you available?" />
                    </SelectTrigger>
                    <SelectContent>
                      {availabilityOptions.map(option => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  
                  <AddressLocationPicker 
                    onLocationSelect={handleLocationSelect}
                    title="Select Meet-up Location"
                    description="Choose where you'd like to meet others"
                    className="mb-4"
                  />
                  
                  <div className="flex justify-end gap-2">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setIsAddingCustom(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="button"
                      onClick={handleAddLocation}
                      disabled={!locationName || !selectedLocation}
                    >
                      Add Location
                    </Button>
                  </div>
                </div>
              </div>
            )}
            
            <div className="space-y-2 mt-4">
              {locations.map((loc, index) => (
                <Card key={index} className="overflow-hidden">
                  <CardContent className="p-0">
                    <div className="flex justify-between items-center p-3">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-primary-purple" />
                        <span className="font-medium">{loc.name}</span>
                      </div>
                      <Button 
                        variant="ghost" 
                        size="sm"
                        onClick={() => handleRemoveLocation(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    {loc.availability && (
                      <div className="px-3 pb-3 pt-0 flex items-center text-sm text-muted-foreground">
                        <Clock className="h-3 w-3 mr-1" />
                        Available: {loc.availability}
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
              
              {locations.length === 0 && !isAddingCustom && (
                <div className="text-center py-12 border-2 border-dashed rounded-lg border-gray-200">
                  <MapPin className="h-8 w-8 mx-auto mb-2 text-muted-foreground opacity-50" />
                  <p className="text-muted-foreground">No locations added yet</p>
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={() => setIsAddingCustom(true)}
                    className="mt-2"
                  >
                    Add your first location
                  </Button>
                </div>
              )}
            </div>
            
            {!isAddingCustom && (
              <div className="text-center mt-6 bg-muted/30 p-4 rounded-lg">
                <p className="text-sm text-muted-foreground">
                  Nearby users complete <span className="font-bold text-primary-purple">3.2 meetups/day</span> on average
                </p>
              </div>
            )}
            
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
};
