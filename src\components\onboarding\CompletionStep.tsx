
import React from 'react';
import { UserAvatar } from "@/components/user/UserAvatar";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { OnboardingFormValues, purposeOptions, allVibes } from "@/types/onboarding";
import { format } from "date-fns";
import { CompletionStepProps } from "@/types/onboarding-props";

export const CompletionStep = ({ onSubmit, isLoading, initialValues }: CompletionStepProps) => {
  // Use initialValues instead of formData
  const formData = initialValues as OnboardingFormValues;
  const previewUrl = formData?.avatar_url || null;
  
  // Find purpose labels based on selected IDs
  const selectedPurposes = purposeOptions
    .filter(purpose => formData?.purposes?.includes(purpose.id))
    .map(purpose => ({ label: purpose.label, icon: purpose.icon }));
  
  // Find vibe labels based on selected IDs
  const selectedVibes = allVibes
    .filter(vibe => formData?.vibes?.includes(vibe.id))
    .map(vibe => vibe.label);

  return (
    <div className="space-y-6 text-center">
      <div className="flex flex-col items-center space-y-4">
        <div className="relative">
          <div className="absolute inset-0 rounded-full bg-gradient-to-r from-primary to-primary-purple/60 blur-lg opacity-60"></div>
          <div className="relative w-24 h-24 rounded-full overflow-hidden border-4 border-background">
            {previewUrl ? (
              <img 
                src={previewUrl} 
                alt="Profile" 
                className="w-full h-full object-cover"
              />
            ) : (
              <UserAvatar 
                user={{ 
                  avatar_url: formData?.avatar_url,
                  display_name: formData?.display_name,
                  username: formData?.username
                }} 
                size="xl"
              />
            )}
          </div>
        </div>
        
        <h2 className="text-2xl font-bold">Profile Complete!</h2>
        <p className="text-muted-foreground">
          Welcome to BuddySurf, {formData?.display_name}! Your profile is ready to go.
        </p>
        
        <div className="py-4 px-2">
          <Card className="w-full bg-muted/30">
            <CardContent className="p-4">
              <div className="grid grid-cols-2 gap-4 text-sm text-left">
                <div>
                  <div className="font-medium">Display Name</div>
                  <div className="text-muted-foreground">{formData.display_name}</div>
                </div>
                <div>
                  <div className="font-medium">Username</div>
                  <div className="text-muted-foreground">@{formData.username}</div>
                </div>
                
                {formData.birthday && (
                  <div>
                    <div className="font-medium">Birthday</div>
                    <div className="text-muted-foreground">
                      {format(formData.birthday, "MMMM d, yyyy")}
                    </div>
                  </div>
                )}
                
                {formData.gender && (
                  <div>
                    <div className="font-medium">Gender</div>
                    <div className="text-muted-foreground capitalize">
                      {formData.gender.replace('_', ' ')}
                    </div>
                  </div>
                )}
                
                {selectedPurposes.length > 0 && (
                  <div className="col-span-2">
                    <div className="font-medium mb-1">Why I'm Here</div>
                    <div className="flex flex-wrap gap-1.5">
                      {selectedPurposes.map((purpose, index) => (
                        <Badge key={index} variant="secondary">
                          {purpose.icon} {purpose.label}
                        </Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {selectedVibes.length > 0 && (
                  <div className="col-span-2">
                    <div className="font-medium mb-1">My Vibe</div>
                    <div className="flex flex-wrap gap-1.5">
                      {selectedVibes.map((vibe, index) => (
                        <Badge key={index} variant="outline">{vibe}</Badge>
                      ))}
                    </div>
                  </div>
                )}
                
                {formData.bio && (
                  <div className="col-span-2">
                    <div className="font-medium">Bio</div>
                    <div className="text-muted-foreground">{formData.bio}</div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="w-full max-w-md mx-auto bg-gradient-to-r from-primary-purple/10 to-blue-100/30 rounded-lg p-4 border border-primary-purple/30">
          <h3 className="font-semibold text-primary-purple">🎉 Your vibe attracted 12 nearby users!</h3>
          <p className="text-sm text-muted-foreground mt-2">
            Next, you'll see our subscription options. Choose the plan that best fits your social lifestyle!
          </p>
        </div>
      </div>
    </div>
  );
};
