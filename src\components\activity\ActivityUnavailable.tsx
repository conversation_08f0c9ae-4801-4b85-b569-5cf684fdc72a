
import React from 'react';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useNavigate } from 'react-router-dom';
import { AlertCircle } from 'lucide-react';

interface ActivityUnavailableProps {
  className?: string;
  title?: string;
  description?: string;
  showReturnButton?: boolean;
  showSetupButton?: boolean;
}

export function ActivityUnavailable({
  className = "",
  title = "Activity Feature Unavailable",
  description = "The activities feature is currently unavailable. The necessary database tables may not be set up yet.",
  showReturnButton = true,
  showSetupButton = false
}: ActivityUnavailableProps) {
  const navigate = useNavigate();

  return (
    <Card className={`max-w-3xl mx-auto ${className}`}>
      <CardContent className="p-8 text-center">
        <div className="bg-red-50 p-4 rounded-full inline-flex items-center justify-center mb-6">
          <AlertCircle className="h-12 w-12 text-red-500" />
        </div>
        <h2 className="text-2xl font-bold mb-4">{title}</h2>
        <p className="text-gray-600 mb-6 max-w-md mx-auto">
          {description}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          {showReturnButton && (
            <Button variant="outline" onClick={() => navigate('/')}>
              Return Home
            </Button>
          )}
          <Button onClick={() => navigate('/meetmap')}>
            Explore Map
          </Button>
          {showSetupButton && (
            <Button 
              variant="default" 
              onClick={() => navigate('/admin/setup')}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Setup Activities
            </Button>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
