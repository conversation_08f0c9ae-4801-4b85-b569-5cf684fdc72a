"use client"

import React, { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { CalendarIcon, ChevronLeft, ChevronRight } from "lucide-react";
import { format, subYears, isValid } from "date-fns";

interface YearFirstDatePickerProps {
  value?: Date;
  onChange: (date: Date | undefined) => void;
  className?: string;
  placeholder?: string;
  minAge?: number;
  maxAge?: number;
  label?: string;
  description?: string;
  error?: string;
  required?: boolean;
  id?: string; // Add ID prop for accessibility
  name?: string; // Add name prop for form association
}

export function YearFirstDatePicker({
  value,
  onChange,
  className,
  placeholder = "Select your birthday",
  minAge = 13,
  maxAge = 100,
  label,
  description,
  error,
  required = false,
  id, // Use the ID prop
  name // Use the name prop
}: YearFirstDatePickerProps) {
  // Generate a unique ID if not provided
  const uniqueId = id || `date-picker-${Math.random().toString(36).substring(2, 9)}`;
  // Use the name prop or fallback to the ID
  const fieldName = name || uniqueId;
  const [date, setDate] = useState<Date | undefined>(value);
  const [view, setView] = useState<'year' | 'month' | 'date'>('year'); // Start with year view
  const [yearDecade, setYearDecade] = useState<number>(new Date().getFullYear() - 30); // Start with a reasonable decade for 18+ users
  const [selectedMonth, setSelectedMonth] = useState<number | null>(date?.getMonth() ?? null);
  const [selectedYear, setSelectedYear] = useState<number | null>(date?.getFullYear() ?? null);
  const [isOpen, setIsOpen] = useState(false);

  // Calculate the maximum and minimum dates based on minAge and maxAge
  const maxDate = subYears(new Date(), minAge);
  const minDate = subYears(new Date(), maxAge);

  // Update the internal date when the value prop changes
  useEffect(() => {
    setDate(value);
    if (value) {
      setSelectedMonth(value.getMonth());
      setSelectedYear(value.getFullYear());
    }
  }, [value]);

  // Months array for selection
  const months = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ];

  // Generate years for the year picker (12 years per view)
  const years = Array.from({ length: 12 }, (_, i) => yearDecade + i);

  // Navigate between decades
  const prevDecade = () => setYearDecade(yearDecade - 12);
  const nextDecade = () => setYearDecade(yearDecade + 12);

  // Handle year selection
  const handleYearSelect = (year: number) => {
    setSelectedYear(year);
    setView('month'); // Move to month selection after year is selected
  };

  // Handle month selection
  const handleMonthSelect = (monthIndex: number) => {
    setSelectedMonth(monthIndex);
    setView('date'); // Move to date selection after month is selected
  };

  // Handle day selection
  const handleDaySelect = (day: number) => {
    if (selectedMonth !== null && selectedYear !== null) {
      const newDate = new Date(selectedYear, selectedMonth, day);
      setDate(newDate);
      onChange(newDate);
      setIsOpen(false); // Close the popover after selection
    }
  };

  // Get days in the selected month
  const getDaysInMonth = () => {
    if (selectedMonth === null || selectedYear === null) return [];

    const daysInMonth = new Date(selectedYear, selectedMonth + 1, 0).getDate();
    return Array.from({ length: daysInMonth }, (_, i) => i + 1);
  };

  // Get the first day of the month (0 = Sunday, 1 = Monday, etc.)
  const getFirstDayOfMonth = () => {
    if (selectedMonth === null || selectedYear === null) return 0;

    return new Date(selectedYear, selectedMonth, 1).getDay();
  };

  // Render the year selection view
  const renderYearView = () => {
    return (
      <div className="p-3">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            className="h-7 w-7 p-0"
            onClick={prevDecade}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="text-sm font-medium">
            {yearDecade} - {yearDecade + 11}
          </div>
          <Button
            variant="ghost"
            className="h-7 w-7 p-0"
            onClick={nextDecade}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>

        <div className="grid grid-cols-3 gap-2">
          {years.map(year => {
            const isSelected = selectedYear === year;
            const isDisabled = year > maxDate.getFullYear() || year < minDate.getFullYear();

            return (
              <Button
                key={year}
                variant={isSelected ? "default" : "outline"}
                className={cn(
                  "h-10",
                  isDisabled && "opacity-50 cursor-not-allowed"
                )}
                disabled={isDisabled}
                onClick={() => !isDisabled && handleYearSelect(year)}
              >
                {year}
              </Button>
            );
          })}
        </div>
      </div>
    );
  };

  // Render the month selection view
  const renderMonthView = () => {
    return (
      <div className="p-3">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            className="h-7 w-7 p-0"
            onClick={() => setView('year')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="text-sm font-medium">
            {selectedYear}
          </div>
          <div className="h-7 w-7"></div> {/* Empty div for alignment */}
        </div>

        <div className="grid grid-cols-3 gap-2">
          {months.map((month, index) => {
            const isSelected = selectedMonth === index;

            // Check if this month in the selected year is valid (within age range)
            const monthDate = new Date(selectedYear!, index, 15); // Use middle of month for comparison
            const isDisabled = monthDate > maxDate || monthDate < minDate;

            return (
              <Button
                key={month}
                variant={isSelected ? "default" : "outline"}
                className={cn(
                  "h-10",
                  isDisabled && "opacity-50 cursor-not-allowed"
                )}
                disabled={isDisabled}
                onClick={() => !isDisabled && handleMonthSelect(index)}
              >
                {month.substring(0, 3)}
              </Button>
            );
          })}
        </div>
      </div>
    );
  };

  // Render the date selection view
  const renderDateView = () => {
    const days = getDaysInMonth();
    const firstDay = getFirstDayOfMonth();
    const dayNames = ['Su', 'Mo', 'Tu', 'We', 'Th', 'Fr', 'Sa'];

    // Create empty cells for days before the first day of the month
    const emptyCells = Array.from({ length: firstDay }, (_, i) => (
      <div key={`empty-${i}`} className="h-8 w-8"></div>
    ));

    return (
      <div className="p-3">
        <div className="flex items-center justify-between mb-4">
          <Button
            variant="ghost"
            className="h-7 w-7 p-0"
            onClick={() => setView('month')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <div className="text-sm font-medium">
            {months[selectedMonth!]} {selectedYear}
          </div>
          <div className="h-7 w-7"></div> {/* Empty div for alignment */}
        </div>

        <div className="grid grid-cols-7 gap-1 mb-2">
          {dayNames.map(day => (
            <div key={day} className="h-8 w-8 flex items-center justify-center text-xs text-muted-foreground">
              {day}
            </div>
          ))}
        </div>

        <div className="grid grid-cols-7 gap-1">
          {emptyCells}
          {days.map(day => {
            const currentDate = new Date(selectedYear!, selectedMonth!, day);
            const isSelected = date && date.getDate() === day &&
                              date.getMonth() === selectedMonth &&
                              date.getFullYear() === selectedYear;
            const isDisabled = currentDate > maxDate || currentDate < minDate;

            return (
              <Button
                key={day}
                variant={isSelected ? "default" : "ghost"}
                className={cn(
                  "h-8 w-8 p-0 font-normal",
                  isDisabled && "opacity-50 cursor-not-allowed",
                  isSelected && "text-primary-foreground"
                )}
                disabled={isDisabled}
                onClick={() => !isDisabled && handleDaySelect(day)}
              >
                {day}
              </Button>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <div className={cn("space-y-2", className)}>
      {label && (
        <div className="flex items-center">
          <label htmlFor={uniqueId} className="text-sm font-medium">{label}</label>
          {required && <span className="text-red-500 ml-1">*</span>}
        </div>
      )}

      {description && (
        <p className="text-xs text-gray-500" id={`${uniqueId}-description`}>{description}</p>
      )}

      <Popover open={isOpen} onOpenChange={setIsOpen}>
        <PopoverTrigger asChild>
          <Button
            id={uniqueId}
            name={fieldName}
            type="button"
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !date && "text-muted-foreground",
              error && "border-red-500"
            )}
            aria-describedby={description ? `${uniqueId}-description` : undefined}
            aria-required={required}
            aria-invalid={!!error}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {date ? format(date, "MMMM d, yyyy") : placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          {view === 'year' && renderYearView()}
          {view === 'month' && renderMonthView()}
          {view === 'date' && renderDateView()}
        </PopoverContent>
      </Popover>

      {error && (
        <p className="text-sm text-red-500" id={`${uniqueId}-error`}>{error}</p>
      )}

      {/* Hidden input to store the actual date value for form submission */}
      <input
        type="hidden"
        id={`${uniqueId}-hidden`}
        name={`${fieldName}-hidden`}
        value={date ? date.toISOString() : ''}
        aria-hidden="true"
      />
    </div>
  );
}
