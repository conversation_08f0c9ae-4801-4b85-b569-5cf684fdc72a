
/**
 * Extracts initials from a display name or username
 * @param name The name to extract initials from
 * @param fallback Fallback character if no name is provided
 * @returns Up to 2 capital letters representing the initials
 */
export function getInitials(name?: string, fallback: string = 'U'): string {
  if (!name) return fallback.substring(0, 1).toUpperCase();

  const parts = name.trim().split(/\s+/);
  
  if (parts.length === 1) {
    return name.substring(0, 2).toUpperCase();
  }
  
  return (parts[0][0] + parts[parts.length - 1][0]).toUpperCase();
}
