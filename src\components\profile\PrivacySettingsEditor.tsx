
import React from 'react';
import { Switch } from "@/components/ui/switch";
import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { ProfileVisibilitySettings } from "@/types/enhanced-profile";
import { Eye, EyeOff, Users, Image, Activity, FileText } from "lucide-react";

interface PrivacySettingsEditorProps {
  isProfilePublic: boolean;
  onToggleProfilePublic: (value: boolean) => void;
  visibilitySettings: ProfileVisibilitySettings;
  onUpdateVisibility: (settings: ProfileVisibilitySettings) => void;
}

export function PrivacySettingsEditor({
  isProfilePublic,
  onToggleProfilePublic,
  visibilitySettings,
  onUpdateVisibility,
}: PrivacySettingsEditorProps) {
  // Handle individual visibility setting toggle
  const handleToggleSetting = (key: keyof ProfileVisibilitySettings) => {
    onUpdateVisibility({
      ...visibilitySettings,
      [key]: !visibilitySettings[key],
    });
  };

  return (
    <div className="space-y-6">
      {/* Public/Private Profile Toggle */}
      <Card className="p-6">
        <div className="flex items-start gap-4">
          <div className={`p-2 rounded-full ${isProfilePublic ? 'bg-green-100 text-green-600' : 'bg-red-100 text-red-600'}`}>
            {isProfilePublic ? <Eye className="h-5 w-5" /> : <EyeOff className="h-5 w-5" />}
          </div>
          <div className="flex-1 space-y-1">
            <div className="flex items-center justify-between">
              <Label htmlFor="public-profile" className="text-base font-medium">
                Public Profile
              </Label>
              <Switch
                id="public-profile"
                checked={isProfilePublic}
                onCheckedChange={onToggleProfilePublic}
              />
            </div>
            <p className="text-sm text-muted-foreground">
              {isProfilePublic
                ? "Your profile is visible to everyone, including non-registered users."
                : "Your profile is only visible to registered users you approve."}
            </p>
          </div>
        </div>
      </Card>

      {/* Granular Section Visibility */}
      <div className="space-y-4">
        <h3 className="text-base font-medium">Section Visibility</h3>
        <p className="text-sm text-muted-foreground mb-4">
          Control which parts of your profile are visible to others when they view your profile.
          {!isProfilePublic && " (These settings apply to users who can view your profile)"}
        </p>

        <div className="grid gap-3">
          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Users className="h-4 w-4 text-blue-500" />
                <Label htmlFor="visibility-followers" className="font-normal">Followers</Label>
              </div>
              <Switch
                id="visibility-followers"
                checked={visibilitySettings.followers}
                onCheckedChange={() => handleToggleSetting('followers')}
              />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Users className="h-4 w-4 text-purple-500" />
                <Label htmlFor="visibility-following" className="font-normal">Following</Label>
              </div>
              <Switch
                id="visibility-following"
                checked={visibilitySettings.following}
                onCheckedChange={() => handleToggleSetting('following')}
              />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Activity className="h-4 w-4 text-green-500" />
                <Label htmlFor="visibility-activities" className="font-normal">Activities</Label>
              </div>
              <Switch
                id="visibility-activities"
                checked={visibilitySettings.activities}
                onCheckedChange={() => handleToggleSetting('activities')}
              />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <Image className="h-4 w-4 text-amber-500" />
                <Label htmlFor="visibility-gallery" className="font-normal">Gallery</Label>
              </div>
              <Switch
                id="visibility-gallery"
                checked={visibilitySettings.gallery}
                onCheckedChange={() => handleToggleSetting('gallery')}
              />
            </div>
          </Card>

          <Card className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <FileText className="h-4 w-4 text-red-500" />
                <Label htmlFor="visibility-about" className="font-normal">About</Label>
              </div>
              <Switch
                id="visibility-about"
                checked={visibilitySettings.about}
                onCheckedChange={() => handleToggleSetting('about')}
              />
            </div>
          </Card>
        </div>
      </div>
    </div>
  );
}
