
import { useEffect, useState } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/use-auth';

export function usePresenceSystem(channelName: string) {
  const [onlineUsers, setOnlineUsers] = useState<Record<string, any>>({});
  const [userCount, setUserCount] = useState(0);
  const { user } = useAuth();

  useEffect(() => {
    if (!user || !channelName) return;

    const channel = supabase.channel(channelName);

    const userStatus = {
      user_id: user.id,
      online_at: new Date().toISOString(),
    };

    channel
      .on('presence', { event: 'sync' }, () => {
        const presenceState = channel.presenceState();
        setOnlineUsers(presenceState);
        
        // Count unique users
        const uniqueUsers = new Set(
          Object.values(presenceState)
            .flat()
            .map(user => (user as any).user_id)
        );
        setUserCount(uniqueUsers.size);
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          await channel.track(userStatus);
        }
      });

    return () => {
      supabase.removeChannel(channel);
    };
  }, [channelName, user]);

  return { onlineUsers, userCount };
}
